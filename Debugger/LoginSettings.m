//
//  LoginSettings.m
//  Debugger
//
//  Created by <PERSON> on 2020/8/26.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "LoginSettings.h"

@implementation LoginSettings
+ (LoginSettings *)sharedInstance
{
    static LoginSettings *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[[self class] alloc] init];
    });
    return instance;
}

@end
