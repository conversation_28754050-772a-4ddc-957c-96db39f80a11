//
//  UPMacros.h
//  UplusInternational
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 17/3/21.
//  Copyright © 2017年 海尔优家智能科技（北京）有限公司. All rights reserved.
//
#import <CoreGraphics/CGBase.h>
#import <UIKit/UIKit.h>

//屏幕尺寸相关
#define SCREEN_WIDTH ([UIScreen mainScreen].bounds.size.width)
#define SCREEN_HEIGHT ([UIScreen mainScreen].bounds.size.height)
#define IOS_VERSION [[[UIDevice currentDevice] systemVersion] floatValue]
#define SCREEN_SCALE [UIScreen mainScreen].bounds.size.width / 320 //屏幕适配比例
#define SCREEN_SCALE_375 [UIScreen mainScreen].bounds.size.width / 375 //屏幕适配比例

////屏幕尺寸相关
////判断 ipad
//#define isPad ([[UIDevice currentDevice] userInterfaceIdiom] == UIUserInterfaceIdiomPad)
////判断 iPhoneX，iPhoneXs ，iPhone 11 pro
//#define IS_IPHONE_X ([UIScreen instancesRespondToSelector:@selector(currentMode)] ? CGSizeEqualToSize(CGSizeMake(1125, 2436), [[UIScreen mainScreen] currentMode].size) && !isPad : NO)
////判断 iPHoneXr，iPHone 11
//#define IS_IPHONE_Xr ([UIScreen instancesRespondToSelector:@selector(currentMode)] ? CGSizeEqualToSize(CGSizeMake(828, 1792), [[UIScreen mainScreen] currentMode].size) && !isPad : NO)
////判断 iPhoneXs Max ，iPhone 11 pro Max
//#define IS_IPHONE_Xs_Max ([UIScreen instancesRespondToSelector:@selector(currentMode)] ? CGSizeEqualToSize(CGSizeMake(1242, 2688), [[UIScreen mainScreen] currentMode].size) && !isPad : NO)
//
////判定是否刘海屏
//#define isIPhoneX ((IS_IPHONE_X == YES || IS_IPHONE_Xr == YES || IS_IPHONE_Xs_Max == YES) ? YES : NO)
#define isIPhoneX (isPhoneXImp())

CG_INLINE BOOL isPhoneXImp()
{
    BOOL isCurrentMode = [UIScreen instancesRespondToSelector:@selector(currentMode)];
    BOOL is_iPad = [[UIDevice currentDevice] userInterfaceIdiom] == UIUserInterfaceIdiomPad;
    if (!isCurrentMode || is_iPad) {
        return NO;
    }

    BOOL is_iPhone_X = CGSizeEqualToSize(CGSizeMake(1125, 2436), [[UIScreen mainScreen] currentMode].size);
    BOOL is_iPhone_Xr = CGSizeEqualToSize(CGSizeMake(828, 1792), [[UIScreen mainScreen] currentMode].size);
    BOOL is_iPhone_Xr_Big = CGSizeEqualToSize(CGSizeMake(750, 1624), [[UIScreen mainScreen] currentMode].size);
    BOOL is_iPhone_Xs_Max = CGSizeEqualToSize(CGSizeMake(1242, 2688), [[UIScreen mainScreen] currentMode].size);
    return is_iPhone_X || is_iPhone_Xr || is_iPhone_Xs_Max || is_iPhone_Xr_Big;
}

#define StatusBarHeight (isIPhoneX ? 44.0f : 20.0f)
#define StatusBarExtraHeight (isIPhoneX ? 24.0f : 0.0f)
#define TabbarHeight (isIPhoneX ? (49.f + 34.f) : 49.f)
#define TabbarSafeBottomMargin (isIPhoneX ? 34.f : 0.f)

// 颜色(RGB)
#define RGBCOLOR(r, g, b) [UIColor colorWithRed:(r) / 255.0f green:(g) / 255.0f blue:(b) / 255.0f alpha:1]
#define RGBACOLOR(r, g, b, a) [UIColor colorWithRed:(r) / 255.0f green:(g) / 255.0f blue:(b) / 255.0f alpha:(a)]
// RGB颜色转换（16进制->10进制）
#define UIColorFromRGB(rgbValue)                                         \
    [UIColor colorWithRed:((float)((rgbValue & 0xFF0000) >> 16)) / 255.0 \
                    green:((float)((rgbValue & 0xFF00) >> 8)) / 255.0    \
                     blue:((float)(rgbValue & 0xFF)) / 255.0             \
                    alpha:1.0]
