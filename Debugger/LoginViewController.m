//
//  LoginViewController.m
//  Debugger
//
//  Created by <PERSON> on 2020/8/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "LoginViewController.h"
#import <Masonry/Masonry.h>
#import "UPUserLoginApi.h"
#import "UpUserDomainHolder.h"
#import "UserCenterAPIBase.h"
#import "UpUserDomain.h"
#import <SVProgressHUD/SVProgressHUD.h>
#import "UPUploadRecordApis.h"
#import "LoginSettings.h"
#import "SEUserLoginApi.h"

@interface LoginViewController () <UIPickerViewDelegate, UIPickerViewDataSource, UITextFieldDelegate>
@property (weak, nonatomic) IBOutlet UILabel *currentEnvLabel;
@property (weak, nonatomic) IBOutlet UILabel *currentEnvStatusLabel;
@property (weak, nonatomic) IBOutlet UILabel *envSelectionLabel;
@property (weak, nonatomic) IBOutlet UIButton *selectionButton;
@property (weak, nonatomic) IBOutlet UILabel *countryLabel;
@property (weak, nonatomic) IBOutlet UIButton *countryButton;
@property (weak, nonatomic) IBOutlet UITextField *usernameTextField;
@property (weak, nonatomic) IBOutlet UITextField *userPwdTextField;
@property (weak, nonatomic) IBOutlet UILabel *usernameLabel;
@property (weak, nonatomic) IBOutlet UILabel *userPwdLabel;
@property (weak, nonatomic) IBOutlet UIButton *loginButton;
@property (weak, nonatomic) IBOutlet UIPickerView *picker;
@property (nonatomic, assign) BOOL shouldShowEnvSelectionPikcerView;
@property (weak, nonatomic) IBOutlet UIView *pickerContainerView;
@property (weak, nonatomic) IBOutlet UIButton *logoutButton;
@property (weak, nonatomic) IBOutlet UIButton *initializeButton;
@property (nonatomic, strong) NSArray *componentsOfPickerView;
@property (nonatomic, strong) NSArray *CountryComponentsOfPickerView;

- (void)initializeViews;
- (void)initlalizePickerView;
- (IBAction)onLoginButton:(UIButton *)sender;
- (IBAction)onEnvSelectionButton:(id)sender;
- (IBAction)onCountrySelectionButton:(UIButton *)sender;
- (IBAction)onPickerContainerDoneButton:(UIButton *)sender;
- (IBAction)onLogoutButton:(UIButton *)sender;
- (IBAction)onInitializeButton:(UIButton *)sender;

@end

@implementation LoginViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    [self initializeViews];
    [self initlalizePickerView];
    UIBarButtonItem *item = [[UIBarButtonItem alloc] initWithTitle:@"返回" style:UIBarButtonItemStyleDone target:self action:@selector(onBackBarButton)];
    self.navigationItem.leftBarButtonItem = item;
}

- (void)onBackBarButton
{
    [self.navigationController dismissViewControllerAnimated:YES completion:NULL];
}

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string
{
    if ([string isEqualToString:@"\n"]) {
        [textField resignFirstResponder];
    }
    return YES;
}

- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView
{
    return 1;
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component
{
    if (self.shouldShowEnvSelectionPikcerView) {
        return self.componentsOfPickerView.count;
    }
    else {
        return self.CountryComponentsOfPickerView.count;
    }
}

- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component
{
    if (self.shouldShowEnvSelectionPikcerView) {
        NSString *title = self.componentsOfPickerView[row];
        if ([title isEqualToString:@"中国-生产"]) {
            [UserCenterAPIBase setUserCenterEnvironment:UserCenterAPIEnvProduction];
            self.countryButton.hidden = YES;
        }
        else if ([title isEqualToString:@"中国-验收"]) {
            [UserCenterAPIBase setUserCenterEnvironment:UserCenterAPIEnvAcceptance];
            self.countryButton.hidden = YES;
        }
        else if ([title isEqualToString:@"中国-联调"]) {
            [UserCenterAPIBase setUserCenterEnvironment:UserCenterAPIEnvDevelopment];
            self.countryButton.hidden = YES;
        }
        else if ([title isEqualToString:@"东南亚-生产"]) {
            self.countryButton.hidden = NO;
        }
        [self.selectionButton setTitle:title forState:UIControlStateNormal];
    }
    else {
        NSString *title = self.CountryComponentsOfPickerView[row];
        [self.countryButton setTitle:title forState:UIControlStateNormal];
    }
}

- (NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component
{
    if (self.shouldShowEnvSelectionPikcerView) {
        return self.componentsOfPickerView[row];
    }
    else {
        return self.CountryComponentsOfPickerView[row];
    }
}

- (void)initializeViews
{
    [self.currentEnvLabel mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.view).offset(20);
      make.top.equalTo(self.view).offset(70);
      make.width.equalTo(@(80));
      make.height.equalTo(@(20));
    }];
    [self.currentEnvStatusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.currentEnvLabel.mas_right).offset(20);
      make.top.equalTo(self.currentEnvLabel.mas_top);
      make.right.equalTo(self.view).offset(-20);
      make.bottom.equalTo(self.currentEnvLabel.mas_bottom);
    }];
    [self.envSelectionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.currentEnvLabel.mas_left);
      make.top.equalTo(self.currentEnvLabel.mas_bottom).offset(20);
      make.height.equalTo(self.currentEnvLabel.mas_height);
      make.width.equalTo(self.currentEnvLabel.mas_width);
    }];
    [self.initializeButton mas_makeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(self.view).offset(-10);
      make.top.equalTo(self.currentEnvLabel.mas_top).offset(20);
      make.height.equalTo(@(30));
      make.width.equalTo(@(46));
    }];
    [self.selectionButton mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.envSelectionLabel.mas_right).offset(20);
      make.top.equalTo(self.envSelectionLabel.mas_top);
      make.right.equalTo(self.currentEnvStatusLabel.mas_right);
      make.bottom.equalTo(self.envSelectionLabel.mas_bottom);
    }];
    [self.countryLabel mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.envSelectionLabel.mas_left);
      make.top.equalTo(self.envSelectionLabel.mas_bottom).offset(20);
      make.width.equalTo(self.envSelectionLabel.mas_width);
      make.height.equalTo(self.envSelectionLabel.mas_height);
    }];
    [self.countryButton mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.countryLabel.mas_right).offset(20);
      make.top.equalTo(self.countryLabel.mas_top);
      make.right.equalTo(self.currentEnvStatusLabel.mas_right);
      make.bottom.equalTo(self.countryLabel.mas_bottom);
    }];
    [self.usernameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.countryLabel.mas_left);
      make.top.equalTo(self.countryLabel.mas_bottom).offset(20);
      make.height.equalTo(self.countryLabel.mas_height);
      make.width.equalTo(self.countryLabel.mas_width);
    }];
    [self.usernameTextField mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.usernameLabel.mas_right).offset(20);
      make.top.equalTo(self.usernameLabel.mas_top);
      //        make.bottom.equalTo(self.usernameLabel.mas_bottom);
      make.right.equalTo(self.view).offset(-20);
    }];
    [self.userPwdLabel mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.usernameLabel.mas_left);
      make.top.equalTo(self.usernameLabel.mas_bottom).offset(20);
      make.width.equalTo(self.usernameLabel.mas_width);
      make.height.equalTo(self.userPwdLabel.mas_height);
    }];
    [self.userPwdTextField mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.userPwdLabel.mas_right).offset(20);
      make.top.equalTo(self.userPwdLabel.mas_top);
      make.bottom.equalTo(self.userPwdLabel.mas_bottom);
      make.right.equalTo(self.view).offset(-20);
    }];
    [self.loginButton mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.view).offset(20);
      make.right.equalTo(self.view).offset(-20);
      make.width.equalTo(@(60));
      make.height.equalTo(@(20));
      make.top.equalTo(self.userPwdTextField.mas_bottom).offset(60);
    }];
    [self.logoutButton mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.loginButton);
      make.right.equalTo(self.loginButton);
      make.width.equalTo(self.loginButton);
      make.height.equalTo(self.loginButton);
      make.top.equalTo(self.loginButton.mas_bottom).offset(20);
    }];
    [self.pickerContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.view);
      make.right.equalTo(self.view);
      make.bottom.equalTo(self.view).offset(226);
      make.height.equalTo(@(226));
    }];
    switch ([LoginSettings sharedInstance].initType) {
        case UserDomainInitTypeSEAisa:
            self.currentEnvStatusLabel.text = @"已初始化东南亚";
            break;
        case UserDomainInitTypeHomeland:
            self.currentEnvStatusLabel.text = @"已初始化国内";
            break;
        default:
            self.currentEnvStatusLabel.text = @"未初始化";
            break;
    }
}

- (void)initlalizePickerView
{
    self.CountryComponentsOfPickerView = @[ @"印度-91", @"巴基斯坦-92", @"越南-84" ];
    self.componentsOfPickerView = @[ @"中国-生产", @"中国-验收", @"中国-联调", @"东南亚-生产" ];
    self.shouldShowEnvSelectionPikcerView = YES;
    [self.picker reloadAllComponents];
    if ([LoginSettings sharedInstance].initType == UserDomainInitTypeHomeland || [LoginSettings sharedInstance].initType == UserDomainInitTypeUnkown) {
        [self.selectionButton setTitle:@"中国-生产" forState:UIControlStateNormal];
        [UserCenterAPIBase setUserCenterEnvironment:UserCenterAPIEnvProduction];
    }
    else {
        [self.selectionButton setTitle:@"东南亚-生产" forState:UIControlStateNormal];
    }
    [self.countryButton setTitle:@"印度-91" forState:UIControlStateNormal];
    if ([self.selectionButton.titleLabel.text containsString:@"东南亚"]) {
        self.countryButton.hidden = NO;
    }
    else {
        self.countryButton.hidden = YES;
    }
}

- (IBAction)onInitializeButton:(UIButton *)sender
{
    if ([self.selectionButton.titleLabel.text containsString:@"中国"]) {
        [[UpUserDomainHolder instance] initializeUserDomainForHomeland];
        self.currentEnvStatusLabel.text = @"已初始化国内";
        [LoginSettings sharedInstance].initType = UserDomainInitTypeHomeland;
    }
    else {
        [[UpUserDomainHolder instance] initializeUserDomainForSouthEastAsia];
        self.currentEnvStatusLabel.text = @"已初始化东南亚";
        [LoginSettings sharedInstance].initType = UserDomainInitTypeSEAisa;
    }
}

- (IBAction)onLogoutButton:(UIButton *)sender
{
    [SVProgressHUD showWithStatus:@"正在注销"];
    [[UpUserDomainHolder instance]
            .userDomain logOut:^(UserDomainSampleResult *_Nonnull result) {
      NSLog(@"登出%@", result.success ? @"成功" : @"失败");
      if (result.success) {
          [SVProgressHUD showSuccessWithStatus:@"注销成功"];
      }
      else {
          [SVProgressHUD showErrorWithStatus:@"注销失败"];
      }
    }];
}

- (IBAction)onPickerContainerDoneButton:(UIButton *)sender
{
    [UIView animateWithDuration:0.5
                     animations:^{
                       CGRect frame = CGRectMake(0, self.view.frame.size.height, self.pickerContainerView.frame.size.width, self.pickerContainerView.frame.size.height);
                       self.pickerContainerView.frame = frame;
                     }];
}

- (IBAction)onCountrySelectionButton:(UIButton *)sender
{
    self.shouldShowEnvSelectionPikcerView = NO;
    [UIView animateWithDuration:0.5
                     animations:^{
                       CGRect frame = CGRectMake(0, self.view.frame.size.height - self.pickerContainerView.frame.size.height, self.pickerContainerView.frame.size.width, self.pickerContainerView.frame.size.height);
                       self.pickerContainerView.frame = frame;
                     }];
    [self.picker reloadAllComponents];
}

- (IBAction)onEnvSelectionButton:(id)sender
{
    self.shouldShowEnvSelectionPikcerView = YES;
    [UIView animateWithDuration:0.5
                     animations:^{
                       CGRect frame = CGRectMake(0, self.view.frame.size.height - self.pickerContainerView.frame.size.height, self.pickerContainerView.frame.size.width, self.pickerContainerView.frame.size.height);
                       self.pickerContainerView.frame = frame;
                     }];
    [self.picker reloadAllComponents];
}

- (IBAction)onLoginButton:(UIButton *)sender
{
    if ([LoginSettings sharedInstance].initType == UserDomainInitTypeHomeland) {
        [self loginHomeland];
    }
    else if ([LoginSettings sharedInstance].initType == UserDomainInitTypeSEAisa) {
        [self loginSEAsia];
    }
}

- (void)loginHomeland
{
    [UPNetworkSettings sharedSettings].appID = @"MB-UZHSH-0001";
    [UPNetworkSettings sharedSettings].appKey = @"5dfca8714eb26e3a776e58a8273c8752";
    [UPNetworkSettings sharedSettings].appVersion = @"6.17.0";
    [SVProgressHUD showWithStatus:@"正在登陆"];
    NSString *userName = self.usernameTextField.text;
    NSString *pwd = self.userPwdTextField.text;
    UPUserLoginApi *api = [[UPUserLoginApi alloc] initWithUsername:userName password:pwd];
    [api startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      NSDictionary *oauthDataDic = [responseObject valueForKey:@"data"];
      NSDictionary *tokenDic = [oauthDataDic objectForKey:@"tokenInfo"];
      NSLog(@"%@", tokenDic);
      [self updateOauthDataWithDict:tokenDic];
      NSString *mobileModel = [[UIDevice currentDevice] model];
      [[[UPUploadRecordApis alloc] initWithUHomeUserID:tokenDic[@"uhomeUserId"] mobileModel:mobileModel] startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
        NSLog(@"上传登录日志%@", responseObject);
      }
          failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
            NSLog(@"上传登录日志%@", error);
          }];
      [SVProgressHUD showSuccessWithStatus:@"登录成功"];
      dispatch_async(dispatch_get_main_queue(), ^{
        [self gotoFunctionsView];
      });
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          NSLog(@"LoginFailure");
          [SVProgressHUD showErrorWithStatus:@"登录失败"];
        }];
}

- (void)loginSEAsia
{
    [UPNetworkSettings sharedSettings].appID = @"MB-SHEYJDNYB-0000";
    //AppKey 生产：5959a2a4679f7990ba3cc557daa53986 验收：19604699e97ce372341772e7d0c9e318
    [UPNetworkSettings sharedSettings].appKey = @"19604699e97ce372341772e7d0c9e318";
    [UPNetworkSettings sharedSettings].appVersion = @"2.10.1";
    [SVProgressHUD showWithStatus:@"正在登录"];
    NSString *userName = self.usernameTextField.text;
    NSString *pwd = self.userPwdTextField.text;
    SEUserLoginApi *api = [[SEUserLoginApi alloc] initWithUsername:userName password:pwd];
    [api startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      NSDictionary *oauthDataDic = [responseObject valueForKey:@"data"];
      NSDictionary *tokenDic = [oauthDataDic objectForKey:@"tokenInfo"];
      NSLog(@"%@", tokenDic);
      [self updateOauthDataWithDict:tokenDic];
      [SVProgressHUD showSuccessWithStatus:@"登录成功"];
      dispatch_async(dispatch_get_main_queue(), ^{
        [self gotoFunctionsView];
      });
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          NSLog(@"LoginFailure");
          [SVProgressHUD showErrorWithStatus:@"登录失败"];
        }];
}

- (void)updateOauthDataWithDict:(NSDictionary *)tokenDic
{
    [[UpUserDomainHolder instance].userDomain updateOauthData:tokenDic[@"accountToken"] refreshToken:tokenDic[@"refreshToken"] uhome_access_token:tokenDic[@"uhomeAccessToken"] expires_in:[NSString stringWithFormat:@"%@", tokenDic[@"expiresIn"]] scope:@"" token_type:tokenDic[@"tokenType"] uhome_user_id:tokenDic[@"uhomeUserId"] uc_user_id:tokenDic[@"uocUserId"]];
}

- (void)gotoFunctionsView
{
    [self onBackBarButton];
}

@end
