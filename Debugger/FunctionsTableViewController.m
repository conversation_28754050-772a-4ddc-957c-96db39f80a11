//
//  FunctionsTableViewController.m
//  Debugger
//
//  Created by <PERSON> on 2020/8/25.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FunctionsTableViewController.h"
#import "UserDetailViewController.h"
#import "UpUserDomainHolder.h"
#import "UpUserDomain.h"
#import "UpUserDomainObserver.h"
#import "UpUserDomain.h"
#import <SVProgressHUD/SVProgressHUD.h>
#import <uplog/UPLog.h>
#import <uplog/UPLogger.h>
#import <upnetwork/UPNetwork.h>
#import "FamilyListViewController.h"
#import "DeviceListViewController.h"

@interface FunctionsTableViewController () <UpUserDomainObserver>
@property (nonatomic, strong) NSArray *functionsArray;
@end

@implementation FunctionsTableViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    [SVProgressHUD setDefaultStyle:SVProgressHUDStyleDark];
    //    UPLog *log = [UPLog initLogger:[[UPLogger alloc] init]];
    //    [log setLoglevel:UPLogLevelDebug];
    //国内环境
    [UPNetworkSettings sharedSettings].appID = @"MB-UZHSH-0001";
    [UPNetworkSettings sharedSettings].appKey = @"5dfca8714eb26e3a776e58a8273c8752";
    [UPNetworkSettings sharedSettings].appVersion = @"6.17.0";
    [[UpUserDomainHolder instance] initializeUserDomainForHomeland];
    //    //东南亚环境
    //    [UPNetworkSettings sharedSettings].appID = @"MB-SHEYJDNYB-0000";
    //    [UPNetworkSettings sharedSettings].appKey = @"5959a2a4679f7990ba3cc557daa53986";
    //    [UPNetworkSettings sharedSettings].appVersion = @"2.1.2";
    //    [[UpUserDomainHolder instance] initializeUserDomainForSouthEastAsia];


    [[UpUserDomainHolder instance].userDomain addObserver:self];
    self.functionsArray = @[ @"用户详情", @"设备列表", @"家庭列表" ];
    UIBarButtonItem *loginButtonItem = [[UIBarButtonItem alloc] initWithTitle:@"登录" style:UIBarButtonItemStyleDone target:self action:@selector(onloginButtonItem:)];
    self.navigationItem.rightBarButtonItem = loginButtonItem;
    [[UpUserDomainHolder instance].userDomain autoRefreshToken];
    if ([UpUserDomainHolder instance].userDomain.state != UpUserDomainStateDidLogin) {
        [self onloginButtonItem:nil];
    }
}

- (void)onloginButtonItem:(UIBarButtonItem *)item
{
    UIStoryboard *board = [UIStoryboard storyboardWithName:@"Main" bundle:[NSBundle mainBundle]];
    UIViewController *controller = [board instantiateViewControllerWithIdentifier:@"LoginViewController"];
    controller.modalPresentationStyle = UIModalPresentationFullScreen;
    UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:controller];
    navController.modalPresentationStyle = UIModalPresentationFullScreen;
    [self presentViewController:navController animated:YES completion:NULL];
}

#pragma mark - Table view data source

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.functionsArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"TableViewCell" forIndexPath:indexPath];
    cell.textLabel.text = self.functionsArray[indexPath.row];
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    switch (indexPath.row) {
        case 0:
            [self gotoUserDetailView];
            break;
        case 1:
            [self gotoDeviceListView];
            break;
        case 2:
            [self gotoFamilyListView];
            break;
    }
}

- (void)gotoUserDetailView
{
    UserDetailViewController *detailView = [[UserDetailViewController alloc] init];
    [self.navigationController pushViewController:detailView animated:YES];
}

- (void)gotoDeviceListView
{
    DeviceListViewController *deviceListView = [[DeviceListViewController alloc] init];
    [self.navigationController pushViewController:deviceListView animated:YES];
}

- (void)gotoFamilyListView
{
    FamilyListViewController *familyVC = [[FamilyListViewController alloc] init];
    [self.navigationController pushViewController:familyVC animated:YES];
}

/*
// Override to support conditional editing of the table view.
- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath {
    // Return NO if you do not want the specified item to be editable.
    return YES;
}
*/

/*
// Override to support editing the table view.
- (void)tableView:(UITableView *)tableView commitEditingStyle:(UITableViewCellEditingStyle)editingStyle forRowAtIndexPath:(NSIndexPath *)indexPath {
    if (editingStyle == UITableViewCellEditingStyleDelete) {
        // Delete the row from the data source
        [tableView deleteRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationFade];
    } else if (editingStyle == UITableViewCellEditingStyleInsert) {
        // Create a new instance of the appropriate class, insert it into the array, and add a new row to the table view
    }   
}
*/

/*
// Override to support rearranging the table view.
- (void)tableView:(UITableView *)tableView moveRowAtIndexPath:(NSIndexPath *)fromIndexPath toIndexPath:(NSIndexPath *)toIndexPath {
}
*/

/*
// Override to support conditional rearranging of the table view.
- (BOOL)tableView:(UITableView *)tableView canMoveRowAtIndexPath:(NSIndexPath *)indexPath {
    // Return NO if you do not want the item to be re-orderable.
    return YES;
}
*/

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
