//
//  DeviceListViewController.m
//  Debugger
//
//  Created by <PERSON> on 2020/8/31.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DeviceListViewController.h"
#import "Device.h"
#import "UpUserDomainHolder.h"
#import "UpUserDomain.h"
#import "User.h"
#import "DeviceDetailViewController.h"
#import "Family.h"
#import "FamilySelectionViewController.h"
#import "UDFamilyInfoDelegate.h"

@interface DeviceListViewController () <UITableViewDelegate, UITableViewDataSource, FamilySelectionViewControllerDelegate>
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSArray *devices;
@property (nonatomic, strong) Family *currentFamily;
@end

@implementation DeviceListViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    [self initializeTableView];
    self.title = @"全部设备";
    [self initializeNavigationBarButton];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    [self.tableView reloadData];
}

- (void)initializeTableView
{
    UITableView *tableView = [[UITableView alloc] initWithFrame:self.view.frame style:UITableViewStylePlain];
    tableView.dataSource = self;
    tableView.delegate = self;
    [self.view addSubview:tableView];
    self.tableView = tableView;
    self.devices = [UpUserDomainHolder instance].userDomain.user.devices.allValues;
}

- (void)initializeNavigationBarButton
{
    UIBarButtonItem *barButtonItem = [[UIBarButtonItem alloc] initWithTitle:@"筛选" style:UIBarButtonItemStyleDone target:self action:@selector(onFilterBarButton:)];
    self.navigationItem.rightBarButtonItem = barButtonItem;
}

- (void)onFilterBarButton:(UIBarButtonItem *)button
{
    FamilySelectionViewController *controller = [[FamilySelectionViewController alloc] init];
    controller.delegate = self;
    controller.currentFamily = self.currentFamily;
    [self.navigationController pushViewController:controller animated:YES];
}

- (void)didSelectFamily:(Family *)family
{
    self.currentFamily = family;
    self.title = self.currentFamily.info.familyName;
    [self filterDevicesByFamily];
}

- (void)filterDevicesByFamily
{
    if (self.currentFamily == nil) {
        return;
    }
    NSArray *allDevices = [UpUserDomainHolder instance].userDomain.user.devices.allValues;
    NSMutableArray *devices = [NSMutableArray array];
    for (Device *device in allDevices) {
        if ([device.familyId isEqualToString:self.currentFamily.familyId]) {
            [devices addObject:device];
        }
    }
    self.devices = devices;
    [self.tableView reloadData];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    DeviceDetailViewController *controller = [[DeviceDetailViewController alloc] init];
    controller.device = self.devices[indexPath.row];
    [self.navigationController pushViewController:controller animated:YES];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"TableViewCell"];
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:@"TableViewCell"];
    }
    Device *device = self.devices[indexPath.row];
    cell.textLabel.text = device.deviceName;
    cell.detailTextLabel.text = [NSString stringWithFormat:@"%@,%@,%@,%@,%@", device.deviceId, device.apptypeCode, device.wifiType, device.model, device.prodNo];
    return cell;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.devices.count;
}

@end
