<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="16097" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="wA1-If-fAi">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Navigation Controller-->
        <scene sceneID="Uhj-gt-bgo">
            <objects>
                <navigationController id="wA1-If-fAi" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="p5c-Uw-wjP">
                        <rect key="frame" x="0.0" y="44" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="bOa-AC-8BD" kind="relationship" relationship="rootViewController" id="Rgw-ay-Wbd"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="O2t-ze-BIL" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-932" y="-33"/>
        </scene>
        <!--Login View Controller-->
        <scene sceneID="shd-ps-IPE">
            <objects>
                <viewController storyboardIdentifier="LoginViewController" id="ekk-1G-569" customClass="LoginViewController" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Emk-Zy-0u1">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <textField opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="0Rv-5g-FNG">
                                <rect key="frame" x="112" y="237" width="220" height="34"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" returnKeyType="next"/>
                                <connections>
                                    <outlet property="delegate" destination="ekk-1G-569" id="8TH-W5-1Bf"/>
                                </connections>
                            </textField>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="账号" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="byF-Py-0Xp">
                                <rect key="frame" x="34" y="244" width="62" height="21"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="密码" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FFe-Hp-qUH">
                                <rect key="frame" x="34" y="309" width="62" height="21"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LsX-5n-M25">
                                <rect key="frame" x="125" y="375" width="165" height="30"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="登录"/>
                                <connections>
                                    <action selector="onLoginButton:" destination="ekk-1G-569" eventType="touchUpInside" id="Iwf-nt-GjA"/>
                                </connections>
                            </button>
                            <textField opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="YgH-9p-irs">
                                <rect key="frame" x="112" y="296" width="213" height="34"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" returnKeyType="done" secureTextEntry="YES"/>
                                <connections>
                                    <outlet property="delegate" destination="ekk-1G-569" id="R9E-30-zh2"/>
                                </connections>
                            </textField>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="国家" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xVk-xA-zwQ">
                                <rect key="frame" x="34" y="173" width="35" height="21"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="可选环境" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QtY-ZN-GI3">
                                <rect key="frame" x="34" y="114" width="82" height="21"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="当前环境" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QW0-T4-e28">
                                <rect key="frame" x="34" y="75" width="82" height="21"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="FPt-qI-g7a">
                                <rect key="frame" x="150" y="109" width="175" height="30"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <connections>
                                    <action selector="onEnvSelectionButton:" destination="ekk-1G-569" eventType="touchUpInside" id="ZyJ-bd-aam"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="X2E-2f-GJb">
                                <rect key="frame" x="150" y="164" width="175" height="30"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="Button"/>
                                <connections>
                                    <action selector="onCountrySelectionButton:" destination="ekk-1G-569" eventType="touchUpInside" id="lpJ-mC-aRh"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="未初始化" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="K0q-cG-d1B">
                                <rect key="frame" x="150" y="75" width="96" height="21"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" id="QCq-IW-rEZ">
                                <rect key="frame" x="0.0" y="636" width="414" height="226"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <subviews>
                                    <pickerView contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="lL1-f2-XYJ">
                                        <rect key="frame" x="0.0" y="64" width="414" height="162"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                                        <connections>
                                            <outlet property="dataSource" destination="ekk-1G-569" id="cUD-SH-oUu"/>
                                            <outlet property="delegate" destination="ekk-1G-569" id="7zj-ho-lwg"/>
                                        </connections>
                                    </pickerView>
                                    <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ane-Yb-Bro">
                                        <rect key="frame" x="189" y="0.0" width="37" height="30"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <state key="normal" title="Done"/>
                                        <connections>
                                            <action selector="onPickerContainerDoneButton:" destination="ekk-1G-569" eventType="touchUpInside" id="MOO-PZ-gHj"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                                <viewLayoutGuide key="safeArea" id="O3S-fc-gRd"/>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="99G-wQ-zzV">
                                <rect key="frame" x="192" y="413" width="31" height="30"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="注销"/>
                                <connections>
                                    <action selector="onLogoutButton:" destination="ekk-1G-569" eventType="touchUpInside" id="UDq-xu-Ifr"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="iEP-n6-feX">
                                <rect key="frame" x="332" y="70" width="46" height="30"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <state key="normal" title="初始化"/>
                                <connections>
                                    <action selector="onInitializeButton:" destination="ekk-1G-569" eventType="touchUpInside" id="gtp-18-qHo"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                        <viewLayoutGuide key="safeArea" id="neB-UW-24c"/>
                    </view>
                    <navigationItem key="navigationItem" id="E4R-Bx-IwW"/>
                    <connections>
                        <outlet property="countryButton" destination="X2E-2f-GJb" id="bWr-6H-qlh"/>
                        <outlet property="countryLabel" destination="xVk-xA-zwQ" id="A7K-FQ-cEp"/>
                        <outlet property="currentEnvLabel" destination="QW0-T4-e28" id="LoB-DF-Ume"/>
                        <outlet property="currentEnvStatusLabel" destination="K0q-cG-d1B" id="aVH-fy-CTt"/>
                        <outlet property="envSelectionLabel" destination="QtY-ZN-GI3" id="Y36-WG-O4X"/>
                        <outlet property="initializeButton" destination="iEP-n6-feX" id="qhe-dx-h2J"/>
                        <outlet property="loginButton" destination="LsX-5n-M25" id="igM-ZH-0Ze"/>
                        <outlet property="logoutButton" destination="99G-wQ-zzV" id="5gq-5e-eWd"/>
                        <outlet property="picker" destination="lL1-f2-XYJ" id="YV8-Vl-4W7"/>
                        <outlet property="pickerContainerView" destination="QCq-IW-rEZ" id="58L-ot-ehT"/>
                        <outlet property="selectionButton" destination="FPt-qI-g7a" id="XoM-yf-ihI"/>
                        <outlet property="userPwdLabel" destination="FFe-Hp-qUH" id="bMq-bm-udH"/>
                        <outlet property="userPwdTextField" destination="YgH-9p-irs" id="pGA-t5-kmQ"/>
                        <outlet property="usernameLabel" destination="byF-Py-0Xp" id="WZG-bA-PDW"/>
                        <outlet property="usernameTextField" destination="0Rv-5g-FNG" id="gX3-x4-85T"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="R3T-WV-LVI" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="661" y="-33"/>
        </scene>
        <!--Functions Table View Controller-->
        <scene sceneID="IVJ-dh-o70">
            <objects>
                <tableViewController storyboardIdentifier="FunctionsTableViewController" id="bOa-AC-8BD" customClass="FunctionsTableViewController" sceneMemberID="viewController">
                    <tableView key="view" clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="grouped" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="18" sectionFooterHeight="18" id="2a8-t3-qeI">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                        <prototypes>
                            <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="TableViewCell" id="PaK-Ef-zEe">
                                <rect key="frame" x="0.0" y="55.5" width="414" height="43.5"/>
                                <autoresizingMask key="autoresizingMask"/>
                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="PaK-Ef-zEe" id="wCN-vT-kx5">
                                    <rect key="frame" x="0.0" y="0.0" width="414" height="43.5"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                </tableViewCellContentView>
                            </tableViewCell>
                        </prototypes>
                        <connections>
                            <outlet property="dataSource" destination="bOa-AC-8BD" id="XD5-6u-ET9"/>
                            <outlet property="delegate" destination="bOa-AC-8BD" id="Z0n-e6-In5"/>
                        </connections>
                    </tableView>
                    <navigationItem key="navigationItem" id="1Yw-7n-8yE"/>
                </tableViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Enc-4j-FP7" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-100" y="-33"/>
        </scene>
    </scenes>
</document>
