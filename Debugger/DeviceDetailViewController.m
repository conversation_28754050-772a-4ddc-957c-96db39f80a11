//
//  DeviceDetailViewController.m
//  Debugger
//
//  Created by <PERSON> on 2020/8/31.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DeviceDetailViewController.h"
#import "UDDevicePermissionDelegate.h"
#import "UDDeviceAuthDelegate.h"
#import "UDDeviceOwnerInfoDelegate.h"
#import "UpUserDomainHolder.h"
#import "UpUserDomain.h"
#import "User.h"
#import "Device.h"
#import <SVProgressHUD/SVProgressHUD.h>

@interface DeviceDetailViewController () <UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UITextField *textField;

@end

@implementation DeviceDetailViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = [UIColor whiteColor];
    [self initializeTableView];
    [self initializeNavigationBarButton];
}

- (void)initializeTableView
{
    UITableView *tableView = [[UITableView alloc] initWithFrame:self.view.frame style:UITableViewStylePlain];
    tableView.dataSource = self;
    tableView.delegate = self;
    [self.view addSubview:tableView];
    self.tableView = tableView;
}

- (void)initializeNavigationBarButton
{
    UIBarButtonItem *barButtonItem = [[UIBarButtonItem alloc] initWithTitle:@"改名" style:UIBarButtonItemStyleDone target:self action:@selector(onChangeNameBarButton:)];
    self.navigationItem.rightBarButtonItem = barButtonItem;
}

- (void)onChangeNameBarButton:(UIBarButtonItem *)button
{
    UIAlertController *controller = [UIAlertController alertControllerWithTitle:@"修改名称" message:@"" preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *action = [UIAlertAction actionWithTitle:@"OK"
                                                     style:UIAlertActionStyleDefault
                                                   handler:^(UIAlertAction *_Nonnull action) {
                                                     [self updateDeviceName];
                                                   }];
    [controller addAction:action];
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"Cancel"
                                                           style:UIAlertActionStyleCancel
                                                         handler:^(UIAlertAction *_Nonnull action) {
                                                           NSLog(@"%@", @"cancel");
                                                         }];
    [controller addAction:cancelAction];
    [controller addTextFieldWithConfigurationHandler:^(UITextField *_Nonnull textField) {
      textField.text = self.device.deviceName;
      self.textField = textField;
    }];
    [self presentViewController:controller animated:YES completion:NULL];
}

- (void)updateDeviceName
{
    [SVProgressHUD showWithStatus:@"正在更新"];
    [self.device updateDeviceName:self.textField.text
        success:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showSuccessWithStatus:@"更新成功"];
          dispatch_async(dispatch_get_main_queue(), ^{
            [self.tableView reloadData];
          });
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showErrorWithStatus:@"更新失败"];
        }];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"TableViewCell"];
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:@"TableViewCell"];
    }
    switch (indexPath.row) {
        case 0:
            cell.textLabel.text = @"deviceId";
            cell.detailTextLabel.text = self.device.deviceId;
            break;
        case 1:
            cell.textLabel.text = @"deviceName";
            cell.detailTextLabel.text = self.device.deviceName;
            break;
        case 2:
            cell.textLabel.text = @"devName";
            cell.detailTextLabel.text = self.device.devName;
            break;
        case 3:
            cell.textLabel.text = @"deviceType";
            cell.detailTextLabel.text = self.device.deviceType;
            break;
        case 4:
            cell.textLabel.text = @"familyId";
            cell.detailTextLabel.text = self.device.familyId;
            break;
        case 5:
            cell.textLabel.text = @"ownerId";
            cell.detailTextLabel.text = self.device.ownerId;
            break;
        case 6: {
            cell.textLabel.text = @"permission";
            id<UDDevicePermissionDelegate> permission = self.device.permission;
            NSString *permissionInfo = [NSString stringWithFormat:@"authType:%@,control:%ld,set:%ld,view:%ld", permission.authType, permission.auth.control, permission.auth.set, permission.auth.view];
            cell.detailTextLabel.text = permissionInfo;
            break;
        }
        case 7:
            cell.textLabel.text = @"wifiType";
            cell.detailTextLabel.text = self.device.wifiType;
            break;
        case 8:
            cell.textLabel.text = @"bindTime";
            cell.detailTextLabel.text = self.device.bindTime;
            break;
        case 9:
            cell.textLabel.text = @"isOnline";
            cell.detailTextLabel.text = self.device.isOnline ? @"YES" : @"NO";
            break;
        case 10: {
            cell.textLabel.text = @"ownerInfo";
            id<UDDeviceOwnerInfoDelegate> ownerInfo = self.device.ownerInfo;
            NSString *ownerInfoStr = [NSString stringWithFormat:@"userId:%@,mobile:%@,nickname:%@,ucUserId:%@", ownerInfo.userId, ownerInfo.mobile, ownerInfo.userNickName, ownerInfo.ucUserId];
            cell.detailTextLabel.text = ownerInfoStr;
            break;
        }
        case 11: {
            cell.textLabel.text = @"subDevIds";
            NSData *data = [NSJSONSerialization dataWithJSONObject:self.device.subDevIds options:0 error:NULL];
            cell.detailTextLabel.text = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
            break;
        }
        case 12:
            cell.textLabel.text = @"parentId";
            cell.detailTextLabel.text = self.device.parentId;
            break;
        case 13:
            cell.textLabel.text = @"deviceRole";
            cell.detailTextLabel.text = self.device.deviceRole;
            break;
        case 14:
            cell.textLabel.text = @"deviceRoleType";
            cell.detailTextLabel.text = self.device.deviceRoleType;
            break;
        case 15:
            cell.textLabel.text = @"appTypeName";
            cell.detailTextLabel.text = self.device.apptypeName;
            break;
        case 16:
            cell.textLabel.text = @"appTypeCode";
            cell.detailTextLabel.text = self.device.apptypeCode;
            break;
        case 17:
            cell.textLabel.text = @"categoryGrouping";
            cell.detailTextLabel.text = self.device.categoryGrouping;
            break;
        case 18:
            cell.textLabel.text = @"barcode";
            cell.detailTextLabel.text = self.device.barcode;
            break;
        case 19:
            cell.textLabel.text = @"bindType";
            cell.detailTextLabel.text = self.device.bindType;
            break;
        case 20:
            cell.textLabel.text = @"brand";
            cell.detailTextLabel.text = self.device.brand;
            break;
        case 21:
            cell.textLabel.text = @"imageAddr1";
            cell.detailTextLabel.text = self.device.imageAddr1;
            break;
        case 22:
            cell.textLabel.text = @"imageAddr2";
            cell.detailTextLabel.text = self.device.imageAddr2;
            break;
        case 23:
            cell.textLabel.text = @"model";
            cell.detailTextLabel.text = self.device.model;
            break;
        case 24:
            cell.textLabel.text = @"prodNo";
            cell.detailTextLabel.text = self.device.prodNo;
            break;
        case 25:
            cell.textLabel.text = @"roomName";
            cell.detailTextLabel.text = self.device.roomName;
            break;
        case 26:
            cell.textLabel.text = @"roomId";
            cell.detailTextLabel.text = self.device.roomId;
            break;
        case 27:
            cell.textLabel.text = @"isOwned";
            cell.detailTextLabel.text = self.device.isOwned ? @"YES" : @"NO";
            break;
        case 28:
            cell.textLabel.text = @"accessType";
            cell.detailTextLabel.text = self.device.accessType;
            break;
        case 29:
            cell.textLabel.text = @"configType";
            cell.detailTextLabel.text = self.device.configType;
            break;
        case 30:
            cell.textLabel.text = @"comunicationMode";
            cell.detailTextLabel.text = self.device.comunicationMode;
            break;
    }
    return cell;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return 31;
}

@end
