//
//  SEUserLoginApi.m
//  Debugger
//
//  Created by <PERSON> on 2020/8/26.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEUserLoginApi.h"
#import <CommonCrypto/CommonDigest.h>
#import <upnetwork/UpXUDID.h>
#import <upnetwork/UPNetworkSettings.h>

@interface SEUserLoginApi ()
@property (nonatomic, copy) NSString *username;
@property (nonatomic, copy) NSString *password;
@property (nonatomic, copy, readonly) NSString *clientId;
@property (nonatomic, copy, readonly) NSString *timestamp;

@end

@implementation SEUserLoginApi
- (instancetype)initWithUsername:(NSString *)username password:(NSString *)password
{
    if (self = [super init]) {
        self.username = username;
        self.password = password;
    }
    return self;
}

- (NSString *)name
{
    return @"用户登录接口（东南亚）";
}

- (NSString *)baseURL
{
    return @"https://uhome-sea.haieriot.net";
}

- (NSString *)path
{
    return @"/uplussea/accounts/v1/login";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSObject *)requestBody
{
    return @{ @"countryCode" : @"91",
              @"username" : self.username,
              @"password" : self.password };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSString *timestamp = self.timestamp;
    return @{ @"appId" : [UPNetworkSettings sharedSettings].appID,
              @"appVersion" : [UPNetworkSettings sharedSettings].appVersion,
              @"clientId" : self.clientId,
              @"timestamp" : timestamp,
              //@"accessToken" : @"",
              @"sign" : [self createSignByTimestamp:timestamp],
              @"Content-Type" : @"application/json;charset=UTF-8;",
              //@"accountToken" : @"",
              @"language" : @"zh-cn",
              @"timezone" : @"Asia/Shanghai" };
}

- (NSString *)createSignByTimestamp:(NSString *)timestamp
{
    NSString *bodyStr = @"";
    NSDictionary *arguments = (NSDictionary *)self.requestBody;
    if ([arguments isKindOfClass:[NSDictionary class]]) {
        NSData *bodyData = [NSJSONSerialization dataWithJSONObject:arguments options:0 error:nil];
        bodyStr = [[NSString alloc] initWithData:bodyData encoding:NSUTF8StringEncoding];
        bodyStr = [bodyStr stringByReplacingOccurrencesOfString:@"\n" withString:@""];
        bodyStr = [bodyStr stringByReplacingOccurrencesOfString:@"\r" withString:@""];
        bodyStr = [bodyStr stringByReplacingOccurrencesOfString:@" " withString:@""];
        bodyStr = [bodyStr stringByReplacingOccurrencesOfString:@"\b" withString:@""];
        bodyStr = [bodyStr stringByReplacingOccurrencesOfString:@"\t" withString:@""];
    }
    NSString *appID = [UPNetworkSettings sharedSettings].appID;
    NSString *appKey = [UPNetworkSettings sharedSettings].appKey;
    NSString *urlStr = self.path;
    if (![urlStr isKindOfClass:[NSString class]]) {
        urlStr = @"";
    }
    if (![urlStr hasPrefix:@"/"]) {
        urlStr = [NSString stringWithFormat:@"/%@", urlStr];
    }
    NSString *originString = [NSString stringWithFormat:@"%@%@%@%@%@", urlStr, bodyStr, appID, appKey, timestamp];
    NSString *signString = [[self getSHA256:originString] lowercaseString];
    return signString;
}

- (NSString *)getSHA256:(NSString *)str
{
    const char *cstr = [str cStringUsingEncoding:NSUTF8StringEncoding];
    NSData *data = [NSData dataWithBytes:cstr length:strlen(cstr)];
    uint8_t digest[CC_SHA256_DIGEST_LENGTH];
    CC_SHA256(data.bytes, (unsigned int)data.length, digest);
    NSMutableString *result = [NSMutableString stringWithCapacity:CC_SHA256_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_SHA256_DIGEST_LENGTH; i++) {
        [result appendFormat:@"%02x", digest[i]];
    }
    return result;
}

- (NSString *)timestamp
{
    NSTimeInterval millisecondTimeInterval = [[NSDate date] timeIntervalSince1970] * 1000;
    NSString *millisecondString = [NSString stringWithFormat:@"%lld", (long long)millisecondTimeInterval];
    return millisecondString;
}

- (NSString *)clientId
{
    NSString *clientId = [UpXUDID UUIDString];
    if (![clientId isKindOfClass:[NSString class]]) {
        clientId = @"";
    }
    return clientId;
}

@end
