//
//  UPUploadRecordApis.m
//  Debugger
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/25.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPUploadRecordApis.h"
#import <upnetwork/UPCommonServerHeader.h>

@interface UPUploadRecordApis ()

@property (nonatomic, copy) NSString *uHomeUserID;
@property (nonatomic, copy) NSString *mobileModel;
@end

@implementation UPUploadRecordApis
- (instancetype)initWithUHomeUserID:(NSString *)userId mobileModel:(NSString *)mobileModel
{
    if (self = [super init]) {
        _uHomeUserID = userId;
        _mobileModel = mobileModel;
    }
    return self;
}

- (NSString *)baseURL
{
    return @"https://zj.haier.net";
}
- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSTimeInterval)timeoutInterval
{
    return 15.0;
}

- (NSUInteger)retryTimes
{
    return 0;
}

- (NSTimeInterval)retryDelay
{
    return 0.0;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return [UPCommonServerHeader signHeaderWithBody:self.requestBody];
}

- (NSString *)name
{
    return @"上传用户登录记录";
}
- (NSString *)path
{
    return @"/emuplus/account/v1.0/loginRecord";
}

- (NSObject *)requestBody
{
    return @{ @"uhomeUserId" : self.uHomeUserID ?: @"",
              @"accountToken" : [UPNetworkSettings sharedSettings].accessToken,
              @"loginType" : @"initiative",
              @"mobileModel" : self.mobileModel ?: @""
    };
}
@end
