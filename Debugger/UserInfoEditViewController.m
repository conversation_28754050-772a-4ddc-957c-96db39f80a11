//
//  UserInfoEditViewController.m
//  Debugger
//
//  Created by <PERSON> on 2020/8/25.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserInfoEditViewController.h"
#import <Masonry/Masonry.h>
#import "UpUserDomainHolder.h"
#import "UpUserDomain.h"
#import "UDUserDelegate.h"
#import "UDUserInfoDelegate.h"
#import "UserInfoArgs.h"
#import <SVProgressHUD/SVProgressHUD.h>

@interface UserInfoEditViewController ()
@property (nonatomic, strong) UITextField *textField;
@end

@implementation UserInfoEditViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    // Do any additional setup after loading the view.
    [self initializeViews];
    UIBarButtonItem *doneItem = [[UIBarButtonItem alloc] initWithTitle:@"更新" style:UIBarButtonItemStyleDone target:self action:@selector(onDoneBarButtonItem:)];
    self.navigationItem.rightBarButtonItem = doneItem;
}

- (void)onDoneBarButtonItem:(UIBarButtonItem *)item
{
    id<UDUserInfoDelegate> userInfo = [UpUserDomainHolder instance].userDomain.user.extraInfo;
    UserInfoArgs *args = [[UserInfoArgs alloc] initUserInfoArgs:userInfo];
    NSString *text = self.textField.text;
    switch (self.editInfoIndex) {
        case 0:
            args.birthday = text;
            break;
        case 1:
            args.extraPhone = text;
            break;
        case 2:
            break;
        case 3:
            break;
        case 4:
            args.givenName = text;
            break;
        case 5:
            args.nickname = text;
            break;
        case 6:
            args.familyNum = text;
            break;
        case 7:
            args.gender = text;
            break;
        case 8:
            args.marriage = text;
            break;
        case 9:
            args.height = text;
            break;
        case 10:
            args.weight = text;
            break;
        case 11:
            args.income = text;
            break;
    }
    [SVProgressHUD showWithStatus:@"正在更新"];
    [[UpUserDomainHolder instance]
            .userDomain.user modifyUserInfo:args
        success:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showSuccessWithStatus:@"更新成功"];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showErrorWithStatus:@"更新失败"];
        }];
}

- (void)initializeViews
{
    NSArray *titleArray = @[ @"birthday", @"extraPhone", @"email", @"username", @"givenName", @"nickname", @"familyNum", @"gender", @"marriage", @"height", @"weight", @"income" ];
    self.title = titleArray[self.editInfoIndex];
    UITextField *textField = [[UITextField alloc] init];
    textField.borderStyle = UITextBorderStyleLine;
    [self.view addSubview:textField];
    self.textField = textField;
    [self.textField mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.view).offset(20);
      make.right.equalTo(self.view).offset(-20);
      make.top.equalTo(self.view).offset(84);
    }];
    self.textField.text = self.value;
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
