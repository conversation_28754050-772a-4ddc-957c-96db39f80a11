//
//  RoomPickerView.m
//  Debugger
//
//  Created by 闫达 on 2020/8/31.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "RoomPickerView.h"
#import "UDRoomDelegate.h"
#define TWW self.frame.size.width
#define TWH self.frame.size.height
#define TWRGB(r, g, b) [UIColor colorWithRed:r / 255.0 green:g / 255.0 blue:b / 255.0 alpha:1]
#define BtnW 60
#define toolH 40
#define BJH 260
@interface RoomPickerView () <UIPickerViewDelegate, UIPickerViewDataSource> {
    UIView *_BJView;
}
@property (nonatomic, strong) UIPickerView *pickView;
@property (nonatomic, copy) NSArray<id<UDRoomDelegate>> *sourceArr;
@property (nonatomic, copy) void (^sele)(id<UDRoomDelegate> room);
@property (nonatomic, strong) id<UDRoomDelegate> selectedRoom;
@property (nonatomic, strong) UILabel *titleLabel;
@end

@implementation RoomPickerView
- (instancetype)initWithFrame:(CGRect)rect dataSource:(NSArray<id<UDRoomDelegate>> *)sourceArr
{
    if (self = [super initWithFrame:rect]) {

        self.sourceArr = sourceArr;
        self.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0];
        [UIView animateWithDuration:0.3
                         animations:^{
                           self.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.3];
                         }];
        //显示pickview和按钮最底下的view
        _BJView = [[UIView alloc] initWithFrame:CGRectMake(0, TWH, TWW, BJH)];
        [self addSubview:_BJView];

        UIView *tool = [[UIView alloc] initWithFrame:CGRectMake(0, 0, TWW, toolH)];
        tool.backgroundColor = TWRGB(237, 236, 234);
        tool.userInteractionEnabled = YES;
        [_BJView addSubview:tool];

        /**
         按钮+中间可以显示标题的UILabel
         */
        UIButton *left = [UIButton buttonWithType:UIButtonTypeRoundedRect];
        left.frame = CGRectMake(0, 0, BtnW, toolH);
        [left setTitle:@"取消" forState:UIControlStateNormal];
        [left addTarget:self action:@selector(leftBTN) forControlEvents:UIControlEventTouchUpInside];
        [tool addSubview:left];

        self.titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(left.frame.size.width, 0, TWW - (left.frame.size.width * 2), toolH)];
        self.titleLabel.text = self.sourceArr.firstObject.roomName;
        self.titleLabel.textAlignment = NSTextAlignmentCenter;
        [tool addSubview:self.titleLabel];

        UIButton *right = [UIButton buttonWithType:UIButtonTypeRoundedRect];
        right.frame = CGRectMake(TWW - BtnW, 0, BtnW, toolH);
        [right setTitle:@"确定" forState:UIControlStateNormal];
        [right addTarget:self action:@selector(rightBTN) forControlEvents:UIControlEventTouchUpInside];
        [tool addSubview:right];


        _pickView = [[UIPickerView alloc] initWithFrame:CGRectMake(0, toolH, TWW, _BJView.frame.size.height - toolH)];
        _pickView.delegate = self;
        _pickView.dataSource = self;
        _pickView.backgroundColor = TWRGB(237, 237, 237);
        [_BJView addSubview:_pickView];
    }
    return self;
}

- (void)showRoomView:(void (^)(id<UDRoomDelegate> room))selectRoom
{
    self.sele = selectRoom;
    [[[UIApplication sharedApplication] keyWindow] addSubview:self];

    __weak typeof(UIView *)blockview = _BJView;
    __block int blockH = TWH;
    __block int bjH = BJH;

    [UIView animateWithDuration:0.3
                     animations:^{
                       CGRect bjf = blockview.frame;
                       bjf.origin.y = blockH - bjH;
                       blockview.frame = bjf;
                     }];
}

//自定义每个pickview的label
- (UIView *)pickerView:(UIPickerView *)pickerView viewForRow:(NSInteger)row forComponent:(NSInteger)component reusingView:(UIView *)view
{
    UILabel *pickerLabel = [UILabel new];
    pickerLabel.numberOfLines = 0;
    pickerLabel.textAlignment = NSTextAlignmentCenter;
    [pickerLabel setFont:[UIFont boldSystemFontOfSize:12]];
    pickerLabel.text = [self pickerView:pickerView titleForRow:row forComponent:component];
    return pickerLabel;
}

#pragma mark - UIPickerView method
- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component
{
    self.titleLabel.text = self.sourceArr[row].roomName;
    self.selectedRoom = self.sourceArr[row];
}
- (NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component
{
    return self.sourceArr[row].roomName;
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component
{
    return self.sourceArr.count;
}

- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView
{
    return 1;
}

/**
 *  左边的取消按钮
 */
- (void)leftBTN
{
    __weak typeof(UIView *)blockview = _BJView;
    __weak typeof(self) blockself = self;
    __block int blockH = TWH;

    [UIView animateWithDuration:0.3
        animations:^{
          CGRect bjf = blockview.frame;
          bjf.origin.y = blockH;
          blockview.frame = bjf;
          blockself.alpha = 0.1;
        }
        completion:^(BOOL finished) {
          [blockself removeFromSuperview];
        }];
}

/**
 *  右边的确认按钮
 */
- (void)rightBTN
{
    __weak typeof(UIView *)blockview = _BJView;
    __weak typeof(self) blockself = self;
    __block int blockH = TWH;
    if (self.sele) {
        self.sele(self.selectedRoom);
    }
    [UIView animateWithDuration:0.3
        animations:^{
          CGRect bjf = blockview.frame;
          bjf.origin.y = blockH;
          blockview.frame = bjf;
          blockself.alpha = 0.1;
        }
        completion:^(BOOL finished) {
          [blockself removeFromSuperview];
        }];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    CGPoint point = [[touches anyObject] locationInView:self];
    if (!CGRectContainsPoint(_BJView.frame, point)) {
        [self leftBTN];
    }
}


@end
