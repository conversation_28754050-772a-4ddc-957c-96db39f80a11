//
//  UserDetailViewController.m
//  Debugger
//
//  Created by <PERSON> on 2020/8/25.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserDetailViewController.h"
#import "UpUserDomainHolder.h"
#import "UpUserDomain.h"
#import <Masonry/Masonry.h>
#import "User.h"
#import "UDUserInfoDelegate.h"
#import <SVProgressHUD/SVProgressHUD.h>
#import "UserInfoEditViewController.h"

@interface UserDetailViewController () <UITableViewDelegate, UITableViewDataSource, UIImagePickerControllerDelegate, UINavigationControllerDelegate>
@property (strong, nonatomic) UIImageView *avatarImageView;
@property (strong, nonatomic) UITableView *userInfoTableView;
@property (nonatomic, strong) id<UDUserInfoDelegate> userInfo;

@end

@implementation UserDetailViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = [UIColor whiteColor];
    self.userInfo = [UpUserDomainHolder instance].userDomain.user.extraInfo;
    [self initializeViews];
    UIBarButtonItem *button = [[UIBarButtonItem alloc] initWithTitle:@"刷新" style:UIBarButtonItemStyleDone target:self action:@selector(onRefreshButton:)];
    self.navigationItem.rightBarButtonItem = button;
    self.avatarImageView.userInteractionEnabled = YES;
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapGestureRecognizer:)];
    [self.avatarImageView addGestureRecognizer:tapGesture];
    [self loadAvatarImage];

    //    UserAddressArgs *args = [[UserAddressArgs alloc] init];
    //    args.city = @"青岛市";
    //    args.city_id = @"370200";
    //    args.district = @"崂山区";
    //    args.district_id = @"370212";
    //    args.line1 = @"哈哈";
    //    args.province = @"山东省";
    //    args.province_id = @"370000";
    //    args.is_default = 0;
    //    args.is_service = 0;
    //    args.receiver_name = @"我加";
    //    args.receiver_mobile = @"13100000000";
    //    args.source = @"uplusappios";
    //    [[UpUserDomainHolder instance].userDomain.user createNewAddress:args success:^(UserDomainSampleResult * _Nonnull result) {
    //
    //        NSLog(@"%@",result);
    //        } failure:^(UserDomainSampleResult * _Nonnull result) {
    //            NSLog(@"%@",result);
    //        }];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    [self.userInfoTableView reloadData];
}

- (void)loadAvatarImage
{
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
      NSURL *url = [NSURL URLWithString:self.userInfo.avatarUrl];
      dispatch_async(dispatch_get_main_queue(), ^{
        self.avatarImageView.image = [UIImage imageWithData:[NSData dataWithContentsOfURL:url]];
      });
    });
}

- (void)tapGestureRecognizer:(UITapGestureRecognizer *)recognizer
{
    UIImagePickerController *imagePicker = [[UIImagePickerController alloc] init];
    imagePicker.delegate = self;
    imagePicker.allowsEditing = YES; //编辑模式  但是编辑框是正方形的
    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:nil message:nil preferredStyle:UIAlertControllerStyleActionSheet];

    UIAlertAction *cameraAction = [UIAlertAction actionWithTitle:@"从相机拍照"
                                                           style:UIAlertActionStyleDefault
                                                         handler:^(UIAlertAction *_Nonnull action) {
                                                           imagePicker.sourceType = UIImagePickerControllerSourceTypeCamera;
                                                           [self presentViewController:imagePicker animated:YES completion:nil];
                                                         }];

    UIAlertAction *photoAction = [UIAlertAction actionWithTitle:@"从相册选择"
                                                          style:UIAlertActionStyleDefault
                                                        handler:^(UIAlertAction *_Nonnull action) {
                                                          imagePicker.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
                                                          [self presentViewController:imagePicker animated:YES completion:nil];
                                                        }];

    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消"
                                                           style:UIAlertActionStyleCancel
                                                         handler:^(UIAlertAction *_Nonnull action) {
                                                           NSLog(@"点击了取消");
                                                         }];

    [actionSheet addAction:cameraAction];
    [actionSheet addAction:photoAction];
    [actionSheet addAction:cancelAction];
    [self presentViewController:actionSheet animated:YES completion:NULL];
}

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [picker dismissViewControllerAnimated:YES completion:nil];
}

- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<UIImagePickerControllerInfoKey, id> *)info
{
    [picker dismissViewControllerAnimated:YES completion:nil];
    UIImage *image = info[UIImagePickerControllerEditedImage];
    [SVProgressHUD showInfoWithStatus:@"正在上传"];
    [[UpUserDomainHolder instance]
            .userDomain.user updateAvatar:image
        success:^(UserDomainSampleResult *_Nonnull result) {
          NSLog(@"上传成功");
          [SVProgressHUD showSuccessWithStatus:@"上传成功"];
          [self onRefreshButton:nil];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          NSLog(@"上传失败");
          [SVProgressHUD showErrorWithStatus:@"上传失败"];
        }];
}

- (void)onRefreshButton:(UIBarButtonItem *)item
{
    [SVProgressHUD showInfoWithStatus:@"正在刷新"];
    [[UpUserDomainHolder instance]
            .userDomain refreshUser:^(UserDomainSampleResult *_Nonnull result) {
      if (result.success) {
          [SVProgressHUD showSuccessWithStatus:@"刷新成功"];
      }
      else {
          [SVProgressHUD showErrorWithStatus:@"刷新失败"];
      }
      self.userInfo = [UpUserDomainHolder instance].userDomain.user.extraInfo;
      [self loadAvatarImage];
      [self.userInfoTableView reloadData];
    }];
}

- (void)initializeViews
{
    self.avatarImageView = [[UIImageView alloc] init];
    [self.view addSubview:self.avatarImageView];
    self.userInfoTableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
    self.userInfoTableView.dataSource = self;
    self.userInfoTableView.delegate = self;
    [self.view addSubview:self.userInfoTableView];
    [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(self.view).offset(64);
      make.centerX.equalTo(self.view);
      make.width.equalTo(@(100));
      make.height.equalTo(@(100));
    }];
    [self.userInfoTableView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(self.avatarImageView.mas_bottom).offset(10);
      make.left.equalTo(self.view);
      make.right.equalTo(self.view);
      make.bottom.equalTo(self.view.mas_bottom);
    }];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"TableViewCell"];
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:@"TableViewCell"];
    }
    switch (indexPath.row) {
        case 0:
            cell.textLabel.text = @"birthday";
            cell.detailTextLabel.text = self.userInfo.birthday;
            break;
        case 1:
            cell.textLabel.text = @"extraPhone";
            cell.detailTextLabel.text = self.userInfo.extraPhone;
            break;
        case 2:
            cell.textLabel.text = @"email";
            cell.detailTextLabel.text = self.userInfo.email;
            break;
        case 3:
            cell.textLabel.text = @"username";
            cell.detailTextLabel.text = self.userInfo.username;
            break;
        case 4:
            cell.textLabel.text = @"givenName";
            cell.detailTextLabel.text = self.userInfo.givenName;
            break;
        case 5:
            cell.textLabel.text = @"nickname";
            cell.detailTextLabel.text = self.userInfo.nickname;
            break;
        case 6:
            cell.textLabel.text = @"familyNum";
            cell.detailTextLabel.text = self.userInfo.familyNum;
            break;
        case 7:
            cell.textLabel.text = @"gender";
            cell.detailTextLabel.text = self.userInfo.gender;
            break;
        case 8:
            cell.textLabel.text = @"marriage";
            cell.detailTextLabel.text = self.userInfo.marriage;
            break;
        case 9:
            cell.textLabel.text = @"height";
            cell.detailTextLabel.text = self.userInfo.height;
            break;
        case 10:
            cell.textLabel.text = @"weight";
            cell.detailTextLabel.text = self.userInfo.weight;
            break;
        case 11:
            cell.textLabel.text = @"income";
            cell.detailTextLabel.text = self.userInfo.income;
            break;
    }
    return cell;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return 12;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 45.0;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (indexPath.row == 2 || indexPath.row == 3) {
        return;
    }
    UserInfoEditViewController *controller = [[UserInfoEditViewController alloc] init];
    controller.editInfoIndex = indexPath.row;
    switch (indexPath.row) {
        case 0:
            controller.value = self.userInfo.birthday;
            break;
        case 1:
            controller.value = self.userInfo.extraPhone;
            break;
        case 2:
            controller.value = self.userInfo.email;
            break;
        case 3:
            controller.value = self.userInfo.username;
            break;
        case 4:
            controller.value = self.userInfo.givenName;
            break;
        case 5:
            controller.value = self.userInfo.nickname;
            break;
        case 6:
            controller.value = self.userInfo.familyNum;
            break;
        case 7:
            controller.value = self.userInfo.gender;
            break;
        case 8:
            controller.value = self.userInfo.marriage;
            break;
        case 9:
            controller.value = self.userInfo.height;
            break;
        case 10:
            controller.value = self.userInfo.weight;
            break;
        case 11:
            controller.value = self.userInfo.income;
            break;
    }
    [self.navigationController pushViewController:controller animated:YES];
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
