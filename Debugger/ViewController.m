//
//  ViewController.m
//  Debugger
//
//  Created by <PERSON> on 2020/3/12.
//  Copyright © 2020 振兴郑. All rights reserved.
//

#import "ViewController.h"
#import "UpUserDomainHolder.h"
#import "UpUserDomainObserver.h"
#import "UpUserDomain.h"
#import <uplog/UPLog.h>
#import <uplog/UPLogger.h>
#import <upnetwork/UPNetwork.h>
#import "UPUserLoginApi.h"
#import "ApplicationOauthData.h"
#import "UDUserDelegate.h"
#import "CreateFamilyArgs.h"
#import "Family.h"
#import "Family+PrivateExtension.h"
#import "UserAddressArgs.h"
#import "UserAddressInfo.h"
#import "UDDeviceDelegate.h"
#import "LoginViewController.h"

@interface ViewController () <UpUserDomainObserver>

@end

@implementation ViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    [UPNetworkSettings sharedSettings].appID = @"MB-UZHSH-0001";
    [UPNetworkSettings sharedSettings].appKey = @"5dfca8714eb26e3a776e58a8273c8752";
    [UPNetworkSettings sharedSettings].appVersion = @"6.17.0";
    //    UPLog *log = [UPLog initLogger:[[UPLogger alloc] init]];
    //    [log setLoglevel:UPLogLevelDebug];
    [[UpUserDomainHolder instance].userDomain addObserver:self];
    UIBarButtonItem *loginButtonItem = [[UIBarButtonItem alloc] initWithTitle:@"登录" style:UIBarButtonItemStyleDone target:self action:@selector(onloginButtonItem:)];
    self.navigationItem.rightBarButtonItem = loginButtonItem;
    if ([UpUserDomainHolder instance].userDomain.state == UpUserDomainStateUnLogin) {
        [self onloginButtonItem:nil];
    }
}

- (void)onloginButtonItem:(UIBarButtonItem *)item
{
    UIStoryboard *board = [UIStoryboard storyboardWithName:@"Main" bundle:[NSBundle mainBundle]];
    UIViewController *controller = [board instantiateViewControllerWithIdentifier:@"LoginViewController"];
    controller.modalPresentationStyle = UIModalPresentationFullScreen;
    UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:controller];
    navController.modalPresentationStyle = UIModalPresentationFullScreen;
    [self presentViewController:navController animated:YES completion:NULL];
}

- (void)onRefreshDeviceListSuccess:(id<UDUserDelegate>)user
{
    NSArray *array = [user getDeviceList:^BOOL(id<UDDeviceDelegate> _Nonnull device) {
      return YES;
    }];
    NSLog(@"%@", array);
}

- (void)login
{
    //13210829885  zy228098
    [[[UPUserLoginApi alloc] initWithUsername:@"13210829885" password:@"zy228098"] startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      NSLog(@"LoginSuccess");
      NSDictionary *oauthDataDic = [responseObject valueForKey:@"data"];
      NSDictionary *tokenDic = [oauthDataDic objectForKey:@"tokenInfo"];
      NSLog(@"%@", tokenDic);
      [self updateOauthDataWithDict:tokenDic];
      [self refreshDeviceList];
      [self refreshFamilyList];
      [self refreshAddressList];
      [self queryUserInfo];
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          NSLog(@"LoginFailure");
        }];
}

- (void)editAddressWithArgs:(UserAddressArgs *)args;
{
    //    UserAddressArgs *args = [[UserAddressArgs alloc] init];
    //    [[UpUserDomainHolder instance].userDomain.user editAddress:args success:^(UserDomainSampleResult * _Nonnull result) {
    //
    //    } failure:^(UserDomainSampleResult * _Nonnull result) {
    //
    //    }];
}

- (void)modifyUserInfo
{
}

- (void)uploadUserLoginRecordUhomeUserId:(NSString *)uhomeUserId
{
}

- (void)updateAvatar:(UIImage *)image
{
}

- (void)queryUserInfo
{
    [[UpUserDomainHolder instance]
            .userDomain.user refreshUserInfo:^(UserDomainSampleResult *_Nonnull result){

    }];
}

- (void)queryLoginTerminal
{
}

- (void)queryLoginLogs
{
    [[UpUserDomainHolder instance]
            .userDomain.user queryLoginLogs:1
        pageSize:3
        success:^(UserDomainSampleResult *_Nonnull result) {

        }
        failure:^(UserDomainSampleResult *_Nonnull result){

        }];
}
//家庭
- (void)refreshFamilyList
{
    [[UpUserDomainHolder instance]
            .userDomain.user refreshFamilyList:^(UserDomainSampleResult *_Nonnull result) {
      NSArray<Family *> *familyList = result.retData;
      //              [self queryInfoByFamily:familyList.firstObject];
      [self queryRoomListOfFamily:familyList.firstObject];
    }
        failure:^(UserDomainSampleResult *_Nonnull result){

        }];
}

- (void)queryInfoByFamily:(Family *)family
{
    [family queryInfoSuccess:^(UserDomainSampleResult *_Nonnull result) {

    }
        failure:^(UserDomainSampleResult *_Nonnull result){

        }];
}

- (void)queryRoomListOfFamily:(Family *)family
{
    //    [family queryRoomList:<#(nonnull NSString *)#> success:<#^(UserDomainSampleResult * _Nonnull result)success#> failure:<#^(UserDomainSampleResult * _Nonnull result)failure#>];
}
//地址
- (void)refreshAddressList
{
    [[UpUserDomainHolder instance]
            .userDomain.user refreshAddressList:^(UserDomainSampleResult *_Nonnull result) {

    }
        failure:^(UserDomainSampleResult *_Nonnull result){

        }];
}
//设备
- (void)refreshDeviceList
{
    [[UpUserDomainHolder instance]
            .userDomain.user refreshDeviceList:^(UserDomainSampleResult *_Nonnull result) {
      NSLog(@"222");
    }
        failure:^(UserDomainSampleResult *_Nonnull result){

        }];
}

//终端
- (void)refreshTerminalList
{
    [[UpUserDomainHolder instance]
            .userDomain.user refreshTerminalList:^(UserDomainSampleResult *_Nonnull result) {

    }
        failure:^(UserDomainSampleResult *_Nonnull result){

        }];
}

- (void)createAddress
{
    //    UserAddressInfo *address = [[UserAddressInfo alloc]init];
    //    UserAddressArgs *args = [[UserAddressArgs alloc] init];
    //    args.addressId
    //    [[UpUserDomainHolder instance].userDomain.user createNewAddress:args success:^(UserDomainSampleResult * _Nonnull result) {
    //    } failure:^(UserDomainSampleResult * _Nonnull result) {
    //    }];
}

- (void)createFamily
{

    CreateFamilyArgs *args = [[CreateFamilyArgs alloc] init];
    args.name = @"124";
    args.cityCode = @"101110109";
    args.latitude = @"34.192035";
    args.longitude = @"108.873059";
    args.position = @"洒到了电脑蕉农";
    args.roomNames = @[ @"124", @"346" ];

    [[UpUserDomainHolder instance]
            .userDomain.user createFamily:args
        success:^(UserDomainSampleResult *_Nonnull result) {

        }
        failure:^(UserDomainSampleResult *_Nonnull result){

        }];
}

- (void)autoRefreshToken
{
    [[UpUserDomainHolder instance].userDomain autoRefreshToken];
}

- (void)updateOauthDataWithDict:(NSDictionary *)tokenDic
{
    [[UpUserDomainHolder instance].userDomain updateOauthData:tokenDic[@"accountToken"] refreshToken:tokenDic[@"refreshToken"] uhome_access_token:tokenDic[@"uhomeAccessToken"] expires_in:[tokenDic[@"expiresIn"] stringValue] scope:@"" token_type:tokenDic[@"tokenType"] uhome_user_id:tokenDic[@"uhomeUserId"] uc_user_id:tokenDic[@"uocUserId"]];
}

- (void)logOut
{
    [[UpUserDomainHolder instance]
            .userDomain logOut:^(UserDomainSampleResult *_Nonnull result) {
      NSLog(@"11111");
    }];
}

- (void)onLogOut:(id<UpUserDomainDelegate>)userDomain
{
}
@end
