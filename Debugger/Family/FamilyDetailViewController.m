//
//  FamilyDetailViewController.m
//  Debugger
//
//  Created by 闫达 on 2020/8/25.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FamilyDetailViewController.h"
#import "UDFamilyDelegate.h"
#import "UDFamilyInfoDelegate.h"
#import "UDMemberInfoDelegate.h"
#import "UDUserInfoDelegate.h"
#import "UpUserDomain.h"
#import "UDFamilyLocationDelegate.h"
#import "UDDeviceInfoDelegate.h"
#import "UDRoomDelegate.h"
#import "UPMacros.h"
#import "UDDeviceDelegate.h"
#import "UpUserDomainHolder.h"
#import "UDUserDelegate.h"
#import "FamilyArgs.h"
#import "MyFamilyController.h"
#import "AddressPickView.h"
#import "RoomArgs.h"
#import "FamilyOwnerViewController.h"
#import "FamilyDevicesViewController.h"
#import "RoomDevicesViewController.h"
#import "AddressViewController.h"
#import <Masonry/Masonry.h>
#import <SVProgressHUD.h>

NSString *const FAMILYNAME = @"familyName";
NSString *const FAMILYPOSITION = @"familyPosition";
NSString *const FAMILYMEMBERS = @"familyMembers";
NSString *const FAMILYOWNER = @"familyOwner";
NSString *const NAME = @"家庭名称";
NSString *const POSITION = @"家庭地址";
NSString *const MEMBERS = @"我的家人";
NSString *const OWNER = @"管理员权限变更";
@interface FamilyDetailViewController ()
@property (nonatomic, strong) NSArray<NSArray<NSString *> *> *familyInfoNames;
@property (nonatomic, strong) NSMutableDictionary *familyInfo;
@property (nonatomic, strong) NSMutableArray<id<UDRoomDelegate>> *familyRooms;
@property (nonatomic, strong) NSMutableDictionary<NSString *, id<UDDeviceDelegate>> *devices;
@property (nonatomic, strong) UIView *footerView;
@property (nonatomic, strong) NSDictionary *selectAddrDic;
@end
static const CGFloat headH = 44.0;
@implementation FamilyDetailViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.navigationItem.title = @"家庭管理";
    self.tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT) style:UITableViewStyleGrouped];
    if (self.isOwner) {
        _familyInfoNames = @[ @[ NAME, POSITION, MEMBERS, OWNER ], @[ @"房间列表" ], @[ @"家庭设备" ] ];
    }
    else {
        _familyInfoNames = @[ @[ NAME, POSITION, MEMBERS ], @[ @"房间列表" ], @[ @"家庭设备" ] ];
    }
    [self initializeFooterView];
}

- (void)setFamily:(id<UDFamilyDelegate>)family
{
    _family = family;
    [self initializePropertyByFamily:family];
}

- (void)initializePropertyByFamily:(id<UDFamilyDelegate>)family
{
    _familyInfo = [NSMutableDictionary dictionary];
    [_familyInfo setValue:family.info.familyName forKey:FAMILYNAME];
    [_familyInfo setValue:family.info.familyPosition forKey:FAMILYPOSITION];
    [_familyInfo setValue:[NSString stringWithFormat:@"%lu", (unsigned long)family.members.count] forKey:FAMILYMEMBERS];
    [_familyInfo setValue:family.owner.name forKey:FAMILYOWNER];
    //    if (family.rooms.count > 0) {
    //        self.familyRooms = [NSMutableArray arrayWithArray:family.rooms];
    //    }
    self.devices = [NSMutableDictionary dictionaryWithDictionary:family.devices];
    dispatch_async(dispatch_get_main_queue(), ^{
      [self.tableView reloadData];
    });
}

- (void)initializeFooterView
{
    UIButton *quitBtn = [[UIButton alloc] init];
    [quitBtn setTitle:@"退出家庭" forState:UIControlStateNormal];
    quitBtn.backgroundColor = UIColor.blueColor;
    quitBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    [quitBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [quitBtn addTarget:self action:@selector(quitFamily) forControlEvents:UIControlEventTouchUpInside];


    UIButton *deleteBtn = [[UIButton alloc] init];
    [deleteBtn setTitle:@"删除家庭" forState:UIControlStateNormal];
    deleteBtn.backgroundColor = UIColor.blueColor;
    deleteBtn.titleLabel.font = [UIFont systemFontOfSize:15];
    [deleteBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [deleteBtn addTarget:self action:@selector(deleteFamily) forControlEvents:UIControlEventTouchUpInside];


    if (self.isOwner && self.family.members.count > 1) {
        [self.footerView addSubview:quitBtn];
        [quitBtn mas_makeConstraints:^(MASConstraintMaker *make) {
          make.leading.mas_equalTo(20);
          make.trailing.mas_equalTo(-(SCREEN_WIDTH * 0.5 + 10));
          make.height.mas_equalTo(50);
          make.centerY.mas_equalTo(self.footerView);
        }];

        [self.footerView addSubview:deleteBtn];
        [deleteBtn mas_makeConstraints:^(MASConstraintMaker *make) {
          make.leading.mas_equalTo(SCREEN_WIDTH * 0.5 + 10);
          make.trailing.mas_equalTo(-20);
          make.height.mas_equalTo(50);
          make.centerY.mas_equalTo(self.footerView);
        }];
    }
    else {
        [self.footerView addSubview:quitBtn];
        [quitBtn mas_makeConstraints:^(MASConstraintMaker *make) {
          make.leading.mas_equalTo(20);
          make.trailing.mas_equalTo(-20);
          make.height.mas_equalTo(50);
          make.centerY.mas_equalTo(self.footerView);
        }];
    }

    self.tableView.tableFooterView = self.footerView;
}


#pragma mark - lazyLoad
- (UIView *)footerView
{
    if (!_footerView) {
        _footerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 100)];
        _footerView.backgroundColor = UIColor.whiteColor;
    }
    return _footerView;
}


#pragma mark - Table view data source

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return _familyInfoNames.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    switch (section) {
        case 0:
            return self.familyInfoNames[section].count;
            break;
        case 1:
            return self.familyRooms.count;
            break;
        case 2:
            return 1;
            break;
        default:
            return 0;
            break;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 44 * SCREEN_SCALE_375;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    static NSString *cellID = @"FamilyDetailViewCell";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellID];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:cellID];
    }
    if (indexPath.section == 0) {
        NSString *textStr = self.familyInfoNames[indexPath.section][indexPath.row];
        cell.textLabel.text = textStr;
        switch (indexPath.row) {
            case 0:
                cell.detailTextLabel.text = self.familyInfo[FAMILYNAME];
                cell.accessoryType = self.isOwner ? UITableViewCellAccessoryDisclosureIndicator : UITableViewCellAccessoryNone;
                break;
            case 1:
                cell.detailTextLabel.text = self.familyInfo[FAMILYPOSITION];
                cell.accessoryType = self.isOwner ? UITableViewCellAccessoryDisclosureIndicator : UITableViewCellAccessoryNone;
                break;
            case 2:
                cell.detailTextLabel.text = self.familyInfo[FAMILYMEMBERS];
                cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
                break;
            case 3:
                cell.detailTextLabel.text = self.familyInfo[FAMILYOWNER];
                cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
                break;
            default:
                break;
        }
    }
    else if (indexPath.section == 1) {
        NSString *textStr = self.familyRooms[indexPath.row].roomName;
        cell.textLabel.text = textStr;
        cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    }
    else {
        NSString *textStr = self.familyInfoNames[indexPath.section][indexPath.row];
        cell.textLabel.text = textStr;
        cell.detailTextLabel.text = [NSString stringWithFormat:@"%ld", self.devices.count];
        cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    }
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (indexPath.section == 0) {
        if ([self.familyInfoNames[indexPath.section][indexPath.row] isEqualToString:NAME] && self.isOwner == YES) {
            UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"修改家庭名称" message:nil preferredStyle:UIAlertControllerStyleAlert];
            [alertController addTextFieldWithConfigurationHandler:^(UITextField *textField) {
              textField.placeholder = @"请输入要修改的家庭名称";
            }];
            UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定"
                                                               style:UIAlertActionStyleDefault
                                                             handler:^(UIAlertAction *action) {
                                                               UITextField *familyName = alertController.textFields.firstObject;
                                                               [self modifyFamilyName:familyName.text indexpath:indexPath];
                                                             }];
            UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil];
            [alertController addAction:okAction];
            [alertController addAction:cancelAction];

            [self presentViewController:alertController
                               animated:YES
                             completion:^{

                             }];
        }
        else if ([self.familyInfoNames[indexPath.section][indexPath.row] isEqualToString:POSITION] && self.isOwner == YES) {
            AddressViewController *addrVC = [[AddressViewController alloc] init];
            addrVC.selectAddrBlock = ^(NSDictionary *_Nonnull selectDic) {
              self.selectAddrDic = selectDic;
              NSString *addrStr = [NSString stringWithFormat:@"%@ %@", selectDic[@"districtCn"], selectDic[@"nameCn"]];
              [self modifyAddress:addrStr AtIndexPath:indexPath];
            };
            [self.navigationController pushViewController:addrVC animated:YES];
            //            AddressPickView *city = [[AddressPickView alloc] initWithFrame:self.view.bounds selectCityTitle:@"选择地区"];
            //            [city showCityView:^(NSString *proviceStr, NSString *cityStr, NSString *distr) {
            //              NSString *selectAddr = [NSString stringWithFormat:@"%@->%@->%@", proviceStr, cityStr, distr];
            //              [self modifyAddress:selectAddr indexpath:indexPath];
            //            }];
        }
        else if ([self.familyInfoNames[indexPath.section][indexPath.row] isEqualToString:MEMBERS]) {
            MyFamilyController *myFamilyVC = [[MyFamilyController alloc] init];
            myFamilyVC.navigationItem.title = @"我的家人";
            myFamilyVC.family = self.family;
            myFamilyVC.members = self.family.members;
            myFamilyVC.currentFamilyName = self.family.info.familyName;
            myFamilyVC.ownerID = self.family.owner.userId;
            myFamilyVC.isOwner = self.isOwner;
            [self.navigationController pushViewController:myFamilyVC animated:YES];
        }
        else if ([self.familyInfoNames[indexPath.section][indexPath.row] isEqualToString:OWNER] && self.isOwner == YES) {
            FamilyOwnerViewController *ownerVC = [[FamilyOwnerViewController alloc] init];
            ownerVC.navigationItem.title = @"我的家人";
            ownerVC.family = self.family;
            [self.navigationController pushViewController:ownerVC animated:YES];
        }
    }
    else if (indexPath.section == 1) {
        id<UDRoomDelegate> room = self.familyRooms[indexPath.row];
        RoomDevicesViewController *deviceVC = [[RoomDevicesViewController alloc] init];
        deviceVC.navigationItem.title = room.roomName;
        NSMutableArray<id<UDDeviceDelegate>> *roomDevices = [NSMutableArray array];
        for (id<UDDeviceDelegate> device in self.devices.allValues) {
            NSLog(@"%@-------%@", device.roomId, room.roomId);
            if ([device.roomId isEqualToString:room.roomId]) {
                [roomDevices addObject:device];
            }
        }
        deviceVC.devices = roomDevices;
        deviceVC.family = self.family;
        deviceVC.isOwner = self.isOwner;
        deviceVC.room = room;

        [self.navigationController pushViewController:deviceVC animated:YES];
    }
    else if (indexPath.section == 2) {
        FamilyDevicesViewController *deviceVC = [[FamilyDevicesViewController alloc] init];
        deviceVC.navigationItem.title = @"设备列表";
        deviceVC.devices = [NSMutableArray arrayWithArray:self.devices.allValues.mutableCopy];
        deviceVC.family = self.family;
        deviceVC.isOwner = self.isOwner;
        [self.navigationController pushViewController:deviceVC animated:YES];
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    if (section == 1 && self.isOwner == YES) {
        UIView *v = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, headH)];
        v.backgroundColor = UIColor.whiteColor;
        UILabel *titleLbl = [[UILabel alloc] initWithFrame:CGRectMake(20, 0, SCREEN_WIDTH * 0.5 - 20, headH)];
        titleLbl.text = @"房间列表";
        titleLbl.font = [UIFont systemFontOfSize:15];
        titleLbl.textColor = [UIColor blackColor];
        [v addSubview:titleLbl];
        UIButton *addBtn = [[UIButton alloc] initWithFrame:CGRectMake(SCREEN_WIDTH * 0.5, 0, SCREEN_WIDTH * 0.5 - 20, headH)];
        [addBtn setTitle:@"添加" forState:UIControlStateNormal];
        addBtn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
        addBtn.titleLabel.font = [UIFont systemFontOfSize:15];
        [addBtn addTarget:self action:@selector(addRoom) forControlEvents:UIControlEventTouchUpInside];
        [addBtn setTitleColor:[UIColor lightGrayColor] forState:normal];
        [v addSubview:addBtn];
        return v;
    }
    else {
        return [[UIView alloc] init];
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    if (section == 1 && self.isOwner == YES) {
        return headH;
    }
    else {
        return 0;
    }
}

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath
{
    return self.isOwner;
}

- (UITableViewCellEditingStyle)tableView:(UITableView *)tableView editingStyleForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return UITableViewCellEditingStyleDelete;
}

- (NSString *)tableView:(UITableView *)tableView titleForDeleteConfirmationButtonForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return @"删除";
}

- (void)tableView:(UITableView *)tableView commitEditingStyle:(UITableViewCellEditingStyle)editingStyle forRowAtIndexPath:(NSIndexPath *)indexPath
{
    [self deleteRoomAtIndexPath:indexPath];
}

#pragma mark -  UIcontrolEventMethod

- (void)addRoom
{
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"添加房间" message:nil preferredStyle:UIAlertControllerStyleAlert];
    [alertController addTextFieldWithConfigurationHandler:^(UITextField *textField) {
      textField.placeholder = @"请输入房间名称";
    }];
    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定"
                                                       style:UIAlertActionStyleDefault
                                                     handler:^(UIAlertAction *action) {
                                                       UITextField *familyName = alertController.textFields.firstObject;
                                                       [self addRoomAndRefreshDataByName:familyName.text];
                                                     }];
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil];
    [alertController addAction:okAction];
    [alertController addAction:cancelAction];

    [self presentViewController:alertController
                       animated:YES
                     completion:^{

                     }];
}

- (void)quitFamily
{
    NSString *userId = [UpUserDomainHolder instance].userDomain.user.extraInfo.userId;
    if ([self.family.owner.userId isEqualToString:userId] || self.isOwner) {
        [self.family exitFamilyAsAdmin:userId
            success:^(UserDomainSampleResult *_Nonnull result) {
              NSLog(@"管理员退出家庭成功");
              dispatch_async(dispatch_get_main_queue(), ^{
                [self.navigationController popViewControllerAnimated:YES];
              });
            }
            failure:^(UserDomainSampleResult *_Nonnull result) {
              NSLog(@"管理员退出家庭失败");
            }];
    }
    else {
        [self.family exitFamilyAsMemberSuccess:^(UserDomainSampleResult *_Nonnull result) {
          NSLog(@"成员退出家庭成功");
          dispatch_async(dispatch_get_main_queue(), ^{
            [self.navigationController popViewControllerAnimated:YES];
          });
        }
            failure:^(UserDomainSampleResult *_Nonnull result) {
              NSLog(@"成员退出家庭失败");
            }];
    }
}

- (void)deleteFamily
{
    [self.family destoryFamilyAsAdminSuccess:^(UserDomainSampleResult *_Nonnull result) {
      dispatch_async(dispatch_get_main_queue(), ^{
        [self.navigationController popViewControllerAnimated:YES];
      });
    }
        failure:^(UserDomainSampleResult *_Nonnull result){

        }];
}

#pragma mark - userDomain method

- (void)modifyFamilyName:(NSString *)name indexpath:(NSIndexPath *)indexpath
{
    FamilyArgs *args = [[FamilyArgs alloc] init];
    args.latitude = self.family.info.familyLocation.latitude;
    args.longitude = self.family.info.familyLocation.longitude;
    args.cityCode = self.family.info.familyLocation.cityCode;
    args.name = name;
    args.position = self.family.info.familyPosition;
    //    args.roomNames = self.family.info.roomNames;
    [self.family updateFamilyInfo:args
        success:^(UserDomainSampleResult *_Nonnull result) {
          [self.familyInfo setValue:name forKey:@"familyName"];
          dispatch_async(dispatch_get_main_queue(), ^{
            [self.tableView reloadRowsAtIndexPaths:@[ indexpath ] withRowAnimation:UITableViewRowAnimationAutomatic];
          });
        }
        failure:^(UserDomainSampleResult *_Nonnull result){

        }];
}

- (void)modifyAddress:(NSString *)address AtIndexPath:(NSIndexPath *)indexPath
{
    FamilyArgs *args = [[FamilyArgs alloc] init];
    args.latitude = [self.selectAddrDic[@"latitude"] stringValue];
    ;
    args.longitude = [self.selectAddrDic[@"longitude"] stringValue];
    ;
    args.cityCode = [self.selectAddrDic[@"areaId"] stringValue];
    ;
    args.name = self.family.info.familyName;
    args.position = address;
    //    args.roomNames = self.family.info.roomNames;
    [self.family updateFamilyInfo:args
        success:^(UserDomainSampleResult *_Nonnull result) {
          [self.familyInfo setValue:address forKey:FAMILYPOSITION];
          dispatch_async(dispatch_get_main_queue(), ^{
            [self.tableView reloadRowsAtIndexPaths:@[ indexPath ] withRowAnimation:UITableViewRowAnimationAutomatic];
          });
        }
        failure:^(UserDomainSampleResult *_Nonnull result){

        }];
}

- (void)addRoomAndRefreshDataByName:(NSString *)name
{
    RoomArgs *args = [[RoomArgs alloc] init];
    args.name = name;
    args.image = @"wwwww";
    args.label = @"哇晒";
    args.logo = @"太阳";
    args.type = @"666";
    __weak typeof(self) weakSelf = self;
    [self.family addRoom:args
        success:^(UserDomainSampleResult *_Nonnull result) {
          NSLog(@"addRoom");
          [weakSelf queryRoomList];
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          NSLog(@"addRoomFailure");
        }];
}

- (void)queryRoomList
{
    //    [self.family queryRoomListSuccess:^(UserDomainSampleResult *_Nonnull result) {
    //      self.familyRooms = result.retData;
    //      dispatch_async(dispatch_get_main_queue(), ^{
    //        [self.tableView reloadData];
    //      });
    //    }
    //        failure:^(UserDomainSampleResult *_Nonnull result){
    //
    //        }];
}

- (void)deleteRoomAtIndexPath:(NSIndexPath *)indexPath
{
    id<UDRoomDelegate> room = self.familyRooms[indexPath.row];
    [self.family removeRoom:room.roomId
        success:^(UserDomainSampleResult *_Nonnull result) {
          dispatch_async(dispatch_get_main_queue(), ^{
            [SVProgressHUD showSuccessWithStatus:@"管理员删除房间成功"];
            [self.tableView reloadRowsAtIndexPaths:@[ indexPath ] withRowAnimation:UITableViewRowAnimationAutomatic];
            [self.navigationController popToRootViewControllerAnimated:YES];
          });
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showSuccessWithStatus:@"管理员删除房间失败"];
        }];
}
@end
