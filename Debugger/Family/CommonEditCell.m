//
//  CommonEditCell.m
//  Debugger
//
//  Created by 闫达 on 2020/8/27.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "CommonEditCell.h"
#import <Masonry/Masonry.h>

@interface CommonEditCell ()
@property (nonatomic, strong) UIImageView *iconView;
@property (nonatomic, strong) UILabel *nameLbl;
@end

@implementation CommonEditCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self initiallizeUI];
    }
    return self;
}

#pragma mark - lazyLoad
- (UIImageView *)iconView
{
    if (!_iconView) {
        _iconView = [[UIImageView alloc] init];
    }
    return _iconView;
}

- (UILabel *)nameLbl
{
    if (!_nameLbl) {
        _nameLbl = [[UILabel alloc] init];
        _nameLbl.textColor = UIColor.blackColor;
        _nameLbl.font = [UIFont systemFontOfSize:15];
    }
    return _nameLbl;
}

#pragma mark - UIMethod
- (void)initiallizeUI
{
    [self.contentView addSubview:self.iconView];
    __weak typeof(self) weakSelf = self;
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.leading.mas_equalTo(20);
      make.size.mas_equalTo(CGSizeMake(40, 40));
      make.centerY.mas_equalTo(weakSelf.contentView);
    }];

    [self.contentView addSubview:self.nameLbl];
    [self.nameLbl mas_makeConstraints:^(MASConstraintMaker *make) {
      make.trailing.mas_equalTo(-100);
      make.leading.mas_equalTo(weakSelf.iconView.mas_right).offset(20);
      make.height.mas_equalTo(15);
      make.centerY.mas_equalTo(weakSelf.iconView);
    }];
}

#pragma mark - setter
- (void)setModel:(NSObject *)model
{
}


@end
