//
//  FamilyDevicesViewController.h
//  Debugger
//
//  Created by 闫达 on 2020/8/26.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@protocol UDFamilyDelegate
, UDDeviceDelegate;
@interface FamilyDevicesViewController : UIViewController
@property (nonatomic, strong) id<UDFamilyDelegate> family;
@property (nonatomic, strong) NSMutableArray<id<UDDeviceDelegate>> *devices;
@property (nonatomic, assign) BOOL isOwner;
@end

NS_ASSUME_NONNULL_END
