//
//  CreatFamilyViewController.m
//  Debugger
//
//  Created by 闫达 on 2020/8/27.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "CreatFamilyViewController.h"
#import "UpUserDomain.h"
#import "UpUserDomainHolder.h"
#import "UDUserDelegate.h"
#import "UPMacros.h"
#import "UDUserDelegate.h"
#import "AddressPickView.h"
#import "CreateFamilyArgs.h"
#import "AddressViewController.h"
#import <SVProgressHUD.h>
NSString *const familyName = @"家庭名称";
NSString *const familyAddress = @"家庭地址";
NSString *const PARAMFAMILYNAME = @"familyName";
NSString *const PARAMFAMILYADDRESS = @"familyAddress";
@interface CreatFamilyViewController ()
@property (nonatomic, strong) NSArray<NSArray<NSString *> *> *familyInfoNames;
@property (nonatomic, strong) NSMutableDictionary *param;
@property (nonatomic, strong) NSMutableArray<NSString *> *roomNames;
@property (nonatomic, strong) NSDictionary *selectAddrDic;
@end
@implementation CreatFamilyViewController
- (void)viewDidLoad
{
    [super viewDidLoad];
    self.param = [NSMutableDictionary dictionary];
    self.tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT) style:UITableViewStyleGrouped];
    NSArray<NSString *> *tmpNames = @[ @"客厅", @"卧室", @"厨房", @"洗漱间" ];
    _familyInfoNames = @[ @[ familyName, familyAddress ], tmpNames ];
    _roomNames = [NSMutableArray arrayWithArray:tmpNames];
    self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithTitle:@"完成" style:UIBarButtonItemStylePlain target:self action:@selector(addComplete)];
}

#pragma mark - Table view data source

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 2;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    if (section == 0) {
        return self.familyInfoNames[section].count;
    }
    else {
        return self.roomNames.count;
    }
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    static NSString *cellID = @"CreatFamilyViewControllerCell";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellID];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:cellID];
    }
    NSString *title;
    if (indexPath.section == 0) {
        title = self.familyInfoNames[indexPath.section][indexPath.row];
        if (indexPath.row == 0) {
            cell.detailTextLabel.text = [self.param valueForKey:PARAMFAMILYNAME];
        }
        else if (indexPath.row == 1) {
            cell.detailTextLabel.text = [self.param valueForKey:PARAMFAMILYADDRESS];
        }
    }
    else {
        title = self.roomNames[indexPath.row];
    }
    cell.textLabel.text = title;
    return cell;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    if (section == 1) {
        UIView *v = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 44)];
        v.backgroundColor = UIColor.whiteColor;
        UILabel *titleLbl = [[UILabel alloc] initWithFrame:CGRectMake(20, 0, SCREEN_WIDTH * 0.5 - 20, 44)];
        titleLbl.text = @"房间列表";
        titleLbl.font = [UIFont systemFontOfSize:15];
        titleLbl.textColor = [UIColor blackColor];
        [v addSubview:titleLbl];
        UIButton *addBtn = [[UIButton alloc] initWithFrame:CGRectMake(SCREEN_WIDTH * 0.5, 0, SCREEN_WIDTH * 0.5 - 20, 44)];
        [addBtn setTitle:@"添加" forState:UIControlStateNormal];
        addBtn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentRight;
        addBtn.titleLabel.font = [UIFont systemFontOfSize:15];
        [addBtn addTarget:self action:@selector(addRoom) forControlEvents:UIControlEventTouchUpInside];
        [addBtn setTitleColor:[UIColor lightGrayColor] forState:normal];
        [v addSubview:addBtn];
        return v;
    }
    else {
        return [[UIView alloc] init];
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    if (section == 1) {
        return 44;
    }
    else {
        return 0;
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (indexPath.section == 0) {
        if ([self.familyInfoNames[indexPath.section][indexPath.row] isEqualToString:familyName]) {
            UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"填写家庭名称" message:nil preferredStyle:UIAlertControllerStyleAlert];
            [alertController addTextFieldWithConfigurationHandler:^(UITextField *textField) {
              textField.placeholder = @"请输入家庭名称";
            }];
            UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定"
                                                               style:UIAlertActionStyleDefault
                                                             handler:^(UIAlertAction *action) {
                                                               UITextField *familyName = alertController.textFields.firstObject;
                                                               [self.param setValue:familyName.text forKey:PARAMFAMILYNAME];
                                                               [self.tableView reloadData];

                                                             }];
            UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil];
            [alertController addAction:okAction];
            [alertController addAction:cancelAction];

            [self presentViewController:alertController
                               animated:YES
                             completion:^{

                             }];
        }
        else if ([self.familyInfoNames[indexPath.section][indexPath.row] isEqualToString:familyAddress]) {
            AddressViewController *addrVC = [[AddressViewController alloc] init];
            addrVC.selectAddrBlock = ^(NSDictionary *_Nonnull selectDic) {
              self.selectAddrDic = selectDic;
              NSString *addrStr = [NSString stringWithFormat:@"%@ %@", self.selectAddrDic[@"districtCn"], self.selectAddrDic[@"nameCn"]];
              [self.param setValue:addrStr forKey:PARAMFAMILYADDRESS];
              [self.tableView reloadRowsAtIndexPaths:@[ indexPath ] withRowAnimation:UITableViewRowAnimationAutomatic];
            };
            [self.navigationController pushViewController:addrVC animated:YES];
            //            AddressPickView *city = [[AddressPickView alloc] initWithFrame:self.view.bounds selectCityTitle:@"选择地区"];
            //            [city showCityView:^(NSString *proviceStr, NSString *cityStr, NSString *distr) {
            //              NSString *selectAddr = [NSString stringWithFormat:@"%@->%@->%@", proviceStr, cityStr, distr];
            //              [self.param setValue:PARAMFAMILYADDRESS forKey:selectAddr];
            //              [self.tableView reloadData];
            //            }];
        }
    }
}

#pragma mark - eventMethod
- (void)addRoom
{
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"添加房间" message:nil preferredStyle:UIAlertControllerStyleAlert];
    [alertController addTextFieldWithConfigurationHandler:^(UITextField *textField) {
      textField.placeholder = @"请输入房间名称";
    }];
    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定"
                                                       style:UIAlertActionStyleDefault
                                                     handler:^(UIAlertAction *action) {
                                                       UITextField *familyName = alertController.textFields.firstObject;
                                                       [self.roomNames addObject:familyName.text];
                                                       [self.tableView reloadData];

                                                     }];
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil];
    [alertController addAction:okAction];
    [alertController addAction:cancelAction];

    [self presentViewController:alertController
                       animated:YES
                     completion:^{

                     }];
}

- (void)addComplete
{
    NSString *name = [self.param valueForKey:PARAMFAMILYNAME];
    if (name.length == 0) {
        [SVProgressHUD showErrorWithStatus:@"请填写家庭名称"];
        return;
    }
    NSString *address = [self.param valueForKey:PARAMFAMILYADDRESS];
    if (address.length == 0) {
        [SVProgressHUD showErrorWithStatus:@"请填写家庭地址"];
        return;
    }

    CreateFamilyArgs *args = [[CreateFamilyArgs alloc] init];
    args.name = name;
    args.latitude = [self.selectAddrDic[@"latitude"] stringValue];
    ;
    args.longitude = [self.selectAddrDic[@"longitude"] stringValue];
    ;
    args.cityCode = [self.selectAddrDic[@"areaId"] stringValue];
    args.position = address;
    args.roomNames = self.roomNames;
    [[UpUserDomainHolder instance]
            .userDomain.user createFamily:args
        success:^(UserDomainSampleResult *_Nonnull result) {
          dispatch_async(dispatch_get_main_queue(), ^{
            [self.navigationController popViewControllerAnimated:YES];
          });
        }
        failure:^(UserDomainSampleResult *_Nonnull result){

        }];
}


@end
