//
//  AddressViewController.m
//  Debugger
//
//  Created by 闫达 on 2020/9/1.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "AddressViewController.h"

@interface AddressViewController ()
@property (nonatomic, strong) NSArray<NSDictionary *> *addrArr;
@end
static NSString *cellID = @"AddressViewControllerCell";
@implementation AddressViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    [self.tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:cellID];
    [self getAddrData];
}

- (void)getAddrData
{
    NSString *filePath = [[NSBundle mainBundle] pathForResource:@"city-v3.0" ofType:@"json"];
    NSString *jsonStr = [NSString stringWithContentsOfFile:filePath encoding:NSUTF8StringEncoding error:nil];
    NSData *jaonData = [jsonStr dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *dataDic = [NSJSONSerialization JSONObjectWithData:jaonData options:NSJSONReadingMutableContainers error:nil];
    NSArray *jsonArray = dataDic[@"data"];
    self.addrArr = [NSArray arrayWithArray:jsonArray];
    [self.tableView reloadData];
}

#pragma mark - Table view data source

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.addrArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSDictionary *dic = self.addrArr[indexPath.row];
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellID forIndexPath:indexPath];
    cell.textLabel.text = [NSString stringWithFormat:@"%@ %@", dic[@"districtCn"], dic[@"nameCn"]];
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSDictionary *selectDic = self.addrArr[indexPath.row];
    if (self.selectAddrBlock) {
        self.selectAddrBlock(selectDic);
        [self.navigationController popViewControllerAnimated:YES];
    }
}

@end
