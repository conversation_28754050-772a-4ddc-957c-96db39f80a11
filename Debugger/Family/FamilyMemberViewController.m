//
//  FamilyMemberViewController.m
//  Debugger
//
//  Created by 闫达 on 2020/8/27.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FamilyMemberViewController.h"
#import "UDFamilyMemberDelegate.h"
#import "UDMemberInfoDelegate.h"
#import "UDFamilyDelegate.h"
#import <Masonry/Masonry.h>
#import "UDDeviceDelegate.h"
#import "UPMacros.h"
@interface FamilyMemberViewController ()
@property (nonatomic, strong) UIView *headerView;
@property (nonatomic, strong) NSMutableArray<id<UDDeviceDelegate>> *datasourceArr;
@end

@implementation FamilyMemberViewController
static const CGFloat headerH = 100.0;

- (NSMutableArray<id<UDDeviceDelegate>> *)datasourceArr
{
    if (!_datasourceArr) {
        _datasourceArr = [NSMutableArray array];
    }
    return _datasourceArr;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStyleGrouped];
    [self initHeaderView];
    [self initDataSource];
}

#pragma mark - initialize

- (void)initHeaderView
{
    UIImageView *memberIcon = [[UIImageView alloc] initWithImage:[UIImage imageWithData:[NSData dataWithContentsOfURL:[NSURL URLWithString:self.member.memberInfo.avatarUrl]]]];
    [self.headerView addSubview:memberIcon];
    __weak typeof(self) weakSelf = self;
    [memberIcon mas_makeConstraints:^(MASConstraintMaker *make) {
      make.top.mas_equalTo(20);
      make.centerX.mas_equalTo(weakSelf.headerView);
      make.size.mas_equalTo(CGSizeMake(40, 40));
    }];

    UILabel *nickLbl = [[UILabel alloc] init];
    nickLbl.text = self.member.memberInfo.name;
    nickLbl.textColor = [UIColor grayColor];
    nickLbl.font = [UIFont systemFontOfSize:15];
    nickLbl.textAlignment = UITextAlignmentCenter;
    [self.headerView addSubview:nickLbl];
    [nickLbl mas_makeConstraints:^(MASConstraintMaker *make) {
      make.centerX.mas_equalTo(memberIcon);
      make.top.mas_equalTo(memberIcon.mas_bottom).offset(10);
      make.width.mas_equalTo(weakSelf.headerView);
    }];
    self.tableView.tableHeaderView = self.headerView;
}

- (void)initDataSource
{
    NSArray<id<UDDeviceDelegate>> *devices = [self.family getDeviceList];
    for (id<UDDeviceDelegate> device in devices) {
        if ([device.ownerId isEqualToString:self.member.memberInfo.userId]) {
            [self.datasourceArr addObject:device];
        }
    }
    [self.tableView reloadData];
}

#pragma mark - lazyLoad

- (UIView *)headerView
{
    if (!_headerView) {
        _headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, headerH)];
        _headerView.backgroundColor = UIColor.whiteColor;
    }
    return _headerView;
}

#pragma mark - Table view data source

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.datasourceArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    static NSString *cellID = @"FamilyMemberViewControllerCell";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellID];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:cellID];
    }
    id<UDDeviceDelegate> device = self.datasourceArr[indexPath.row];
    cell.textLabel.text = device.deviceName;
    //    cell.imageView.image = [UIImage imageWithData:[NSData dataWithContentsOfURL:[NSURL URLWithString:device.imageAddr1]]];
    NSLog(@"%@", device.imageAddr1);
    return cell;
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section
{
    return [NSString stringWithFormat:@"分享了%ld个设备", self.datasourceArr.count];
}


@end
