//
//  FamilyOwnerViewController.m
//  Debugger
//
//  Created by 闫达 on 2020/8/27.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FamilyOwnerViewController.h"
#import "UDFamilyMemberDelegate.h"
#import "UDMemberInfoDelegate.h"
#import "UDFamilyDelegate.h"
@interface FamilyOwnerViewController ()

@end

@implementation FamilyOwnerViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
}

#pragma mark - Table view data source

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.family.members.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    static NSString *cellID = @"FamilyOwnerViewControllerCell";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellID];

    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:cellID];
    }
    id<UDFamilyMemberDelegate> member = self.family.members[indexPath.row];
    cell.textLabel.text = member.memberInfo.name;
    //    cell.imageView.image = [UIImage imageWithData:[NSData dataWithContentsOfURL:[NSURL URLWithString:member.memberInfo.avatarUrl]]];
    if ([member.memberInfo.userId isEqualToString:self.family.owner.userId]) {
        cell.detailTextLabel.text = @"管理员";
    }
    cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    id<UDFamilyMemberDelegate> member = self.family.members[indexPath.row];
    if ([member.memberInfo.userId isEqualToString:self.family.owner.userId]) {
        return;
    }
    else {
        NSString *msg = [NSString stringWithFormat:@"选择%@为管理员,你将失去管理员权限", member.memberInfo.name];
        UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"管理员变更" message:msg preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定"
                                                           style:UIAlertActionStyleDefault
                                                         handler:^(UIAlertAction *action) {
                                                           [self changeAdministratorByMember:member];
                                                         }];
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil];
        [alertController addAction:okAction];
        [alertController addAction:cancelAction];

        [self presentViewController:alertController
                           animated:YES
                         completion:^{

                         }];
    }
}

#pragma mark - changeAdministrator

- (void)changeAdministratorByMember:(id<UDFamilyMemberDelegate>)member
{
    [self.family changeFamilyAdminUserId:member.memberInfo.userId
        success:^(UserDomainSampleResult *_Nonnull result) {
          NSLog(@"更换家庭管理员成功");
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          NSLog(@"更换家庭管理员失败");
        }];
}
@end
