//
//  FamilyDevicesViewController.m
//  Debugger
//
//  Created by 闫达 on 2020/8/26.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FamilyDevicesViewController.h"
#import "UDDeviceDelegate.h"
#import "UDFamilyDelegate.h"
#import "UPMacros.h"
#import "CommonEditCell.h"
#import "DeviceActionView.h"
#import <SVProgressHUD.h>
#import "UDRoomDelegate.h"
#import "UpUserDomainHolder.h"
#import "UpUserDomain.h"
#import "UDUserDelegate.h"
#import "UDFamilyInfoDelegate.h"
#import "FamilyPickerView.h"
#import "RoomPickerView.h"
#import "UpUserDomainHolder.h"
#import "UpUserDomain.h"
#import "UDUserDelegate.h"
#import "UDDeviceInfoDelegate.h"
static const CGFloat kBottomH = 50.0;
static const CGFloat BJH = 260.0;
NSString *const kReName = @"重命名";
NSString *const kReNameAndCheck = @"改名+校验";
NSString *const kTransLateFamily = @"转移家庭";
NSString *const kMoveLocation = @"移动位置";
NSString *const kRemoveFromFamily = @"移出家庭";
NSString *const kDelete = @"删除";

@interface FamilyDevicesViewController () <UITableViewDelegate, UITableViewDataSource, DeviceActionViewDelegate>
@property (nonatomic, assign) BOOL isEdit;
@property (nonatomic, strong) UIView *bottomActionView;
@property (nonatomic, strong) DeviceActionView *deviceActionView;
@property (nonatomic, copy) NSArray<NSString *> *actions;
@property (nonatomic, copy) NSMutableArray<NSIndexPath *> *selectIndexs; //多选选中的行
@property (nonatomic, strong) id<UDFamilyDelegate> selectedFamily;
@property (nonatomic, strong) UITableView *myTableView;
@property (nonatomic, strong) FamilyPickerView *pickView;
@property (nonatomic, strong) RoomPickerView *roomPicker;

@end
static NSString *cellID = @"FamilyDevicesViewControllerCell";
@implementation FamilyDevicesViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    _isEdit = NO;
    if (self.devices.count > 0) {
        self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithTitle:@"编辑" style:UIBarButtonItemStylePlain target:self action:@selector(editDevice)];
    }
    CGFloat safeBottom = 0;
    if (@available(iOS 11.0, *)) {
        safeBottom = self.view.safeAreaInsets.bottom;
    }
    _selectIndexs = [[NSMutableArray alloc] init];
    self.actions = @[ kReName, kReNameAndCheck, kTransLateFamily, kMoveLocation, kRemoveFromFamily, kDelete ];
    self.deviceActionView = [[DeviceActionView alloc] initWithFrame:CGRectMake(0, SCREEN_HEIGHT - kBottomH - safeBottom, SCREEN_WIDTH, kBottomH + safeBottom) orders:self.actions];
    self.deviceActionView.delegate = self;
    [self.view addSubview:self.myTableView];
}

- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    [self.selectIndexs removeAllObjects];
    [self.deviceActionView removeFromSuperview];
    [self.pickView removeFromSuperview];
    [self.roomPicker removeFromSuperview];
}

#pragma mark - lazyLoad
- (UIView *)bottomActionView
{
    if (!_bottomActionView) {
        _bottomActionView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, kBottomH)];
        _bottomActionView.backgroundColor = UIColor.whiteColor;
    }
    return _bottomActionView;
}

- (UITableView *)myTableView
{
    if (!_myTableView) {
        _myTableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)];
        _myTableView.backgroundColor = [UIColor whiteColor];
        _myTableView.delegate = self;
        _myTableView.dataSource = self;
        [_myTableView registerClass:[UITableViewCell class] forCellReuseIdentifier:cellID];
        _myTableView.tableFooterView = [[UIView alloc] init];
    }
    return _myTableView;
}

#pragma mark - tableView method

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.devices.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellID];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cellID];
    }
    id<UDDeviceDelegate> device = self.devices[indexPath.row];
    //    cell.imageView.image = [UIImage imageWithData:[NSData dataWithContentsOfURL:[NSURL URLWithString:device.imageAddr1]]];
    cell.textLabel.text = device.deviceName;
    cell.accessoryType = UITableViewCellAccessoryNone;
    for (NSIndexPath *index in _selectIndexs) {
        if (index == indexPath && _isEdit == YES) {
            cell.accessoryType = UITableViewCellAccessoryCheckmark;
            break;
        }
    }
    cell.userInteractionEnabled = self.isEdit;
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView cellForRowAtIndexPath:indexPath];
    if (cell.accessoryType == UITableViewCellAccessoryNone) {
        cell.accessoryType = UITableViewCellAccessoryCheckmark;
        [_selectIndexs addObject:indexPath];
    }
    else {
        cell.accessoryType = UITableViewCellAccessoryNone;
        [_selectIndexs removeObject:indexPath];
    }
}

#pragma mark - eventMethod
- (void)editDevice
{
    self.isEdit = !self.isEdit;
    self.navigationItem.rightBarButtonItem.title = self.isEdit ? @"取消" : @"编辑";
    if (self.isEdit) {
        [self.deviceActionView show];
    }
    else {
        [self.selectIndexs removeAllObjects];
        [self.deviceActionView dismiss];
        [self.pickView removeFromSuperview];
        [self.roomPicker removeFromSuperview];
    }
    [self.myTableView reloadData];
}


#pragma mark - DeviceActionViewDelegate

- (void)didselectedBtn:(UIButton *)btn
{
    if ([btn.currentTitle isEqualToString:kReName]) {
        if (self.selectIndexs.count > 1) {
            [SVProgressHUD showErrorWithStatus:@"不能同时修改多个设备,请确保只有1个设备选中"];
        }
        if (self.selectIndexs.count == 0) {
            [SVProgressHUD showErrorWithStatus:@"请先选中需要修改的设备"];
        }
        else {
            if ([self checkPermissons]) {
                [self updateDeviceNameAndCheck:NO];
            }
            else {
                [SVProgressHUD showErrorWithStatus:@"该设备不是你的设备,没有权限修改"];
            }
        }
    }
    else if ([btn.currentTitle isEqualToString:kReNameAndCheck]) {
        if (self.selectIndexs.count > 1) {
            [SVProgressHUD showErrorWithStatus:@"不能同时修改多个设备,请确保只有1个设备选中"];
        }
        if (self.selectIndexs.count == 0) {
            [SVProgressHUD showErrorWithStatus:@"请先选中需要修改的设备"];
        }
        else {
            if ([self checkPermissons]) {
                [self updateDeviceNameAndCheck:YES];
            }
            else {
                [SVProgressHUD showErrorWithStatus:@"该设备不是你的设备,没有权限修改"];
            }
        }
    }
    else if ([btn.currentTitle isEqualToString:kTransLateFamily]) {
        if (self.selectIndexs.count == 0) {
            [SVProgressHUD showErrorWithStatus:@"请先选中需要转移的设备"];
        }
        else {
            if ([self checkPermissons]) {
                [self transformDeviceToOtherFamily];
            }
            else {
                [SVProgressHUD showErrorWithStatus:@"该设备不是你的设备,没有权限修改"];
            }
        }
    }
    else if ([btn.currentTitle isEqualToString:kMoveLocation]) {

        //        if (self.family.rooms.count <= 1) {
        //            [SVProgressHUD showErrorWithStatus:@"当前家庭只有一个房间无法移动"];
        //        }
        if (self.selectIndexs.count == 0) {
            [SVProgressHUD showErrorWithStatus:@"请先选中需要移动的设备"];
        }
        else {
            if ([self checkPermissons]) {
                [self moveToRoom];
            }
            else {
                [SVProgressHUD showErrorWithStatus:@"该设备不是你的设备,没有权限修改"];
            }
        }
    }
    else if ([btn.currentTitle isEqualToString:kRemoveFromFamily]) {
        if (self.selectIndexs.count == 0) {
            [SVProgressHUD showErrorWithStatus:@"请先选中需要移除的设备"];
        }
        else {
            if ([self checkPermissons]) {
                [self removeFromFamily];
            }
            else {
                [SVProgressHUD showErrorWithStatus:@"该设备不是你的设备,没有权限修改"];
            }
        }
    }
    else if ([btn.currentTitle isEqualToString:kDelete]) {
        if (self.selectIndexs.count == 0) {
            [SVProgressHUD showErrorWithStatus:@"请先选中需要删除的设备"];
        }
        else {
            [self deleteDevice];
        }
    }
}

- (BOOL)checkPermissons
{
    BOOL permission = YES;
    for (NSIndexPath *indexPath in self.selectIndexs) {
        if (self.devices[indexPath.row].isOwned == NO) {
            permission = NO;
        }
    }
    return permission && self.isOwner;
}

- (void)updateDeviceNameAndCheck:(BOOL)needCheck
{
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"修改设备名称" message:nil preferredStyle:UIAlertControllerStyleAlert];
    [alertController addTextFieldWithConfigurationHandler:^(UITextField *textField) {
      textField.placeholder = @"请输入要修改的设备名称";
    }];
    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定"
                                                       style:UIAlertActionStyleDefault
                                                     handler:^(UIAlertAction *action) {
                                                       UITextField *deviceTextField = alertController.textFields.firstObject;
                                                       if (needCheck) {
                                                           [self userDomainUpdateDeviceName:deviceTextField.text checkLevel:YES];
                                                           return;
                                                       }
                                                       [self userDomainUpdateDeviceName:deviceTextField.text];
                                                     }];
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil];
    [alertController addAction:okAction];
    [alertController addAction:cancelAction];

    [self presentViewController:alertController
                       animated:YES
                     completion:^{

                     }];
}

- (void)transformDeviceToOtherFamily
{
    self.pickView = [[FamilyPickerView alloc] initWithFrame:CGRectMake(0, SCREEN_HEIGHT - BJH, SCREEN_WIDTH, BJH) dataSource:[[UpUserDomainHolder instance].userDomain.user getFamilyList]];
    __weak typeof(self) weakSelf = self;
    [self.pickView showFamilyView:^(id<UDFamilyDelegate> _Nonnull family) {
      weakSelf.selectedFamily = family;
      NSMutableArray *transformedDevices = [NSMutableArray array];
      for (NSIndexPath *indexPath in weakSelf.selectIndexs) {
          [transformedDevices addObject:weakSelf.devices[indexPath.row]];
      }
      [weakSelf.family moveDevicesToOtherFamily:weakSelf.selectedFamily.familyId
          devices:transformedDevices.copy
          success:^(UserDomainSampleResult *_Nonnull result) {
            [SVProgressHUD showSuccessWithStatus:@"设备转移成功"];
            dispatch_async(dispatch_get_main_queue(), ^{
              //TODO: 设备监听onreceive
              [weakSelf.navigationController popToRootViewControllerAnimated:YES];
            });
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            [SVProgressHUD showSuccessWithStatus:@"设备转移失败"];
          }];
    }];
}

- (void)moveToRoom
{
    //    self.roomPicker = [[RoomPickerView alloc] initWithFrame:CGRectMake(0, SCREEN_HEIGHT - BJH, SCREEN_WIDTH, BJH) dataSource:self.family.rooms];
    //    __weak typeof(self) weakSelf = self;
    //    [self.roomPicker showRoomView:^(id<UDRoomDelegate> _Nonnull room) {
    //      NSMutableArray *movedDevices = [NSMutableArray array];
    //      for (NSIndexPath *indexPath in weakSelf.selectIndexs) {
    //          [movedDevices addObject:weakSelf.devices[indexPath.row]];
    //      }
    //      [weakSelf.family moveDevicesToOtherRoom:room
    //          devices:movedDevices
    //          success:^(UserDomainSampleResult *_Nonnull result) {
    //            [SVProgressHUD showSuccessWithStatus:@"设备移动成功"];
    //            dispatch_async(dispatch_get_main_queue(), ^{
    //              //TODO: 设备监听onreceive
    //              [weakSelf.navigationController popToRootViewControllerAnimated:YES];
    //            });
    //          }
    //          failure:^(UserDomainSampleResult *_Nonnull result) {
    //            [SVProgressHUD showSuccessWithStatus:@"设备移动失败"];
    //          }];
    //    }];
}

- (void)removeFromFamily
{
    NSMutableArray *deleteDevices = [NSMutableArray array];
    for (NSIndexPath *indexPath in self.selectIndexs) {
        [deleteDevices addObject:self.devices[indexPath.row]];
    }
    __weak typeof(self) weakSelf = self;
    [self.family removeDevices:deleteDevices
        success:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showSuccessWithStatus:@"设备移除成功"];
          dispatch_async(dispatch_get_main_queue(), ^{
            //TODO: 设备监听onreceive
            [weakSelf.navigationController popToRootViewControllerAnimated:YES];
          });
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showSuccessWithStatus:@"设备移除失败"];
        }];
}

- (void)deleteDevice
{
    NSMutableArray *unbindDevices = [NSMutableArray array];
    for (NSIndexPath *indexPath in self.selectIndexs) {
        [unbindDevices addObject:self.devices[indexPath.row]];
    }
    __weak typeof(self) weakSelf = self;
    [self.family unBindDevices:unbindDevices
        success:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showSuccessWithStatus:@"设备删除成功"];
          dispatch_async(dispatch_get_main_queue(), ^{
            //TODO: 设备监听onreceive
            [weakSelf.navigationController popToRootViewControllerAnimated:YES];
          });
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showSuccessWithStatus:@"设备删除失败"];
        }];
}

- (void)userDomainUpdateDeviceName:(NSString *)deviceName
{
    NSIndexPath *selectedIndexPath = self.selectIndexs.firstObject;
    __block id<UDDeviceDelegate> device = self.devices[selectedIndexPath.row];
    [device updateDeviceName:deviceName
        success:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showSuccessWithStatus:@"修改设备名称成功"];
          dispatch_async(dispatch_get_main_queue(), ^{
            [self.navigationController popViewControllerAnimated:YES];
          });
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showErrorWithStatus:@"修改设备名称失败"];
        }];
}

- (void)userDomainUpdateDeviceName:(NSString *)deviceName checkLevel:(BOOL)checkLevel
{
    NSIndexPath *selectedIndexPath = self.selectIndexs.firstObject;
    __block id<UDDeviceDelegate> device = self.devices[selectedIndexPath.row];
    [device updateDeviceName:deviceName
        checkLevel:checkLevel
        type:@"2"
        success:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showSuccessWithStatus:@"修改设备名称成功"];
          dispatch_async(dispatch_get_main_queue(), ^{
            [self.navigationController popViewControllerAnimated:YES];
          });
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          NSString *errMsg = [NSString stringWithFormat:@"【%@】%@", result.retCode, result.retInfo];
          dispatch_async(dispatch_get_main_queue(), ^{
            UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"修改设备名称失败，确定要修改成此名称吗？" message:errMsg preferredStyle:UIAlertControllerStyleAlert];
            UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定修改"
                                                               style:UIAlertActionStyleDefault
                                                             handler:^(UIAlertAction *action) {
                                                               UITextField *deviceTextField = alertController.textFields.firstObject;
                                                               [self userDomainUpdateDeviceName:deviceName checkLevel:NO];
                                                             }];
            UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil];
            [alertController addAction:okAction];
            [alertController addAction:cancelAction];

            [self presentViewController:alertController
                               animated:YES
                             completion:^{

                             }];
          });
        }];
    ;
}

@end
