//
//  MyFamilyController.m
//  Debugger
//
//  Created by 闫达 on 2020/8/26.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "MyFamilyController.h"
#import "FamilyMemberViewController.h"
#import "UDFamilyMemberDelegate.h"
#import "UDMemberInfoDelegate.h"
#import "UPMacros.h"
#import "UDFamilyDelegate.h"
#import <SVProgressHUD.h>
@interface MyFamilyController ()
@property (nonatomic, strong) NSArray<NSArray<NSString *> *> *dataSourceArr;
@property (nonatomic, strong) NSArray<NSString *> *sectionTitles;
@end

@implementation MyFamilyController
- (void)viewDidLoad
{
    [super viewDidLoad];
    self.tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStyleGrouped];
    self.dataSourceArr = @[ @[ @"智家账号邀请（手机号）", @"家庭二维码邀请", @"添加虚拟账号家庭成员", @"扫一扫加入其它家庭" ],
                            @[] ];
    NSString *sectionHeaderStr = [NSString stringWithFormat:@"邀请家人 （当前家庭是%@）", self.currentFamilyName];
    self.sectionTitles = @[ sectionHeaderStr, @"成员" ];
}

#pragma mark - Table view data source

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return _sectionTitles.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    switch (section) {
        case 0:
            return self.dataSourceArr[section].count;
            break;

        default:
            return self.members.count;
            ;
            break;
    }
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    static NSString *cellID = @"MyFamilyControllerCell";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellID];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:cellID];
    }
    if (indexPath.section == 1) {
        id<UDFamilyMemberDelegate> member = self.members[indexPath.row];
        cell.textLabel.text = member.memberInfo.name;
        //        cell.imageView.image = [UIImage imageWithData:[NSData dataWithContentsOfURL:[NSURL URLWithString:member.memberInfo.avatarUrl]]];
        if ([member.memberInfo.userId isEqualToString:self.ownerID]) {
            cell.detailTextLabel.text = @"管理员";
        }
    }
    else {
        cell.textLabel.text = self.dataSourceArr[indexPath.section][indexPath.row];
    }
    cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 44 * SCREEN_SCALE_375;
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section
{
    return self.sectionTitles[section];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (indexPath.section == 0) {

        //TODO: 第一组的功能
        switch (indexPath.row) {
            case 0:
                [self inviteMember];
                break;

            default:
                break;
        }
    }
    else if (indexPath.section == 1) {
        id<UDFamilyMemberDelegate> member = self.members[indexPath.row];
        FamilyMemberViewController *memeberVC = [[FamilyMemberViewController alloc] init];
        memeberVC.navigationItem.title = @"家庭成员";
        memeberVC.member = member;
        memeberVC.family = self.family;
        [self.navigationController pushViewController:memeberVC animated:YES];
    }
}

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath
{
    return self.isOwner;
}

- (UITableViewCellEditingStyle)tableView:(UITableView *)tableView editingStyleForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return UITableViewCellEditingStyleDelete;
}

- (NSString *)tableView:(UITableView *)tableView titleForDeleteConfirmationButtonForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return @"删除";
}

- (void)tableView:(UITableView *)tableView commitEditingStyle:(UITableViewCellEditingStyle)editingStyle forRowAtIndexPath:(NSIndexPath *)indexPath
{
    [self deleteMemberAtIndexPath:indexPath];
}


#pragma mark - upuserDomainMethod
- (void)inviteMember
{
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"手机号邀请" message:nil preferredStyle:UIAlertControllerStyleAlert];
    [alertController addTextFieldWithConfigurationHandler:^(UITextField *textField) {
      textField.placeholder = @"请输入被邀请人的手机号";
      textField.keyboardType = UIKeyboardTypePhonePad;
    }];
    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定"
                                                       style:UIAlertActionStyleDefault
                                                     handler:^(UIAlertAction *action) {
                                                       UITextField *phoneText = alertController.textFields.firstObject;
                                                       [self realInviteMemberByPhone:phoneText.text];
                                                     }];
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil];
    [alertController addAction:okAction];
    [alertController addAction:cancelAction];

    [self presentViewController:alertController
                       animated:YES
                     completion:^{

                     }];
}

- (void)realInviteMemberByPhone:(NSString *)phone
{
    [self.family adminInvitateMember:phone
        success:^(UserDomainSampleResult *_Nonnull result) {

        }
        failure:^(UserDomainSampleResult *_Nonnull result){

        }];
}

- (void)deleteMemberAtIndexPath:(NSIndexPath *)indexPath
{
    id<UDFamilyMemberDelegate> member = self.members[indexPath.row];
    [self.family adminDeleteMember:member.memberInfo.userId
        success:^(UserDomainSampleResult *_Nonnull result) {
          dispatch_async(dispatch_get_main_queue(), ^{
            [SVProgressHUD showSuccessWithStatus:@"管理员删除家庭成员成功"];
            [self.tableView reloadRowsAtIndexPaths:@[ indexPath ] withRowAnimation:UITableViewRowAnimationAutomatic];
            [self.navigationController popToRootViewControllerAnimated:YES];
          });
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showSuccessWithStatus:@"管理员删除家庭成员失败"];
        }];
}
@end
