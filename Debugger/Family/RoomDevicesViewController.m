//
//  RoomDevicesViewController.m
//  Debugger
//
//  Created by 闫达 on 2020/9/1.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "RoomDevicesViewController.h"
#import "UDDeviceDelegate.h"
#import "UDFamilyDelegate.h"
#import "UDRoomDelegate.h"
#import "UPMacros.h"
#import <SVProgressHUD.h>

@interface RoomDevicesViewController () <UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong) UITableView *myTableView;
@property (nonatomic, strong) NSArray<NSString *> *sectionTitles;
@property (nonatomic, strong) NSArray<NSString *> *defaultRooms;
@end

@implementation RoomDevicesViewController
static NSString *cellID = @"RoomDevicesViewControllerCell";
- (void)viewDidLoad
{
    [super viewDidLoad];
    [self.view addSubview:self.myTableView];
    self.sectionTitles = @[ @"房间名称", @"房间设备" ];
    self.defaultRooms = @[ @"客厅", @"卧室", @"厨房", @"洗漱间" ];
}

#pragma mark - lazyLoad
- (UITableView *)myTableView
{
    if (!_myTableView) {
        _myTableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT) style:UITableViewStyleGrouped];
        _myTableView.backgroundColor = [UIColor whiteColor];
        _myTableView.delegate = self;
        _myTableView.dataSource = self;
        _myTableView.tableFooterView = [[UIView alloc] init];
    }
    return _myTableView;
}

#pragma mark - Table view data source

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 2;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    if (section == 0) {
        return 1;
    }
    return self.devices.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellID];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:cellID];
    }
    if (indexPath.section == 0) {
        cell.textLabel.text = @"房间名称";
        cell.detailTextLabel.text = self.room.roomName;
    }
    else {
        id<UDDeviceDelegate> device = self.devices[indexPath.row];
        cell.textLabel.text = device.deviceName;
    }

    return cell;
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section
{
    return self.sectionTitles[section];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (indexPath.section == 0 && ![self.defaultRooms containsObject:self.room.roomName] && self.isOwner == YES) {
        UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"修改房间名称" message:nil preferredStyle:UIAlertControllerStyleAlert];
        [alertController addTextFieldWithConfigurationHandler:^(UITextField *textField) {
          textField.placeholder = @"请输入新的房间名称";
        }];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定"
                                                           style:UIAlertActionStyleDefault
                                                         handler:^(UIAlertAction *action) {
                                                           UITextField *roomTextField = alertController.textFields.firstObject;
                                                           [self updateRoomName:roomTextField.text];
                                                         }];
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil];
        [alertController addAction:okAction];
        [alertController addAction:cancelAction];

        [self presentViewController:alertController
                           animated:YES
                         completion:^{

                         }];
    }
}

#pragma mark - userDomainMethod
- (void)updateRoomName:(NSString *)name;
{
    __weak typeof(self) weakSelf = self;
    [self.family updateRoomName:name
        roomId:self.room.roomId
        success:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showSuccessWithStatus:@"修改房间名称成功"];
          dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf.navigationController popToRootViewControllerAnimated:YES];
          });
        }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          [SVProgressHUD showSuccessWithStatus:@"修改房间名称失败"];
        }];
}
@end
