//
//  DeviceActionView.h
//  Debugger
//
//  Created by 闫达 on 2020/8/28.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol DeviceActionViewDelegate <NSObject>
@optional
- (void)didselectedBtn:(UIButton *)btn;

@end

@interface DeviceActionView : UIView
/** 设备代理 */
@property (nonatomic, weak) id<DeviceActionViewDelegate> delegate;

- (instancetype)initWithFrame:(CGRect)frame orders:(NSArray<NSString *> *)orders;
- (void)show;
- (void)dismiss;
@end

NS_ASSUME_NONNULL_END
