//
//  FamilyListViewController.m
//  Debugger
//
//  Created by 闫达 on 2020/8/25.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//
#import "FamilyListViewController.h"
#import "UPMacros.h"
#import "UpUserDomainHolder.h"
#import "UpUserDomain.h"
#import "Family.h"
#import "User.h"
#import "UDFamilyDelegate.h"
#import <upnetwork/UPNetwork.h>
#import <uplog/UPLog.h>
#import <uplog/UPLogger.h>
#import "UPUserLoginApi.h"
#import "UDUserInfoDelegate.h"
#import "UDFamilyInfoDelegate.h"
#import "UDMemberInfoDelegate.h"
#import "FamilyDetailViewController.h"
#import "UpUserDomainObserver.h"
#import "CreatFamilyViewController.h"
#define bottomBtnH 44
@interface FamilyListViewController () <UITableViewDelegate, UITableViewDataSource, UpUserDomainObserver>
@property (nonatomic, strong) NSMutableArray<NSMutableArray<id<UDFamilyDelegate>> *> *familyDatasource;
@property (nonatomic, strong) NSMutableArray *familyList;
@property (nonatomic, strong) UIButton *creatFamilyBtn;
@property (nonatomic, strong) UITableView *myTableView;
@property (nonatomic, copy) NSArray *sectionTitles;
@end

@implementation FamilyListViewController
static NSString *cellID = @"FamilyListViewCell";
#pragma mark - lazyLoad
- (UITableView *)myTableView
{
    if (!_myTableView) {
        _myTableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT - bottomBtnH)];
        _myTableView.backgroundColor = [UIColor whiteColor];
        _myTableView.delegate = self;
        _myTableView.dataSource = self;
        [_myTableView registerClass:[UITableViewCell class] forCellReuseIdentifier:cellID];
        _myTableView.tableFooterView = [[UIView alloc] init];
    }
    return _myTableView;
}

- (UIButton *)creatFamilyBtn
{
    if (!_creatFamilyBtn) {
        _creatFamilyBtn = [[UIButton alloc] initWithFrame:CGRectMake(0, SCREEN_HEIGHT - bottomBtnH, SCREEN_WIDTH, bottomBtnH)];
        [_creatFamilyBtn setTitle:@"新建家庭" forState:UIControlStateNormal];
        [_creatFamilyBtn addTarget:self action:@selector(creatFamily) forControlEvents:UIControlEventTouchUpInside];
        _creatFamilyBtn.backgroundColor = [UIColor blueColor];
    }
    return _creatFamilyBtn;
}

- (NSMutableArray *)familyDatasource
{
    if (!_familyDatasource) {
        _familyDatasource = [NSMutableArray array];
    }
    return _familyDatasource;
}
#pragma mark - life
- (void)viewDidLoad
{
    [super viewDidLoad];
    self.navigationItem.title = @"家庭列表";
    [[UpUserDomainHolder instance].userDomain addObserver:self];
    [self.view addSubview:_creatFamilyBtn];
    [self.view addSubview:self.myTableView];
    [self.view addSubview:self.creatFamilyBtn];
    [self setHeaderView];
    self.sectionTitles = @[ @"我创建的家庭", @"我加入的家庭" ];
    if ([[UpUserDomainHolder instance].userDomain.user getFamilyList].count) {
        [self checkFamily];
    }
    else {
        [self getFamilyList];
    }
}

- (void)setHeaderView
{
    UILabel *tipLbl = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 30)];
    tipLbl.text = @"侧滑设置默认家庭";
    tipLbl.textColor = UIColor.lightGrayColor;
    tipLbl.textAlignment = UITextAlignmentRight;
    tipLbl.font = [UIFont systemFontOfSize:13];
    self.myTableView.tableHeaderView = tipLbl;
}

- (void)getFamilyList
{
    [[UpUserDomainHolder instance]
            .userDomain.user refreshFamilyList:^(UserDomainSampleResult *_Nonnull result) {
      NSLog(@"刷新家庭列表成功");
    }
        failure:^(UserDomainSampleResult *_Nonnull result) {
          NSLog(@"刷新家庭列表失败");
        }];
}

#pragma mark - Table view data source

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return self.familyDatasource.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.familyDatasource[section].count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellID forIndexPath:indexPath];
    UILabel *defaultLabel = [[UILabel alloc] init];
    defaultLabel.text = @"默认家庭";
    defaultLabel.textColor = [UIColor lightGrayColor];
    [defaultLabel sizeToFit];
    id<UDFamilyDelegate> family = self.familyDatasource[indexPath.section][indexPath.row];
    cell.textLabel.text = [NSString stringWithFormat:@"%@(%ld人,%ld个设备)", family.info.familyName, family.members.count, [family getDeviceList].count];
    cell.detailTextLabel.text = family.info.familyPosition;
    if (family.defaultFamily == YES) {
        cell.accessoryView = defaultLabel;
    }
    else {
        cell.accessoryView = nil;
    }
    cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    return cell;
}


- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    id<UDFamilyDelegate> family = self.familyDatasource[indexPath.section][indexPath.row];
    FamilyDetailViewController *detailVC = [[FamilyDetailViewController alloc] init];
    //家庭成员只有一个人 不能退出
    if (indexPath.section == 0) {
        detailVC.isOwner = YES;
    }
    else {
        detailVC.isOwner = NO;
    }
    detailVC.family = family;
    [self.navigationController pushViewController:detailVC animated:YES];
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section
{
    return _sectionTitles[section];
}

- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath
{
    return YES;
}

- (UITableViewCellEditingStyle)tableView:(UITableView *)tableView editingStyleForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return UITableViewCellEditingStyleDelete;
}

- (NSString *)tableView:(UITableView *)tableView titleForDeleteConfirmationButtonForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return @"设置默认家庭";
}

- (void)tableView:(UITableView *)tableView commitEditingStyle:(UITableViewCellEditingStyle)editingStyle forRowAtIndexPath:(NSIndexPath *)indexPath
{
    [self setDefaultFamilyAthIndexpath:indexPath];
}

#pragma mark - upuserdomainObsever

- (void)onRefreshFamilyDetailSuccess:(id<UDUserDelegate>)user
{
    [self checkFamily];
}
- (void)checkFamily
{
    [self.familyDatasource removeAllObjects];
    self.familyList = [[UpUserDomainHolder instance].userDomain.user getFamilyList].copy;
    NSMutableArray<id<UDFamilyDelegate>> *createFamilyList = [NSMutableArray array];
    NSMutableArray<id<UDFamilyDelegate>> *joinedFamilyList = [NSMutableArray array];
    for (id<UDFamilyDelegate> family in self.familyList) {
        if ([family.owner.userId isEqualToString:[UpUserDomainHolder instance].userDomain.user.uHomeUserId] || family.owner.userId.length == 0) {
            [createFamilyList addObject:family];
        }
        else {
            [joinedFamilyList addObject:family];
        }
    }
    [self.familyDatasource addObject:createFamilyList];
    [self.familyDatasource addObject:joinedFamilyList];
    [self.myTableView reloadData];
}

#pragma mark - eventMethod
- (void)creatFamily
{
    CreatFamilyViewController *creatFamilyVC = [[CreatFamilyViewController alloc] init];
    creatFamilyVC.navigationItem.title = @"新建家庭";
    [self.navigationController pushViewController:creatFamilyVC animated:YES];
}

- (void)setDefaultFamilyAthIndexpath:(NSIndexPath *)indexPath
{
    id<UDFamilyDelegate> family = self.familyDatasource[indexPath.section][indexPath.row];
    [[UpUserDomainHolder instance].userDomain.user setCurrentFamily:family];
    [self checkFamily];
}
@end
