//
//  DeviceActionView.m
//  Debugger
//
//  Created by 闫达 on 2020/8/28.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DeviceActionView.h"
#import "UPMacros.h"
@interface DeviceActionView ()
@property (nonatomic, copy) NSArray *orders;
@end

@implementation DeviceActionView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame orders:(NSArray<NSString *> *)orders
{
    if (self = [super initWithFrame:frame]) {
        _orders = orders;
        float width = (frame.size.width - orders.count - 1) / orders.count;
        float height = frame.size.height;
        for (int i = 0; i < self.orders.count; i++) {
            UIButton *btn = [[UIButton alloc] init];
            [btn setTitle:self.orders[i] forState:UIControlStateNormal];
            btn.frame = CGRectMake(i * frame.size.width / orders.count, 0, width, height);
            btn.backgroundColor = UIColor.orangeColor;
            btn.titleLabel.font = [UIFont systemFontOfSize:14];
            [btn addTarget:self action:@selector(deciceAction:) forControlEvents:UIControlEventTouchUpInside];
            [self addSubview:btn];
        }
    }
    return self;
}

- (void)deciceAction:(UIButton *)sender
{
    if ([self.delegate respondsToSelector:@selector(didselectedBtn:)]) {
        [self.delegate didselectedBtn:sender];
    }
}

- (void)show
{
    [[[UIApplication sharedApplication] keyWindow] addSubview:self];
    [UIView animateWithDuration:0.3
                     animations:^{
                       self.frame = CGRectMake(0, SCREEN_HEIGHT - self.frame.size.height, self.frame.size.width, self.frame.size.height);
                       self.alpha = 1;
                     }];
}


- (void)dismiss
{
    [UIView animateWithDuration:0.3
        animations:^{
          self.frame = CGRectMake(0, SCREEN_HEIGHT, self.frame.size.width, self.frame.size.height);
          self.alpha = 0.1;
        }
        completion:^(BOOL finished) {
          [self removeFromSuperview];
        }];
}
@end
