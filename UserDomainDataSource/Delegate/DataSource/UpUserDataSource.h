//
//  UpUserDataSource.h
//  AFNetworking
//
//  Created by 振兴郑 on 2019/2/28.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "UserDomainSampleResult.h"
NS_ASSUME_NONNULL_BEGIN

/*
 * 用户数据源
 */
@protocol UpUserDataSource <NSObject>
/*
 查询用户信息

 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)queryUserInfosuccess:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 查询用户地址

 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)queryUserAddresssuccess:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 刷新用户Token

 @param refreshToken refreshToken
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)refreshToken:(NSString *)refreshToken success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 用户注销

 @param uhome_token 云平台token
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)logout:(NSString *)uhome_token success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 修改用户密码

 @param password 新密码，必填
 @param oldPassword 旧密码，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)modifyPassword:(NSString *)password oldPassword:(NSString *)oldPassword success:(userDomainCallback)success failure:(userDomainCallback)failure;
/*
 修改用户信息

 @param userInfo 用户信息，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)modifyUserInfo:(NSDictionary *)userInfo success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
创建新的收货地址

 @param address 地址信息，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)createNewAddress:(NSDictionary *)address success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 删除用户地址

 @param addressId 地址ID，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)deleteAddress:(NSString *)addressId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 编辑用户地址

 @param addressInfo 地址信息，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)editAddress:(NSDictionary *)addressInfo success:(userDomainCallback)success failure:(userDomainCallback)failure;
/*
 查询用户登录终端

 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)queryLoginTerminalSuccess:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 轮询二维码登陆状态

 @param uuid 扫码得到的uuid，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)pollqrCodeState:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure;
/*
 确认扫码
 
 @param uuid 扫码得到的uuid，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)qrConfirmScan:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure;
/*
 确认登陆
 
 @param uuid 扫码得到的uuid，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)qrConfirmLogin:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 取消登陆
 
 @param uuid 扫码得到的uuid，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)qrCancleLogin:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 分页查询用户登录日志

 @param pageNo 起始页，必填
 @param pageSize 单页数量，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)queryLoginLogs:(NSInteger)pageNo pageSize:(NSInteger)pageSize success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 查询服务工单

 @param phoneNumber 手机号码，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)queryServiceOrder:(NSString *)phoneNumber success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 更新用户头像

 @param image 头像图片，必填
 @param success 成功回调，非必填，成功回调中retData：{ "avatarUrl":"https://account.haier.com/avatar/b1120a5ef93ed15e792e557124139a12.jpg"}
 @param failure 失败回调，非必填
 @since 1.2.7
 */
- (void)updateAvatar:(UIImage *)image success:(userDomainCallback)success failure:(userDomainCallback)failure;


@optional

/// 查询uhome用户信息
/// @param success 成功回调
/// @param failure 失败回调
- (void)uHomeSearchUserInfo:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 打开/关闭聚合开关
///
/// - Parameters:
///   - familyAgg: 聚合开关模型,结构参照AggregationSwitchArg模型
///     ```
///       [{
///         "familyId": "ALL",  // 家庭id或"ALL"
///         "lightAgg": "1",    // 灯光聚合
///         "curtainAgg": "1",  // 窗帘聚合
///         "envAgg": "0",      // 环境聚合
///         "offlineAgg": "1",  // 长期离线设备聚合
///         "nonnetAgg": "0",   // 非网器设备聚合
///         "cameraAgg": "1"    // 摄像头聚合
///       },
///       ...]
///     ```
///   - success: 成功回调
///   - failure: 失败回调
- (void)modifyAggregationSwitch:(NSArray<NSDictionary *> *)familyAgg
                         source:(NSString *)source
                        success:(userDomainCallback)success
                        failure:(userDomainCallback)failure;

/**
 @param shareUuid  分享关系id，不可为空
 @param success 成功回调
 @param failure 失败回调
 */
- (void)confirmDeviceSharingRelation:(NSString *)shareUuid success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 @param shareUuids  分享关系数组id，不可为空
 @param success 成功回调
 @param failure 失败回调
 */
- (void)cancelDeviceSharingRelation:(NSArray<NSString *> *)shareUuids success:(userDomainCallback)success failure:(userDomainCallback)failure;

@end

NS_ASSUME_NONNULL_END
