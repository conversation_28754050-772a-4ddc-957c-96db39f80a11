//
//  UpDeviceDataSource.h
//  AFNetworking
//
//  Created by 振兴郑 on 2019/2/28.
//

#import <Foundation/Foundation.h>
#import "UserDomainSampleResult.h"
NS_ASSUME_NONNULL_BEGIN

/*
 * 设备列表数据源
 */
@protocol UpDeviceListDataSource <NSObject>
/*
 查询设备列表

 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)queryDeviceList:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 更新设备名称

 @param deviceId 设备Id，必填
 @param oldName 原设备名称，必填
 @param newName 新设备名称，必填
 @param deviceNetType 是否网器设备，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 3.4.4
 */
- (void)updateDeviceNameWithDeviceId:(NSString *)deviceId oldName:(NSString *)oldName newName:(NSString *)newName familyId:(NSString *)familyId deviceNetType:(NSString *)deviceNetType prodNo:(NSString *)prodNo success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 更新设备信息
 @param deviceInfo 设备信息
 @param success 成功回调
 @param failure 失败回调
 */
- (void)updateDeviceInfo:(NSDictionary<NSString *, NSString *> *)deviceInfo success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 获取组设备
/// @param familyId 家庭ID
/// @param filterFlag 组设备过滤，默认不传返回组设备 false:设备不过滤   true：设备过滤    该字段不传设备默认不过滤
/// @param success 成功回调
/// @param failure 失败回调
- (void)getGroupDeviceList:(NSString *)familyId filterFlag:(BOOL)filterFlag success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 修改设备名称并检查名称合法性
/// @param deviceId 设备Id，必填
/// @param oldName 原设备名称，必填
/// @param newName 新设备名称，必填
/// @param familyId 家庭id
/// @param type 1:设备绑定成功 2:设备编辑管理
/// @param checkLevel 是否开启二级验证 （true 开启，false 不开启）
/// @param success 成功回调，非必填
/// @param failure 失败回调，非必填
- (void)updateDeviceNameWithDeviceId:(NSString *)deviceId oldName:(NSString *)oldName newName:(NSString *)newName familyId:(NSString *)familyId type:(NSString *)type checkLevel:(BOOL)checkLevel success:(userDomainCallback)success failure:(userDomainCallback)failure;

@end

NS_ASSUME_NONNULL_END
