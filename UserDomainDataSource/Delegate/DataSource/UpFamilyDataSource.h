//
//  UpFamilyDataSource.h
//  AFNetworking
//
//  Created by 振兴郑 on 2019/2/28.
//

#import <Foundation/Foundation.h>
#import "UserDomainSampleResult.h"
NS_ASSUME_NONNULL_BEGIN
@class CreateFamilyArgs, FamilyArgs, RoomArgs, RoomNewArgs, FloorArg, VirtualMemberArgs, AdminInviteMemberArgs;

@protocol UDDeviceDelegate
, UDRoomDelegate;

/*
 * 家庭列表数据源接口
 */
@protocol UpFamilyDataSource <NSObject>

/*
 查询用户家庭列表

 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)queryFamilyListsuccess:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 查询家庭信息

 @param familyId 家庭ID，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)queryFamilyInfo:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
  查询第一个加入家庭的家庭成员
 @param success 成功回调
 @param failure 失败回调
 */
- (void)queryFirstMemeber:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
   虚拟用户加入家庭
 @param virtualUCId 虚拟用户用户中心ID
 @param userFamilyName 用户加入家庭附属参数,为用户在家庭中昵称
 @param success 成功回调
 @param failure 失败回调
 */
- (void)addVirtualMember:(NSString *)familyId memberId:(NSString *)memberId memberName:(NSString *)memberName success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
  编辑虚拟成员信息
 @param virtualMemberArgs 虚拟成员对象
 @param success 成功回调
 @param failure 失败回调
 */
- (void)modifyVirtualMember:(VirtualMemberArgs *)virtualMemberArgs success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 管理员退出家庭

 @param familyId 家庭ID，必填
 @param userId 新管理员ID，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)exitFamilyAsAdmin:(NSString *)familyId userId:(NSString *)userId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 家庭成员退出家庭

 @param familyId 家庭ID，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)exitFamilyAsMember:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 更新家庭信息

 @param familyArgs 家庭信息，必填(familyArgs)
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)updateFamilyInfo:(FamilyArgs *)familyArgs familyId:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 更换家庭管理员

 @param familyId 家庭ID，必填
 @param userId 新管理员ID，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)changeFamilyAdmin:(NSString *)familyId userId:(NSString *)userId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 管理员邀请家庭成员

 @param args 邀请参数对象，必填
 @param familyId 家庭Id，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)adminInvitateMember:(AdminInviteMemberArgs *)args familyId:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 查询家庭房间列表

 @param familyId 家庭ID，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)queryRoomListOfFamily:(NSString *)familyId floorId:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 删除家庭成员

 @param familyId 家庭ID，必填
 @param memberId 被删除的成员ID，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)deleteFamilyMemberAsAdmin:(NSString *)familyId memberId:(NSString *)memberId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 解散家庭

 @param familyId 家庭IDFamilyInfo
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)destoryFamilyAsAdmin:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 创建家庭

 @param familyArgs 家庭信息(familyArgs)，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)createFamily:(CreateFamilyArgs *)familyArgs success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 添加房间

 @param roomArgs 房间信息，必填(roomArgs)
 @param familyId 家庭ID，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)addRoomForFamily:(RoomArgs *)roomArgs familyId:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 在楼层添加房间
 
 @param roomArgs 房间信息，必填(RoomNewArgs)
 @param familyId 家庭ID，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 3.31.0
 */
- (void)addRoomForFamilyNew:(RoomNewArgs *)roomArgs familyId:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 移除房间

 @param familyId 家庭ID，必填
 @param roomId 房间ID，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)removeRoomFromFamily:(NSString *)familyId roomId:(NSString *)roomId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 从家庭批量移除设备
 
 @param familyId 家庭ID，必填
 @param devices 设备集合，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)removeDevicesFromFamily:(NSString *)familyId devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 更新房间名称

 @param familyId 家庭ID，必填
 @param roomId 房间ID，必填
 @param roomName 房间名称，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)updateFamilyRoomName:(NSString *)familyId roomId:(NSString *)roomId roomName:(NSString *)roomName floorId:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 家庭批量解除绑定设备
 @param devices
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)unBindFamilyDevices:(NSString *)familyId devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 批量移动设备到新的房间

 @param familyId 家庭ID，必填
 @param newRoom 新房间名称，必填
 @param deviceIds 设备IDs，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)moveDevicesToOtherRoom:(NSString *)familyId newRoom:(id<UDRoomDelegate>)newRoom devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 批量移动设备到新的家庭

 @param newFamilyId 新的家庭ID，必填
 @param oldFamilyId 老的家庭ID，必填
 @param deviceIds 设备IDs，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)moveDevicesToOtherFamily:(NSString *)newFamilyId oldFamilyId:(NSString *)oldFamilyId devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 回复家庭邀请

 @param inviteCode 邀请码，必填
 @param familyId 家庭ID，必填
 @param memberName 成员名称，必填
 @param agree 是否同意，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 @see userDomainCallback
 @since 1.0.0
 */
- (void)replyFamilyInviteWithCode:(NSString *)inviteCode familyId:(NSString *)familyId memberName:(NSString *)memberName agree:(BOOL)agree success:(userDomainCallback)success failure:(userDomainCallback)failure;
/*
 管理员同意/拒绝用户加入家庭
 @param applicationId 申请ID，必填
 @param agree BOOL，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)replyJoinFamily:(NSString *)applicationId agree:(BOOL)agree success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 设置默认家庭
/// @param familyId 家庭Id，必填
/// @param success 成功回调，非必填
/// @param failure 失败回调，非必填
- (void)setDefaultFamily:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 创建楼层
/// @param floorArg 楼层参数
/// @param success 成功回调
/// @param failure 失败回调
- (void)addFloorOfFamily:(NSString *)familyId floorArg:(FloorArg *)floorArg success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 编辑楼层
/// @param floorArg 楼层参数
/// @param success 成功回调
/// @param failure 失败回调
- (void)editFloorOfFamily:(NSString *)familyId floorArg:(FloorArg *)floorArg success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 删除楼层
/// @param floorId 楼层Id
/// @param success 成功回调
/// @param failure 失败回调
- (void)deleteFloorOfFamily:(NSString *)familyId floorId:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure;


/// 编辑家庭成员身份
/// @param familyId 家庭ID
/// @param memberId 成员ID
/// @param memberRole 成员身份
/// @param success 成功回调
/// @param failure 失败回调
- (void)modifyMemberRoleOfFamily:(NSString *)familyId memberId:(NSString *)memberId memberRole:(NSString *)memberRole success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 编辑家庭成员权限类型
/// @param familyId 家庭ID
/// @param memberId 成员ID
/// @param memberType 权限类型, 0-创建者,1-管理员,2-成员
/// @param success 成功回调
/// @param failure 失败回调
- (void)modifyMemberTypeOfFamily:(NSString *)familyId memberId:(NSString *)memberId memberType:(NSInteger)memberType success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 编辑家庭虚拟成员身份
/// @param familyId 家庭ID
/// @param memberId 成员ID
/// @param memberRole 成员身份
/// @param success 成功回调
/// @param failure 失败回调
- (void)modifyVirtualMemberRoleOfFamily:(NSString *)familyId memberId:(NSString *)memberId memberRole:(NSString *)memberRole success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 保存房间顺序
/// @param familyId 家庭ID
/// @param rooms 房间名称列表
/// @param floorId 房间所属楼层ID
/// @param success 成功回调
/// @param failure 失败回调
- (void)saveRoomsOrderOfFamily:(NSString *)familyId rooms:(NSArray<NSString *> *)rooms floorId:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 保存房间顺序New
/// @param familyId 家庭ID
/// @param sortList 楼层+房间名称列表
///   [
///     {"floorId": "123", "rooms": ["1", "2", ...]},
///     {"floorId": "abc", "rooms": ["a", "b", ...]},
///   ]
/// @param success 成功回调
/// @param failure 失败回调
- (void)saveRoomsOrderNewForFamily:(NSString *)familyId sortList:(NSArray<NSDictionary *> *)sortList success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 编辑设备卡片顺序和切换设备卡片大小状态。
///
/// - Parameters:
///   - familyId: 家庭id
///   - orderList: 已排序的全量设备id列表
///   - bigCardList: 大卡片设备id列表
///   - middleCardList: 中卡片设备id列表
///   - smallCardList: 小卡片设备id列表
///   - success: 成功回调
///   - failure: 失败回调
///
- (void)modifyDeviceCardList:(NSString *)familyId
                   orderList:(NSArray<NSString *> *)orderList
                 bigCardList:(NSArray<NSString *> *)bigCardList
              middleCardList:(NSArray<NSString *> *)middleCardList
               smallCardList:(NSArray<NSString *> *)smallCardList
                     success:(userDomainCallback)success
                     failure:(userDomainCallback)failure;

/// 设备聚合卡片操作
///
/// - Parameters:
///   - familyId: 家庭id
///   - aggCards: 聚合卡片信息列表,结构参照DeviceCardAggregationArgs模型
///     ```
///       [{
///         "aggType": "0", // 必填参数
///         "sortList": ["deviceId1", "deviceId2", ...],      // 必填参数
///         "bigCardList": ["deviceId1", "deviceId2", ...],   // 可选参数
///         "smallCardList": ["deviceId3", "deviceId4", ...]  // 可选参数
///       },
///       ...]
///     ```
///   - success: 成功回调
///   - failure: 失败回调
- (void)modifyDeviceAggregation:(NSString *)familyId
               aggregationCards:(NSArray<NSDictionary *> *)aggCards
                        success:(userDomainCallback)success
                        failure:(userDomainCallback)failure;

/// 设置聚合设备在首页隐藏与否
///
/// - Parameters:
///   - familyId: 家庭id
///   - devicesDisplayed: 需要由"隐藏"变更为"展示"的设备id集合，参与灯光|窗帘|环境|摄像头聚合的deviceId
///   - devicesHidden: 需要由"展示"变更为"隐藏"的设备id集合，参与灯光|窗帘|环境|摄像头聚合的deviceId
///   - success: 成功回调
///   - failure: 失败回调
- (void)setAggregationDeviceHomepageHidden:(NSString *)familyId
                          devicesDisplayed:(nullable NSArray<NSString *> *)devicesDisplayed
                             devicesHidden:(nullable NSArray<NSString *> *)devicesHidden
                                   success:(userDomainCallback)success
                                   failure:(userDomainCallback)failure;

@end

NS_ASSUME_NONNULL_END
