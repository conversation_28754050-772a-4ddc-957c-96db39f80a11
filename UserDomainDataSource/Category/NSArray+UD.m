//
//  NSArray+UD.m
//  AFNetworking
//
//  Created by <PERSON> on 2019/12/12.
//

#import "NSArray+UD.h"
#import "NSObject+UD.h"

@implementation NSArray (UD)
#pragma mark - Overrides
- (BOOL)ud_isEqualTo:(NSObject *)obj
{
    NSArray *arrObj = (NSArray *)obj;
    if (![arrObj isKindOfClass:[NSArray class]] || self.count != arrObj.count) {
        return NO;
    }
    BOOL bEqual = YES;
    for (NSObject *object in self) {
        if (![arrObj ud_containsObject:object]) {
            bEqual = NO;
            break;
        }
    }
    if (!bEqual) {
        return bEqual;
    }
    for (NSObject *object in arrObj) {
        if (![self ud_containsObject:object]) {
            bEqual = NO;
            break;
        }
    }
    return bEqual;
}

#pragma mark - Public Methods
- (BOOL)ud_containsObject:(NSObject *)obj
{
    BOOL bContainsObject = NO;
    for (NSObject *object in self) {
        if ([object ud_isEqualTo:obj]) {
            bContainsObject = YES;
            break;
        }
    }
    return bContainsObject;
}

@end
