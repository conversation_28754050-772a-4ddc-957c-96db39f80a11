//
//  NSDictionary+UD.m
//  AFNetworking
//
//  Created by <PERSON> on 2019/12/12.
//

#import "NSDictionary+UD.h"
#import "NSObject+UD.h"

@implementation NSDictionary (UD)
#pragma mark - Overrides
- (BOOL)ud_isEqualTo:(NSObject *)obj
{
    NSDictionary *dictObj = (NSDictionary *)obj;
    if (![dictObj isKindOfClass:[NSDictionary class]] || self.count != dictObj.count) {
        return NO;
    }
    BOOL bEqual = YES;
    for (NSString *key in self.allKeys) {
        NSObject *object = self[key];
        NSObject *objectInDict = dictObj[key];
        if (![object ud_isEqualTo:objectInDict]) {
            bEqual = NO;
            break;
        }
    }
    if (!bEqual) {
        return bEqual;
    }
    for (NSString *key in dictObj.allKeys) {
        NSObject *objectInDict = dictObj[key];
        NSObject *object = self[key];
        if (![objectInDict ud_isEqualTo:object]) {
            bEqual = NO;
            break;
        }
    }
    return bEqual;
}

@end
