//
//  ShopUserDataSource.m
//  UserDomainDataSource
//
//  Created by 冉东军 on 2021/12/31.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ShopUserDataSource.h"
#import "ShopRefreshTokenApi.h"
#import "DataSourceCallbackFunctions.h"
#import "ShopOauthDataTransformer.h"
#import "CommonTransformer.h"
#import "ShopLogoutApi.h"

@implementation ShopUserDataSource

- (void)refreshToken:(NSString *)refreshToken success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    ShopRefreshTokenApi *api = [[ShopRefreshTokenApi alloc] initWithrefresh_token:refreshToken];
    api.responseParser = [ShopOauthDataTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)logout:(NSString *)uhome_token success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    ShopLogoutApi *api = [[ShopLogoutApi alloc] initWithAccessToken:uhome_token];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

@end
