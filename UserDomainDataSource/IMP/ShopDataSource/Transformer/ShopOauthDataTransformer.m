//
//  ShopOauthDataTransformer.m
//  UserDomainDataSource
//
//  Created by 冉东军 on 2021/12/31.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ShopOauthDataTransformer.h"
#import "UserDomainSampleResult.h"
#import "CloudOauthData.h"
#import "ApplicationOauthData.h"
#import "MJExtension.h"
#import "ApplicationOauthData+PrivateExtension.h"

@implementation ShopOauthDataTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    if (!object || ![object isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    NSDictionary *responseObject = (NSDictionary *)object;
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:responseObject];
    if (result.success) {
        ApplicationOauthData *oauthData = [[ApplicationOauthData alloc] init];
        NSDictionary *tokenInfo = responseObject[@"data"];
        oauthData.realAccess_token = tokenInfo[@"access_token"];
        oauthData.realExpires_in = [tokenInfo[@"expires_in"] stringValue];
        oauthData.realRefresh_token = tokenInfo[@"refresh_token"];
        oauthData.realScope = tokenInfo[@"scope"];
        oauthData.realToken_type = tokenInfo[@"token_type"];
        oauthData.realUhome_user_id = [tokenInfo[@"uhome_user_id"] stringValue];
        oauthData.realUhome_access_token = tokenInfo[@"uhome_access_token"];
        oauthData.realUc_user_id = oauthData.realUhome_user_id;
        result.retData = oauthData;
    }
    else {
        result.retData = nil;
    }
    return result;
}

@end
