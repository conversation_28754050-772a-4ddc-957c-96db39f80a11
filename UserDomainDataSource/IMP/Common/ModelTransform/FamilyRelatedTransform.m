//
//  FamilyRelatedTransform.m
//  UserDomainDataSource
//
//  Created by 吴子航 on 2022/5/11.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FamilyRelatedTransform.h"
#import "MJExtension.h"
#import "CloudFamilyList.h"
#import "Family+PrivateExtension.h"
#import "FamilyInfo+PrivateExtension.h"
#import "FamilyMember+PrivateExtension.h"
#import "Room+PrivateExtension.h"
#import "FamilyLocation+PrivateExtension.h"
#import "MemberInfo+PrivateExtension.h"
#import "UDFloorInfo+PrivateExtension.h"

@implementation FamilyRelatedTransform

#pragma mark - public
+ (Family *)jsonToFamily:(NSObject *)json
{
    CloudFamily *family = [CloudFamily mj_objectWithKeyValues:json];
    Family *realFamily = [[Family alloc] init];
    realFamily.realFamilyId = family.familyId;
    realFamily.realCreateTime = family.createTime;
    realFamily.realAppId = family.appId;
    realFamily.realInfo = [self productRealInfoWithFamily:family];
    realFamily.realMembers = [self productRealMembersWithFamily:family];
    realFamily.realOwner = [self productRealOwnerWithFamily:family];
    realFamily.realOwnerId = family.familyOwner;
    realFamily.realFloorInfos = [self productFloorInfosWithFamily:family];
    realFamily.realDefaultFamily = [family.isDefault isEqualToString:@"1"];
    realFamily.realLocationChangeFlag = [family.locationChangeFlag isEqualToString:@"1"];

    return realFamily;
}

+ (NSArray<Family *> *)jsonToFamilyList:(id)json
{
    CloudFamilyList *list = [CloudFamilyList mj_objectWithKeyValues:json];
    NSMutableArray<Family *> *familyList = [NSMutableArray array];
    NSArray<CloudFamily *> *creatFamilyList = list.createfamilies;
    for (CloudFamily *family in creatFamilyList) {
        Family *realFamily = [[Family alloc] init];
        realFamily.realFamilyId = family.familyId;
        realFamily.realCreateTime = family.createTime;
        realFamily.realAppId = family.appId;
        realFamily.realInfo = [self productRealInfoWithFamily:family];
        realFamily.realMembers = [self productRealMembersWithFamily:family];
        realFamily.realOwner = nil; //创建者此项为空
        realFamily.realOwnerId = family.familyOwner;
        realFamily.realFloorInfos = [self productFloorInfosWithFamily:family];
        realFamily.realDefaultFamily = [family.isDefault isEqualToString:@"1"];
        realFamily.realLocationChangeFlag = [family.locationChangeFlag isEqualToString:@"1"];
        realFamily.realFamilyDeviceCount = family.familyDeviceCount;
        [familyList addObject:realFamily];
    }

    NSArray<CloudFamily *> *joinFamilyList = list.joinfamilies;
    for (CloudFamily *family in joinFamilyList) {
        Family *realFamily = [[Family alloc] init];
        realFamily.realFamilyId = family.familyId;
        realFamily.realCreateTime = family.createTime;
        realFamily.realAppId = family.appId;
        realFamily.realInfo = [self productRealInfoWithFamily:family];
        realFamily.realMembers = [self productRealMembersWithFamily:family];
        realFamily.realOwner = [self productRealOwnerWithFamily:family];
        realFamily.realOwnerId = family.familyOwner;
        realFamily.realFloorInfos = [self productFloorInfosWithFamily:family];
        realFamily.realDefaultFamily = [family.isDefault isEqualToString:@"1"];
        realFamily.realLocationChangeFlag = [family.locationChangeFlag isEqualToString:@"1"];
        realFamily.realFamilyDeviceCount = family.familyDeviceCount;
        [familyList addObject:realFamily];
    }

    return familyList.copy;
}

+ (NSArray<Room *> *)jsonToRoomList:(id)json
{
    NSArray *roomData = json[@"rooms"];
    NSArray<CloudRoom *> *rooms = [CloudRoom mj_objectArrayWithKeyValuesArray:roomData];
    NSMutableArray<Room *> *realRooms = [NSMutableArray array];
    for (CloudRoom *room in rooms) {
        Room *roomModel = [[Room alloc] init];
        roomModel.realRoomId = room.roomId;
        roomModel.realRoomName = room.roomName;
        roomModel.realRoomClass = room.roomClass;
        roomModel.realRoomLabel = room.roomLabel;
        roomModel.realRoomLogo = room.roomLogo;
        roomModel.realRoomPicture = room.roomPicture;
        roomModel.realSortCode = room.sortCode;
        [realRooms addObject:roomModel];
    }

    return realRooms.copy;
}

#pragma mark - private
+ (FamilyInfo *)productRealInfoWithFamily:(CloudFamily *)family
{
    FamilyLocation *familyLocationModel = [[FamilyLocation alloc] init];
    familyLocationModel.realLongitude = family.familyLocation.longitude;
    familyLocationModel.realLatitude = family.familyLocation.latitude;
    familyLocationModel.realCityCode = family.familyLocation.cityCode;
    FamilyInfo *familyInfoModel = [[FamilyInfo alloc] init];
    familyInfoModel.realFamilyName = family.familyName;
    familyInfoModel.realFamilyPosition = family.familyPosition;
    familyInfoModel.realFamilyLocation = familyLocationModel;
    return familyInfoModel;
}


+ (NSArray<FamilyMember *> *)productRealMembersWithFamily:(CloudFamily *)family
{
    NSMutableArray<FamilyMember *> *memberList = [NSMutableArray array];
    for (CloudFamilyMember *member in family.familyMembers) {
        FamilyMember *memberModel = [[FamilyMember alloc] init];
        memberModel.realFamilyId = member.familyId;
        memberModel.realJoinTime = member.joinTime;
        MemberInfo *mem = [[MemberInfo alloc] init];
        mem.realUserId = member.memberInfo.userId;
        mem.realName = member.memberInfo.name;
        mem.realMobile = member.memberInfo.mobile;
        mem.realAvatarUrl = member.memberInfo.avatarUrl;
        mem.realVirtualUserFlag = [member.memberInfo.virtualUserFlag isEqualToString:@"1"];
        mem.realUcUserId = member.memberInfo.ucUserId;
        mem.realHostUserId = member.memberInfo.hostUserId;
        mem.realBirthday = member.memberInfo.birthday;
        memberModel.realMemberInfo = mem;
        memberModel.realShareDeviceCount = member.shareDeviceCount;
        memberModel.realMemberName = member.memberName;
        memberModel.realMemberRole = member.memberRole;
        memberModel.realMemberType = member.memberType;
        [memberList addObject:memberModel];
    }
    return memberList;
}

+ (MemberInfo *)productRealOwnerWithFamily:(CloudFamily *)family
{
    MemberInfo *memberModel = [[MemberInfo alloc] init];
    memberModel.realUserId = family.familyOwnerInfo.userId;
    memberModel.realName = family.familyOwnerInfo.name;
    memberModel.realMobile = family.familyOwnerInfo.mobile;
    memberModel.realAvatarUrl = family.familyOwnerInfo.avatarUrl;
    memberModel.realVirtualUserFlag = [family.familyOwnerInfo.virtualUserFlag isEqualToString:@"1"];
    memberModel.realUcUserId = family.familyOwnerInfo.ucUserId;
    return memberModel;
}

+ (NSArray<UDFloorInfo *> *)productFloorInfosWithFamily:(CloudFamily *)family
{
    NSMutableArray<UDFloorInfo *> *floorInfos = [NSMutableArray array];
    for (CloudFloorInfo *info in family.floorInfos) {
        UDFloorInfo *floorInfo = [[UDFloorInfo alloc] init];
        floorInfo.realFloorPicture = info.floorPicture;
        floorInfo.realFloorLabel = info.floorLabel;
        floorInfo.realFloorClass = info.floorClass;
        floorInfo.realFloorName = info.floorName;
        floorInfo.realFloorId = info.floorId;
        floorInfo.realFloorLogo = info.floorLogo;
        floorInfo.realFloorCreateTime = info.floorCreateTime;
        floorInfo.realFloorOrderId = info.floorOrderId;
        floorInfo.realRooms = [self productRoomsWithFloorInfo:info];
        [floorInfos addObject:floorInfo];
    }
    return floorInfos;
}
+ (NSArray<Room *> *)productRoomsWithFloorInfo:(CloudFloorInfo *)floorInfo
{
    NSMutableArray<Room *> *roomList = [NSMutableArray array];
    if (floorInfo.rooms.count > 0) {
        for (CloudRoom *room in floorInfo.rooms) {
            Room *roomModel = [[Room alloc] init];
            roomModel.realRoomId = room.roomId;
            roomModel.realRoomName = room.roomName;
            roomModel.realRoomClass = room.roomClass;
            roomModel.realRoomLabel = room.roomLabel;
            roomModel.realRoomLogo = room.roomLogo;
            roomModel.realRoomPicture = room.roomPicture;
            roomModel.realSortCode = room.sortCode;
            [roomList addObject:roomModel];
        }
    }
    return roomList;
}

@end
