//
//  SEUserInfoConverter.m
//  userdomain_seasia
//
//  Created by <PERSON> on 2019/7/1.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEUserInfoConverter.h"
#import <MJExtension/MJExtension.h>
#import "CloudUserInfo.h"
#import "SECloudUserInfo.h"
#import "UserInfo.h"
#import "UserInfo+PrivateExtension.h"

@implementation SEUserInfoConverter
#pragma mark - Overrides
- (void)serverSuccessResultDidParse:(UserDomainSampleResult *)result
{
    if (![result.retData isKindOfClass:[NSDictionary class]]) {
        return;
    }
    NSDictionary *userInfoDict = result.retData[@"userInfo"];
    SECloudUserInfo *userInfo = [SECloudUserInfo mj_objectWithKeyValues:userInfoDict];
    result.retData = [self convertSEUserInfoToCloudUserInfo:userInfo];
}

#pragma mark - Non-Public Methods
- (UserInfo *)convertSEUserInfoToCloudUserInfo:(SECloudUserInfo *)userInfo
{
    if (![userInfo isKindOfClass:[SECloudUserInfo class]]) {
        return nil;
    }
    UserInfo *info = [[UserInfo alloc] init];
    info.realUserId = userInfo.userId;
    info.realMobile = userInfo.phone;
    info.realUsername = userInfo.username;
    info.realGivenName = userInfo.givenName;
    info.realNickname = userInfo.nickname;
    info.realAvatarUrl = userInfo.avatarUrl;
    info.realCountryCode = userInfo.countryCode;
    info.realEmail = userInfo.email;
    info.realPrivacyCountryCode = userInfo.privacyCountryCode;
    return info;
}
@end
