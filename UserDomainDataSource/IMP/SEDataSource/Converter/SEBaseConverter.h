//
//  SEBaseConverter.h
//  userdomain_seasia
//
//  Created by <PERSON> on 2019/7/8.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UserDomainSampleResult.h"
#import <upnetwork/UPNetwork.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString *const kSEServerResponseCode;
extern NSString *const kSEServerResponseInfo;
extern NSString *const kSEServerResponseData;
extern NSString *const SEServerResponseCode_Success;

@interface SEBaseConverter : NSObject <UPResponseParser>
- (UserDomainSampleResult *)createResultWithResponseObject:(NSDictionary *)responseInfo;
- (void)serverSuccessResultDidParse:(UserDomainSampleResult *)result;
@end

NS_ASSUME_NONNULL_END
