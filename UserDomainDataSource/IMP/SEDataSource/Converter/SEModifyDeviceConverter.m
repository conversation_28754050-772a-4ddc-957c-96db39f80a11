//
//  SEModifyDeviceConverter.m
//  userdomain_seasia
//
//  Created by <PERSON> on 2019/7/25.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEModifyDeviceConverter.h"
#import "UpUserDomainHolder.h"
#import "UpUserDomain.h"
#import "User.h"
@implementation SEModifyDeviceConverter
#pragma mark - Overrides
- (void)serverSuccessResultDidParse:(UserDomainSampleResult *)result
{
    [[UpUserDomainHolder instance].userDomain.user refreshDeviceList:nil failure:nil];
}

@end
