//
//  SEUserAuthInfoConverter.m
//  userdomain_seasia
//
//  Created by <PERSON> on 2019/7/8.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEUserAuthInfoConverter.h"
#import <MJExtension/MJExtension.h>
#import "CloudOauthData.h"
#import "SERequestConfig.h"
#import "SECloudUserAuthInfo.h"
#import "ApplicationOauthData.h"
#import "ApplicationOauthData+PrivateExtension.h"
@implementation SEUserAuthInfoConverter
#pragma mark - Overrides
- (void)serverSuccessResultDidParse:(UserDomainSampleResult *)result
{
    SECloudUserAuthInfo *userAuthInfo = [SECloudUserAuthInfo mj_objectWithKeyValues:result.retData];
    [UPNetworkSettings sharedSettings].uhomeAccessToken = userAuthInfo.uhomeAccessToken;
    [UPNetworkSettings sharedSettings].accessToken = userAuthInfo.accountToken;
    [SERequestConfig sharedConfig].userId = userAuthInfo.uhomeUserId;
    result.retData = [self convertSEUserAuthInfoToCloudOauthData:userAuthInfo];
}

#pragma mark - Non-Public Methods
- (ApplicationOauthData *)convertSEUserAuthInfoToCloudOauthData:(SECloudUserAuthInfo *)info
{
    if (![info isKindOfClass:[SECloudUserAuthInfo class]]) {
        return nil;
    }
    ApplicationOauthData *oauthData = [[ApplicationOauthData alloc] init];
    oauthData.realAccess_token = info.accountToken;
    oauthData.realExpires_in = info.expiresIn;
    oauthData.realRefresh_token = info.refreshToken;
    oauthData.realScope = info.scope;
    oauthData.realToken_type = info.tokenType;
    oauthData.realUhome_access_token = info.uhomeAccessToken;
    oauthData.realUhome_user_id = info.uhomeUserId;
    oauthData.realUc_user_id = info.uocUserId;
    return oauthData;
}

@end
