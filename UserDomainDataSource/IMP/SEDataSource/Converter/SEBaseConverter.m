//
//  SEBaseConverter.m
//  userdomain_seasia
//
//  Created by <PERSON> on 2019/7/8.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEBaseConverter.h"

NSString *const kSEServerResponseCode = @"retCode";
NSString *const kSEServerResponseInfo = @"retInfo";
NSString *const kSEServerResponseData = @"data";
NSString *const SEServerResponseCode_Success = @"00000";

@implementation SEBaseConverter
#pragma mark - UpRequestResponseTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    if (!object || ![object isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult defaultFailureResult];
    }
    UserDomainSampleResult *result = [self createResultWithResponseObject:(NSDictionary *)object];
    if (result.success) {
        [self serverSuccessResultDidParse:result];
    }
    return result;
}

#pragma mark - Public Methods
- (UserDomainSampleResult *)createResultWithResponseObject:(NSDictionary *)responseInfo
{
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
    result.success = [responseInfo isKindOfClass:[NSDictionary class]] && [responseInfo[kSEServerResponseCode] isEqualToString:SEServerResponseCode_Success];
    result.retCode = responseInfo[kSEServerResponseCode];
    result.retInfo = responseInfo[kSEServerResponseInfo];
    result.retData = responseInfo[kSEServerResponseData];
    return result;
}

- (void)serverSuccessResultDidParse:(UserDomainSampleResult *)result
{
}

@end
