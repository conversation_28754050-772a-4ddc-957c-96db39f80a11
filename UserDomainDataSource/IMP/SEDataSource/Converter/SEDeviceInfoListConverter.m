//
//  SEDeviceInfoListConverter.m
//  userdomain_seasia
//
//  Created by <PERSON> on 2019/7/9.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEDeviceInfoListConverter.h"
#import <MJExtension/MJExtension.h>
#import "CloudDevice.h"
#import "SECloudDeviceInfo.h"
#import "UDDeviceInfo.h"
#import "UDDeviceInfo+PrivateExtension.h"
#import "Device.h"
#import "DevicePermission.h"
#import "DevicePermission+PrivateExtension.h"
#import "DeviceAuth.h"
#import "DeviceAuth+PrivateExtension.h"

@implementation SEDeviceInfoListConverter
#pragma mark - Overrides
- (void)serverSuccessResultDidParse:(UserDomainSampleResult *)result
{
    NSDictionary *deviceInfosDict = (NSDictionary *)result.retData;
    if (![deviceInfosDict isKindOfClass:[NSDictionary class]]) {
        result.retData = @[];
        return;
    }
    NSArray *deviceInfos = deviceInfosDict[@"deviceInfos"];
    if (![deviceInfos isKindOfClass:[NSArray class]]) {
        result.retData = @[];
        return;
    }
    NSMutableArray *deviceInfoList = [NSMutableArray array];
    for (NSDictionary *dict in deviceInfos) {
        SECloudDeviceInfo *deviceInfo = [SECloudDeviceInfo mj_objectWithKeyValues:dict];
        Device *device = [self convertSEDeviceInfoToDevice:deviceInfo];
        if ([device isKindOfClass:[Device class]]) {
            [deviceInfoList addObject:device];
        }
    }
    result.retData = deviceInfoList;
}

#pragma mark - Non-Public Methods
- (Device *)convertSEDeviceInfoToDevice:(SECloudDeviceInfo *)deviceInfo
{
    if (![deviceInfo isKindOfClass:[SECloudDeviceInfo class]]) {
        return nil;
    }
    UDDeviceInfo *info = [[UDDeviceInfo alloc] init];
    SECloudDeviceBaseInfo *baseInfo = deviceInfo.baseInfo;
    if ([baseInfo isKindOfClass:[SECloudDeviceBaseInfo class]]) {
        info.realDeviceId = baseInfo.deviceId;
        info.realDeviceName = baseInfo.deviceName;
        info.realDeviceType = baseInfo.deviceType;
        info.realIsOnline = baseInfo.isOnline;
        info.realWifiType = baseInfo.wifiType;
        info.realFamilyId = baseInfo.familyId;
        info.realPermission = [self productSEDevicePermissionBy:baseInfo];
        info.realDeviceRole = baseInfo.deviceRole;
        info.realDeviceRoleType = baseInfo.deviceRoleType;
        info.realBindTime = baseInfo.bindTime;
    }
    SECloudDeviceExtendedInfo *extendedInfo = deviceInfo.extendedInfo;
    if ([extendedInfo isKindOfClass:[SECloudDeviceExtendedInfo class]]) {
        info.realBrand = extendedInfo.brand;
        info.realApptypeName = extendedInfo.appTypeName;
        info.realRoomName = extendedInfo.room;
        info.realRoomId = extendedInfo.roomId;
        info.realModel = extendedInfo.model;
        info.realProdNo = extendedInfo.prodNo;
        info.realBindType = extendedInfo.bindType;
        info.realImageAddr1 = extendedInfo.imageAddr1;
    }
    Device *device = [[Device alloc] init];
    [device setDeviceInfo:info];
    return device;
}
- (DevicePermission *)productSEDevicePermissionBy:(SECloudDeviceBaseInfo *)cloudDevice
{
    DevicePermission *realPermissonModel = [[DevicePermission alloc] init];
    realPermissonModel.realAuth = [self productSEDeivceAuthBy:cloudDevice];
    realPermissonModel.realAuthType = cloudDevice.permission.authType;
    return realPermissonModel;
}

- (DeviceAuth *)productSEDeivceAuthBy:(SECloudDeviceBaseInfo *)cloudDevice
{
    DeviceAuth *deviceAuthModel = [[DeviceAuth alloc] init];
    deviceAuthModel.realControl = cloudDevice.permission.auth.control;
    deviceAuthModel.realSet = cloudDevice.permission.auth.set;
    deviceAuthModel.realView = cloudDevice.permission.auth.view;
    return deviceAuthModel;
}

@end
