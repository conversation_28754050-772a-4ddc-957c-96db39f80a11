//
//  SEUnbindDeviceTransformer.m
//  UserDomainDataSource
//
//  Created by 冉东军 on 2022/9/21.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEUnbindDeviceTransformer.h"
#import "MJExtension.h"
#import "CloudBatchProcessDevicesResponse.h"

@implementation SEUnbindDeviceTransformer
- (void)serverSuccessResultDidParse:(UserDomainSampleResult *)result
{
    NSDictionary *dataDict = result.retData ?: @{};
    NSArray *successDivices = dataDict[@"successDevices"];
    NSArray *failureDevices = dataDict[@"failureDevices"];
    NSMutableArray *successDivicesArray = [NSMutableArray array];
    NSMutableArray *failureDevicesArray = [NSMutableArray array];
    [successDivices enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      CloudBatchProcessDevice *cloudDevice = [CloudBatchProcessDevice new];
      cloudDevice.deviceId = obj;
      [successDivicesArray addObject:cloudDevice];
    }];
    [failureDevices enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      CloudBatchProcessDevice *cloudDevice = [CloudBatchProcessDevice new];
      cloudDevice.deviceId = obj;
      [failureDevicesArray addObject:cloudDevice];
    }];
    CloudBatchProcessDevicesResponse *devicesResponseModel = [[CloudBatchProcessDevicesResponse alloc] init];
    devicesResponseModel.successDevices = successDivicesArray;
    devicesResponseModel.failureDevices = failureDevicesArray;
    result.retData = devicesResponseModel;
}
@end
