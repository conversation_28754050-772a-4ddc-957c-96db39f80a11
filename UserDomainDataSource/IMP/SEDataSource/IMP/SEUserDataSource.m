//
//  SEUserDataSource.m
//  userdomain_seasia
//
//  Created by <PERSON> on 2019/7/1.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEUserDataSource.h"
#import <MJExtension/MJExtension.h>
#import "DataSourceCallbackFunctions.h"
#import "SEUserInfoAPI.h"
#import "SEUserInfoConverter.h"
#import "SEUserRefreshTokenAPI.h"
#import "SEUserAuthInfoConverter.h"
#import "SEUserLogoutAPI.h"
#import "SEUserModifyAPI.h"

@implementation SEUserDataSource

#pragma mark - UpUserDataSource
- (void)refreshToken:(NSString *)refreshToken success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    SEUserRefreshTokenAPI *api = [[SEUserRefreshTokenAPI alloc] initWithRefreshToken:refreshToken];
    api.responseParser = [[SEUserAuthInfoConverter alloc] init];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)queryUserAddresssuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}

- (void)queryUserInfosuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    SEUserInfoAPI *api = [[SEUserInfoAPI alloc] init];
    api.responseParser = [[SEUserInfoConverter alloc] init];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)logout:(NSString *)uhome_token success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    SEUserLogoutAPI *api = [[SEUserLogoutAPI alloc] init];
    api.responseParser = [[SEBaseConverter alloc] init];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)modifyPassword:(NSString *)password oldPassword:(NSString *)oldPassword success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}

- (void)modifyUserInfo:(NSDictionary *)userInfo success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    SECloudUserInfo *user = [SECloudUserInfo mj_objectWithKeyValues:userInfo];
    SEUserModifyAPI *api = [[SEUserModifyAPI alloc] initWithUserInfo:user];
    api.responseParser = [[SEBaseConverter alloc] init];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)createNewAddress:(NSDictionary *)address success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}

- (void)queryLoginTerminalSuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}

- (void)deleteAddress:(NSString *)addressId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}

- (void)editAddress:(NSDictionary *)addressInfo success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}

- (void)pollqrCodeState:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}

- (void)qrConfirmLogin:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}

- (void)qrConfirmScan:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}

- (void)qrCancleLogin:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}
- (void)queryLoginLogs:(NSInteger)pageNo pageSize:(NSInteger)pageSize success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}
- (void)queryApplicationAccessTokenSuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}
- (void)queryServiceOrder:(NSString *)phoneNumber success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}
- (void)updateAvatar:(UIImage *)image success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}

- (void)uHomeSearchUserInfo:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}
@end
