//
//  SEFamilyDataSource.m
//  userdomain_seasia
//
//  Created by <PERSON> on 2019/7/1.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEFamilyDataSource.h"
#import "SEFamilyListConverter.h"
#import "SEUnbindDeviceAPI.h"
#import "DataSourceCallbackFunctions.h"
#import "SEBaseConverter.h"
#import "UDDeviceDelegate.h"
#import "SEAddRoomToFamilyApi.h"
#import "SECreateFamilyApi.h"
#import "SEDestroyFamilyApi.h"
#import "SEFamilyInfoApi.h"
#import "SEFamilyListApi.h"
#import "SEFamilyMoveDevicesToNewRoomApi.h"
#import "SEFamilyRoomListApi.h"
#import "SERemoveRoomFromFamilyApi.h"
#import "SESetDefaultFamilyApi.h"
#import "SEUpdateFamilyInfoApi.h"
#import "SEUpdateFamilyRoomNameApi.h"
#import "SEFamilyMoveDevicesToOtherFamilyApi.h"
#import "RoomArgs.h"
#import "CreateFamilyArgs.h"
#import "UDRoomDelegate.h"
#import "FamilyArgs.h"
#import "SEFamilyInfoTransformer.h"
#import "SEAddRoomTransformer.h"
#import "SECreateFamilyTransformer.h"
#import "SEFamilyRoomTransformer.h"
#import "SEBatchProcessDevicesOfFamilyTransformer.h"
#import "SEUnbindDeviceTransformer.h"

@implementation SEFamilyDataSource
#pragma mark - Non-Public Methods
- (void)unsupportedResponseWithSuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}

#pragma mark - UpFamilyDataSource
- (void)queryFamilyListsuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    SEFamilyListApi *api = [[SEFamilyListApi alloc] init];
    api.responseParser = [SEFamilyListConverter new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)queryFamilyInfo:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    SEFamilyInfoApi *api = [[SEFamilyInfoApi alloc] initWithFamilyId:familyId];
    api.responseParser = [SEFamilyInfoTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)setDefaultFamily:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    SESetDefaultFamilyApi *api = [[SESetDefaultFamilyApi alloc] initWithFamilyId:familyId];
    api.responseParser = [SEBaseConverter new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)addRoomForFamily:(RoomArgs *)roomArgs familyId:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    SEAddRoomToFamilyApi *api = [[SEAddRoomToFamilyApi alloc] initWithFamilyId:familyId roomName:roomArgs.name roomClass:roomArgs.type floorId:roomArgs.floorId];
    api.responseParser = [SEAddRoomTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)adminInvitateMember:(NSString *)phone familyId:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}

- (void)changeFamilyAdmin:(NSString *)familyId userId:(NSString *)userId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}

- (void)createFamily:(CreateFamilyArgs *)familyArgs success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSMutableArray<NSDictionary<NSString *, NSString *> *> *rooms = [NSMutableArray array];
    [familyArgs.roomNames enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [rooms addObject:@{
          @"roomName" : obj ?: @""
      }];
    }];
    SECreateFamilyApi *api = [[SECreateFamilyApi alloc] initWithFamilyName:familyArgs.name familyPosition:familyArgs.position longitude:familyArgs.longitude latitude:familyArgs.latitude rooms:rooms.copy];
    api.responseParser = [SECreateFamilyTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)deleteFamilyMemberAsAdmin:(NSString *)familyId memberId:(NSString *)memberId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}

- (void)destoryFamilyAsAdmin:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    SEDestroyFamilyApi *api = [[SEDestroyFamilyApi alloc] initWithFamilyId:familyId];
    api.responseParser = [SEBaseConverter new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)exitFamilyAsAdmin:(NSString *)familyId userId:(NSString *)userId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}

- (void)exitFamilyAsMember:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}

- (void)queryRoomListOfFamily:(NSString *)familyId floorId:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    SEFamilyRoomListApi *api = [[SEFamilyRoomListApi alloc] initWithFamilyId:familyId floorId:floorId];
    api.responseParser = [SEFamilyRoomTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)removeRoomFromFamily:(NSString *)familyId roomId:(NSString *)roomId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    SERemoveRoomFromFamilyApi *api = [[SERemoveRoomFromFamilyApi alloc] initWithFamilyId:familyId roomId:roomId];
    api.responseParser = [SEBaseConverter new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)updateFamilyInfo:(FamilyArgs *)familyArgs familyId:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    SEUpdateFamilyInfoApi *api = [[SEUpdateFamilyInfoApi alloc] initWithFamilyId:familyId familyName:familyArgs.name];
    api.responseParser = [SEBaseConverter new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)updateFamilyRoomName:(NSString *)familyId roomId:(NSString *)roomId roomName:(NSString *)roomName floorId:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    SEUpdateFamilyRoomNameApi *api = [[SEUpdateFamilyRoomNameApi alloc] initWithRoomName:roomName familyId:familyId roomId:roomId floorId:floorId];
    api.responseParser = [SEBaseConverter new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)unBindFamilyDevices:(NSString *)familyId devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSMutableArray *deviceIDs = [NSMutableArray array];
    [devices enumerateObjectsUsingBlock:^(id<UDDeviceDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [deviceIDs addObject:obj.deviceId ?: @""];
    }];
    SEUnbindDeviceAPI *api = [[SEUnbindDeviceAPI alloc] initAPIWithDeviceIDs:deviceIDs];
    api.responseParser = [[SEUnbindDeviceTransformer alloc] init];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)moveDevicesToOtherRoom:(NSString *)familyId newRoom:(id<UDRoomDelegate>)newRoom devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSMutableArray *dev = [NSMutableArray array];
    [devices enumerateObjectsUsingBlock:^(id<UDDeviceDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [dev addObject:@{ @"deviceId" : obj.deviceId ?: @"",
                        @"deviceName" : obj.deviceName ?: @"",
                        @"deviceNetType" : obj.deviceNetType ?: @"",
                        @"oldRoom" : obj.roomName ?: @"",
                        @"oldRoomId" : obj.roomId ?: @""
      }];
    }];
    SEFamilyMoveDevicesToNewRoomApi *api = [[SEFamilyMoveDevicesToNewRoomApi alloc] initWithFamilyId:familyId deviceIds:dev newRoomName:newRoom.roomName newRoomId:newRoom.roomId];
    api.responseParser = [SEBatchProcessDevicesOfFamilyTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)removeDevicesFromFamily:(NSString *)familyId devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}

- (void)moveDevicesToOtherFamily:(NSString *)newFamilyId oldFamilyId:(NSString *)oldFamilyId devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSMutableArray *dev = [NSMutableArray array];
    [devices enumerateObjectsUsingBlock:^(id<UDDeviceDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [dev addObject:@{ @"deviceId" : obj.deviceId ?: @"",
                        @"deviceName" : obj.deviceName ?: @"",
                        @"deviceNetType" : obj.deviceNetType ?: @"",
                        @"room" : obj.roomName ?: @""
      }];
    }];
    SEFamilyMoveDevicesToOtherFamilyApi *api = [[SEFamilyMoveDevicesToOtherFamilyApi alloc] initWithDeviceIds:dev oldFamilyId:oldFamilyId newFamilyId:newFamilyId];
    api.responseParser = [SEBatchProcessDevicesOfFamilyTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)replyFamilyInviteWithCode:(NSString *)inviteCode familyId:(NSString *)familyId memberName:(NSString *)memberName agree:(BOOL)agree success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}
- (void)replyJoinFamily:(NSString *)applicationId agree:(BOOL)agree success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}
- (void)addFloorOfFamily:(NSString *)familyId floorArg:(FloorArg *)floorArg success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}
- (void)editFloorOfFamily:(NSString *)familyId floorArg:(FloorArg *)floorArg success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}
- (void)deleteFloorOfFamily:(NSString *)familyId floorId:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}
- (void)modifyVirtualMember:(VirtualMemberArgs *)virtualMemberArgs success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}
- (void)addVirtualMember:(NSString *)familyId memberId:(NSString *)memberId memberName:(NSString *)memberName success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}
- (void)queryFirstMemeber:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}
- (void)modifyMemberRoleOfFamily:(NSString *)familyId memberId:(NSString *)memberId memberRole:(NSString *)memberRole success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}
- (void)modifyVirtualMemberRoleOfFamily:(NSString *)familyId memberId:(NSString *)memberId memberRole:(NSString *)memberRole success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}
- (void)saveRoomsOrderOfFamily:(NSString *)familyId rooms:(NSArray<NSString *> *)rooms floorId:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self unsupportedResponseWithSuccess:success failure:failure];
}
@end
