//
//  SEDeviceListDataSource.m
//  userdomain_seasia
//
//  Created by <PERSON> on 2019/7/1.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEDeviceListDataSource.h"
#import "DataSourceCallbackFunctions.h"
#import "SEQueryDeviceListAPI.h"
#import "SEDeviceInfoListConverter.h"
#import "SEModifyDeviceInfoAPI.h"
#import "SEModifyDeviceConverter.h"

@implementation SEDeviceListDataSource
#pragma mark - UpDeviceListDataSource
- (void)queryDeviceList:(userDomainCallback)success failure:(userDomainCallback)failure
{
    SEQueryDeviceListAPI *api = [[SEQueryDeviceListAPI alloc] init];
    api.responseParser = [[SEDeviceInfoListConverter alloc] init];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)updateDeviceNameWithDeviceId:(NSString *)deviceId oldName:(NSString *)oldName newName:(NSString *)newName familyId:(NSString *)familyId deviceNetType:(NSString *)deviceNetType prodNo:(NSString *)prodNo success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (![deviceId isKindOfClass:[NSString class]] || ![newName isKindOfClass:[NSString class]] || ![familyId isKindOfClass:[NSString class]]) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    SEModifyDeviceInfoAPI *api = [[SEModifyDeviceInfoAPI alloc] initAPIWithParameters:@{
        kSEModifyDeviceInfoDeviceID : deviceId,
        kSEModifyDeviceInfoDeviceName : newName,
        kSEModifyDeviceInfoFamilyId : familyId
    }];
    api.responseParser = [[SEModifyDeviceConverter alloc] init];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)updateDeviceInfo:(NSDictionary<NSString *, NSString *> *)deviceInfo success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (![deviceInfo isKindOfClass:[NSDictionary class]]) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    SEModifyDeviceInfoAPI *api = [[SEModifyDeviceInfoAPI alloc] initAPIWithParameters:deviceInfo];
    api.responseParser = [[SEModifyDeviceConverter alloc] init];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)getGroupDeviceList:(NSString *)familyId filterFlag:(BOOL)filterFlag success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}

- (void)updateDeviceNameWithDeviceId:(NSString *)deviceId oldName:(NSString *)oldName newName:(NSString *)newName familyId:(NSString *)familyId type:(NSString *)type checkLevel:(BOOL)checkLevel success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (failure) {
        NSError *error = [NSError errorWithDomain:@"暂不支持该接口!" code:-1 userInfo:nil];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        result.retInfo = error.localizedDescription;
        failure(result);
    }
}

@end
