//
//  FamilyInfoTransformer.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/11.
//

#import "FamilyInfoTransformer.h"
#import "UserDomainSampleResult.h"
#import "FamilyRelatedTransform.h"

@implementation FamilyInfoTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:responseObject];
    if (result.success) {
        result.retData = [FamilyRelatedTransform jsonToFamily:responseObject[kret_Data]];
    }
    else {
        result.retData = nil;
    }
    return result;
}
@end
