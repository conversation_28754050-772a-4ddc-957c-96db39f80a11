//
//  FamilyFirstMemberTransformer.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/14.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "FamilyFirstMemberTransformer.h"
#import "CloudFamilyFirstMember.h"
#import "UserDomainSampleResult.h"
#import "MJExtension.h"
@implementation FamilyFirstMemberTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }

    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:responseObject];
    if (result.success) {
        NSArray *memberData = responseObject[kret_Data];
        CloudFamilyFirstMember *member = [CloudFamilyFirstMember mj_objectWithKeyValues:memberData];
        result.retData = member;
    }
    else {
        result.retData = nil;
    }
    return result;
}
@end
