//
//  UserInfoTransformer.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/7.
//

#import "UserInfoTransformer.h"
#import "UserDomainSampleResult.h"
#import "CloudUserInfo.h"
#import "UserInfo.h"
#import "MJExtension.h"
#import "UpUserDomainHolder.h"
#import "UserInfo.h"
#import "UserInfo+PrivateExtension.h"
#import "UserAddressInfo.h"
#import "UserAddressInfo+PrivateExtension.h"
@implementation UserInfoTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    if (!object || ![object isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:object];
    if (result.success) {
        NSDictionary *dataDic = (NSDictionary *)object;
        CloudUserInfo *userInfo = [CloudUserInfo mj_objectWithKeyValues:object];
        UserInfo *userInfoModel = [[UserInfo alloc] init];
        userInfoModel.realUserId = userInfo.userId;
        userInfoModel.realMobile = userInfo.mobile;
        if (!userInfo.email) {
            userInfo.email = @"";
        }
        userInfoModel.realUsername = userInfo.username;
        userInfoModel.realGivenName = userInfo.givenName;
        userInfoModel.realNickname = userInfo.nickname;
        userInfoModel.realFamilyNum = userInfo.familyNum;
        userInfoModel.realGender = userInfo.gender;
        userInfoModel.realMarriage = userInfo.marriage;
        userInfoModel.realBirthday = userInfo.birthday;
        userInfoModel.realEducation = userInfo.education;
        userInfoModel.realAvatarUrl = userInfo.avatarUrl;
        userInfoModel.realExtraPhone = userInfo.extraPhone;
        userInfoModel.realIncome = userInfo.income;
        userInfoModel.realHeight = userInfo.height;
        userInfoModel.realWeight = userInfo.weight;
        userInfoModel.realCountryCode = userInfo.countryCode;
        userInfoModel.realSignature = dataDic[@"signature"];
        userInfoModel.realRegClientId = dataDic[@"regClientId"];
        result.retData = userInfoModel;
    }
    else {
        result.retData = nil;
    }
    return result;
}
@end
