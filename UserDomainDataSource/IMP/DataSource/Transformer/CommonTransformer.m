//
//  CommonTransformer.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "CommonTransformer.h"
#import "UserDomainSampleResult.h"

@implementation CommonTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    if (!object || ![object isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:object];
    return result;
}

@end
