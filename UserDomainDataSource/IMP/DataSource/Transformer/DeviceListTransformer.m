//
//  DeviceListTransformer.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "DeviceListTransformer.h"
#import "CloudDeviceList.h"
#import "UserDomainSampleResult.h"
#import "MJExtension.h"
#import "UpUserDomainHolder.h"
#import "Device.h"
#import "DeviceAuth.h"
#import "DeviceAuth+PrivateExtension.h"
#import "UDDeviceInfo.h"
#import "UDDeviceInfo+PrivateExtension.h"
#import "DeviceOwnerInfo.h"
#import "DeviceOwnerInfo+PrivateExtension.h"
#import "DevicePermission.h"
#import "DevicePermission+PrivateExtension.h"
#import "Room.h"
#import "Room+PrivateExtension.h"
#import "DeviceShareDeviceCardInfo.h"
#import "DeviceShareDeviceCardInfo+PrivateExtension.h"
static NSString *const kdeviceinfos = @"deviceinfos";

@implementation DeviceListTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:responseObject];
    if (!result.success) {
        result.retData = nil;
        return result;
    }
    NSDictionary *retdata = responseObject[kret_Data];
    if (!retdata || ![retdata isKindOfClass:NSDictionary.class]) {
        result.retData = nil;
        return result;
    }
    NSArray *deviceinfos = retdata[kdeviceinfos];
    NSArray<CloudDevice *> *deviceList = [CloudDevice mj_objectArrayWithKeyValuesArray:deviceinfos];
    NSArray<UDDeviceInfo *> *deviceInfoList = [self productDeviceListWithCloudDeviceList:deviceList];
    NSMutableArray<Device *> *realDeviceList = [NSMutableArray array];
    for (UDDeviceInfo *deviceInfo in deviceInfoList) {
        Device *device = [[Device alloc] init];
        [device setDeviceInfo:deviceInfo];
        [realDeviceList addObject:device];
    }
    result.retData = realDeviceList;
    return result;
}

- (NSArray<UDDeviceInfo *> *)productDeviceListWithCloudDeviceList:(NSArray<CloudDevice *> *)deviceList
{
    NSMutableArray<UDDeviceInfo *> *realDeviceList = [NSMutableArray array];
    for (CloudDevice *cloudDevice in deviceList) {
        UDDeviceInfo *deviceInfoModel = [[UDDeviceInfo alloc] init];
        deviceInfoModel.realDeviceId = cloudDevice.baseInfo.deviceId;
        deviceInfoModel.realDeviceName = cloudDevice.baseInfo.deviceName;
        deviceInfoModel.realDevName = cloudDevice.baseInfo.devName;
        deviceInfoModel.realDeviceType = cloudDevice.baseInfo.deviceType;
        deviceInfoModel.realFamilyId = cloudDevice.baseInfo.familyId;
        deviceInfoModel.realOwnerId = cloudDevice.baseInfo.ownerId;
        deviceInfoModel.realPermission = [self productDevicePermissionBy:cloudDevice];
        deviceInfoModel.realWifiType = cloudDevice.baseInfo.wifiType;
        deviceInfoModel.realDeviceNetType = cloudDevice.baseInfo.deviceNetType;
        deviceInfoModel.realBindTime = cloudDevice.baseInfo.bindTime;
        deviceInfoModel.realIsOnline = cloudDevice.baseInfo.isOnline;
        deviceInfoModel.realOwnerInfo = [self productDeivceOwnerInfoBy:cloudDevice];
        deviceInfoModel.realParentId = cloudDevice.baseInfo.parentsDeviceId;
        deviceInfoModel.realDeviceRole = cloudDevice.baseInfo.deviceRole;
        deviceInfoModel.realDeviceRoleType = cloudDevice.baseInfo.deviceRoleType;
        deviceInfoModel.realApptypeName = cloudDevice.extendedInfo.apptypeName;
        deviceInfoModel.realApptypeCode = cloudDevice.extendedInfo.apptypeCode;
        deviceInfoModel.realCategoryGrouping = cloudDevice.extendedInfo.categoryGrouping;
        deviceInfoModel.realBrand = cloudDevice.extendedInfo.brand;
        deviceInfoModel.realImageAddr1 = cloudDevice.extendedInfo.imageAddr1;
        deviceInfoModel.realModel = cloudDevice.extendedInfo.model;
        deviceInfoModel.realProdNo = cloudDevice.extendedInfo.prodNo;
        deviceInfoModel.realRoomName = cloudDevice.extendedInfo.room;
        deviceInfoModel.realRoomId = cloudDevice.extendedInfo.roomId;
        deviceInfoModel.realAccessType = cloudDevice.extendedInfo.accessType;
        deviceInfoModel.realConfigType = cloudDevice.extendedInfo.configType;
        deviceInfoModel.realComunicationMode = cloudDevice.extendedInfo.comunicationMode;
        deviceInfoModel.realDevFloorId = cloudDevice.extendedInfo.devFloorId;
        deviceInfoModel.realDevFloorName = cloudDevice.extendedInfo.devFloorName;
        deviceInfoModel.realDevFloorOrderId = cloudDevice.extendedInfo.devFloorOrderId;
        deviceInfoModel.realApptypeIcon = cloudDevice.extendedInfo.apptypeIcon;
        deviceInfoModel.realDeviceGroupId = cloudDevice.baseInfo.deviceGroupId;
        deviceInfoModel.realDeviceGroupType = cloudDevice.baseInfo.deviceGroupType;
        deviceInfoModel.realNoKeepAlive = cloudDevice.extendedInfo.noKeepAlive;
        deviceInfoModel.realTwoGroupingName = cloudDevice.extendedInfo.twoGroupingName;
        deviceInfoModel.realCardPageImg = cloudDevice.extendedInfo.cardPageImg;
        deviceInfoModel.realCardSort = cloudDevice.extendedInfo.cardSort;
        deviceInfoModel.realCardStatus = cloudDevice.extendedInfo.cardStatus;
        deviceInfoModel.realAggregationParentId = cloudDevice.extendedInfo.aggregationParentId;
        deviceInfoModel.realSupportAggregationFlag = cloudDevice.extendedInfo.supportAggregationFlag;
        deviceInfoModel.realDeviceAggregateType = cloudDevice.extendedInfo.deviceAggregateType;
        deviceInfoModel.realShareDeviceCardInfo = [self productShareDeviceCardInfosBy:cloudDevice];
        deviceInfoModel.realSharedDeviceFlag = cloudDevice.extendedInfo.sharedDeviceFlag;
        deviceInfoModel.realAttachmentSortCode = cloudDevice.extendedInfo.attachmentSortCode;
        deviceInfoModel.realDeviceShareSupportFlag = cloudDevice.extendedInfo.deviceShareSupportFlag;
        deviceInfoModel.realReBind = cloudDevice.extendedInfo.rebind.boolValue;
        [realDeviceList addObject:deviceInfoModel];
    }
    return realDeviceList.copy;
}


- (DevicePermission *)productDevicePermissionBy:(CloudDevice *)cloudDevice
{
    DevicePermission *realPermissonModel = [[DevicePermission alloc] init];
    realPermissonModel.realAuth = [self productDeivceAuthBy:cloudDevice];
    realPermissonModel.realAuthType = cloudDevice.baseInfo.permission.authType;
    return realPermissonModel;
}

- (DeviceAuth *)productDeivceAuthBy:(CloudDevice *)cloudDevice
{
    DeviceAuth *deviceAuthModel = [[DeviceAuth alloc] init];
    deviceAuthModel.realControl = cloudDevice.baseInfo.permission.auth.control;
    deviceAuthModel.realSet = cloudDevice.baseInfo.permission.auth.set;
    deviceAuthModel.realView = cloudDevice.baseInfo.permission.auth.view;
    return deviceAuthModel;
}
- (DeviceOwnerInfo *)productDeivceOwnerInfoBy:(CloudDevice *)cloudDevice
{
    DeviceOwnerInfo *deviceOwnerInfoModel = [[DeviceOwnerInfo alloc] init];
    deviceOwnerInfoModel.realUserId = cloudDevice.baseInfo.ownerInfo.userId;
    deviceOwnerInfoModel.realMobile = cloudDevice.baseInfo.ownerInfo.mobile;
    deviceOwnerInfoModel.realUserNickName = cloudDevice.baseInfo.ownerInfo.name;
    deviceOwnerInfoModel.realUcUserId = cloudDevice.baseInfo.ownerInfo.ucUserId;
    return deviceOwnerInfoModel;
}

- (NSArray<DeviceShareDeviceCardInfo *> *)productShareDeviceCardInfosBy:(CloudDevice *)cloudDevice
{
    NSMutableArray<DeviceShareDeviceCardInfo *> *array = [NSMutableArray array];
    NSArray<CloudShareDeviceCardInfo *> *shareDevices = cloudDevice.extendedInfo.shareDeviceCardInfo;
    [shareDevices enumerateObjectsUsingBlock:^(CloudShareDeviceCardInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      DeviceShareDeviceCardInfo *model = [[DeviceShareDeviceCardInfo alloc] init];
      model.realFamilyId = obj.familyId;
      model.realCardSort = obj.cardSort;
      model.realCardStatus = obj.cardStatus;
      [array addObject:model];
    }];
    return array;
}

@end
