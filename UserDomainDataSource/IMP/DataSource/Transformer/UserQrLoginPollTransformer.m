//
//  UserQrLoginPollTransformer.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/17.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UserQrLoginPollTransformer.h"
#import "UserDomainSampleResult.h"
#import "CloudQRLoginStage.h"
#import "MJExtension.h"

@implementation UserQrLoginPollTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:responseObject];
    if (result.success) {
        CloudQRLoginStage *stage = [CloudQRLoginStage mj_objectWithKeyValues:responseObject];
        result.retData = stage;
    }
    else {
        result.retData = nil;
    }
    return result;
}
@end
