//
//  UserLoginTerminalTransformer.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/17.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UserLoginTerminalTransformer.h"
#import "UserDomainSampleResult.h"
//#import "CloudLoginTerminal.h"
#import "MJExtension.h"
#import "UpUserDomainHolder.h"
#import "UserTerminal.h"
#import "UserTerminal+PrivateExtension.h"
@implementation UserLoginTerminalTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:responseObject];
    if (result.success) {
        NSArray<NSDictionary *> *terminalsData = responseObject[@"result"];
        NSMutableArray<UserTerminal *> *terminalList = [NSMutableArray array];
        for (NSDictionary *terminalInfo in terminalsData) {
            UserTerminal *infoModel = [[UserTerminal alloc] init];
            infoModel.realUser_id = terminalInfo[@"user_id"];
            infoModel.realAction_at = terminalInfo[@"action_at"];
            infoModel.realClient_id = terminalInfo[@"client_id"];
            infoModel.realUser_name = terminalInfo[@"uer_name"];
            [terminalList addObject:infoModel];
        }
        result.retData = terminalList;
    }
    else {
        result.retData = nil;
    }
    return result;
}
@end
