//
//  UserAddressTransformer.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/8.
//

#import "UserAddressTransformer.h"
#import "UserDomainSampleResult.h"
#import "CloudUserInfo.h"
#import "MJExtension.h"
#import "UpUserDomainHolder.h"
#import "UserAddressInfo.h"
#import "UserAddressInfo+PrivateExtension.h"
#import "UserAddress.h"
#import "UserAddress+PrivateExtension.h"
@implementation UserAddressTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:responseObject];
    if (result.success) {
        NSArray *data = responseObject[kret_Data];
        NSMutableArray<UserAddressInfo *> *infos = [NSMutableArray array];
        for (NSDictionary *info in data) {
            NSDictionary *addrInfo = info[@"address"];
            UserAddress *addrModel = [[UserAddress alloc] init];
            addrModel.realCity = addrInfo[@"city"];
            addrModel.realCity_id = addrInfo[@"city_id"];
            addrModel.realCountry_code = addrInfo[@"country_code"];
            addrModel.realDistrict = addrInfo[@"district"];
            addrModel.realDistrict_id = addrInfo[@"district_id"];
            addrModel.realLine1 = addrInfo[@"line1"];
            addrModel.realLine2 = addrInfo[@"line2"];
            addrModel.realPostcode = addrInfo[@"postcode"];
            addrModel.realProvince = addrInfo[@"province"];
            addrModel.realProvince_id = addrInfo[@"province_id"];
            addrModel.realTown = addrInfo[@"town"];
            addrModel.realTown_id = addrInfo[@"town_id"];
            UserAddressInfo *addrInfoModel = [[UserAddressInfo alloc] init];
            addrInfoModel.realAddress = addrModel;
            addrInfoModel.realEmail = info[@"email"];
            addrInfoModel.realAddressId = [NSString stringWithFormat:@"%@", info[@"id"]];
            addrInfoModel.realIs_default = [info[@"is_default"] integerValue];
            addrInfoModel.realIs_service = [info[@"is_service"] integerValue];
            addrInfoModel.realReceiver_mobile = info[@"receiver_mobile"];
            addrInfoModel.realReceiver_name = info[@"receiver_name"];
            addrInfoModel.realSource = info[@"source"];
            addrInfoModel.realTag = info[@"tag"];
            addrInfoModel.realUser_id = info[@"user_id"];
            [infos addObject:addrInfoModel];
        }
        result.retData = infos;
    }
    else {
        result.retData = nil;
    }
    return result;
}
@end
