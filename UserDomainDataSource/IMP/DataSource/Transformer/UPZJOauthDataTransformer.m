//
//  UPZJOauthDataTransformer.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/6/17.
//

#import "UPZJOauthDataTransformer.h"
#import "UserDomainSampleResult.h"
#import "CloudOauthData.h"
#import "ApplicationOauthData.h"
#import "MJExtension.h"
#import "ApplicationOauthData+PrivateExtension.h"
@implementation UPZJOauthDataTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{

    if (!object || ![object isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    NSDictionary *responseObject = (NSDictionary *)object;
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:responseObject];
    if (result.success) {
        ApplicationOauthData *oauthData = [[ApplicationOauthData alloc] init];
        NSDictionary *tokenInfo = responseObject[@"data"][@"tokenInfo"];
        oauthData.realAccess_token = tokenInfo[@"accountToken"];
        oauthData.realExpires_in = [tokenInfo[@"expiresIn"] stringValue];
        oauthData.realRefresh_token = tokenInfo[@"refreshToken"];
        oauthData.realScope = tokenInfo[@"scope"];
        oauthData.realToken_type = tokenInfo[@"tokenType"];
        oauthData.realUhome_access_token = tokenInfo[@"uhomeAccessToken"];
        oauthData.realUhome_user_id = tokenInfo[@"uhomeUserId"];
        oauthData.realUc_user_id = tokenInfo[@"uocUserId"];
        result.retData = oauthData;
    }
    else {
        result.retData = nil;
    }
    return result;
}
@end
