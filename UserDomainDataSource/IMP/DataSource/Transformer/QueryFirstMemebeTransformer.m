//
//  FamilyQueryFirstMemebeTransformer.m
//  UserDomainDataSource
//
//  Created by 冉东军 on 2022/1/18.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "QueryFirstMemebeTransformer.h"
#import "UserDomainSampleResult.h"
#import "FamilyMember.h"
#import "FamilyMember+PrivateExtension.h"
#import "MemberInfo.h"
#import "MemberInfo+PrivateExtension.h"

@implementation QueryFirstMemebeTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:responseObject];
    if (result.success) {
        NSDictionary *data = responseObject[@"data"];
        MemberInfo *memberInfoModel = [[MemberInfo alloc] init];
        memberInfoModel.realUserId = data[@"userId"];
        memberInfoModel.realName = data[@"name"];
        FamilyMember *memberModel = [[FamilyMember alloc] init];
        memberModel.realMemberName = data[@"memberName"];
        memberModel.realMemberInfo = memberInfoModel;
        result.retData = memberModel;
    }
    return result;
}
@end
