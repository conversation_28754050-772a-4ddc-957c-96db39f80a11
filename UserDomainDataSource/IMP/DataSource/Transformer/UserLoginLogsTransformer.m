//
//  UserLoginLogsTransformer.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/8/8.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UserLoginLogsTransformer.h"
#import "CloudUserLoginLogInfo.h"
#import "UserDomainSampleResult.h"
#import "UserLoginLogInfo.h"
#import "UserLoginLogInfo+PrivateExtension.h"
#import "MJExtension.h"

@implementation UserLoginLogsTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    if (!object || ![object isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    NSDictionary *responseInfo = (NSDictionary *)object;
    BOOL success = [responseInfo[@"success"] boolValue];
    if (success) {
        NSArray *logData = responseInfo[@"result"];
        NSMutableArray<UserLoginLogInfo *> *infos = [NSMutableArray array];
        for (NSDictionary *logInfo in logData) {
            UserLoginLogInfo *logInfoModel = [[UserLoginLogInfo alloc] init];
            logInfoModel.realLogId = [logInfo[@"id"] stringValue];
            logInfoModel.realUser_id = logInfo[@"user_id"];
            logInfoModel.realUser_name = logInfo[@"user_name"];
            logInfoModel.realClient_id = logInfo[@"client_id"];
            logInfoModel.realProvince = logInfo[@"province"];
            logInfoModel.realCity = logInfo[@"city"];
            logInfoModel.realLongitude = logInfo[@"longitude"];
            logInfoModel.realLatitude = logInfo[@"latitude"];
            logInfoModel.realAction_at = logInfo[@"action_at"];
            [infos addObject:logInfoModel];
        }
        UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
        result.retData = infos;
        return result;
    }
    else {
        NSString *error = responseInfo[@"error"];
        UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
        result.retInfo = error;
        return result;
    }
}
@end
