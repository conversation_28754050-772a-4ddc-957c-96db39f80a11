//
//  CommonDataTransformer.m
//  UserDomainDataSource
//
//  Created by 冉东军 on 2022/3/28.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "CommonDataTransformer.h"
#import "UserDomainSampleResult.h"

@implementation CommonDataTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithAnalysisDataResponse:responseObject];
    return result;
}
@end
