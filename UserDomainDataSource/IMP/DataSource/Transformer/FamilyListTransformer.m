//
//  FamilyListTransformer.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/7.
//

#import "FamilyListTransformer.h"
#import "UserDomainSampleResult.h"
#import "FamilyRelatedTransform.h"

@implementation FamilyListTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }

    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:responseObject];
    if (result.success) {
        result.retData = [FamilyRelatedTransform jsonToFamilyList:responseObject[kret_Data]];
    }
    else {
        result.retData = nil;
    }
    return result;
}

@end
