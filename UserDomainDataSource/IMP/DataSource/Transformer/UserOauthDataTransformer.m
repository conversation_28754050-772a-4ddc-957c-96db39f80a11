//
//  UserOauthDataTransformer.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/12.
//

#import "UserOauthDataTransformer.h"
#import "UserDomainSampleResult.h"
#import "CloudOauthData.h"
#import "MJExtension.h"

@implementation UserOauthDataTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:responseObject];
    if (result.success) {
        CloudOauthData *oauthData = [CloudOauthData mj_objectWithKeyValues:responseObject];
        result.retData = oauthData;
    }
    else {
        result.retData = nil;
    }
    return result;
}
@end
