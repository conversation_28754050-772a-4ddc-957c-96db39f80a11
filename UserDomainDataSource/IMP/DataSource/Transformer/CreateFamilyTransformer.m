//
//  CreateFamilyTransformer.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/6/20.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "CreateFamilyTransformer.h"
#import "UserDomainSampleResult.h"
#import "CloudFamily.h"
#import "MJExtension.h"

@implementation CreateFamilyTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:responseObject];
    if (!result.success) {
        result.retData = nil;
        return result;
    }

    NSDictionary *data = responseObject[kret_Data];
    if (!data || ![data isKindOfClass:NSDictionary.class]) {
        result.retData = nil;
        return result;
    }

    NSDictionary *familyInfo = data[@"familyInfo"];
    result.retData = familyInfo[@"familyId"];
    return result;
}
@end
