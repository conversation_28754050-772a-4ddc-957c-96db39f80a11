//
//  UpUserServiceOrderTransformer.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/8/26.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UpUserServiceOrderTransformer.h"
#import "UserDomainSampleResult.h"
#import <MJExtension/MJExtension.h>

@implementation UpUserServiceOrderTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    BOOL success = [responseObject[@"success"] boolValue];
    if (success) {
        NSArray *logData = responseObject[@"result"];
        UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
        result.retData = logData;
        return result;
    }
    else {
        NSString *error = responseObject[@"error"];
        UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
        result.retInfo = error;
        return result;
    }
}
@end
