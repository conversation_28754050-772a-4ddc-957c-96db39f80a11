//
//  RemoveDevicesFromFamilyTransformer.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "BatchProcessDevicesOfFamilyTransformer.h"
#import "UserDomainSampleResult.h"
#import "MJExtension.h"
#import "CloudBatchProcessDevicesResponse.h"

@implementation BatchProcessDevicesOfFamilyTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }

    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:responseObject];
    if (result.success) {

        NSDictionary *responseData = responseObject[kret_Data];
        CloudBatchProcessDevicesResponse *reponse = [CloudBatchProcessDevicesResponse mj_objectWithKeyValues:responseData];
        result.retData = reponse;
    }
    else {
        result.retData = nil;
    }
    return result;
}
@end
