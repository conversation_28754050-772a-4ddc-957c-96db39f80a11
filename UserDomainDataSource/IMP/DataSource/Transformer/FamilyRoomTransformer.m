//
//  FamilyRoomTransformer.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/15.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "FamilyRoomTransformer.h"
#import "UserDomainSampleResult.h"
#import "FamilyRelatedTransform.h"
@implementation FamilyRoomTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    id responseObject = object;
    if (!responseObject || ![responseObject isKindOfClass:NSDictionary.class]) {
        return [UserDomainSampleResult responseDataError];
    }
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseObject:responseObject];
    if (!result.success) {
        result.retData = nil;
        return result;
    }

    NSDictionary *retdata = responseObject[kret_Data];

    if (!retdata || ![retdata isKindOfClass:NSDictionary.class]) {
        result.retData = nil;
        return result;
    }

    result.retData = [FamilyRelatedTransform jsonToRoomList:retdata];
    return result;
}
@end
