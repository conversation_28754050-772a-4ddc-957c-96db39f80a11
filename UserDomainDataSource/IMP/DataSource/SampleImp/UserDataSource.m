//
//  UserDataSource.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "UserDataSource.h"
#import "UPZJUserInfoApi.h"
#import "DataSourceCallbackFunctions.h"
#import "UserInfoTransformer.h"
#import "UPUserAddressApi.h"
#import "UserAddressTransformer.h"
#import "UPUserRefreshTokenApi.h"
#import "UserOauthDataTransformer.h"
#import "UPUserUpdateUserInfoApi.h"
#import "CommonTransformer.h"
#import "UPUserCreateNewAddressApi.h"
#import "UPUserLoginTerminalApi.h"
#import "UserLoginTerminalTransformer.h"
#import "UPUserDeleteAddressApi.h"
#import "UPUserEditAddressApi.h"
#import "UPUserQRloginPollApi.h"
#import "UserQrLoginPollTransformer.h"
#import "UPUserQRConfirmLoginApi.h"
#import "UPUserQRCancleLoginApi.h"
#import "UPUserUpdatePwdApi.h"
#import "UPUserQRScanApi.h"
#import "UPUserLoginLogsApi.h"
#import "UserLoginLogsTransformer.h"
#import "UPUserApplitionTokenApi.h"
#import "UPUserServiceOrderApi.h"
#import "UpUserServiceOrderTransformer.h"
#import "UPUserUploadAvatarApi.h"
#import "UPUserInfoApi.h"
#import "UPZJRefreshTokenApi.h"
#import "UPZJOauthDataTransformer.h"
#import "UPZJUserLogOutApi.h"
#import "UPUserDomainSettings.h"
#import "UpEditAggregationSwitchApi.h"
#import "UPConfirmDeviceSharingRelationApi.h"
#import "UPCancelDeviceSharingRelationApi.h"

@implementation UserDataSource
- (void)refreshToken:(NSString *)refreshToken success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPZJRefreshTokenApi *api = [[UPZJRefreshTokenApi alloc] initWithrefresh_token:refreshToken];
    api.responseParser = [UPZJOauthDataTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)queryUserAddresssuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserAddressApi *api = [[UPUserAddressApi alloc] init];
    api.responseParser = [UserAddressTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)queryUserInfosuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserInfoApi *api = [[UPUserInfoApi alloc] init];
    api.responseParser = [UserInfoTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)uHomeSearchUserInfo:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPZJUserInfoApi *api = [[UPZJUserInfoApi alloc] init];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)logout:(NSString *)uhome_token success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPZJUserLogOutApi *api = [[UPZJUserLogOutApi alloc] initWithuhome_token:uhome_token];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)modifyPassword:(NSString *)password oldPassword:(NSString *)oldPassword success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserUpdatePwdApi *api = [[UPUserUpdatePwdApi alloc] initWithold_password:oldPassword nw_password:password];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)modifyUserInfo:(NSDictionary *)userInfo success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserUpdateUserInfoApi *api = [[UPUserUpdateUserInfoApi alloc] initWithInfo:userInfo];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)createNewAddress:(NSDictionary *)address success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserCreateNewAddressApi *api = [[UPUserCreateNewAddressApi alloc] initWithAddressInfo:address];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)queryLoginTerminalSuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserLoginTerminalApi *api = [[UPUserLoginTerminalApi alloc] init];
    api.responseParser = [UserLoginTerminalTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)deleteAddress:(NSString *)addressId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserDeleteAddressApi *api = [[UPUserDeleteAddressApi alloc] initWithAddressId:addressId];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)editAddress:(NSDictionary *)addressInfo success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserEditAddressApi *api = [[UPUserEditAddressApi alloc] initWithAddressInfo:addressInfo];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)pollqrCodeState:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserQRloginPollApi *api = [[UPUserQRloginPollApi alloc] initWithUUID:uuid];
    api.responseParser = [UserQrLoginPollTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)qrConfirmLogin:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserQRConfirmLoginApi *api = [[UPUserQRConfirmLoginApi alloc] initWithUUID:uuid];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)qrConfirmScan:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserQRScanApi *api = [[UPUserQRScanApi alloc] initWithUUID:uuid];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)qrCancleLogin:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserQRCancleLoginApi *api = [[UPUserQRCancleLoginApi alloc] initWithUUID:uuid];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)queryLoginLogs:(NSInteger)pageNo pageSize:(NSInteger)pageSize success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserLoginLogsApi *api = [[UPUserLoginLogsApi alloc] initWithPageNo:pageNo pageSize:pageSize];
    api.responseParser = [UserLoginLogsTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)queryApplicationAccessTokenSuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserApplitionTokenApi *api = [[UPUserApplitionTokenApi alloc] init];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)queryServiceOrder:(NSString *)phoneNumber success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUserServiceOrderApi *api = [[UPUserServiceOrderApi alloc] initWithPhoneNumber:phoneNumber];
    api.responseParser = [UpUserServiceOrderTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)updateAvatar:(UIImage *)image success:(userDomainCallback)success failure:(userDomainCallback)failure
{

    UPUserUploadAvatarApi *api = [[UPUserUploadAvatarApi alloc] initWithImage:image];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)confirmDeviceSharingRelation:(NSString *)shareUuid success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPConfirmDeviceSharingRelationApi *api = [[UPConfirmDeviceSharingRelationApi alloc] initWithShareUuid:shareUuid];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)cancelDeviceSharingRelation:(NSArray<NSString *> *)shareUuids success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPCancelDeviceSharingRelationApi *api = [[UPCancelDeviceSharingRelationApi alloc] initWithShareUuids:shareUuids];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)modifyAggregationSwitch:(NSArray<NSDictionary *> *)familyAgg
                         source:(NSString *)source
                        success:(userDomainCallback)success
                        failure:(userDomainCallback)failure
{
    UpEditAggregationSwitchApi *api = [[UpEditAggregationSwitchApi alloc] initWithSource:source familyAgg:familyAgg];
    api.responseParser = [[CommonTransformer alloc] init];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

@end
