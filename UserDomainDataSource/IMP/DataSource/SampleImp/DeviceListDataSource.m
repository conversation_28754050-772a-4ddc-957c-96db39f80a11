//
//  DeviceDataSource.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "DeviceListDataSource.h"
#import "UPDeviceListApi.h"
#import "DeviceListTransformer.h"
#import "UserDomainSampleResult.h"
#import "DataSourceCallbackFunctions.h"
#import "UPDeviceUpdateNameApi.h"
#import "CommonTransformer.h"
#import "UPUpdateDeviceInfoApi.h"
#import "UPGetGroupDevicesApi.h"
#import "UPDeviceUpdateAndCheckNameApi.h"

@implementation DeviceListDataSource
- (void)queryDeviceList:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPDeviceListApi *api = [[UPDeviceListApi alloc] initWithfamilyId:nil];
    api.responseParser = [DeviceListTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)updateDeviceNameWithDeviceId:(NSString *)deviceId oldName:(NSString *)oldName newName:(NSString *)newName familyId:(NSString *)familyId deviceNetType:(NSString *)deviceNetType prodNo:(NSString *)prodNo success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPDeviceUpdateNameApi *api = [[UPDeviceUpdateNameApi alloc] initWithDeviceId:deviceId oldName:oldName newName:newName familyId:familyId deviceNetType:deviceNetType prodNo:prodNo];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)updateDeviceInfo:(NSDictionary<NSString *, NSString *> *)deviceInfo success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUpdateDeviceInfoApi *api = [[UPUpdateDeviceInfoApi alloc] initWithDeviceInfo:deviceInfo];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)getGroupDeviceList:(NSString *)familyId filterFlag:(BOOL)filterFlag success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPGetGroupDevicesApi *api = [[UPGetGroupDevicesApi alloc] initWithfamilyId:familyId filterFlag:filterFlag];
    api.responseParser = [DeviceListTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)updateDeviceNameWithDeviceId:(NSString *)deviceId oldName:(NSString *)oldName newName:(NSString *)newName familyId:(NSString *)familyId type:(NSString *)type checkLevel:(BOOL)checkLevel success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPDeviceUpdateAndCheckNameApi *api = [[UPDeviceUpdateAndCheckNameApi alloc] initWithDeviceId:deviceId oldName:oldName newName:newName familyId:familyId type:type checkLevel:checkLevel];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
@end
