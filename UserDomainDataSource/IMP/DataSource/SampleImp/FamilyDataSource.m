//
//  FamilyDataSource.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "FamilyDataSource.h"
#import "UPFamilyListApi.h"
#import "DataSourceCallbackFunctions.h"
#import "CommonTransformer.h"
#import "FamilyListTransformer.h"
#import "UPFamilyInfoApi.h"
#import "FamilyInfoTransformer.h"
#import "UPAddRoomToFamilyApi.h"
#import "UpAddRoomToFamilyNewApi.h"
#import "UPRemoveRoomFromFamilyApi.h"
#import "UPFamilyInviteMemberApi.h"
#import "UPChangeFamilyAdminApi.h"
#import "UPCreateFamilyApi.h"
#import "UPFamilyDeleteMemberApi.h"
#import "UPDestroyFamilyApi.h"
#import "UPQuitFamilyAsAdminApi.h"
#import "UPQuitFamilyAsMemberApi.h"
#import "UPFamilyRoomListApi.h"
#import "UPdateFamilyInfoApi.h"
#import "UPUpdateFamilyRoomNameApi.h"
#import "UPFamilyUnbindDevicesApi.h"
#import "BatchProcessDevicesOfFamilyTransformer.h"
#import "UPFamilyMoveDevicesToNewRoomApi.h"
#import "UPFamilyMoveDevicesToOtherFamilyApi.h"
#import "UPFamilyReplyApi.h"
#import "FamilyFirstMemberTransformer.h"
#import "FamilyRoomTransformer.h"
#import "UPFamilyRemoveDevicesApi.h"
#import "CreateFamilyTransformer.h"
#import "AddRoomTransformer.h"
#import "UPSetDefaultFamilyApi.h"
#import "Room.h"
#import "FamilyInfo.h"
#import "UDDeviceDelegate.h"
#import "FamilyArgs.h"
#import "CreateFamilyArgs.h"
#import "RoomArgs.h"
#import "RoomNewArgs.h"
#import "UPDeleteFloorApi.h"
#import "UPAddFloorApi.h"
#import "UPEditFloorApi.h"
#import "FloorArg.h"
#import "UPReplyJoinFamilyApi.h"
#import "UPQueryFirstMemeberApi.h"
#import "UPAddVirtualMemberApi.h"
#import "UPModifyVirtualMemberApi.h"
#import "QueryFirstMemebeTransformer.h"
#import "CommonDataTransformer.h"
#import "UPModifyMemberRoleApi.h"
#import "UPModifyMemberTypeApi.h"
#import "VirtualMemberArgs.h"
#import "UPModifyVirtualMemberRoleApi.h"
#import "UPSaveRoomsOrderApi.h"
#import "UpEditDeviceCardStatusApi.h"
#import "UpEditDeviceAggregationApi.h"
#import "AdminInviteMemberArgs.h"
#import "UpSaveRoomsOrderNewApi.h"

@implementation FamilyDataSource
- (void)queryFamilyListsuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPFamilyListApi *api = [[UPFamilyListApi alloc] init];
    api.responseParser = [FamilyListTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)queryFamilyInfo:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPFamilyInfoApi *api = [[UPFamilyInfoApi alloc] initWithFamilyId:familyId];
    api.responseParser = [FamilyInfoTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)queryFirstMemeber:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPQueryFirstMemeberApi *api = [[UPQueryFirstMemeberApi alloc] initWithFamilyId:familyId];
    api.responseParser = [QueryFirstMemebeTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)addVirtualMember:(NSString *)familyId memberId:(NSString *)memberId memberName:(NSString *)memberName success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPAddVirtualMemberApi *api = [[UPAddVirtualMemberApi alloc] initWithFamilyId:familyId memberId:memberId memberName:memberName];
    api.responseParser = [CommonDataTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)modifyVirtualMember:(VirtualMemberArgs *)virtualMemberArgs success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPModifyVirtualMemberApi *api = [[UPModifyVirtualMemberApi alloc] initWithVirtualMemberId:virtualMemberArgs.memberId memberName:virtualMemberArgs.memberName avatarUrl:virtualMemberArgs.avatarUrl isCreater:virtualMemberArgs.isCreater birthday:virtualMemberArgs.birthday];
    api.responseParser = [CommonDataTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)addRoomForFamily:(RoomArgs *)roomInfo familyId:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPAddRoomToFamilyApi *api = [[UPAddRoomToFamilyApi alloc] initWithfamilyId:familyId roomName:roomInfo.name roomClass:roomInfo.type roomLabel:roomInfo.label roomLogo:roomInfo.logo roomPicture:roomInfo.image floorId:roomInfo.floorId];
    api.responseParser = [AddRoomTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)addRoomForFamilyNew:(RoomNewArgs *)roomArgs familyId:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UpAddRoomToFamilyNewApi *api = [[UpAddRoomToFamilyNewApi alloc] initWithFamilyId:familyId roomName:roomArgs.roomName roomClass:roomArgs.roomClass floorOrderId:roomArgs.floorOrderId];
    api.responseParser = [AddRoomTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)adminInvitateMember:(AdminInviteMemberArgs *)args familyId:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPFamilyInviteMemberApi *api = [[UPFamilyInviteMemberApi alloc] initWithUserId:args.userId nickname:args.nickname memberRole:args.memberRole memberType:args.memberType familyId:familyId];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}


- (void)changeFamilyAdmin:(nonnull NSString *)familyId userId:(nonnull NSString *)userId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPChangeFamilyAdminApi *api = [[UPChangeFamilyAdminApi alloc] initWithfamilyId:familyId userId:userId];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)createFamily:(CreateFamilyArgs *)familyArgs success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPCreateFamilyApi *api = [[UPCreateFamilyApi alloc] initWithfamilyName:familyArgs.name familyPosition:familyArgs.position longitude:familyArgs.longitude latitude:familyArgs.latitude cityCode:familyArgs.cityCode rooms:familyArgs.roomNames];
    api.responseParser = [CreateFamilyTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)deleteFamilyMemberAsAdmin:(nonnull NSString *)familyId memberId:(nonnull NSString *)memberId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPFamilyDeleteMemberApi *api = [[UPFamilyDeleteMemberApi alloc] initWithUserId:memberId familyId:familyId];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)destoryFamilyAsAdmin:(nonnull NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPDestroyFamilyApi *api = [[UPDestroyFamilyApi alloc] initWithFamilyId:familyId];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)exitFamilyAsAdmin:(nonnull NSString *)familyId userId:(nonnull NSString *)userId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPQuitFamilyAsAdminApi *api = [[UPQuitFamilyAsAdminApi alloc] initWithfamilyId:familyId userId:userId];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)exitFamilyAsMember:(nonnull NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPQuitFamilyAsMemberApi *api = [[UPQuitFamilyAsMemberApi alloc] initWithfamilyId:familyId];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)queryRoomListOfFamily:(nonnull NSString *)familyId floorId:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPFamilyRoomListApi *api = [[UPFamilyRoomListApi alloc] initWithFamilyId:familyId floorId:floorId];
    api.responseParser = [FamilyRoomTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)removeRoomFromFamily:(nonnull NSString *)familyId roomId:(nonnull NSString *)roomId success:(userDomainCallback)success failure:(userDomainCallback)failure
{

    UPRemoveRoomFromFamilyApi *api = [[UPRemoveRoomFromFamilyApi alloc] initWithFamilyId:familyId roomId:roomId];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)updateFamilyInfo:(FamilyArgs *)familyArgs familyId:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPdateFamilyInfoApi *api = [[UPdateFamilyInfoApi alloc] initWithfamilyId:familyId familyName:familyArgs.name familyPosition:familyArgs.position longitude:familyArgs.longitude latitude:familyArgs.latitude cityCode:familyArgs.cityCode];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)updateFamilyRoomName:(nonnull NSString *)familyId roomId:(nonnull NSString *)roomId roomName:(nonnull NSString *)roomName floorId:(nonnull NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPUpdateFamilyRoomNameApi *api = [[UPUpdateFamilyRoomNameApi alloc] initWithRoomName:roomName familyId:familyId roomId:roomId floorId:floorId];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)unBindFamilyDevices:(NSString *)familyId devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSMutableArray *unbindDevices = [NSMutableArray array];
    NSMutableArray *shareDevices = [NSMutableArray array];
    [devices enumerateObjectsUsingBlock:^(id<UDDeviceDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (obj.familyId.length) {
          [unbindDevices addObject:@{ @"deviceId" : obj.deviceId ?: @"",
                                      @"deviceName" : obj.deviceName ?: @"",
                                      @"barCode" : obj.barcode ?: @"",
                                      @"deviceNetType" : obj.deviceNetType ?: @"",
                                      @"parentsDeviceId" : obj.parentId ?: @""
          }];
      }
      else {
          [shareDevices addObject:@{ @"deviceId" : obj.deviceId ?: @"",
                                     @"deviceRole" : obj.deviceRole ?: @"",
                                     @"deviceName" : obj.deviceName ?: @""

          }];
      }
    }];
    UPFamilyUnbindDevicesApi *api = [[UPFamilyUnbindDevicesApi alloc] initWithfamilyId:familyId unbindDevices:unbindDevices shareDevices:shareDevices];
    api.responseParser = [BatchProcessDevicesOfFamilyTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)moveDevicesToOtherRoom:(NSString *)familyId newRoom:(id<UDRoomDelegate>)newRoom devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSMutableArray *dev = [NSMutableArray array];
    [devices enumerateObjectsUsingBlock:^(id<UDDeviceDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [dev addObject:@{ @"deviceId" : obj.deviceId ?: @"",
                        @"deviceName" : obj.deviceName ?: @"",
                        @"deviceNetType" : obj.deviceNetType ?: @"",
                        @"oldRoom" : obj.roomName ?: @"",
                        @"oldRoomId" : obj.roomId ?: @""
      }];
    }];
    UPFamilyMoveDevicesToNewRoomApi *api = [[UPFamilyMoveDevicesToNewRoomApi alloc] initWithFamilyId:familyId deviceIds:dev newRoomName:newRoom.roomName newRoomId:newRoom.roomId];
    api.responseParser = [BatchProcessDevicesOfFamilyTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)removeDevicesFromFamily:(NSString *)familyId devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSMutableArray *dev = [NSMutableArray array];
    [devices enumerateObjectsUsingBlock:^(id<UDDeviceDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [dev addObject:@{ @"deviceId" : obj.deviceId ?: @"",
                        @"deviceName" : obj.deviceName ?: @"",
                        @"deviceNetType" : obj.deviceNetType ?: @"",
                        @"room" : obj.roomName ?: @""
      }];
    }];
    UPFamilyRemoveDevicesApi *api = [[UPFamilyRemoveDevicesApi alloc] initWithfamilyId:familyId deviceIds:dev];
    api.responseParser = [BatchProcessDevicesOfFamilyTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)moveDevicesToOtherFamily:(NSString *)newFamilyId oldFamilyId:(NSString *)oldFamilyId devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSMutableArray *dev = [NSMutableArray array];
    [devices enumerateObjectsUsingBlock:^(id<UDDeviceDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [dev addObject:@{ @"deviceId" : obj.deviceId ?: @"",
                        @"deviceName" : obj.deviceName ?: @"",
                        @"deviceNetType" : obj.deviceNetType ?: @"",
                        @"room" : obj.roomName ?: @"",
                        @"parentsDeviceId" : obj.parentId ?: @""
      }];
    }];
    UPFamilyMoveDevicesToOtherFamilyApi *api = [[UPFamilyMoveDevicesToOtherFamilyApi alloc] initWithDeviceIds:dev oldFamilyId:oldFamilyId newFamilyId:newFamilyId];
    api.responseParser = [BatchProcessDevicesOfFamilyTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)replyFamilyInviteWithCode:(NSString *)inviteCode familyId:(NSString *)familyId memberName:(NSString *)memberName agree:(BOOL)agree success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPFamilyReplyApi *api = [[UPFamilyReplyApi alloc] initWithinviteCode:inviteCode familyId:familyId memberName:memberName agree:agree];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)replyJoinFamily:(NSString *)applicationId agree:(BOOL)agree success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPReplyJoinFamilyApi *api = [[UPReplyJoinFamilyApi alloc] initWithApplicationId:applicationId agree:agree];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)setDefaultFamily:(NSString *)familyId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPSetDefaultFamilyApi *api = [[UPSetDefaultFamilyApi alloc] initWithFamilyId:familyId];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)addFloorOfFamily:(NSString *)familyId floorArg:(FloorArg *)floorArg success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPAddFloorApi *api = [[UPAddFloorApi alloc] initWithfamilyId:familyId floorOrderId:floorArg.floorOrderId floorName:floorArg.floorName floorLabel:floorArg.floorLabel floorLogo:floorArg.floorLogo floorPicture:floorArg.floorPicture];
    api.responseParser = [CommonDataTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)editFloorOfFamily:(NSString *)familyId floorArg:(FloorArg *)floorArg success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPEditFloorApi *api = [[UPEditFloorApi alloc] initWithfamilyId:familyId floorOrderId:floorArg.floorOrderId floorName:floorArg.floorName floorLabel:floorArg.floorLabel floorLogo:floorArg.floorLogo floorPicture:floorArg.floorPicture floorId:floorArg.floorId];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}
- (void)deleteFloorOfFamily:(NSString *)familyId floorId:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPDeleteFloorApi *api = [[UPDeleteFloorApi alloc] initWithFloorId:floorId familyId:familyId];
    api.responseParser = [CommonTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)modifyMemberRoleOfFamily:(NSString *)familyId memberId:(NSString *)memberId memberRole:(NSString *)memberRole success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPModifyMemberRoleApi *api = [[UPModifyMemberRoleApi alloc] initWithMemberId:memberId familyId:familyId memberRole:memberRole];
    api.responseParser = [CommonDataTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)modifyMemberTypeOfFamily:(NSString *)familyId memberId:(NSString *)memberId memberType:(NSInteger)memberType success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPModifyMemberTypeApi *api = [[UPModifyMemberTypeApi alloc] initWithMemberId:memberId familyId:familyId memberType:memberType];
    api.responseParser = [CommonDataTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)modifyVirtualMemberRoleOfFamily:(NSString *)familyId memberId:(NSString *)memberId memberRole:(NSString *)memberRole success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPModifyVirtualMemberRoleApi *api = [[UPModifyVirtualMemberRoleApi alloc] initWithVirtualMemberId:memberId familyId:familyId memberRole:memberRole];
    api.responseParser = [CommonDataTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)saveRoomsOrderOfFamily:(NSString *)familyId rooms:(NSArray<NSString *> *)rooms floorId:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPSaveRoomsOrderApi *api = [[UPSaveRoomsOrderApi alloc] initWithFamilyId:familyId rooms:rooms floorId:floorId];
    api.responseParser = [CommonDataTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)saveRoomsOrderNewForFamily:(NSString *)familyId sortList:(NSArray<NSDictionary *> *)sortList success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPSaveRoomsOrderNewApi *api = [[UPSaveRoomsOrderNewApi alloc] initWithFamilyId:familyId sortList:sortList];
    api.responseParser = [CommonDataTransformer new];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)modifyDeviceCardList:(NSString *)familyId
                   orderList:(NSArray<NSString *> *)orderList
                 bigCardList:(NSArray<NSString *> *)bigCardList
              middleCardList:(nonnull NSArray<NSString *> *)middleCardList
               smallCardList:(nonnull NSArray<NSString *> *)smallCardList
                     success:(nonnull userDomainCallback)success
                     failure:(nonnull userDomainCallback)failure
{
    UpEditDeviceCardStatusApi *api = [[UpEditDeviceCardStatusApi alloc] initWithFamilyId:familyId orderList:orderList bigCardList:bigCardList middleCardList:middleCardList smallCardList:smallCardList];
    api.responseParser = [[CommonDataTransformer alloc] init];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

- (void)modifyDeviceAggregation:(NSString *)familyId
               aggregationCards:(NSArray<NSDictionary *> *)aggCards
                        success:(userDomainCallback)success
                        failure:(userDomainCallback)failure
{

    UpEditDeviceAggregationApi *api = [[UpEditDeviceAggregationApi alloc] initWithFamilyId:familyId aggregationCards:aggCards];
    api.responseParser = [[CommonDataTransformer alloc] init];
    [DataSourceCallbackFunctions startApi:api success:success failure:failure];
}

@end
