//
//  DataSourceCallbackFunctions.h
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import <Foundation/Foundation.h>
#import <upnetwork/UPRequest.h>
#import "UserDomainSampleResult.h"

NS_ASSUME_NONNULL_BEGIN

@interface DataSourceCallbackFunctions : NSObject

/*
 统一datasource启动Api 和处理回调
 @param api API对象
 @param success 成功回调
 @param failure 失败回调
 */
+ (void)startApi:(UPRequest *)api success:(userDomainCallback __nullable)success failure:(userDomainCallback __nullable)failure;

@end

NS_ASSUME_NONNULL_END
