//
//  DataSourceCallbackFunctions.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "DataSourceCallbackFunctions.h"
@implementation DataSourceCallbackFunctions
#pragma mark - Public Methods
+ (void)startApi:(UPRequest *)api success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPRequestSuccess successBlock = ^(NSObject *_Nonnull responseObject) {
      UserDomainSampleResult *result = responseObject;
      if (result.success) {
          if (success) {
              success(result);
          }
          return;
      }
      if (failure) {
          failure(result);
      }
    };
    UPRequestFailure failureBlock = ^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
      UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
      if (failure) {
          failure(result);
      }
    };
    [api startRequestWithSuccess:successBlock failure:failureBlock];
}

@end
