//
//  CloudOauthData.h
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/12.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface CloudOauthData : NSObject
@property (strong, nonatomic) NSString *access_token;
@property (strong, nonatomic) NSString *expires_in;
@property (strong, nonatomic) NSString *refresh_token;
@property (strong, nonatomic) NSString *scope;
@property (strong, nonatomic) NSString *token_type;
@property (strong, nonatomic) NSString *uhome_access_token;
@property (strong, nonatomic) NSString *uhome_user_id;
@end

NS_ASSUME_NONNULL_END
