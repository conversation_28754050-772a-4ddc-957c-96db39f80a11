//
//  CloudUserAddress.h
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface CloudUserAddress : NSObject
@property (copy, nonatomic) NSString *city;

@property (copy, nonatomic) NSString *city_id;

@property (copy, nonatomic) NSString *country_code;

@property (copy, nonatomic) NSString *district;

@property (copy, nonatomic) NSString *district_id;

@property (copy, nonatomic) NSString *line1;

@property (copy, nonatomic) NSString *line2;

@property (copy, nonatomic) NSString *postcode;

@property (copy, nonatomic) NSString *province;

@property (copy, nonatomic) NSString *province_id;

@property (copy, nonatomic) NSString *town;

@property (copy, nonatomic) NSString *town_id;
@end

@interface CloudUserAddressInfo : NSObject
@property (strong, nonatomic) CloudUserAddress *address;
@property (copy, nonatomic) NSString *email;
@property (copy, nonatomic) NSString *addressId;
@property (assign, nonatomic) BOOL is_default;
@property (assign, nonatomic) BOOL is_service;
@property (copy, nonatomic) NSString *receiver_mobile;
@property (copy, nonatomic) NSString *receiver_name;
@property (copy, nonatomic) NSString *source;
@property (copy, nonatomic) NSString *tag;
@property (copy, nonatomic) NSString *user_id;
@end

NS_ASSUME_NONNULL_END
