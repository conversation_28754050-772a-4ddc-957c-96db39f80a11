//
//  CloudUserInfo.h
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import <Foundation/Foundation.h>
#import "CloudUserAddressInfo.h"
NS_ASSUME_NONNULL_BEGIN

@interface CloudUserInfo : NSObject

@property (copy, nonatomic) NSString *userId;

@property (copy, nonatomic) NSString *mobile;

@property (copy, nonatomic) NSString *email;

@property (copy, nonatomic) NSString *username;

@property (copy, nonatomic) NSString *givenName;

@property (copy, nonatomic) NSString *nickname;

@property (copy, nonatomic) NSString *familyNum;

@property (copy, nonatomic) NSString *gender;

@property (copy, nonatomic) NSString *marriage;

@property (copy, nonatomic) NSString *birthday;

@property (copy, nonatomic) NSString *education;

@property (copy, nonatomic) NSString *avatarUrl;

@property (copy, nonatomic) NSString *extraPhone;

@property (copy, nonatomic) NSString *income;

@property (copy, nonatomic) NSString *height;

@property (copy, nonatomic) NSString *weight;

/**
 国家编码
 **/
@property (nonatomic, copy) NSString *countryCode;

@property (strong, nonatomic) CloudUserAddressInfo *address;
@end

NS_ASSUME_NONNULL_END
