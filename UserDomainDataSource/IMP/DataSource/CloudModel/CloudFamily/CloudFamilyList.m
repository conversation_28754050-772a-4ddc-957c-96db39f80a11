//
//  CloudFamilyList.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "CloudFamilyList.h"
#import <MJExtension/MJExtension.h>

@implementation CloudFamilyList
+ (NSDictionary *)mj_objectClassInArray
{
    return @{
        @"createfamilies" : CloudFamily.class,
        @"joinfamilies" : CloudFamily.class
    };
}

+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"createfamilies"]) {
          [property.type setValue:NSClassFromString(@"NSArray") forKey:@"typeClass"];
      }
      if ([property.name isEqualToString:@"joinfamilies"]) {
          [property.type setValue:NSClassFromString(@"NSArray") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}
@end
