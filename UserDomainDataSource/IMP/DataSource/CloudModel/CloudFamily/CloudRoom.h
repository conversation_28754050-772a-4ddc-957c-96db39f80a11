//
//  CloudRoom.h
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/11.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface CloudRoom : NSObject
@property (strong, nonatomic) NSString *roomId;

@property (strong, nonatomic) NSString *roomName;

@property (strong, nonatomic) NSString *roomClass;

@property (strong, nonatomic) NSString *roomLabel;

@property (strong, nonatomic) NSString *roomLogo;

@property (strong, nonatomic) NSString *roomPicture;

@property (strong, nonatomic) NSString *sortCode;
@end

NS_ASSUME_NONNULL_END
