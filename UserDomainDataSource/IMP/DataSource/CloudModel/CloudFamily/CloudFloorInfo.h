//
//  CloudFloorInfo.h
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/3.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "CloudRoom.h"
NS_ASSUME_NONNULL_BEGIN

@interface CloudFloorInfo : NSObject
/**
 楼层名称
 */
@property (nonatomic, copy, readonly) NSString *floorName;
/**
 楼层Id
 */
@property (nonatomic, copy, readonly) NSString *floorId;
/**
 楼层次序（-3到5，没有0）
 */
@property (nonatomic, copy, readonly) NSString *floorOrderId;
/**
 楼层类型
 */
@property (nonatomic, copy, readonly) NSString *floorClass;
/**
 楼层标签
 */
@property (nonatomic, copy, readonly) NSString *floorLabel;
/**
 楼层logo url
 */
@property (nonatomic, copy, readonly) NSString *floorLogo;
/**
 楼层图片 url
 */
@property (nonatomic, copy, readonly) NSString *floorPicture;
/**
 楼层创建时间
 */
@property (nonatomic, copy, readonly) NSString *floorCreateTime;
/**
 房间列表
 */
@property (nonatomic, strong, readonly) NSArray<CloudRoom *> *rooms;
@end

NS_ASSUME_NONNULL_END
