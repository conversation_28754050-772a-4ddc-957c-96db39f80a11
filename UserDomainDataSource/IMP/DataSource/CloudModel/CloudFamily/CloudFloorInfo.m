//
//  CloudFloorInfo.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/3.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "CloudFloorInfo.h"
#import <MJExtension/MJExtension.h>
@implementation CloudFloorInfo
+ (NSDictionary *)mj_objectClassInArray
{

    return @{ @"rooms" : [CloudRoom class]
    };
}
+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"rooms"]) {
          [property.type setValue:NSClassFromString(@"NSArray") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}
@end
