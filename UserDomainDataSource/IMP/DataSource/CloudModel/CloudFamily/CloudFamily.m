//
//  CloudFamily.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "CloudFamily.h"
#import <MJExtension/MJExtension.h>
@implementation CloudFamily
+ (NSDictionary *)mj_objectClassInArray
{

    return @{ @"familyMembers" : [CloudFamilyMember class],
              @"floorInfos" : CloudFloorInfo.class
    };
}
+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"familyMembers"]) {
          [property.type setValue:NSClassFromString(@"NSArray") forKey:@"typeClass"];
      }
      if ([property.name isEqualToString:@"floorInfos"]) {
          [property.type setValue:NSClassFromString(@"NSArray") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}
@end
