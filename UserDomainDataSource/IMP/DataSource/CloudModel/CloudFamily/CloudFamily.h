//
//  CloudFamily.h
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import <Foundation/Foundation.h>
#import "CloudFamilyMemberInfo.h"
#import "CloudFamilyLocation.h"
#import "CloudFloorInfo.h"
#import "CloudFamilyMember.h"
NS_ASSUME_NONNULL_BEGIN

@interface CloudFamily : NSObject
@property (copy, nonatomic) NSString *appId;

@property (copy, nonatomic) NSString *createTime;

@property (copy, nonatomic) NSString *familyId;

@property (copy, nonatomic) NSString *familyName;

@property (strong, nonatomic) CloudFamilyMemberInfo *familyOwnerInfo;

@property (strong, nonatomic) NSArray<CloudFamilyMember *> *familyMembers;

@property (strong, nonatomic) NSArray<CloudFloorInfo *> *floorInfos;

@property (copy, nonatomic) NSString *familyOwner;

@property (copy, nonatomic) NSString *familyPosition;

@property (copy, nonatomic) NSString *locationChangeFlag;

@property (strong, nonatomic) CloudFamilyLocation *familyLocation;

@property (copy, nonatomic) NSString *isDefault;
@property (copy, nonatomic) NSString *familyDeviceCount;

@end

NS_ASSUME_NONNULL_END
