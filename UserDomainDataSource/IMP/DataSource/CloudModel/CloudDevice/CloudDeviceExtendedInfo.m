//
//  CloudDeviceExtendedInfo.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "CloudDeviceExtendedInfo.h"
#import <MJExtension/MJExtension.h>
@implementation CloudDeviceExtendedInfo
+ (NSDictionary *)mj_objectClassInArray
{

    return @{
        @"shareDeviceCardInfo" : [CloudShareDeviceCardInfo class]
    };
}
+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"shareDeviceCardInfo"]) {
          [property.type setValue:NSClassFromString(@"NSArray") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}
@end
