//
//  CloudRemoveFamilyDevicesResponseDeviceModel.h
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface CloudBatchProcessDevice : NSObject
@property (strong, nonatomic) NSString *deviceId;
@property (strong, nonatomic) NSString *deviceName;

/**
 reasonCode定义：
 1801000 用户绑定了三方授权
 1801001 设备绑定了支付业务
 1801002 设备绑定了三方授权
 1801003 设备是高安全设备
 1801004 设备是个人属性设备
 */
@property (strong, nonatomic) NSString *reasonCode;
@end

@interface CloudBatchProcessDevicesResponse : NSObject
@property (strong, nonatomic) NSArray<CloudBatchProcessDevice *> *successDevices;
@property (strong, nonatomic) NSArray<CloudBatchProcessDevice *> *failureDevices;
@end

NS_ASSUME_NONNULL_END
