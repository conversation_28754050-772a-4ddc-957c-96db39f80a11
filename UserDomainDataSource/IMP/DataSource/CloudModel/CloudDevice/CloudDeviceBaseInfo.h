//
//  CloudDeviceBaseInfo.h
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import <Foundation/Foundation.h>
#import "CloudDeviceOwnerInfo.h"
#import "CloudDevicePermission.h"
NS_ASSUME_NONNULL_BEGIN

@interface CloudDeviceBaseInfo : NSObject
@property (copy, nonatomic) NSString *deviceId;

@property (copy, nonatomic) NSString *deviceName;

@property (copy, nonatomic) NSString *devName;

@property (copy, nonatomic) NSString *deviceType;

@property (copy, nonatomic) NSString *familyId;

@property (nonatomic, copy) NSString *ownerId;

@property (strong, nonatomic) CloudDevicePermission *permission;

@property (copy, nonatomic) NSString *wifiType;

@property (nonatomic, copy) NSString *deviceNetType;

@property (assign, nonatomic) BOOL isOnline;

@property (strong, nonatomic) CloudDeviceOwnerInfo *ownerInfo;
/**
 子设备ID列表
 */
@property (nonatomic, strong) NSArray *subDeviceIds;
/**
 父设备ID
 */
@property (nonatomic, copy) NSString *parentsDeviceId;
/**
 1 普通设备; 2 网关设备; 3 附件设备; 4 子设备;
 */
@property (nonatomic, copy) NSString *deviceRole;
/**
 主设备/子设备/无主从关系，0 无主设备，1 主设备，2 子设备
 */
@property (nonatomic, copy) NSString *deviceRoleType;
/**
设备绑定时间
*/
@property (copy, nonatomic) NSString *bindTime;
/**
 组设备ID
*/
@property (nonatomic, copy) NSString *deviceGroupId;
/**
 组设备类型
*/
@property (nonatomic, copy) NSString *deviceGroupType;
@end

NS_ASSUME_NONNULL_END
