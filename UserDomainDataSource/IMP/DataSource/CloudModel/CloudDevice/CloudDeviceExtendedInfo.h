//
//  CloudDeviceExtendedInfo.h
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import <Foundation/Foundation.h>
#import "CloudShareDeviceCardInfo.h"
NS_ASSUME_NONNULL_BEGIN
@interface CloudDeviceExtendedInfo : NSObject
@property (copy, nonatomic) NSString *apptypeName;
@property (copy, nonatomic) NSString *apptypeCode;
@property (nonatomic, copy) NSString *categoryGrouping;

@property (copy, nonatomic) NSString *barcode;

@property (nonatomic, copy) NSString *bindType;

@property (copy, nonatomic) NSString *brand;

@property (copy, nonatomic) NSString *imageAddr1;

@property (copy, nonatomic) NSString *imageAddr2;

@property (nonatomic, copy) NSString *cardPageImg;

@property (nonatomic, assign) NSInteger cardSort;
@property (nonatomic, assign) NSInteger cardStatus;
@property (nonatomic, copy) NSString *aggregationParentId;
@property (nonatomic, assign) NSInteger supportAggregationFlag;
@property (nonatomic, copy) NSString *deviceAggregateType;

@property (copy, nonatomic) NSString *model;

@property (copy, nonatomic) NSString *prodNo;

@property (copy, nonatomic) NSString *room;

@property (nonatomic, copy) NSString *roomId;

@property (nonatomic, copy) NSString *accessType;

@property (nonatomic, copy) NSString *configType;
@property (nonatomic, copy) NSString *comunicationMode;
/**
 楼层ID
 */
@property (nonatomic, copy) NSString *devFloorId;
/**
 在家庭中的楼层序列（-3到5，没有0）
 */
@property (nonatomic, copy) NSString *devFloorOrderId;
/**
 楼层名
 */
@property (nonatomic, copy) NSString *devFloorName;
/**
 应用分类图片
 */
@property (nonatomic, copy) NSString *apptypeIcon;
/**
 0:保活  1：非保活
 */
@property (nonatomic, assign) NSInteger noKeepAlive;
/**
 二级应用分组
 */
@property (nonatomic, copy) NSString *twoGroupingName;
/**
 二级应用分组
 */
@property (nonatomic, strong) NSArray<CloudShareDeviceCardInfo *> *shareDeviceCardInfo;
@property (assign, nonatomic) BOOL sharedDeviceFlag;
/**
 按照品类是否支持   true：支持  false:不支持
 */
@property (assign, nonatomic) BOOL deviceShareSupportFlag;

/**
 附件设备排序码
 */
@property (assign, nonatomic) NSInteger attachmentSortCode;

/**
 首页展示状态：0:未设置过 1:首页展示 2:首页隐藏
 */
@property (assign, nonatomic) NSInteger displayedInHomePage;

/**
 是否支持二次绑定.0-不支持,1-支持
 */
@property (assign, nonatomic) NSNumber *rebind;

@end

NS_ASSUME_NONNULL_END
