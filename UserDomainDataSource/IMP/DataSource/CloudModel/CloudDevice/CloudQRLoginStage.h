//
//  CloudQRLoginStage.h
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/17.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import <Foundation/Foundation.h>
typedef NS_ENUM(NSInteger, CloudQRStage) {
    /**
     *二维码被创建(或刷新)    stage = 0    持续轮询
     */
    CloudQRStageCreate = 0,
    /**
     * 手机端第一次扫过二维码    stage = 1    异步刷新页面提示用户手机端确认登录，并持续轮询
     */
    CloudQRStageDidScan = 1,
    /**
     * 手机端点击确认登录    stage = 2    根据并随stage提供的token获取信息并写登录状态
     */
    CloudQRStageConfirmLogin = 2,
    /**
     *手机端点击取消登录    stage = 3    刷新二维码，持续轮询，手机端扫了一个过期码后点击重新扫码    stage=3    刷新二维码，持续轮询
     */
    CloudQRStageCancleLoginOrOverdue = 3,
};
NS_ASSUME_NONNULL_BEGIN

@interface CloudQRLoginStage : NSObject
@property (nonatomic, assign) CloudQRStage stage;
@property (nonatomic, strong) NSString *access_token;
@property (nonatomic, strong) NSString *expires_in;
@property (nonatomic, strong) NSString *scope;
@property (nonatomic, strong) NSString *token_type;
@property (nonatomic, strong) NSString *refresh_token;
@end

NS_ASSUME_NONNULL_END
