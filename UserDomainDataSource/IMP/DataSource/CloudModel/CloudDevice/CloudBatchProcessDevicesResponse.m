//
//  CloudRemoveFamilyDevicesResponseDeviceModel.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "CloudBatchProcessDevicesResponse.h"
#import <MJExtension/MJExtension.h>

@implementation CloudBatchProcessDevice

@end

@implementation CloudBatchProcessDevicesResponse
+ (NSDictionary *)mj_objectClassInArray
{

    return @{
        @"successDevices" : [CloudBatchProcessDevice class],
        @"failureDevices" : [CloudBatchProcessDevice class]
    };
}

+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"successDevices"]) {
          [property.type setValue:NSClassFromString(@"NSArray") forKey:@"typeClass"];
      }
      if ([property.name isEqualToString:@"failureDevices"]) {
          [property.type setValue:NSClassFromString(@"NSArray") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}
@end
