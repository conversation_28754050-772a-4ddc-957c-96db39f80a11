//
//  CloudDeviceBaseInfo.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "CloudDeviceBaseInfo.h"

@implementation CloudDeviceBaseInfo
- (NSArray *)subDeviceIds
{
    if (!_subDeviceIds) {
        return @[];
    }
    return _subDeviceIds;
}
- (NSString *)parentsDeviceId
{
    if (!_parentsDeviceId) {
        return @"";
    }
    return _parentsDeviceId;
}
- (NSString *)deviceRole
{
    if (!_deviceRole) {
        return @"";
    }
    return _deviceRole;
}
- (NSString *)deviceRoleType
{
    if (!_deviceRoleType) {
        return @"";
    }
    return _deviceRoleType;
}
@end
