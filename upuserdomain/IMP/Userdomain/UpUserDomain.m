//
//  UpUserDomain.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpUserDomain.h"
#import "ApplicationOauthData.h"
#import "UpUserDomainObserver.h"
#import <uplog/UPLog.h>
#import "UDOperatorManager.h"
#import "UpEventHandlerManagerDelegate.h"
#import "UDOperator.h"
#import "UpUserDomainStore.h"
#import "UpUserStore.h"
#import "UpFamilyStore.h"
#import "UpDeviceStore.h"
#import "UDRefreshTokenOp.h"
#import "User.h"
#import "UpEventHandlerManager.h"
#import "UpListenerTransformObserver.h"
#import "UDLogoutOp.h"
#import "UpEvent.h"
#import "UpUserDomainCacheDelegate.h"
#import "UDUserDomainCache.h"
#import "UPUserDomainSettings.h"
#import "ApplicationOauthData.h"
#import "ApplicationOauthData+PrivateExtension.h"
#import "UPTimeMillisDelegate.h"
#import <UIKit/UIKit.h>
@interface UpUserDomain ()
@property (nonatomic, strong) UpUserDomainStore *userDomainStore;
@property (nonatomic, strong) id<UDCache> cache;
@property (nonatomic, strong) id<UDUserDelegate> userDelegate;
@property (nonatomic, strong) UpListenerTransformObserver *transfer;
@property (nonatomic, strong) NSHashTable<id<UpUserDomainObserver>> *observers;
@property (nonatomic, strong) id<UpEventHandlerManagerDelegate> eventManager;
@property (nonatomic, strong) UDOperatorManager *operatorManager;
@property (nonatomic, strong) id<UpUserDataSource> userDataSource;
@property (nonatomic, strong) id<UpFamilyDataSource> familyDataSource;
@property (nonatomic, strong) id<UpDeviceListDataSource> deviceDataSource;
@property (nonatomic, strong) id<UPUserDomainSettingsDelegate> settings;
@property (nonatomic, strong) id<UPTimeMillisDelegate> timeDelegate;

@end

@implementation UpUserDomain

- (instancetype)initDomain:(id<UpUserDataSource>)userDataSource familyDataSource:(id<UpFamilyDataSource>)familyDataSource deviceDataSource:(id<UpDeviceListDataSource>)deviceDataSource cache:(id<UDCache>)cache time:(id<UPTimeMillisDelegate>)time
{
    if (self = [super init]) {
        _eventManager = [[UpEventHandlerManager alloc] initWithProvider:self];
        _userDataSource = userDataSource;
        _familyDataSource = familyDataSource;
        _deviceDataSource = deviceDataSource;
        _cache = cache;
        _timeDelegate = time;
        _settings = [[UPUserDomainSettings alloc] init];
        _transfer = [[UpListenerTransformObserver alloc] init];
        [_eventManager registerListener:_transfer];
        _operatorManager = [[UDOperatorManager alloc] initWithProvider:self];
        id<UpUserDomainCacheDelegate> userdomainCache = [[UDUserDomainCache alloc] initUserDomainCache:cache];
        _userDomainStore = [[UpUserDomainStore alloc] initUpUserDomainWithStoreCache:userdomainCache userDomainprovider:self];
        _userDelegate = [[User alloc] initUserWithProvider:self operateMamager:self.operatorManager];
        UpUserStore *userStore = [[UpUserStore alloc] initUpUserStoreWithStoreCache:userdomainCache userDomainprovider:self];
        UpDeviceStore *deviceStore = [[UpDeviceStore alloc] initUpDeviceStoreWithStoreCache:userdomainCache userDomainprovider:self];
        UpFamilyStore *familyStore = [[UpFamilyStore alloc] initUpFamilyStoreWithStoreCache:userdomainCache userDomainprovider:self];
        [self.userDomainStore setUserStore:userStore];
        [self.userDomainStore setFamilyStore:familyStore];
        [self.userDomainStore setDeviceStore:deviceStore];

        if (self.userDomainStore.loginState == UpUserDomainStateDidLogin) {
            [_userDomainStore loadDataWithCache];
        }

        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationWillEnterForegroundNotification:) name:UIApplicationWillEnterForegroundNotification object:nil];
    }
    return self;
}
- (void)applicationWillEnterForegroundNotification:(NSNotification *)info
{
    [self applicationWillEnterForegroundCheckRefreshToken];
}

#pragma mark UDPlanRefreshTokenTaskDelegate

- (void)planRefreshTokenTask:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSTimeInterval times = [self.userDomainStore caculatePlanRefreshTokenExpresIn];
    if (times > 10 * 60) {
        UPLogInfo(@"UPUserDomain", @"%s[%d]token过期还有%.f秒，无需刷新", __PRETTY_FUNCTION__, __LINE__, times);
        return;
    }
    UPLogInfo(@"UPUserDomain", @"%s[%d]token过期时长剩余不到10分钟,定时任务开始刷新token", __PRETTY_FUNCTION__, __LINE__);
    [self.operatorManager operateWithClass:UDRefreshTokenOp.class
                                      args:@{ @"token" : [self oauthData].refresh_token,
                                              @"isPlaned" : @(YES) }
                                   success:success
                                   failure:failure];
}
- (void)applicationWillEnterForegroundCheckRefreshToken
{
    NSTimeInterval times = [self.userDomainStore caculateOauthDataExpire];
    if (times > 10 * 60) {
        return;
    }
    UPLogInfo(@"UPUserDomain", @"%s[%d]从后台进入前台token过期时长剩余不到10分钟,需重新刷新token", __PRETTY_FUNCTION__, __LINE__);
    [self.operatorManager operateWithClass:UDRefreshTokenOp.class
                                      args:@{ @"token" : [self oauthData].refresh_token,
                                              @"isPlaned" : @(YES) }
                                   success:nil
                                   failure:nil];
}
#pragma mark - UpUserDomainProvider
- (id<UpUserDataSource>)provideUserDataSource
{
    return _userDataSource;
}

- (id<UpDeviceListDataSource>)provideDeviceDataSource
{
    return _deviceDataSource;
}

- (id<UpFamilyDataSource>)provideFamilyDataSource
{
    return _familyDataSource;
}

- (id<UpEventHandlerManagerDelegate>)provideEventManager
{
    return _eventManager;
}

- (UDOperatorManager *)provideOperatorManager
{
    return _operatorManager;
}

- (UpUserDomain *)provideUpUserDomain
{
    return self;
}

- (UpUserDomainStore *)provideUpUserDomainStore
{
    return _userDomainStore;
}
- (id<UPTimeMillisDelegate>)providerTimeDelegate
{
    return _timeDelegate;
}
#pragma mark - UpUserDomainDelegate
- (void)addObserver:(id<UpUserDomainObserver>)observer
{
    [self.transfer addObserver:observer];
}

- (void)removeObserver:(id<UpUserDomainObserver>)observer
{
    [self.transfer removeObserver:observer];
}

- (void)autoRefreshToken:(userDomainCallback)callback
{
    id<UDAuthDataDelegate> authData = [self oauthData];
    if (authData == nil) {
        if (callback) {
            UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
            result.retCode = kUserDomainInvalid_token;
            result.retInfo = @"无效token";
            callback(result);
        }
        return;
    }
    self.userDomainStore.loginState = UpUserDomainStateDidLogin;
    NSTimeInterval times = [self.userDomainStore caculateOauthDataExpire];
    if (times <= 10 * 60 || ![authData.uc_user_id isKindOfClass:NSString.class] || !authData.uc_user_id.length) {
        UPLogInfo(@"UPUserDomain", @"%s[%d]token过期时长剩余不到10分钟,需重新刷新token", __PRETTY_FUNCTION__, __LINE__);
        [self.operatorManager operateWithClass:[UDRefreshTokenOp class] args:authData.refresh_token success:callback failure:callback];
    }
    else {
        UPLogInfo(@"UPUserDomain", @"%s[%d]token有效，无需刷新服务器token", __func__, __LINE__);
        self.userDomainStore.loginState = UpUserDomainStateDidLogin;
        [self.eventManager sendEvent:NOTIFY_AUTH_DATA_REFRESHED_SUCCESS];
        [self.eventManager sendEvent:OPERATE_REFRESH_TOKEN_SCHEDULED_TASK];
        [self.eventManager sendEvent:OPERATE_REFRESH_USER];
        if (callback) {
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            result.retData = self.oauthData;
            callback(result);
        }
    }
}


- (void)autoRefreshToken
{
    [self autoRefreshToken:^(UserDomainSampleResult *_Nonnull result){

    }];
}

- (void)cancleLogin
{
    [self.eventManager sendEvent:NOTIFY_CANCEL_LOGIN];
}

- (id<UPUserDomainSettingsDelegate>)getSettings
{
    return _settings;
}

- (BOOL)isLogin
{
    return self.state == UpUserDomainStateDidLogin;
}

- (BOOL)isRefreshCompleted
{
    return self.userDomainStore.userStore.isRefreshUserCompleted && self.userDomainStore.familyStore.isRefreshFamilyListCompleted && self.userDomainStore.deviceStore.isRefreshDeviceListCompleted;
}

- (BOOL)isRefreshDeviceListCompleted
{
    return _userDomainStore.deviceStore.isRefreshDeviceListCompleted;
}

- (BOOL)isRefreshFamilyListCompleted
{
    return _userDomainStore.familyStore.isRefreshFamilyListCompleted;
}

- (BOOL)isRefreshUserCompleted
{
    return _userDomainStore.userStore.isRefreshUserCompleted;
}


- (void)logOut:(userDomainCallback)callback
{
    [self.eventManager sendEvent:NOTIFY_WILL_LOG_OUT];
    [self.operatorManager operateWithClass:[UDLogoutOp class] args:self.oauthData.uhome_access_token success:callback failure:callback];
}

- (id<UDAuthDataDelegate>)oauthData
{
    return self.userDomainStore.oauthData;
}

- (void)queryServiceOrder:(NSString *)phoneNumber success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self.user qrWorkOrderInfo:phoneNumber success:success failure:failure];
}

- (void)refreshUser:(userDomainCallback)callback
{
    [self refreshUser:YES callback:callback];
}

- (void)refreshUser:(BOOL)immediate callback:(userDomainCallback)callback
{
    [self.user refreshUser:immediate callback:callback];
}

- (UpUserDomainState)state
{
    return _userDomainStore.loginState;
}

- (BOOL)updateOauthData:(NSString *)access_token refreshToken:(NSString *)refreshToken uhome_access_token:(NSString *)uhome_access_token expires_in:(NSString *)expires_in scope:(NSString *)scope token_type:(NSString *)token_type uhome_user_id:(NSString *)uhome_user_id uc_user_id:(NSString *)uc_user_id
{
    ApplicationOauthData *oauthData = [[ApplicationOauthData alloc] init];
    oauthData.realAccess_token = access_token;
    oauthData.realRefresh_token = refreshToken;
    oauthData.realUhome_access_token = uhome_access_token;
    oauthData.realExpires_in = expires_in;
    oauthData.realScope = scope;
    oauthData.realToken_type = token_type;
    oauthData.realUhome_user_id = uhome_user_id;
    oauthData.realCreateTime = [NSDate dateWithTimeIntervalSince1970:self.timeDelegate.currentTimeMillis];
    oauthData.realUc_user_id = uc_user_id;
    BOOL isVerify = [self.userDomainStore verifyOauthData:oauthData];
    if (isVerify) {
        self.userDomainStore.oauthData = oauthData;
        [self.userDomainStore updateCacheOauthData:oauthData];
        UPLogInfo(@"UPUserDomain", @"%s[%d]用户的鉴权信息已更新至缓存中。", __PRETTY_FUNCTION__, __LINE__);
        [self.eventManager sendEvent:NOTIFY_AUTH_DATA_REFRESHED_SUCCESS];
        [self.eventManager sendEvent:OPERATE_REFRESH_TOKEN_SCHEDULED_TASK];
        [self.eventManager sendEvent:OPERATE_REFRESH_USER];
    }
    else {
        if ([uhome_user_id isEqualToString:self.userDomainStore.oauthData.uhome_user_id]) {
            [self.userDomainStore clearUserData];
        }
        self.userDomainStore.loginState = UpUserDomainStateUnLogin;
    }
    return isVerify;
}

- (BOOL)updateOauthDataWithCache:(NSString *)access_token refreshToken:(NSString *)refreshToken uhome_access_token:(NSString *)uhome_access_token expires_in:(NSString *)expires_in scope:(NSString *)scope token_type:(NSString *)token_type uhome_user_id:(NSString *)uhome_user_id
{
    ApplicationOauthData *oauthData = [[ApplicationOauthData alloc] init];
    oauthData.realAccess_token = access_token;
    oauthData.realRefresh_token = refreshToken;
    oauthData.realUhome_access_token = uhome_access_token;
    oauthData.realExpires_in = expires_in;
    oauthData.realScope = scope;
    oauthData.realToken_type = token_type;
    oauthData.realUhome_user_id = uhome_user_id;
    oauthData.realCreateTime = [NSDate dateWithTimeIntervalSince1970:self.timeDelegate.currentTimeMillis];
    BOOL isVerify = [self.userDomainStore verifyOauthData:oauthData];
    if (isVerify) {
        self.userDomainStore.oauthData = oauthData;
        [self.eventManager sendEvent:NOTIFY_CACHED_AUTH_DATA_LOADED];
    }
    else {
        if ([uhome_user_id isEqualToString:self.userDomainStore.oauthData.uhome_user_id]) {
            [self.userDomainStore clearUserData];
        }
        self.userDomainStore.loginState = UpUserDomainStateUnLogin;
    }
    return isVerify;
}
- (id<UDUserDelegate>)user
{
    return _userDelegate;
}

@end
