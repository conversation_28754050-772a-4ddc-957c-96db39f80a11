//
//  UpUserDomain.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpUserDomainDelegate.h"
#import "UpUserDomainProvider.h"
@protocol UDCache
, UpUserDataSource, UpFamilyDataSource, UpDeviceListDataSource, UPTimeMillisDelegate;
NS_ASSUME_NONNULL_BEGIN

@interface UpUserDomain : NSObject <UpUserDomainDelegate, UpUserDomainProvider>
- (instancetype)initDomain:(id<UpUserDataSource>)userDataSource familyDataSource:(id<UpFamilyDataSource>)familyDataSource deviceDataSource:(id<UpDeviceListDataSource>)deviceDataSource cache:(id<UDCache>)cache time:(id<UPTimeMillisDelegate>)time;
@end

NS_ASSUME_NONNULL_END
