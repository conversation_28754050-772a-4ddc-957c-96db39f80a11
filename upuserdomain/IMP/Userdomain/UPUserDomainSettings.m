//
//  UPUserDomainSettings.m
//  upuserdomain
//
//  Created by 闫达 on 2020/8/2.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPUserDomainSettings.h"
#import <upnetwork/UPNetwork.h>
@interface UPUserDomainSettings ()
@property (nonatomic, assign) BOOL singleClientCheck;
@property (nonatomic, copy) NSString *clientID;
@property (nonatomic, assign) NSTimeInterval planReTime;
@property (nonatomic, assign) UPUserDomainPlatform platform;
@property (nonatomic, assign) BOOL isRefreshFamily;
@property (nonatomic, assign) BOOL isRefreshDevice;
@end

@implementation UPUserDomainSettings

- (instancetype)init
{
    if (self = [super init]) {
        _planReTime = 10 * 60;
        _singleClientCheck = YES;
        _isRefreshDevice = YES;
        _isRefreshFamily = YES;
        _clientID = [UPNetworkSettings sharedSettings].clientID;
    }
    return self;
}
- (NSString *)getClientId
{
    return _clientID;
}

- (NSInteger)getHttpRequestRetryDelay
{
    return 0;
}
- (BOOL)isSingleClientCheckEnabled
{
    return _singleClientCheck;
}

- (void)setClientId:(NSString *)clientId
{
    _clientID = clientId;
}

- (void)setHttpRequestRetryDelay:(NSInteger)delay
{
}

- (void)setSingleClientCheckEnabled:(BOOL)enabled
{
    _singleClientCheck = enabled;
}

- (void)setPlanRefreshTokenTime:(NSTimeInterval)planRefreshTokenTime
{
    _planReTime = planRefreshTokenTime;
}
- (NSTimeInterval)planRefreshTokenTime
{
    return _planReTime;
}
- (void)setUserDomainPlatform:(UPUserDomainPlatform)platform
{
    _platform = platform;
}
- (UPUserDomainPlatform)getUserDomainPlatform
{
    return _platform;
}
- (void)setRefreshFamilyListEnable:(BOOL)enabled
{
    _isRefreshFamily = enabled;
}
- (BOOL)isRefreshFamilyListEnable
{
    return _isRefreshFamily;
}
- (void)setRefreshDeviceListEnable:(BOOL)enabled
{
    _isRefreshDevice = enabled;
}
- (BOOL)isRefreshDeviceListEnable
{
    return _isRefreshDevice;
}
@end
