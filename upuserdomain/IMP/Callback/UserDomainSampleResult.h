//
//  UserDomainSampleResult.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/7.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpUserDomainResult.h"
NS_ASSUME_NONNULL_BEGIN
extern NSString *const kUserDomainInvalid_grant;
extern NSString *const kUserDomainInvalid_token;
extern NSString *const kUserDomainRetCodeNetError;
extern NSString *const kret_Data;

@class UserDomainSampleResult;

/**
 * 用户基础信息组件回调Block

 @param result (UserDomainSampleResult)
 @see UserDomainSampleResult
 */
typedef void (^userDomainCallback)(UserDomainSampleResult *result);

/**
 用户基础信息组件回调结果类
 */
@interface UserDomainSampleResult : NSObject <UpUserDomainResult>

- (instancetype)initWithResponseObject:(id)responseObject;
+ (UserDomainSampleResult *)defaultSuccessResult;
+ (UserDomainSampleResult *)defaultFailureResult;
+ (UserDomainSampleResult *)responseDataError;
- (instancetype)initWithResponseError:(NSError *)error;
- (instancetype)initWithAnalysisDataResponse:(id)responseObject;
@end

NS_ASSUME_NONNULL_END
