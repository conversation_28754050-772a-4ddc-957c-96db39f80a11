//
//  UserDomainSampleResult.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/7.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserDomainSampleResult.h"
NSString *const kretCode_Key = @"retCode";

NSString *const kretInfo_Key = @"retInfo";

NSString *const kret_Data = @"data";

NSString *const kretCode_Success = @"00000";

NSString *const kretCode_Failure = @"-1";

NSString *const kretInfo_Failure = @"操作失败";

NSString *const kretInfo_Success = @"操作成功";

NSString *const kUserDomainRetCodeNetError = @"99999";

NSString *const kretInfoNetError = @"网络异常";

NSString *const kresponseDataError = @"服务器返回数据异常";

NSString *const kResponseErrorDataKey = @"com.alamofire.serialization.response.error.data";

NSString *const kResponseErrorKey = @"error";
NSString *const kerror_description = @"error_description";

NSString *const kUserCenter_success = @"success";

NSString *const kUserCenter_error = @"error";

NSString *const kretCodeNotLogin = @"999999";
NSString *const kretInfoNotLogin = @"用户未登录";

NSString *const kUserDomainInvalid_grant = @"400";
NSString *const kUserDomainInvalid_token = @"401";

extern NSString *const AFNetworkingOperationFailingURLResponseDataErrorKey;

@implementation UserDomainSampleResult
@synthesize retCode, retData, retInfo, success;

- (instancetype)initWithretCode:(NSString *)retCode retInfo:(NSString *)retInfo retData:(id)retData
{
    if (self = [super init]) {
        self.retCode = retCode;
        self.retInfo = retInfo;
        self.retData = retData;
        self.success = [retCode isEqualToString:kretCode_Success];
    }
    return self;
}
- (instancetype)initWithResponseObject:(id)responseObject
{
    if (self = [super init]) {
        if (![responseObject isKindOfClass:NSDictionary.class]) {
            self.retCode = kretCode_Success;
            self.retInfo = kretInfo_Success;
            self.success = YES;
            return self;
        }
        NSString *successValue = responseObject[kUserCenter_success];
        NSString *errorValue = responseObject[kUserCenter_error];
        if ([successValue isKindOfClass:NSString.class] && successValue.length) {
            [self configWithUserCenterResponse:responseObject successValue:successValue errorValue:errorValue];
        }
        else {
            [self configWithServerResponse:responseObject];
        }
    }
    return self;
}
- (void)configWithServerResponse:(id)responseObject
{
    NSString *retCode = responseObject[kretCode_Key];
    NSString *retInfo = responseObject[kretInfo_Key];
    self.retCode = retCode ?: kretCode_Success;
    self.retInfo = retInfo ?: kretInfo_Success;
    self.retData = responseObject;
    self.success = [self.retCode isEqualToString:kretCode_Success];
}
- (void)configWithUserCenterResponse:(id)responseObject successValue:(NSString *)successValue errorValue:(NSString *)errorValue
{
    self.success = successValue.boolValue;
    if (self.success) {
        self.retCode = kretCode_Success;
        self.retInfo = kretInfo_Success;
    }
    else if (errorValue) {
        self.retCode = kretCode_Failure;
        self.retInfo = errorValue;
    }
    else {
        self.retCode = kretCode_Failure;
        self.retInfo = kretInfo_Failure;
    }
    self.retData = responseObject;
}

+ (UserDomainSampleResult *)defaultSuccessResult
{
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
    result.retCode = kretCode_Success;
    result.retInfo = kretInfo_Success;
    result.success = YES;
    return result;
}
+ (UserDomainSampleResult *)defaultFailureResult
{
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
    result.retCode = kretCode_Failure;
    result.retInfo = kretInfo_Failure;
    result.success = NO;
    return result;
}
+ (UserDomainSampleResult *)responseDataError
{
    UserDomainSampleResult *result = [[UserDomainSampleResult alloc] init];
    result.retCode = kretCode_Failure;
    result.retInfo = kresponseDataError;
    result.success = NO;
    return result;
}
- (instancetype)initWithResponseError:(NSError *)error
{
    self = [super init];
    if (!self) {
        return self;
    }
    NSHTTPURLResponse *response = error.userInfo[@"com.alamofire.serialization.response.error.response"];
    if (response == nil) {
        self.retCode = kUserDomainRetCodeNetError;
        self.retInfo = [NSString stringWithFormat:@"%@", error];
        return self;
    }
    if (response.statusCode == 401) {
        self.retCode = kUserDomainInvalid_token;
        self.retInfo = @"invalid_token";
    }
    else if (response.statusCode == 400) {
        self.retCode = kUserDomainInvalid_grant;
        self.retInfo = @"invalid_grant";

        NSData *responseData = error.userInfo[AFNetworkingOperationFailingURLResponseDataErrorKey];
        if (responseData == nil) {
            return self;
        }

        id errorResponse = [[NSJSONSerialization JSONObjectWithData:responseData options:NSJSONReadingMutableLeaves error:nil] valueForKey:@"error"];
        if (errorResponse && [errorResponse isKindOfClass:NSString.class]) {
            self.retInfo = errorResponse;
        }
    }
    else {
        self.retCode = [NSString stringWithFormat:@"%d", response.statusCode];
        self.retInfo = [NSString stringWithFormat:@"%@", error];
    }
    return self;
}

- (instancetype)initWithAnalysisDataResponse:(id)responseObject
{
    if (self = [super init]) {
        NSDictionary *data = responseObject[kret_Data];
        NSString *successValue = responseObject[kUserCenter_success];
        NSString *errorValue = responseObject[kUserCenter_error];
        if ([successValue isKindOfClass:NSString.class] && successValue.length) {
            [self configWithUserCenterResponse:data successValue:successValue errorValue:errorValue];
        }
        else {
            NSString *retCode = responseObject[kretCode_Key];
            NSString *retInfo = responseObject[kretInfo_Key];
            self.retCode = retCode ?: kretCode_Success;
            self.retInfo = retInfo ?: kretInfo_Success;
            self.retData = data;
            self.success = [self.retCode isEqualToString:kretCode_Success];
        }
    }
    return self;
}

@end
