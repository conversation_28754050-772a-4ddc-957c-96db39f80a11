//
//  ApplicationOauthData+PrivateExtension.h
//  UserDomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/23.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ApplicationOauthData.h"

NS_ASSUME_NONNULL_BEGIN

@interface ApplicationOauthData ()
/**
 * 用户中心个人token
 */
@property (nonatomic, copy) NSString *realAccess_token;
/**
 * 用户中心access_token 过期剩余时间：秒
 */
@property (nonatomic, copy) NSString *realExpires_in;

/**
 * 用户中心刷新token
 */
@property (nonatomic, copy) NSString *realRefresh_token;

/**
 * 用户中心access_token作用域
 */
@property (nonatomic, copy) NSString *realScope;

/**
 * 用户中心access_token 类型
 */
@property (nonatomic, copy) NSString *realToken_type;

/**
 * 云平台用户token
 */
@property (nonatomic, copy) NSString *realUhome_access_token;

/**
 * 云平台用户ID
 */
@property (nonatomic, copy) NSString *realUhome_user_id;
/**
 * 创建时间
 */
@property (nonatomic, strong) NSDate *realCreateTime;
/**
 用户中心userID
 */
@property (nonatomic, copy) NSString *realUc_user_id;

@end

NS_ASSUME_NONNULL_END
