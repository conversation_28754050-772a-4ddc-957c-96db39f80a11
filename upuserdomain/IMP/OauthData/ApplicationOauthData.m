//
//  ApplicationOauthData.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ApplicationOauthData.h"
#import "ApplicationOauthData+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation ApplicationOauthData
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realAccess_token", @"realExpires_in", @"realRefresh_token", @"realScope", @"realToken_type", @"realUhome_access_token", @"realUhome_user_id", @"realCreateTime", @"createTime", @"realUc_user_id" ];
}

- (NSString *)access_token
{
    return self.realAccess_token;
}
- (void)setAccess_token:(NSString *)access_token
{
    self.realAccess_token = access_token;
}

- (NSString *)expires_in
{
    return self.realExpires_in;
}

- (void)setExpires_in:(NSString *)expires_in
{
    self.realExpires_in = expires_in;
}

- (NSString *)refresh_token
{
    return self.realRefresh_token;
}
- (void)setRefresh_token:(NSString *)refresh_token
{
    self.realRefresh_token = refresh_token;
}

- (NSString *)scope
{
    return self.realScope;
}

- (void)setScope:(NSString *)scope
{
    self.realScope = scope;
}

- (NSString *)token_type
{
    return self.realToken_type;
}
- (void)setToken_type:(NSString *)token_type
{
    self.realToken_type = token_type;
}

- (NSString *)uhome_access_token
{
    return self.realUhome_access_token;
}

- (void)setUhome_access_token:(NSString *)uhome_access_token
{
    self.realUhome_access_token = uhome_access_token;
}

- (NSString *)uhome_user_id
{
    return self.realUhome_user_id;
}
- (void)setUhome_user_id:(NSString *)uhome_user_id
{
    self.realUhome_user_id = uhome_user_id;
}
- (NSDate *)createTime
{
    return self.realCreateTime;
}
- (void)setCreateTime:(NSDate *)createTime
{
    self.realCreateTime = createTime;
}
- (NSString *)uc_user_id
{
    return self.realUc_user_id;
}
- (void)setUc_user_id:(NSString *)uc_user_id
{
    self.realUc_user_id = uc_user_id;
}
@end
