//
//  DeviceOwnerInfo.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DeviceOwnerInfo.h"
#import "DeviceOwnerInfo+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation DeviceOwnerInfo
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realMobile", @"realUserId", @"realUserNickName", @"realUcUserId" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"mobile", @"userId", @"userNickName", @"ucUserId" ];
}

- (NSString *)mobile
{
    return self.realMobile;
}

- (NSString *)userId
{
    return self.realUserId;
}

- (NSString *)userNickName
{
    return self.realUserNickName;
}
- (NSString *)ucUserId
{
    return self.realUcUserId;
}
@end
