//
//  Device.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "Device.h"
#import "UDDeviceInfoDelegate.h"
#import <MJExtension/MJExtension.h>
#import "UDModifyDeviceNameOp.h"
#import "UDOperatorManager.h"
#import <uplog/UPLog.h>
#import "Family.h"
#import "Room.h"
#import "Room+PrivateExtension.h"
#import "UDFloorInfoDelegate.h"
#import "UDModifyDeviceNameNewOp.h"
#import "UDDeviceInfo+PrivateExtension.h"
#import "UDAuthDataDelegate.h"
@interface Device ()
@property (nonatomic, strong) id<UDDeviceInfoDelegate> deviceInfo;
@property (nonatomic, strong) UDOperatorManager *opManager;
@property (nonatomic, strong) id<UpUserDomainProvider> userDoma<PERSON>Provider;
@end
@implementation Device
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"deviceInfo", @"opManager", @"userDomainProvider" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"opManager", @"userDomainProvider", @"deviceId", @"deviceName", @"devName", @"deviceType", @"familyId", @"ownerId", @"permission", @"wifiType", @"bindTime", @"isOnline", @"ownerInfo", @"subDevIds", @"parentId", @"deviceRole", @"deviceRoleType", @"apptypeName", @"apptypeCode", @"categoryGrouping", @"barcode", @"bindType", @"brand", @"imageAddr1", @"imageAddr2", @"model", @"prodNo", @"roomName", @"roomId", @"room", @"isOwned", @"accessType", @"configType", @"comunicationMode", @"devFloorId", @"devFloorName", @"devFloorOrderId", @"apptypeIcon", @"deviceNetType", @"deviceGroupId", @"deviceGroupType", @"noKeepAlive", @"twoGroupingName", @"cardPageIcon", @"cardPageImg", @"smallCardSort", @"largeCardSort", @"cardSort", @"cardStatus", @"aggregationParentId", @"supportAggregationFlag", @"deviceAggregateType", @"shareDeviceCardInfo", @"sharedDeviceFlag", @"supportShared", @"attachmentSortCode", @"deviceShareSupportFlag", @"reBind" ];
}
+ (NSDictionary *)mj_objectClassInArray
{
    return @{ @"shareDeviceCardInfo" : @"DeviceShareDeviceCardInfo" };
}

+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"ownerInfo"]) {
          [property.type setValue:NSClassFromString(@"DeviceOwnerInfo") forKey:@"typeClass"];
      }
      if ([property.name isEqualToString:@"permission"]) {
          [property.type setValue:NSClassFromString(@"DevicePermission") forKey:@"typeClass"];
      }
      if ([property.name isEqualToString:@"room"]) {
          [property.type setValue:NSClassFromString(@"Room") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}
- (NSString *)accessType
{
    return self.getInfo.accessType;
}

- (NSString *)apptypeName
{
    return self.getInfo.apptypeName;
}

- (NSString *)barcode
{
    return self.getInfo.barcode;
}

- (NSString *)bindTime
{
    return self.getInfo.bindTime;
}

- (NSString *)bindType
{
    return self.getInfo.bindType;
}

- (NSString *)brand
{
    return self.getInfo.brand;
}

- (NSString *)categoryGrouping
{
    return self.getInfo.categoryGrouping;
}

- (NSString *)twoGroupingName
{
    return self.getInfo.twoGroupingName;
}

- (NSString *)configType
{
    return self.getInfo.configType;
}

- (NSString *)devName
{
    return self.getInfo.devName;
}

- (NSString *)deviceId
{
    return self.getInfo.deviceId;
}

- (NSString *)deviceName
{
    return self.getInfo.deviceName;
}

- (NSString *)deviceRole
{
    return self.getInfo.deviceRole;
}

- (NSString *)deviceRoleType
{
    return self.getInfo.deviceRoleType;
}

- (NSString *)deviceType
{
    return self.getInfo.deviceType;
}

- (NSString *)familyId
{
    return self.getInfo.familyId;
}

- (NSString *)imageAddr1
{
    return self.getInfo.imageAddr1;
}

- (NSString *)imageAddr2
{
    return self.getInfo.imageAddr2;
}

- (NSString *)cardPageIcon
{
    return self.getInfo.cardPageIcon;
}

- (NSString *)cardPageImg
{
    return self.getInfo.cardPageImg;
}

- (NSInteger)smallCardSort
{
    return self.getInfo.smallCardSort;
}

- (NSInteger)largeCardSort
{
    return self.getInfo.largeCardSort;
}
- (NSInteger)cardSort
{
    return self.getInfo.cardSort;
}
- (NSInteger)cardStatus
{
    return self.getInfo.cardStatus;
}
- (NSString *)aggregationParentId
{
    return self.getInfo.aggregationParentId;
}
- (NSInteger)supportAggregationFlag
{
    return self.getInfo.supportAggregationFlag;
}
- (NSString *)deviceAggregateType
{
    return self.getInfo.deviceAggregateType;
}


- (BOOL)isOnline
{
    return self.getInfo.isOnline;
}

- (BOOL)isOwned
{
    return self.getInfo.isOwned;
}

- (NSString *)model
{
    return self.getInfo.model;
}

- (NSString *)ownerId
{
    return self.getInfo.ownerId;
}
- (id<UDDeviceOwnerInfoDelegate>)ownerInfo
{
    return self.getInfo.ownerInfo;
}

- (NSString *)parentId
{
    return self.getInfo.parentId;
}

- (id<UDDevicePermissionDelegate>)permission
{
    return self.getInfo.permission;
}


- (NSString *)prodNo
{
    return self.getInfo.prodNo;
}

- (id<UDRoomDelegate>)room
{
    __block Room *room;
    NSArray<Family *> *familyList = self.userDomainProvider.provideUpUserDomainStore.familyStore.familyList;
    if (familyList.count == 0) {
        room = [[Room alloc] init];
        room.realRoomId = self.getInfo.roomId ?: @"";
        room.realRoomName = self.getInfo.roomName ?: @"";
        return room;
    }
    __block Family *family;
    [familyList enumerateObjectsUsingBlock:^(Family *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([self.getInfo.familyId isEqualToString:obj.familyId]) {
          family = obj;
          *stop = YES;
      }
    }];
    if (family == nil) {
        room = [[Room alloc] init];
        room.realRoomId = self.getInfo.roomId ?: @"";
        room.realRoomName = self.getInfo.roomName ?: @"";
        return room;
    }
    __block NSArray<id<UDRoomDelegate>> *rooms;
    [family.floorInfos enumerateObjectsUsingBlock:^(id<UDFloorInfoDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([self.devFloorId isEqualToString:obj.floorId]) {
          rooms = obj.rooms;
          *stop = YES;
      }
    }];
    [rooms enumerateObjectsUsingBlock:^(id<UDRoomDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      NSString *roomId = self.getInfo.roomId;
      if (roomId) {
          if ([obj.roomId isEqualToString:roomId]) {
              room = obj;
              *stop = YES;
          }
      }

    }];
    if (room == nil) {
        room = [[Room alloc] init];
        room.realRoomId = self.getInfo.roomId ?: @"";
        room.realRoomName = self.getInfo.roomName ?: @"";
    }
    return room;
}


- (NSString *)roomId
{
    return self.getInfo.roomId;
}

- (NSString *)roomName
{
    return self.getInfo.roomName;
}

- (NSArray *)subDevIds
{
    return self.getInfo.subDevIds;
}

- (NSString *)wifiType
{
    return self.getInfo.wifiType;
}
- (NSString *)deviceNetType
{
    return self.getInfo.deviceNetType;
}
- (NSString *)apptypeCode
{
    return self.getInfo.apptypeCode;
}
- (NSString *)comunicationMode
{
    return self.getInfo.comunicationMode;
}
- (NSString *)devFloorId
{
    return self.getInfo.devFloorId;
}
- (NSString *)devFloorName
{
    return self.getInfo.devFloorName;
}
- (NSString *)devFloorOrderId
{
    return self.getInfo.devFloorOrderId;
}
- (NSString *)apptypeIcon
{
    return self.getInfo.apptypeIcon;
}
- (NSString *)deviceGroupId
{
    return self.getInfo.deviceGroupId;
}
- (NSString *)deviceGroupType
{
    return self.getInfo.deviceGroupType;
}
- (NSInteger)noKeepAlive
{
    return self.getInfo.noKeepAlive;
}
- (id<UDDeviceInfoDelegate>)getInfo
{
    return self.deviceInfo;
}
- (BOOL)supportShared
{
    return self.getInfo.supportShared;
}

- (BOOL)reBind
{
    return self.getInfo.reBind;
}

- (void)setDeviceInfo:(id<UDDeviceInfoDelegate>)deviceInfo
{
    _deviceInfo = deviceInfo;
}
- (NSArray<id<UDShareDeviceCardInfoDelegate>> *)shareDeviceCardInfo
{
    return self.getInfo.shareDeviceCardInfo;
}
- (BOOL)sharedDeviceFlag
{
    return self.getInfo.sharedDeviceFlag;
}
- (NSInteger)attachmentSortCode
{
    return self.getInfo.attachmentSortCode;
}
- (BOOL)deviceShareSupportFlag
{
    return self.getInfo.deviceShareSupportFlag;
}
- (void)setOperatorManager:(UDOperatorManager *)opManager
{
    _opManager = opManager;
}
- (void)setUserDomainprovider:(id)provider
{
    _userDomainProvider = provider;
    UDDeviceInfo *deviceInfo = (UDDeviceInfo *)_deviceInfo;
    deviceInfo.realIsOwned = [_userDomainProvider.provideUpUserDomain.oauthData.uhome_user_id isEqualToString:deviceInfo.ownerId];
}
- (void)updateDeviceName:(NSString *)name success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (name.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:UDModifyDeviceNameOp.class
                                args:@{ @"deviceId" : self.deviceId,
                                        @"oldName" : self.deviceName,
                                        @"name" : name,
                                        @"familyId" : self.familyId,
                                        @"deviceNetType" : self.deviceNetType ?: @"",
                                        @"prodNo" : self.prodNo
                                }
                             success:success
                             failure:failure];
}

- (void)updateRoomName:(NSString *)name success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPLogInfo(@"UPUserDomain", @"%s%d---当前方法已经废弃，请调用Family中的  moveDevicesToOtherRoom:", __func__, __LINE__);
}

- (void)updateDeviceName:(NSString *)name checkLevel:(BOOL)checkLevel type:(NSString *)type success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (name.length == 0 || type.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:UDModifyDeviceNameNewOp.class
                                args:@{ @"deviceId" : self.deviceId,
                                        @"oldName" : self.deviceName,
                                        @"name" : name,
                                        @"familyId" : self.familyId,
                                        @"checkLevel" : @(checkLevel),
                                        @"type" : type }
                             success:success
                             failure:failure];
}


@end
