//
//  DeviceAuth.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DeviceAuth.h"
#import "DeviceAuth+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation DeviceAuth
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realControl", @"realSet", @"realView" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"control", @"set", @"view" ];
}

- (BOOL)control
{
    return self.realControl;
}

- (BOOL)set
{
    return self.realSet;
}

- (BOOL)view
{
    return self.realView;
}

@end
