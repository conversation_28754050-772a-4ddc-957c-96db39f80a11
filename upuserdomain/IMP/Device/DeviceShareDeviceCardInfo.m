//
//  DeviceShareDeviceCardInfo.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/3/11.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DeviceShareDeviceCardInfo.h"
#import "DeviceShareDeviceCardInfo+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation DeviceShareDeviceCardInfo
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realFamilyId", @"realCardSort", @"realCardStatus" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"familyId", @"cardSort", @"cardStatus" ];
}

- (NSString *)familyId
{
    return self.realFamilyId;
}

- (NSInteger)cardSort
{
    return self.realCardSort;
}

- (NSInteger)cardStatus
{
    return self.realCardStatus;
}

@end
