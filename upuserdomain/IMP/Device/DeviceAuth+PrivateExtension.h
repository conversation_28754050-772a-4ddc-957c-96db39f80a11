//
//  DeviceAuth+PrivateExtension.h
//  UserDomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/23.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DeviceAuth.h"

NS_ASSUME_NONNULL_BEGIN

@interface DeviceAuth ()
/**
 * 控制
 */
/**
 *  权限信息(DevicePermission)
 */
@property (nonatomic, assign) BOOL realControl;
/**
 * 设置
 */
@property (nonatomic, assign) BOOL realSet;
/**
 * 查看
 */
@property (nonatomic, assign) BOOL realView;
@end

NS_ASSUME_NONNULL_END
