//
//  DeviceShareDeviceCardInfo+PrivateExtension.h
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/3/11.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DeviceShareDeviceCardInfo.h"

NS_ASSUME_NONNULL_BEGIN

@interface DeviceShareDeviceCardInfo ()
/**
 * 家庭id
 */
@property (nonatomic, copy) NSString *realFamilyId;

/**
 *  卡片排序码
 */
@property (nonatomic, assign) NSInteger realCardSort;

/**
 *  卡片状态 0:小卡 1:中卡 2:大卡
 */
@property (nonatomic, assign) NSInteger realCardStatus;
@end

NS_ASSUME_NONNULL_END
