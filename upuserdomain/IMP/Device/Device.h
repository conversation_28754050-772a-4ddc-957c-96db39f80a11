//
//  Device.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UDDeviceDelegate.h"
#import "UpUserDomainProvider.h"
NS_ASSUME_NONNULL_BEGIN
@class UDOperatorManager;
@interface Device : NSObject <UDDeviceDelegate, NSCoding>
- (void)setDeviceInfo:(id<UDDeviceInfoDelegate>)deviceInfo;
- (void)setOperatorManager:(UDOperatorManager *)opManager;
- (void)setUserDomainprovider:(id<UpUserDomainProvider>)provider;
@end

NS_ASSUME_NONNULL_END
