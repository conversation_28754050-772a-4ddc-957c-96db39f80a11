//
//  DeviceInfo.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDDeviceInfo.h"
#import "UDDeviceInfo+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation UDDeviceInfo
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realAccessType", @"realApptypeName", @"realBarcode", @"realBindTime", @"realBindType", @"realBrand", @"realCategoryGrouping", @"realConfigType", @"realDevName", @"realDeviceId", @"realDeviceName", @"realDeviceRole", @"realDeviceRoleType", @"realDeviceType", @"realFamilyId", @"realImageAddr1", @"realImageAddr2", @"realIsOnline", @"realIsOwned", @"realModel", @"realOwnerId", @"realOwnerInfo", @"realParentId", @"realPermission", @"realProdNo", @"realRoom", @"realRoomId", @"realRoomName", @"realSubDevIds", @"realWifiType", @"realApptypeCode", @"realComunicationMode", @"realDevFloorId", @"realDevFloorName", @"realDevFloorOrderId", @"realApptypeIcon", @"realDeviceNetType", @"realDeviceGroupId", @"realDeviceGroupType", @"realNoKeepAlive", @"realTwoGroupingName", @"realCardPageIcon", @"realCardPageImg", @"realSmallCardSort", @"realLargeCardSort", @"realCardSort", @"realCardStatus", @"realAggregationParentId", @"realSupportAggregationFlag", @"realDeviceAggregateType", @"realShareDeviceCardInfo", @"realSharedDeviceFlag", @"realAttachmentSortCode", @"realDisplayedInHomePage", @"realDeviceShareSupportFlag", @"realReBind" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"accessType", @"apptypeName", @"barcode", @"bindTime", @"bindType", @"brand", @"categoryGrouping", @"configType", @"devName", @"deviceId", @"deviceName", @"deviceRole", @"deviceRoleType", @"deviceType", @"familyId", @"imageAddr1", @"imageAddr2", @"isOnline", @"isOwned", @"model", @"ownerId", @"ownerInfo", @"parentId", @"permission", @"prodNo", @"room", @"roomId", @"roomName", @"subDevIds", @"wifiType", @"apptypeCode", @"comunicationMode", @"devFloorId", @"devFloorName", @"devFloorOrderId", @"apptypeIcon", @"deviceNetType", @"deviceGroupId", @"deviceGroupType", @"noKeepAlive", @"twoGroupingName", @"cardPageIcon", @"cardPageImg", @"smallCardSort", @"largeCardSort", @"cardSort", @"cardStatus", @"aggregationParentId", @"supportAggregationFlag", @"deviceAggregateType", @"shareDeviceCardInfo", @"sharedDeviceFlag", @"supportShared", @"attachmentSortCode", @"displayedInHomePage", @"deviceShareSupportFlag", @"reBind" ];
}
+ (NSDictionary *)mj_objectClassInArray
{
    return @{ @"shareDeviceCardInfo" : @"DeviceShareDeviceCardInfo" };
}

+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"ownerInfo"]) {
          [property.type setValue:NSClassFromString(@"DeviceOwnerInfo") forKey:@"typeClass"];
      }
      if ([property.name isEqualToString:@"permission"]) {
          [property.type setValue:NSClassFromString(@"DevicePermission") forKey:@"typeClass"];
      }
      if ([property.name isEqualToString:@"room"]) {
          [property.type setValue:NSClassFromString(@"Room") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}
- (NSString *)accessType
{
    return self.realAccessType;
}

- (NSString *)apptypeName
{
    return self.realApptypeName;
}

- (NSString *)barcode
{
    return @"";
}

- (NSString *)bindTime
{
    return self.realBindTime;
}

- (NSString *)bindType
{
    return self.realBindType;
}

- (NSString *)brand
{
    return self.realBrand;
}

- (NSString *)categoryGrouping
{
    return self.realCategoryGrouping;
}

- (NSString *)configType
{
    return self.realConfigType;
}

- (NSString *)devName
{
    return self.realDevName;
}

- (NSString *)deviceId
{
    return self.realDeviceId;
}

- (NSString *)deviceName
{
    return self.realDeviceName;
}

- (NSString *)deviceRole
{
    return self.realDeviceRole;
}

- (NSString *)deviceRoleType
{
    return self.realDeviceRoleType;
}

- (NSString *)deviceType
{
    return self.realDeviceType;
}

- (NSString *)familyId
{
    return self.realFamilyId;
}

- (NSString *)imageAddr1
{
    return self.realImageAddr1;
}

- (NSString *)imageAddr2
{
    return @"";
}

- (NSString *)cardPageIcon
{
    return @"";
}

- (NSString *)cardPageImg
{
    return self.realCardPageImg;
}
- (NSInteger)smallCardSort
{
    return 0;
}
- (NSInteger)largeCardSort
{
    return 0;
}
- (NSInteger)cardSort
{
    return self.realCardSort;
}
- (NSInteger)cardStatus
{
    return self.realCardStatus;
}
- (NSString *)aggregationParentId
{
    return self.realAggregationParentId;
}
- (NSInteger)supportAggregationFlag
{
    return self.realSupportAggregationFlag;
}
- (BOOL)deviceShareSupportFlag
{
    return self.realDeviceShareSupportFlag;
}
- (NSString *)deviceAggregateType
{
    return self.realDeviceAggregateType;
}
- (BOOL)isOnline
{
    return self.realIsOnline;
}

- (BOOL)isOwned
{
    return self.realIsOwned;
}

- (NSString *)model
{
    return self.realModel;
}

- (NSString *)ownerId
{
    return self.realOwnerId;
}
- (id<UDDeviceOwnerInfoDelegate>)ownerInfo
{
    return self.realOwnerInfo;
}

- (NSString *)parentId
{
    return self.realParentId;
}

- (id<UDDevicePermissionDelegate>)permission
{
    return self.realPermission;
}


- (NSString *)prodNo
{
    return self.realProdNo;
}

- (id<UDRoomDelegate>)room
{
    return self.realRoom;
}


- (NSString *)roomId
{
    return self.realRoomId;
}

- (NSString *)roomName
{
    return self.realSharedDeviceFlag ? @"共享设备" : self.realRoomName;
}

- (NSArray *)subDevIds
{
    return @[];
}

- (NSString *)wifiType
{
    return self.realWifiType;
}
- (NSString *)deviceNetType
{
    return self.realDeviceNetType;
}
- (NSString *)apptypeCode
{
    return self.realApptypeCode;
}
- (NSString *)comunicationMode
{
    return self.realComunicationMode;
}
- (NSString *)devFloorId
{
    return self.realDevFloorId;
}
- (NSString *)devFloorName
{
    return self.realDevFloorName;
}
- (NSString *)devFloorOrderId
{
    return self.realDevFloorOrderId;
}
- (NSString *)apptypeIcon
{
    return self.realApptypeIcon;
}
- (NSString *)deviceGroupId
{
    return self.realDeviceGroupId;
}
- (NSString *)deviceGroupType
{
    return self.realDeviceGroupType;
}
- (NSInteger)noKeepAlive
{
    return self.realNoKeepAlive;
}
- (NSString *)twoGroupingName
{
    return self.realTwoGroupingName;
}
- (NSArray<id<UDShareDeviceCardInfoDelegate>> *)shareDeviceCardInfo
{
    return self.realShareDeviceCardInfo;
}
- (BOOL)sharedDeviceFlag
{
    return self.realSharedDeviceFlag;
}
- (BOOL)supportShared
{
    //是否单设备卡片
    BOOL isSingleDeviceCard = self.realDeviceAggregateType.length == 0;

    //是否主设备
    BOOL isPrimaryDevice = [self.realDeviceRole isEqualToString:@"1"] || [self.realDeviceRole isEqualToString:@"2"] || self.realDeviceRole.length == 0;

    //是否三方设备
    BOOL isThirdPartyDevice = [self.realConfigType isEqualToString:@"1"];

    // 是否网器设备
    BOOL isNetworkDevice = [self.realDeviceNetType isEqualToString:@"device"];

    //是否型号缺失
    BOOL isModelAvailable = self.realModel.length > 0;

    //按照品类是否支持   true：支持  false:不支持
    BOOL shareSupport = self.realDeviceShareSupportFlag;

    // 是否支持共享
    return shareSupport && isSingleDeviceCard && isPrimaryDevice && !isThirdPartyDevice && isNetworkDevice && isModelAvailable;
}
- (NSInteger)attachmentSortCode
{
    return self.realAttachmentSortCode;
}

- (NSInteger)displayedInHomePage
{
    return self.realDisplayedInHomePage;
}

- (BOOL)reBind
{
    return self.realReBind;
}

@end
