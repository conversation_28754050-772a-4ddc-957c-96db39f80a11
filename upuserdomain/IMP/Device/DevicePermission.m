//
//  DevicePermission.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DevicePermission.h"
#import "DevicePermission+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation DevicePermission
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realAuth", @"realAuthType" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"auth", @"authType" ];
}

+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"auth"]) {
          [property.type setValue:NSClassFromString(@"DeviceAuth") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}
- (id<UDDeviceAuthDelegate>)auth
{
    return self.realAuth;
}

- (NSString *)authType
{
    return self.realAuthType;
}

@end
