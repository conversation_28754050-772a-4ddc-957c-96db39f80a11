//
//  DevicePermission+PrivateExtension.h
//  UserDomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/23.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DevicePermission.h"
@protocol UDDeviceAuthDelegate;
NS_ASSUME_NONNULL_BEGIN

@interface DevicePermission ()
/**
 权限信息
 */
@property (nonatomic, strong) id<UDDeviceAuthDelegate> realAuth;

/**
 权限类型
 */
@property (nonatomic, copy) NSString *realAuthType;
@end

NS_ASSUME_NONNULL_END
