//
//  UDDeviceInfo+PrivateExtension.h
//  UserDomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/23.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDDeviceInfo.h"
@protocol UDDevicePermissionDelegate
, UDDeviceOwnerInfoDelegate, UDRoomDelegate, UDShareDeviceCardInfoDelegate;
NS_ASSUME_NONNULL_BEGIN

@interface UDDeviceInfo ()
/**
 * 设备mac
 */
@property (nonatomic, copy) NSString *realDeviceId;

/**
 *  设备名称
 */
@property (nonatomic, copy) NSString *realDeviceName;

/**
 *  家庭下设备名称
 */
@property (nonatomic, copy) NSString *realDevName;

/**
 *  设备类型
 */
@property (nonatomic, copy) NSString *realDeviceType;

/**
 * 设备家庭id
 */
@property (nonatomic, copy) NSString *realFamilyId;

/**
 *  设备拥有者ID
 */
@property (nonatomic, copy) NSString *realOwnerId;

/**
 *  权限信息(DevicePermission)
 */
@property (nonatomic, strong) id<UDDevicePermissionDelegate> realPermission;

/**
 *  typeId
 */
@property (nonatomic, copy) NSString *realWifiType;
/**
 网器：netDevice，非网器 ：nonNetDevice，
 */
@property (nonatomic, copy) NSString *realDeviceNetType;
/**
设备绑定时间
*/
@property (nonatomic, copy) NSString *realBindTime;

/**
 *  是否在线
 */
@property (nonatomic, assign) BOOL realIsOnline;

/**
 *  拥有者信息(DeviceOwnerInfo)
 */
@property (nonatomic, strong) id<UDDeviceOwnerInfoDelegate> realOwnerInfo;
/**
 子设备ID列表
 */
@property (nonatomic, strong) NSArray *realSubDevIds;
/**
 父设备ID
 */
@property (nonatomic, copy) NSString *realParentId;
/**
 1 普通设备; 2 网关设备; 3 附件设备; 4 子设备;
 */
@property (nonatomic, copy) NSString *realDeviceRole;
/**
 主设备/子设备/无主从关系，0 无主设备，1 主设备，2 子设备
 */
@property (nonatomic, copy) NSString *realDeviceRoleType;

/**
 *  应用分类
 */
@property (nonatomic, copy) NSString *realApptypeName;
/**
 *  应用分类
 */
@property (nonatomic, copy) NSString *realApptypeCode;

/**
 * 一级应用分组
 */
@property (nonatomic, copy) NSString *realCategoryGrouping;

/**
 *  机编码
 */
@property (nonatomic, copy) NSString *realBarcode;

/**
 *  绑定方式（SmartLink SmartAP SoftAP Scancode bt DirectLink Nolink BLE&SoftAP）
 */
@property (nonatomic, copy) NSString *realBindType;

/**
 *  品牌
 */
@property (nonatomic, copy) NSString *realBrand;

/**
 *  实物图1
 */
@property (nonatomic, copy) NSString *realImageAddr1;

/**
 *  实物图2，（备用字段，暂时无用）
 */
@property (nonatomic, copy) NSString *realImageAddr2;

/**
 *  大卡图
 */
@property (nonatomic, copy) NSString *realCardPageIcon;

/**
 *  小卡图
 */
@property (nonatomic, copy) NSString *realCardPageImg;

/**
 *  小卡排序码
 */
@property (nonatomic, assign) NSInteger realSmallCardSort;

/**
 *  大卡排序码
 */
@property (nonatomic, assign) NSInteger realLargeCardSort;
/**
 卡片排序码
 */
@property (nonatomic, assign) NSInteger realCardSort;
/**
 卡片状态 0:小卡 1:中卡 2:大卡
 */
@property (nonatomic, assign) NSInteger realCardStatus;
/**
 聚合设备父节点id。灯聚合id:light_aggregation_id 窗帘聚合id:curtain_aggregation_id
 */
@property (nonatomic, copy) NSString *realAggregationParentId;
/**
 1:支持灯光聚合 2:支持窗帘聚合 0： 不支持聚合。
 */
@property (nonatomic, assign) NSInteger realSupportAggregationFlag;

/**
 按照品类是否支持   true：支持  false:不支持
 */
@property (nonatomic, assign) BOOL realDeviceShareSupportFlag;

/**
 灯光聚合为light
 窗帘聚合为curtain
 默认为“”
 */
@property (nonatomic, copy) NSString *realDeviceAggregateType;

/**
 *  型号
 */
@property (nonatomic, copy) NSString *realModel;

/**
 *  产品编码
 */
@property (nonatomic, copy) NSString *realProdNo;

/**
 *  房间名称
 */
@property (nonatomic, copy) NSString *realRoomName;

/**
 *  房间ID
 */
@property (nonatomic, copy) NSString *realRoomId;

/**
 *  房间(Room)
 */
@property (nonatomic, strong) id<UDRoomDelegate> realRoom;

/**
 *  是否归当前用户所有
 */
@property (nonatomic, assign) BOOL realIsOwned;

/**
 联网类型
 13:WIFI, 14:SDK_LINUX, 15:SDK_RTOS,31:SDK_ANDROID,90:NB-IoT, 93:Wi-Fi&BLE, 98:DEVICE_CLOUD
 */
@property (nonatomic, copy) NSString *realAccessType;

/**
 app端是否支持绑定
 0 app端支持配网绑定；1 app端不支持配网绑定
 */
@property (nonatomic, copy) NSString *realConfigType;
/**
 通讯协议
 */
@property (nonatomic, copy) NSString *realComunicationMode;
/**
 楼层ID
 */
@property (nonatomic, copy) NSString *realDevFloorId;
/**
 在家庭中的楼层序列（-3到5，没有0）
 */
@property (nonatomic, copy) NSString *realDevFloorOrderId;
/**
 楼层名
 */
@property (nonatomic, copy) NSString *realDevFloorName;
/**
 应用分类图片
 */
@property (nonatomic, copy) NSString *realApptypeIcon;
/**
 组设备ID
 */
@property (nonatomic, copy) NSString *realDeviceGroupId;
/**
 组设备类型
 */
@property (nonatomic, copy) NSString *realDeviceGroupType;
/**
 0:保活  1：非保活
 */
@property (nonatomic, assign) NSInteger realNoKeepAlive;
/**
 * 二级应用分组
 */
@property (nonatomic, copy) NSString *realTwoGroupingName;
/**
 分享的设备信息数组
 */
@property (nonatomic, strong) NSArray<id<UDShareDeviceCardInfoDelegate>> *realShareDeviceCardInfo;
/**
 是否共享设备 true：是 false:否
 */
@property (nonatomic, assign) BOOL realSharedDeviceFlag;
/**
 附件设备排序码
 */
@property (assign, nonatomic) NSInteger realAttachmentSortCode;

/**
 首页展示状态：0:未设置过 1:首页展示 2:首页隐藏
 */
@property (assign, nonatomic) NSInteger realDisplayedInHomePage;

/**
 是否支持二次绑定
 */
@property (nonatomic, assign) BOOL realReBind;

@end

NS_ASSUME_NONNULL_END
