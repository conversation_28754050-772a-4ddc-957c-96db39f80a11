//
//  UpUserDomainStore.m
//  upuserdomain
//
//  Created by 闫达 on 2020/7/29.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpUserDomainStore.h"
#import "UpEvent.h"
#import <uplog/UPLog.h>
#import "UpUserDomainCacheDelegate.h"
#import "UDAuthDataDelegate.h"
#import "UpUserDomainProvider.h"
#import "UPTimeMillisDelegate.h"
#import "UDOperatorManager.h"
#import <upnetwork/UPNetwork.h>
#import "UserDataSource.h"
#import "FamilyDataSource.h"
#import "DeviceListDataSource.h"
#import "UDServerCacheManager.h"
#define MAXTIME **********.0f;
@interface UpUserDomainStore ()
@property (nonatomic, strong) id<UpUserDomainCacheDelegate> userDomainCache;
@property (nonatomic, strong) id<UpUserDomainProvider> provider;

@end

@implementation UpUserDomainStore
- (instancetype)initUpUserDomainWithStoreCache:(id<UpUserDomainCacheDelegate>)userDomainCache userDomainprovider:(id<UpUserDomainProvider>)provider;
{
    if (self == [super init]) {
        self.userDomainCache = userDomainCache;
        self.provider = provider;
        _loginState = UpUserDomainStateUnLogin;
        self.oauthData = [self.userDomainCache getAuthData];
    }
    return self;
}

- (void)loadDataWithCache
{
    [self.userStore loadDataWithCache];
    [self.deviceStore loadDataWithCache];
    [self.familyStore loadDataWithCache];
}

- (void)clearUserData
{
    _loginState = UpUserDomainStateUnLogin;
    self.oauthData = nil;
    self.userStore.extraInfo = nil;
    self.userStore.terminals = nil;
    self.userStore.addressList = nil;
    self.deviceStore.deviceList = nil;
    self.familyStore.familyList = nil;
    self.familyStore.currentFamliy = nil;
    self.familyStore.isRefreshFamilyListCompleted = NO;
    self.userStore.isRefreshUserCompleted = NO;
    self.deviceStore.isRefreshDeviceListCompleted = NO;
    [UPNetworkSettings sharedSettings].uhomeAccessToken = nil;
    [UPNetworkSettings sharedSettings].accessToken = nil;
    [self.provider.provideOperatorManager dispose];
    [self.userDomainCache clearAllData];
    [[UDServerCacheManager getInstance] clearAllServerResponseCache];
}
- (void)setOauthData:(id<UDAuthDataDelegate>)oauthData
{
    _oauthData = oauthData;
    if (_oauthData && [self caculateOauthDataExpire] > 0) {
        self.loginState = UpUserDomainStateDidLogin;
    }
    [UPNetworkSettings sharedSettings].uhomeAccessToken = oauthData.uhome_access_token;
    [UPNetworkSettings sharedSettings].accessToken = oauthData.access_token;
}
- (void)updateCacheOauthData:(id<UDAuthDataDelegate>)oauthData
{
    [self.userDomainCache setAuthData:oauthData];
}
- (BOOL)verifyOauthData:(id<UDAuthDataDelegate>)oauthData
{
    NSString *regex = @"[0-9]*";
    NSPredicate *pred = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", regex];
    BOOL isMatch = [pred evaluateWithObject:oauthData.expires_in];
    NSArray<NSString *> *args = @[ oauthData.access_token ?: @"", oauthData.refresh_token ?: @"", oauthData.uhome_access_token ?: @"", oauthData.expires_in ?: @"", oauthData.token_type ?: @"", oauthData.uhome_user_id ?: @"", oauthData.uc_user_id ?: @"" ];
    for (NSString *arg in args) {
        if (![arg isKindOfClass:NSString.class] || !arg.length) {
            return NO;
        }
    }
    return isMatch && oauthData.expires_in.integerValue;
}
- (NSTimeInterval)caculateOauthDataExpire
{
    if (self.oauthData == nil) {
        return MAXTIME;
    }
    return self.oauthData.createTime.timeIntervalSince1970 + self.oauthData.expires_in.longLongValue - self.provider.providerTimeDelegate.currentTimeMillis;
}
- (NSTimeInterval)caculatePlanRefreshTokenExpresIn
{
    NSTimeInterval times = [self caculateOauthDataExpire];
    if (times < 60 * 60) {
        times = [self.provider.provideUpUserDomain getSettings].planRefreshTokenTime;
    }
    else {
        times = times - 60 * 60;
    }
    return times;
}
@end
