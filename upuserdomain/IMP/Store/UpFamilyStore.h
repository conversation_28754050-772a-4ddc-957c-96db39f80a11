//
//  UpFamilyStore.h
//  upuserdomain
//
//  Created by 闫达 on 2020/8/2.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@class Family, FamilyArgs, FloorArg, VirtualMemberArgs;
@protocol UpUserDomainProvider
, UpUserDomainCacheDelegate;
NS_ASSUME_NONNULL_BEGIN

@interface UpFamilyStore : NSObject
@property (nonatomic, assign) BOOL isRefreshFamilyListCompleted;
@property (atomic, strong) NSArray<Family *> *familyList;
@property (nonatomic, strong, nullable) Family *currentFamliy;
- (instancetype)initUpFamilyStoreWithStoreCache:(id<UpUserDomainCacheDelegate>)userDomainCache userDomainprovider:(id<UpUserDomainProvider>)provider;
- (void)updateFamilyList:(NSArray<Family *> *)familyList;
- (void)updateCurrentFamily:(Family *)currentFamliy;
- (void)updateFamliy:(Family *)family;
- (void)deleteRoom:(NSString *)familyId floorId:(NSString *)floorId roomId:(NSString *)roomId;
- (void)deleteMember:(NSString *)familyId userId:(NSString *)userId;
- (void)updateRoomName:(NSString *)familyId floorId:(NSString *)floorId roomId:(NSString *)roomId roomName:(NSString *)roomName;
- (void)updateFloorInfo:(NSString *)familyId floorArg:(FloorArg *)floorArg;
- (void)deleteFloorInfo:(NSString *)familyId floorId:(NSString *)floorId;
- (void)loadDataWithCache;
- (void)updateFamilyVirtualMemberInfo:(NSString *)familyId virtualMemberArgs:(VirtualMemberArgs *)virtualMemberArgs;
- (void)updateFamilyMemberInfo:(NSString *)familyId memberId:(NSString *)memberId memberRole:(NSString *)memberRole;
- (void)updateFamilyMemberInfo:(NSString *)familyId memberId:(NSString *)memberId memberType:(NSInteger)memberType;
- (void)updateFamilyVirtualMemberInfo:(NSString *)familyId memberId:(NSString *)memberId memberRole:(NSString *)memberRole;
- (void)updateFamilyRoomsOrder:(NSString *)familyId rooms:(NSArray<NSString *> *)rooms floorId:(NSString *)floorId;

- (void)updateFamilyRoomsOrderNew:(NSString *)familyId floorRooms:(NSArray<NSDictionary *> *)floorRooms;
@end

NS_ASSUME_NONNULL_END
