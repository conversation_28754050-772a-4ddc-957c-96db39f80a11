//
//  UpUserDomainStore.h
//  upuserdomain
//
//  Created by 闫达 on 2020/7/29.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpUserDomainDelegate.h"
#import "UpUserStore.h"
#import "UpDeviceStore.h"
#import "UpFamilyStore.h"
NS_ASSUME_NONNULL_BEGIN
@protocol UpUserDomainProvider
, UpUserDomainCacheDelegate, UDAuthDataDelegate;
@interface UpUserDomainStore : NSObject
- (instancetype)initUpUserDomainWithStoreCache:(id<UpUserDomainCacheDelegate>)userDomainCache userDomainprovider:(id<UpUserDomainProvider>)provider;
@property (nonatomic, strong) UpUserStore *userStore;
@property (nonatomic, strong) UpDeviceStore *deviceStore;
@property (nonatomic, strong) UpFamilyStore *familyStore;
@property (nonatomic, assign) UpUserDomainState loginState;
@property (nonatomic, strong) id<UDAuthDataDelegate> oauthData;

- (void)clearUserData;
- (void)updateCacheOauthData:(id<UDAuthDataDelegate>)oauthData;
- (BOOL)verifyOauthData:(id<UDAuthDataDelegate>)oauthData;
- (NSTimeInterval)caculateOauthDataExpire;
- (void)loadDataWithCache;
- (NSTimeInterval)caculatePlanRefreshTokenExpresIn;
@end

NS_ASSUME_NONNULL_END
