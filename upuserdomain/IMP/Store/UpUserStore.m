//
//  UpUserStore.m
//  upuserdomain
//
//  Created by 闫达 on 2020/7/29.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpUserStore.h"
#import "UserInfo.h"
#import "UserInfo+PrivateExtension.h"
#import "UserAddressInfo.h"
#import "UserAddressInfo+PrivateExtension.h"
#import "UpUserDomainCacheDelegate.h"
#import "UserAddressArgs.h"
#import "UserAddress.h"
#import "UserAddress+PrivateExtension.h"
#import "UserInfoArgs.h"
#import "UserTerminal.h"
#import <uplog/UPLog.h>
@interface UpUserStore ()
@property (nonatomic, strong) id<UpUserDomainCacheDelegate> userDomainCache;
@property (nonatomic, strong) id<UpUserDomainProvider> provider;
@end

@implementation UpUserStore
- (instancetype)initUpUserStoreWithStoreCache:(id<UpUserDomainCacheDelegate>)userDomainCache userDomainprovider:(id<UpUserDomainProvider>)provider
{
    if (self = [super init]) {
        _userDomainCache = userDomainCache;
        _provider = provider;
    }
    return self;
}

- (void)loadDataWithCache
{
    self.extraInfo = (UserInfo *)[self.userDomainCache getUserInfo];
    if ([self.extraInfo isKindOfClass:UserInfo.class]) {
        self.isRefreshUserCompleted = YES;
    }
    UPLogInfo(@"UPUserDomain", @"%s[%d]用户信息已从缓存中读取完毕", __PRETTY_FUNCTION__, __LINE__);
    self.terminals = (NSArray<UserTerminal *> *)[self.userDomainCache getTerminalList];
    UPLogInfo(@"UPUserDomain", @"%s[%d]用户登录终端列表已从缓存中读取完毕", __PRETTY_FUNCTION__, __LINE__);
    self.addressList = (NSArray<UserAddressInfo *> *)[self.userDomainCache getAddressList];
    UPLogInfo(@"UPUserDomain", @"%s[%d]用户登录地址列表已从缓存中读取完毕", __PRETTY_FUNCTION__, __LINE__);
}

- (void)deleteAddressId:(NSString *)addressId
{
    NSMutableArray<UserAddressInfo *> *addressList = self.addressList.mutableCopy;
    if (addressList == nil) {
        addressList = self.extraInfo.addresses.mutableCopy;
    }
    UserAddressInfo *addre;
    for (UserAddressInfo *address in addressList) {
        if ([addressId isEqualToString:address.addressId]) {
            addre = address;
            break;
        }
    }
    if (addre) {
        [addressList removeObject:addre];
    }
    self.addressList = addressList;
    [self.userDomainCache setAddressList:addressList];
}
- (void)updateAddress:(UserAddressArgs *)args
{
    UserAddressInfo *addre;
    for (UserAddressInfo *address in self.addressList) {
        if ([args.addressId isEqualToString:address.addressId]) {
            addre = address;
        }
        if (args.is_default) {
            address.realIs_default = NO;
        }
    }
    if (addre == nil) {
        return;
    }
    addre.realTag = args.tag ?: addre.realTag;
    addre.realEmail = args.email ?: addre.realEmail;
    addre.realSource = args.source ?: addre.realSource;
    addre.realUser_id = args.user_id ?: addre.realUser_id;
    addre.realIs_default = args.is_default;
    addre.realIs_service = args.is_service;
    addre.realReceiver_name = args.receiver_name ?: addre.realReceiver_name;
    addre.realReceiver_mobile = args.receiver_mobile ?: addre.realReceiver_mobile;
    UserAddress *address = (UserAddress *)addre.realAddress;
    address.realCity = args.city ?: address.realCity;
    address.realCity_id = args.city_id ?: address.realCity_id;
    address.realTown = args.town ?: address.realTown;
    address.realTown_id = args.town_id ?: address.realTown_id;
    address.realProvince = args.province ?: address.realProvince;
    address.realProvince_id = args.province_id ?: address.realProvince_id;
    address.realDistrict = args.district ?: address.realDistrict;
    address.realDistrict_id = args.district_id ?: address.realDistrict_id;
    address.realCountry_code = args.country_code ?: address.realCountry_code;
    address.realPostcode = args.postcode ?: address.realPostcode;
    address.realLine1 = args.line1 ?: address.realLine1;
    address.realLine2 = args.line2 ?: address.realLine2;
    [self.userDomainCache setAddressList:self.addressList];
}
- (void)updateUserInfoWithArgs:(UserInfoArgs *)args
{
    self.extraInfo.realGender = args.gender ?: self.extraInfo.realGender;
    self.extraInfo.realAvatarUrl = args.avatarUrl ?: self.extraInfo.realAvatarUrl;
    self.extraInfo.realEducation = args.education ?: self.extraInfo.realEducation;
    self.extraInfo.realExtraPhone = args.extraPhone ?: self.extraInfo.realExtraPhone;
    self.extraInfo.realBirthday = args.birthday ?: self.extraInfo.realBirthday;
    self.extraInfo.realHeight = args.height ?: self.extraInfo.realHeight;
    self.extraInfo.realWeight = args.weight ?: self.extraInfo.realWeight;
    self.extraInfo.realNickname = args.nickname ?: self.extraInfo.realNickname;
    self.extraInfo.realMarriage = args.marriage ?: self.extraInfo.realMarriage;
    self.extraInfo.realIncome = args.income ?: self.extraInfo.realIncome;
    self.extraInfo.realFamilyNum = args.familyNum ?: self.extraInfo.realFamilyNum;
    self.extraInfo.realGivenName = args.givenName ?: self.extraInfo.realGivenName;
    self.extraInfo.realDefaultAddress = nil;
    self.extraInfo.realAddresses = nil;
    [self.userDomainCache setUserInfo:_extraInfo];
}
- (void)updateAddressList:(NSArray<UserAddressInfo *> *)addressList
{
    self.addressList = addressList;
    [self.userDomainCache setAddressList:addressList];
}
- (void)updateTerminalsList:(NSArray<UserTerminal *> *)terminals
{
    self.terminals = terminals;
    [self.userDomainCache setTerminalList:terminals];
}
- (void)updateUserInfo:(UserInfo *)userInfo
{
    self.extraInfo = userInfo;
    [self.userDomainCache setUserInfo:userInfo];
}
@end
