//
//  UpFamilyStore.m
//  upuserdomain
//
//  Created by 闫达 on 2020/8/2.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpFamilyStore.h"
#import "UpUserDomainCacheDelegate.h"
#import "Family.h"
#import "Family+PrivateExtension.h"
#import "UpUserDomainProvider.h"
#import "UpEventHandlerManagerDelegate.h"
#import "UpEvent.h"
#import "Room.h"
#import "Room+PrivateExtension.h"
#import "FamilyMember.h"
#import "MemberInfo.h"
#import <uplog/UPLog.h>
#import "UDFloorInfo.h"
#import "UDFloorInfo+PrivateExtension.h"
#import "FloorArg.h"
#import "UPUserDomainSettings.h"
#import "FamilyMember+PrivateExtension.h"
#import "MemberInfo+PrivateExtension.h"
#import "VirtualMemberArgs.h"

@interface UpFamilyStore ()
@property (nonatomic, strong) id<UpUserDomainCacheDelegate> userDomainCache;
@property (nonatomic, strong) id<UpUserDomainProvider> provider;
@end
@implementation UpFamilyStore
- (instancetype)initUpFamilyStoreWithStoreCache:(id<UpUserDomainCacheDelegate>)userDomainCache userDomainprovider:(id<UpUserDomainProvider>)provider
{
    if (self = [super init]) {
        _userDomainCache = userDomainCache;
        _provider = provider;
    }
    return self;
}

- (void)loadDataWithCache
{
    if (!self.provider.provideUpUserDomain.getSettings.isRefreshFamilyListEnable) {
        self.isRefreshFamilyListCompleted = YES;
        return;
    }
    self.familyList = (NSArray<Family *> *)[self.userDomainCache getFamilyList];
    if (self.familyList.count == 0) {
        return;
    }
    self.isRefreshFamilyListCompleted = YES;
    UPLogInfo(@"UPUserDomain", @"%s[%d]用户家庭列表已从缓存中读取完毕", __PRETTY_FUNCTION__, __LINE__);
    self.currentFamliy = [self currentFamliySelector:[self.familyList copy]];
}

#pragma mark - property
- (Family *)currentFamliy
{
    _currentFamliy = [self currentFamliySelector:[self.familyList copy]];
    return _currentFamliy;
}

- (void)setFamilyList:(NSArray<Family *> *)familyList
{
    for (Family *family in familyList) {
        if (family.defaultFamily && _currentFamliy && ![family.familyId isEqualToString:_currentFamliy.familyId]) {
            [[self.provider provideEventManager] sendEvent:NOTIFY_CURRENT_FAMILY_CHANGED];
        }
        [family setOperatorManager:[self.provider provideOperatorManager]];
        [family setUserDomainprovider:self.provider];
    }
    _familyList = familyList;
}

#pragma mark - public methods
- (void)updateFamilyList:(NSArray<Family *> *)familyList
{
    self.familyList = familyList;
    [self.userDomainCache setFamilyList:familyList];
}
- (void)updateCurrentFamily:(Family *)currentFamliy
{
    self.currentFamliy = currentFamliy;
    [self.familyList enumerateObjectsUsingBlock:^(Family *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([currentFamliy.familyId isEqualToString:obj.familyId]) {
          obj.realDefaultFamily = YES;
      }
      else {
          obj.realDefaultFamily = NO;
      }
    }];
    [self.userDomainCache setFamilyList:self.familyList];
    [[self.provider provideEventManager] sendEvent:NOTIFY_CURRENT_FAMILY_CHANGED];
}
- (void)updateFamliy:(Family *)family
{
    NSMutableArray<Family *> *familyList = self.familyList.mutableCopy;
    __block NSUInteger index = 0;
    [familyList enumerateObjectsUsingBlock:^(Family *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([family.familyId isEqualToString:obj.familyId]) {
          index = idx;
          *stop = YES;
      }
    }];
    if (familyList.count > index) {
        [familyList replaceObjectAtIndex:index withObject:family];
        self.familyList = familyList;
        [self.userDomainCache setFamilyList:self.familyList];
    }
}
- (void)deleteRoom:(NSString *)familyId floorId:(NSString *)floorId roomId:(NSString *)roomId
{
    Family *family = [self findFamily:familyId];
    if (family == nil) {
        return;
    }
    UDFloorInfo *floorInfo = [self findFloorInfo:floorId floorInfos:family.floorInfos];
    if (floorInfo == nil) {
        return;
    }
    NSMutableArray<id<UDRoomDelegate>> *rooms = [floorInfo.rooms mutableCopy];
    __block id<UDRoomDelegate> room;
    [rooms enumerateObjectsUsingBlock:^(id<UDRoomDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([obj.roomId isEqualToString:roomId]) {
          room = obj;
      }
    }];
    if (room == nil) {
        return;
    }
    [rooms removeObject:room];
    floorInfo.realRooms = rooms;
    [self updateFamliy:family];
}
- (void)deleteMember:(NSString *)familyId userId:(NSString *)userId
{
    Family *family = [self findFamily:familyId];
    if (family == nil) {
        return;
    }
    NSMutableArray<id<UDFamilyMemberDelegate>> *members = [family.members mutableCopy];
    __block id<UDFamilyMemberDelegate> member;
    [members enumerateObjectsUsingBlock:^(id<UDFamilyMemberDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([obj.memberInfo.userId isEqualToString:userId]) {
          member = obj;
      }
    }];
    [members removeObject:member];
    family.realMembers = members;
}
- (void)updateRoomName:(NSString *)familyId floorId:(NSString *)floorId roomId:(NSString *)roomId roomName:(NSString *)roomName
{
    Family *family = [self findFamily:familyId];
    if (family == nil) {
        return;
    }
    UDFloorInfo *floorInfo = [self findFloorInfo:floorId floorInfos:family.floorInfos];
    if (floorInfo == nil) {
        return;
    }
    [floorInfo.rooms enumerateObjectsUsingBlock:^(id<UDRoomDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([obj.roomId isEqualToString:roomId]) {
          ((Room *)obj).realRoomName = roomName;
      }
    }];
    [self updateFamliy:family];
}
- (void)updateFloorInfo:(NSString *)familyId floorArg:(FloorArg *)floorArg
{
    Family *family = [self findFamily:familyId];
    if (family == nil) {
        return;
    }
    UDFloorInfo *floorInfo = [self findFloorInfo:floorArg.floorId floorInfos:family.floorInfos];
    if (floorInfo == nil) {
        return;
    }
    floorInfo.realFloorLogo = floorArg.floorLogo;
    floorInfo.realFloorName = floorArg.floorName;
    floorInfo.realFloorLabel = floorArg.floorLabel;
    floorInfo.realFloorPicture = floorArg.floorPicture;
    floorInfo.realFloorOrderId = floorArg.floorOrderId;
    [self updateFamliy:family];
}
- (void)deleteFloorInfo:(NSString *)familyId floorId:(NSString *)floorId
{
    Family *family = [self findFamily:familyId];
    if (family == nil) {
        return;
    }
    UDFloorInfo *floorInfo = [self findFloorInfo:floorId floorInfos:family.floorInfos];
    if (floorInfo == nil) {
        return;
    }
    NSMutableArray<id<UDFloorInfoDelegate>> *floorInfos = [family.floorInfos mutableCopy];
    [floorInfos removeObject:floorInfo];
    family.realFloorInfos = floorInfos;
    [self updateFamliy:family];
}

- (void)updateFamilyVirtualMemberInfo:(NSString *)familyId virtualMemberArgs:(VirtualMemberArgs *)virtualMemberArgs
{
    Family *family = [self findFamily:familyId];
    if (family == nil) {
        return;
    }
    FamilyMember *member = [self findFamilyMemberInfo:virtualMemberArgs.memberId members:family.members];
    if (member == nil) {
        return;
    }
    id<UDMemberInfoDelegate> memberInfo = member.memberInfo;
    MemberInfo *memberInfoModel = (MemberInfo *)memberInfo;
    if (virtualMemberArgs.avatarUrl) {
        memberInfoModel.realAvatarUrl = virtualMemberArgs.avatarUrl;
    }
    memberInfoModel.realName = virtualMemberArgs.memberName;
    if (virtualMemberArgs.birthday) {
        memberInfoModel.realBirthday = virtualMemberArgs.birthday;
    }
    member.realMemberInfo = memberInfoModel;
    [self updateFamliy:family];
}

- (void)updateFamilyMemberInfo:(NSString *)familyId memberId:(NSString *)memberId memberRole:(NSString *)memberRole
{
    Family *family = [self findFamily:familyId];
    if (family == nil) {
        return;
    }
    FamilyMember *member = [self findFamilyMemberInfo:memberId members:family.members];
    if (member == nil) {
        return;
    }
    member.realMemberRole = memberRole;
    [self updateFamliy:family];
}

- (void)updateFamilyMemberInfo:(NSString *)familyId memberId:(NSString *)memberId memberType:(NSInteger)memberType
{
    Family *family = [self findFamily:familyId];
    if (family == nil) {
        return;
    }
    FamilyMember *member = [self findFamilyMemberInfo:memberId members:family.members];
    if (member == nil) {
        return;
    }
    member.realMemberType = memberType;
    [self updateFamliy:family];
}

- (void)updateFamilyVirtualMemberInfo:(NSString *)familyId memberId:(NSString *)memberId memberRole:(NSString *)memberRole
{
    Family *family = [self findFamily:familyId];
    if (family == nil) {
        return;
    }
    FamilyMember *member = [self findFamilyMemberInfoWithUserId:memberId members:family.members];
    if (member == nil) {
        return;
    }
    member.realMemberRole = memberRole;
    [self updateFamliy:family];
}

- (void)updateFamilyRoomsOrder:(NSString *)familyId rooms:(NSArray<NSString *> *)rooms floorId:(NSString *)floorId
{
    Family *family = [self findFamily:familyId];
    if (family == nil) {
        return;
    }
    UDFloorInfo *floorInfo = [self findFloorInfo:floorId floorInfos:family.floorInfos];
    if (floorInfo == nil) {
        return;
    }
    NSMutableArray<id<UDRoomDelegate>> *array = [NSMutableArray array];
    [rooms enumerateObjectsUsingBlock:^(NSString *_Nonnull roomName, NSUInteger idx, BOOL *_Nonnull stop) {
      [floorInfo.rooms enumerateObjectsUsingBlock:^(id<UDRoomDelegate> _Nonnull room, NSUInteger idx, BOOL *_Nonnull stop) {
        if ([roomName isEqualToString:room.roomName]) {
            [array addObject:room];
        }
      }];
    }];
    floorInfo.realRooms = [array copy];
    [self updateFamliy:family];
}

- (void)updateFamilyRoomsOrderNew:(NSString *)familyId floorRooms:(NSArray<NSDictionary *> *)floorRooms
{
    Family *family = [self findFamily:familyId];
    if (family == nil) {
        return;
    }

    [floorRooms enumerateObjectsUsingBlock:^(NSDictionary *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      NSString *floorId = obj[@"floorId"];
      UDFloorInfo *floorInfo = [self findFloorInfo:floorId floorInfos:family.floorInfos];
      if (floorInfo == nil) {
          return;
      }

      NSArray *rooms = obj[@"rooms"];
      NSArray *sortedArray = [floorInfo.rooms sortedArrayUsingComparator:^NSComparisonResult(Room *_Nonnull obj1, Room *_Nonnull obj2) {
        NSNumber *idx1 = @([rooms indexOfObject:obj1.roomName]);
        NSNumber *idx2 = @([rooms indexOfObject:obj2.roomName]);
        obj1.realSortCode = idx1.stringValue;
        obj2.realSortCode = idx2.stringValue;
        return [idx1 compare:idx2];
      }];

      floorInfo.realRooms = sortedArray;
    }];
    [self updateFamliy:family];
}

#pragma mark - private methods
- (FamilyMember *)findFamilyMemberInfoWithUserId:(NSString *)memberId members:(NSArray<id<UDFamilyMemberDelegate>> *)members
{
    if (memberId.length == 0 || members.count == 0) {
        return nil;
    }
    __block FamilyMember *member;
    [members enumerateObjectsUsingBlock:^(id<UDFamilyMemberDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([obj.memberInfo.userId isEqualToString:memberId]) {
          member = (FamilyMember *)obj;
          *stop = YES;
      }
    }];
    return member;
}

- (FamilyMember *)findFamilyMemberInfo:(NSString *)memberId members:(NSArray<id<UDFamilyMemberDelegate>> *)members
{
    if (memberId.length == 0 || members.count == 0) {
        return nil;
    }
    __block FamilyMember *member;
    [members enumerateObjectsUsingBlock:^(id<UDFamilyMemberDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([obj.memberInfo.ucUserId isEqualToString:memberId]) {
          member = (FamilyMember *)obj;
          *stop = YES;
      }
    }];
    return member;
}

- (Family *)currentFamliySelector:(NSArray<Family *> *)familyList
{
    __block Family *tempCurrentFamily = nil;
    [familyList enumerateObjectsUsingBlock:^(Family *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (obj.defaultFamily) {
          tempCurrentFamily = obj;
          *stop = YES;
      }
    }];
    if (tempCurrentFamily) {
        return tempCurrentFamily;
    }
    NSUInteger maxCount = 0;
    for (Family *family in familyList) {
        NSUInteger count = family.getDeviceList.count;
        if (count > maxCount) {
            maxCount = count;
            tempCurrentFamily = family;
        }
    }
    return tempCurrentFamily;
}
- (Family *)findFamily:(NSString *)familyId
{
    if (familyId.length == 0) {
        return nil;
    }
    __block Family *family;
    [self.familyList enumerateObjectsUsingBlock:^(Family *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([familyId isEqualToString:obj.familyId]) {
          family = obj;
          *stop = YES;
      }
    }];
    return family;
}
- (UDFloorInfo *)findFloorInfo:(NSString *)floorId floorInfos:(NSArray<UDFloorInfo *> *)floorInfos
{
    if (floorId.length == 0 || floorInfos.count == 0) {
        return nil;
    }
    __block UDFloorInfo *floorInfo;
    [floorInfos enumerateObjectsUsingBlock:^(id<UDFloorInfoDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([floorId isEqualToString:obj.floorId]) {
          floorInfo = obj;
          *stop = YES;
      }
    }];
    return floorInfo;
}
@end
