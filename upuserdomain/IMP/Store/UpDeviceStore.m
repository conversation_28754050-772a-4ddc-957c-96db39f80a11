//
//  UpDeviceStore.m
//  upuserdomain
//
//  Created by 闫达 on 2020/8/2.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpDeviceStore.h"
#import "UpUserDomainCacheDelegate.h"
#import "Device.h"
#import "UpUserDomainProvider.h"
#import "UDDeviceInfo.h"
#import "UDDeviceInfo+PrivateExtension.h"
#import <uplog/UPLog.h>
#import "UPUserDomainSettings.h"

@interface UpDeviceStore ()
@property (nonatomic, strong) id<UpUserDomainCacheDelegate> userDomainCache;
@property (nonatomic, strong) id<UpUserDomainProvider> provider;
@end
@implementation UpDeviceStore
- (instancetype)initUpDeviceStoreWithStoreCache:(id<UpUserDomainCacheDelegate>)userDomainCache userDomainprovider:(id<UpUserDomainProvider>)provider
{
    if (self = [super init]) {
        _userDomainCache = userDomainCache;
        _provider = provider;
    }
    return self;
}

- (void)loadDataWithCache
{
    if (!self.provider.provideUpUserDomain.getSettings.isRefreshDeviceListEnable) {
        self.isRefreshDeviceListCompleted = YES;
        return;
    }
    self.deviceList = (NSArray<Device *> *)[self.userDomainCache getDeviceList];
    if (self.deviceList) {
        self.isRefreshDeviceListCompleted = YES;
    }
    UPLogInfo(@"UPUserDomain", @"%s[%d]用户设备列表已从缓存中读取完毕", __PRETTY_FUNCTION__, __LINE__);
}

- (void)setDeviceList:(NSArray<Device *> *)deviceList
{
    for (Device *device in deviceList) {
        [device setOperatorManager:[self.provider provideOperatorManager]];
        [device setUserDomainprovider:self.provider];
    }
    _deviceList = deviceList;
}
- (void)updateDeviceList:(NSArray<Device *> *)deviceList
{
    self.deviceList = deviceList;
    [self.userDomainCache setDeviceList:deviceList];
}
- (void)updateDevice:(NSString *)deviceId deviceName:(NSString *)deviceName
{
    for (Device *device in self.deviceList) {
        if ([deviceId isEqualToString:device.deviceId]) {
            UDDeviceInfo *deviceInfo = (UDDeviceInfo *)[device getInfo];
            deviceInfo.realDeviceName = deviceName;
            break;
        }
    }
    [self.userDomainCache setDeviceList:self.deviceList];
}
@end
