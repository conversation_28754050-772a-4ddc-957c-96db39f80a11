//
//  UpDeviceStore.h
//  upuserdomain
//
//  Created by 闫达 on 2020/8/2.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@class Device;
@protocol UpUserDomainProvider
, UpUserDomainCacheDelegate;
NS_ASSUME_NONNULL_BEGIN

@interface UpDeviceStore : NSObject
- (instancetype)initUpDeviceStoreWithStoreCache:(id<UpUserDomainCacheDelegate>)userDomainCache userDomainprovider:(id<UpUserDomainProvider>)provider;
@property (nonatomic, assign) BOOL isRefreshDeviceListCompleted;
@property (nonatomic, assign) BOOL isPreRefreshDeviceList;
@property (nonatomic, strong) NSArray<Device *> *deviceList;
- (void)updateDeviceList:(NSArray<Device *> *)deviceList;
- (void)updateDevice:(NSString *)deviceId deviceName:(NSString *)deviceName;
- (void)loadDataWithCache;
@end

NS_ASSUME_NONNULL_END
