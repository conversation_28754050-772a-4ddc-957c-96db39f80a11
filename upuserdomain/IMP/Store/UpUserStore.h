//
//  UpUserStore.h
//  upuserdomain
//
//  Created by 闫达 on 2020/7/29.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@class UserInfo, UserTerminal, UserAddressInfo, UserAddressArgs, UserInfoArgs;
@protocol UpUserDomainProvider
, UpUserDomainCacheDelegate;
NS_ASSUME_NONNULL_BEGIN
@interface UpUserStore : NSObject
- (instancetype)initUpUserStoreWithStoreCache:(id<UpUserDomainCacheDelegate>)userDomainCache userDomainprovider:(id<UpUserDomainProvider>)provider;
@property (nonatomic, strong) UserInfo *extraInfo;
@property (nonatomic, strong) NSArray<UserTerminal *> *terminals;
@property (nonatomic, assign) BOOL isRefreshUserCompleted;
@property (nonatomic, strong) NSArray<UserAddressInfo *> *addressList;
- (void)deleteAddressId:(NSString *)addressId;
- (void)updateAddress:(UserAddressArgs *)args;
- (void)updateUserInfoWithArgs:(UserInfoArgs *)args;
- (void)updateAddressList:(NSArray<UserAddressInfo *> *)addressList;
- (void)updateTerminalsList:(NSArray<UserTerminal *> *)terminals;
- (void)updateUserInfo:(UserInfo *)userInfo;
- (void)loadDataWithCache;
@end


NS_ASSUME_NONNULL_END
