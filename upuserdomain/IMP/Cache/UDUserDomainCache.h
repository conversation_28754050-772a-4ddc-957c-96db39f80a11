//
//  UDUserDomainCache.h
//  upuserdomain
//
//  Created by 闫达 on 2020/8/2.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpUserDomainCacheDelegate.h"

extern NSString *const UDOAUTHDATAKEY;
extern NSString *const OLDOAUTHDATAKEY;

@protocol UDCache;
NS_ASSUME_NONNULL_BEGIN

@interface UDUserDomainCache : NSObject <UpUserDomainCacheDelegate>
- (instancetype)initUserDomainCache:(id<UDCache>)udCache;
@end

NS_ASSUME_NONNULL_END
