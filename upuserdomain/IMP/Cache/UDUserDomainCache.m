//
//  UDUserDomainCache.m
//  upuserdomain
//
//  Created by 闫达 on 2020/8/2.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDUserDomainCache.h"
#import "UDCache.h"
#import <uplog/UPLog.h>
#import "UDFamilyDelegate.h"
#import "UDDeviceDelegate.h"
static NSString *const UDFAMLILYLISTKEY = @"familyList";
static NSString *const UDDEVICELISTKEY = @"deviceList";
static NSString *const UDUSERINFOKEY = @"userInfo";
static NSString *const UDADDRESSLISTKEY = @"addressList";
NSString *const UDOAUTHDATAKEY = @"authData";
static NSString *const UDTERMINALLISTKEY = @"terminalList";
NSString *const OLDOAUTHDATAKEY = @"ApplicationOathData";
@interface UDUserDomainCache ()
@property (nonatomic, strong) id<UDCache> udCache;
@end
@implementation UDUserDomainCache
- (instancetype)initUserDomainCache:(id<UDCache>)udCache
{
    if (self = [super init]) {
        _udCache = udCache;
    }
    return self;
}
- (void)clearAllData
{
    [self.udCache clear];
    UPLogInfo(@"UPUserDomain", @"%s[%d]清空缓存", __PRETTY_FUNCTION__, __LINE__);
}

- (id<UDAuthDataDelegate>)getAuthData
{
    UPLogInfo(@"UPUserDomain", @"%s[%d]读取鉴权信息缓存", __PRETTY_FUNCTION__, __LINE__);
    id<UDAuthDataDelegate> oathData = (id<UDAuthDataDelegate>)[self.udCache getObject:UDOAUTHDATAKEY];
    if (oathData) {
        return oathData;
    }
    oathData = (id<UDAuthDataDelegate>)[self.udCache getObject:OLDOAUTHDATAKEY];
    return oathData;
}

- (NSArray<id<UDDeviceDelegate>> *)getDeviceList
{
    NSArray<id<UDDeviceDelegate>> *deviceList = (NSArray<id<UDDeviceDelegate>> *)[self.udCache getObject:UDDEVICELISTKEY];
    UPLogInfo(@"UPUserDomain", @"%s[%d]读取设备列表缓存", __PRETTY_FUNCTION__, __LINE__);
    return deviceList;
}

- (NSArray<id<UDFamilyDelegate>> *)getFamilyList
{
    NSArray<id<UDFamilyDelegate>> *familyList = (NSArray<id<UDFamilyDelegate>> *)[self.udCache getObject:UDFAMLILYLISTKEY];
    UPLogInfo(@"UPUserDomain", @"%s[%d]读取家庭列表缓存", __PRETTY_FUNCTION__, __LINE__);
    return familyList;
}

- (NSArray<id<UDUserTermDelegate>> *)getTerminalList
{
    NSArray<id<UDUserTermDelegate>> *terminalList = (NSArray<id<UDUserTermDelegate>> *)[self.udCache getObject:UDTERMINALLISTKEY];
    UPLogInfo(@"UPUserDomain", @"%s[%d]读取终端列表缓存", __PRETTY_FUNCTION__, __LINE__);
    return terminalList;
}

- (NSArray<id<UDAddressDelegate>> *)getAddressList
{
    NSArray<id<UDAddressDelegate>> *addressList = (NSArray<id<UDAddressDelegate>> *)[self.udCache getObject:UDADDRESSLISTKEY];
    UPLogInfo(@"UPUserDomain", @"%s[%d]读取地址列表缓存", __PRETTY_FUNCTION__, __LINE__);
    return addressList;
}
- (id<UDUserInfoDelegate>)getUserInfo
{
    UPLogInfo(@"UPUserDomain", @"%s[%d]读取用户信息缓存", __PRETTY_FUNCTION__, __LINE__);
    return (id<UDUserInfoDelegate>)[self.udCache getObject:UDUSERINFOKEY];
}

- (void)setAuthData:(id<UDAuthDataDelegate>)authData
{
    UPLogInfo(@"UPUserDomain", @"%s[%d]设置鉴权信息缓存", __PRETTY_FUNCTION__, __LINE__);
    [self.udCache putObject:authData key:UDOAUTHDATAKEY];
}

- (void)setDeviceList:(NSArray<id<UDDeviceDelegate>> *)infoList
{
    UPLogInfo(@"UPUserDomain", @"%s[%d]设置设备列表缓存", __PRETTY_FUNCTION__, __LINE__);
    [self.udCache putObject:infoList key:UDDEVICELISTKEY];
}

- (void)setFamilyList:(NSArray<id<UDFamilyDelegate>> *)infoList
{
    UPLogInfo(@"UPUserDomain", @"%s[%d]设置家庭列表缓存", __PRETTY_FUNCTION__, __LINE__);
    [self.udCache putObject:infoList key:UDFAMLILYLISTKEY];
}

- (void)setTerminalList:(NSArray<id<UDUserTermDelegate>> *)infoList
{
    UPLogInfo(@"UPUserDomain", @"%s[%d]设置终端列表缓存", __PRETTY_FUNCTION__, __LINE__);
    [self.udCache putObject:infoList key:UDTERMINALLISTKEY];
}
- (void)setUserInfo:(id<UDUserInfoDelegate>)userInfo
{
    UPLogInfo(@"UPUserDomain", @"%s[%d]设置用户信息缓存", __PRETTY_FUNCTION__, __LINE__);
    [self.udCache putObject:userInfo key:UDUSERINFOKEY];
}
- (void)setAddressList:(NSArray<id<UDAddressDelegate>> *)addressList
{
    UPLogInfo(@"UPUserDomain", @"%s[%d]设置地址列表缓存", __PRETTY_FUNCTION__, __LINE__);
    [self.udCache putObject:addressList key:UDADDRESSLISTKEY];
}

@end
