//
//  UDCacheIMP.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/26.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDCacheIMP.h"
#import <uplog/UPLog.h>
#define fileFloderPath [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject] stringByAppendingPathComponent:@"oauthData"]
#define filePath(fileName) [fileFloderPath stringByAppendingPathComponent:fileName]
@implementation UDCacheIMP
- (instancetype)init
{
    if (self = [super init]) {
        [self createUserDomainCacheFloder];
    }
    return self;
}

- (BOOL)putObject:(id<NSCoding>)object key:(NSString *)key
{
    return [NSKeyedArchiver archiveRootObject:object toFile:filePath(key)];
}

- (id<NSCoding>)getObject:(NSString *)key
{
    id<NSCoding> object = [NSKeyedUnarchiver unarchiveObjectWithFile:filePath(key)];
    return object;
}

- (void)createUserDomainCacheFloder
{
    BOOL isDir = NO;
    [[NSFileManager defaultManager] fileExistsAtPath:fileFloderPath isDirectory:(&isDir)];
    if (!isDir) {
        NSError *error = NULL;
        [[NSFileManager defaultManager] createDirectoryAtPath:fileFloderPath withIntermediateDirectories:YES attributes:nil error:&error];
        if (error) {
            UPLogError(@"UPUserDomain", @"%s[%d]本地缓存文件夹创建失败！error:%@", __PRETTY_FUNCTION__, __LINE__, error);
        }
    }
}
- (void)clear
{
    BOOL exist = [[NSFileManager defaultManager] fileExistsAtPath:fileFloderPath];
    if (exist) {
        NSError *error = NULL;
        [[NSFileManager defaultManager] removeItemAtPath:fileFloderPath error:&error];
        if (error) {
            UPLogError(@"UPUserDomain", @"%s[%d]本地缓存文件夹删除失败！error:%@", __PRETTY_FUNCTION__, __LINE__, error);
        }
    }
    [self createUserDomainCacheFloder];
}

@end
