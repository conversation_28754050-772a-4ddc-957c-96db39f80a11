//
//  Room.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "Room.h"
#import "Room+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation Room
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realRoomClass", @"realRoomId", @"realRoomLabel", @"realRoomLogo", @"realRoomName", @"realRoomPicture", @"realSortCode" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"roomClass", @"roomId", @"roomLabel", @"roomLogo", @"roomName", @"roomPicture", @"sortCode" ];
}

- (NSString *)roomClass
{
    return self.realRoomClass;
}

- (NSString *)roomId
{
    return self.realRoomId;
}

- (NSString *)roomLabel
{
    return self.realRoomLabel;
}

- (NSString *)roomLogo
{
    return self.realRoomLogo;
}

- (NSString *)roomName
{
    return self.realRoomName;
}

- (NSString *)roomPicture
{
    return self.realRoomPicture;
}

- (NSString *)sortCode
{
    return self.realSortCode;
}

@end
