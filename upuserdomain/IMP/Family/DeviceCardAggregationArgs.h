//
//  DeviceCardAggregationArgs.h
//  upuserdomain
//
//  Created by lubiao on 2025/3/26.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface AggCard : NSObject

//0-灯光 1-窗帘 2-环境 3-非网器 4-长期离线 5-摄像头
@property (nonnull, nonatomic, copy) NSString *aggType;

/// 聚合卡片内部已排序的所有设备id列表
@property (nonnull, nonatomic, strong) NSArray<NSString *> *sortList;

/// 聚合卡片内部大卡片设备id列表
@property (nullable, nonatomic, strong) NSArray<NSString *> *bigCardList;

/// 聚合卡片内部小卡片设备id列表
@property (nullable, nonatomic, strong) NSArray<NSString *> *smallCardList;

@end

@interface DeviceCardAggregationArgs : NSObject

/// 家庭id
@property (nullable, nonatomic, copy) NSString *familyId;

/// 聚合卡片
@property (nullable, nonatomic, strong) NSArray<AggCard *> *aggCard;

@end

NS_ASSUME_NONNULL_END
