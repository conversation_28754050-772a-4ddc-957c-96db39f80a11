//
//  Family.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UDFamilyDelegate.h"
#import "UDUserModelTransformer.h"
NS_ASSUME_NONNULL_BEGIN
@class UDOperatorManager;
@protocol UpUserDomainProvider;
@interface Family : NSObject <UDFamilyDelegate, UDUserModelTransformer, NSCoding>
- (void)setOperatorManager:(UDOperatorManager *)opManager;
- (void)setUserDomainprovider:(id<UpUserDomainProvider>)provider;
@end

NS_ASSUME_NONNULL_END
