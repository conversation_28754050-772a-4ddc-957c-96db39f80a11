//
//  FamilyLocation.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FamilyLocation.h"
#import "FamilyLocation+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation FamilyLocation
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realCityCode", @"realLatitude", @"realLongitude" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"cityCode", @"latitude", @"longitude" ];
}

- (NSString *)cityCode
{
    return self.realCityCode;
}

- (NSString *)latitude
{
    return self.realLatitude;
}

- (NSString *)longitude
{
    return self.realLongitude;
}

@end
