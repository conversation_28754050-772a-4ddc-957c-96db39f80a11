//
//  FamilyInfo.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/8.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FamilyInfo.h"
#import "FamilyInfo+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation FamilyInfo
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realFamilyLocation", @"realFamilyName", @"realFamilyPosition" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"familyLocation", @"familyName", @"familyPosition" ];
}

+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"familyLocation"]) {
          [property.type setValue:NSClassFromString(@"FamilyLocation") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}
- (id<UDFamilyLocationDelegate>)familyLocation
{
    return self.realFamilyLocation;
}

- (NSString *)familyName
{
    return self.realFamilyName;
}

- (NSString *)familyPosition
{
    return self.realFamilyPosition;
}

@end
