//
//  Family.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "Family.h"
#import "Family+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
#import "UDUpdateFamilyDetailOp.h"
#import "UDOperatorManager.h"
#import <uplog/UPLog.h>
#import "UDCreateRoomOp.h"
#import "UDCreateRoomNewOp.h"
#import "RoomArgs.h"
#import "RoomNewArgs.h"
#import "UDDeleteRoomOp.h"
#import "UDEditRoomNameOp.h"
#import "UDInviteFamilyMemberOp.h"
#import "UDDeleteMemberOp.h"
#import "UDUnbindDevicesOp.h"
#import "UpUserDomainProvider.h"
#import "Device.h"
#import "UDRoomDelegate.h"
#import "UDMoveDevicesToRoomOp.h"
#import "UDDeleteDevicesOp.h"
#import "UDMoveDevicesToFamilyOp.h"
#import "UDExitFamilyAsAdminOp.h"
#import "UDDeleteFamilyAsAdminOp.h"
#import "UDChangeFamilyAdminOp.h"
#import "UDEditFamilyInfoOp.h"
#import "FamilyArgs.h"
#import "UDRefreshRoomListOp.h"
#import "UDExitFamilyAsMemberOp.h"
#import "UDFloorInfo.h"
#import "UDAddFloorOp.h"
#import "UDEditFloorOp.h"
#import "UDDeleteFloorOp.h"
#import "FloorArg.h"
#import "UDQueryFirstMemeberOp.h"
#import "UDAddVirtualMemberOp.h"
#import "UDModifyVirtualMemberOp.h"
#import "UDModifyMemberRoleOp.h"
#import "UDModifyMemberTypeOp.h"
#import "VirtualMemberArgs.h"
#import "UDModifyVirtualMemberRoleOp.h"
#import "UDSaveRoomsOrderOp.h"
#import "UDGetGroupDevicesOp.h"
#import "DeviceCardStatusArgs.h"
#import "DeviceCardAggregationArgs.h"
#import "UDModifyDeviceCardListOp.h"
#import "UDModifyDeviceAggOp.h"
#import "UDFamilyInfoDelegate.h"
#import "UDAuthDataDelegate.h"
#import "UDFamilyMemberDelegate.h"
#import "UDMemberInfoDelegate.h"
#import "AdminInviteMemberArgs.h"
#import "UDSaveRoomsOrderNewOp.h"

@interface Family ()
@property (nonatomic, strong) UDOperatorManager *opManager;
@property (nonatomic, strong) id<UpUserDomainProvider> userDomainProvider;
@end
@implementation Family
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realFamilyId", @"realCreateTime", @"realAppId", @"realInfo", @"realMembers", @"realFloorInfos", @"realOwner", @"realOwnerId", @"realLocationChangeFlag", @"realDefaultFamily", @"devices", @"sharedDevices", @"opManager", @"userDomainProvider", @"getDeviceList", @"realFamilyDeviceCount", @"memberType", @"joinTime" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"familyId", @"createTime", @"appId", @"info", @"members", @"floorInfos", @"owner", @"ownerId", @"locationChangeFlag", @"defaultFamily", @"devices", @"sharedDevices", @"opManager", @"userDomainProvider", @"getDeviceList", @"firstMember", @"familyDeviceCount", @"memberType", @"joinTime" ];
}

+ (NSDictionary *)mj_objectClassInArray
{
    return @{ @"members" : @"FamilyMember",
              @"floorInfos" : @"UDFloorInfo" };
}
+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"info"]) {
          [property.type setValue:NSClassFromString(@"FamilyInfo") forKey:@"typeClass"];
      }
      if ([property.name isEqualToString:@"owner"]) {
          [property.type setValue:NSClassFromString(@"MemberInfo") forKey:@"typeClass"];
      }
      if ([property.name isEqualToString:@"firstMember"]) {
          [property.type setValue:NSClassFromString(@"FamilyMember") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}
- (NSDictionary *)up_keyValues
{
    NSMutableDictionary *keyValues = [self mj_keyValues];
    NSMutableDictionary *devices = [NSMutableDictionary dictionary];
    NSMutableDictionary *shareDevices = [NSMutableDictionary dictionary];
    [self.devices enumerateKeysAndObjectsUsingBlock:^(NSString *_Nonnull key, id<UDDeviceDelegate> _Nonnull obj, BOOL *_Nonnull stop) {
      [devices setValue:((Device *)obj).mj_keyValues forKey:key];
    }];
    [self.sharedDevices enumerateKeysAndObjectsUsingBlock:^(NSString *_Nonnull key, id<UDDeviceDelegate> _Nonnull obj, BOOL *_Nonnull stop) {
      [shareDevices setValue:((Device *)obj).mj_keyValues forKey:key];
    }];

    [keyValues setObject:devices forKey:@"devices"];
    [keyValues setObject:shareDevices forKey:@"sharedDevices"];
    return keyValues;
}
- (void)setOperatorManager:(UDOperatorManager *)opManager
{
    _opManager = opManager;
}
- (void)setUserDomainprovider:(id<UpUserDomainProvider>)provider
{
    _userDomainProvider = provider;
}
- (void)addRoom:(RoomArgs *)roomArgs success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (roomArgs.name.length == 0 || roomArgs.floorId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDCreateRoomOp class]
                                args:@{ @"roomArgs" : roomArgs,
                                        @"familyId" : self.familyId }
                             success:success
                             failure:failure];
}

- (void)addRoomNew:(RoomNewArgs *)roomArgs success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (roomArgs.roomName.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }

    NSDictionary *args = @{
        @"roomNewArgs" : roomArgs,
        @"familyId" : self.familyId
    };
    [self.opManager operateWithClass:[UDCreateRoomNewOp class] args:args success:success failure:failure];
}

- (void)adminDeleteMember:(NSString *)userId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (userId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDDeleteMemberOp class]
                                args:@{ @"userId" : userId,
                                        @"familyId" : self.familyId }
                             success:success
                             failure:failure];
}

- (void)adminInvitateMember:(AdminInviteMemberArgs *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (args.userId.length == 0 || args.nickname.length == 0 || args.memberType < 1) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }

    NSDictionary *params = @{
        @"adminInviteMemberArgs" : args,
        @"familyId" : self.familyId
    };
    [self.opManager operateWithClass:UDInviteFamilyMemberOp.class
                                args:params
                             success:success
                             failure:failure];
}

- (void)changeFamilyAdminUserId:(NSString *)userId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (userId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDChangeFamilyAdminOp class]
                                args:@{ @"userId" : userId,
                                        @"familyId" : self.familyId }
                             success:success
                             failure:failure];
}

- (void)destoryFamilyAsAdminSuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self.opManager operateWithClass:[UDDeleteFamilyAsAdminOp class]
                                args:self.familyId
                             success:success
                             failure:failure];
}

- (void)removeRoom:(NSString *)roomId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (roomId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDDeleteRoomOp class]
                                args:@{ @"roomId" : roomId,
                                        @"familyId" : self.familyId }
                             success:success
                             failure:failure];
}

- (BOOL)defaultFamily
{
    return self.realDefaultFamily;
}


- (NSDictionary<NSString *, id<UDDeviceDelegate>> *)devices
{
    NSArray<Device *> *deviceList = self.userDomainProvider.provideUpUserDomainStore.deviceStore.deviceList;
    NSMutableDictionary *deviceDict = [NSMutableDictionary dictionary];
    [deviceList enumerateObjectsUsingBlock:^(Device *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([obj.familyId isEqualToString:self.familyId] || obj.sharedDeviceFlag) {
          [deviceDict setValue:obj forKey:obj.deviceId];
      }
    }];
    return deviceDict;
}


- (void)exitFamilyAsAdmin:(NSString *)userId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (userId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDExitFamilyAsAdminOp class]
                                args:@{ @"userId" : userId,
                                        @"familyId" : self.familyId }
                             success:success
                             failure:failure];
}


- (void)exitFamilyAsMemberSuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self.opManager operateWithClass:[UDExitFamilyAsMemberOp class]
                                args:self.familyId
                             success:success
                             failure:failure];
}


- (NSString *)familyId
{
    return self.realFamilyId;
}

- (NSString *)createTime
{
    return self.realCreateTime;
}
- (NSString *)appId
{
    return self.realAppId;
}

- (NSArray<id<UDDeviceDelegate>> *)getDeviceList
{
    NSArray<Device *> *deviceList = self.userDomainProvider.provideUpUserDomainStore.deviceStore.deviceList;
    NSMutableArray *devices = [NSMutableArray array];
    [deviceList enumerateObjectsUsingBlock:^(Device *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([obj.familyId isEqualToString:self.familyId] || obj.sharedDeviceFlag) {
          [devices addObject:obj];
      }
    }];
    return devices;
}


- (id<UDFamilyInfoDelegate>)info
{
    return self.realInfo;
}


- (NSArray<id<UDFamilyMemberDelegate>> *)members
{
    return self.realMembers;
}
- (id<UDFamilyMemberDelegate>)firstMember
{
    return self.realMembers.firstObject;
}


- (void)moveDevicesToOtherRoom:(id<UDRoomDelegate>)newRoom devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (newRoom.roomId.length == 0 || devices.count == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    BOOL checkDeviceResult = YES;
    for (id<UDDeviceDelegate> device in devices) {
        if (device.deviceId.length == 0 || device.deviceName.length == 0 || device.roomName.length == 0) {
            checkDeviceResult = NO;
            break;
        }
    }
    if (checkDeviceResult == NO) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDMoveDevicesToRoomOp class]
                                args:@{ @"familyId" : self.familyId,
                                        @"devices" : devices,
                                        @"room" : newRoom }
                             success:success
                             failure:failure];
}


- (id<UDMemberInfoDelegate>)owner
{
    return self.realOwner;
}

- (NSString *)ownerId
{
    return self.realOwnerId;
}

- (BOOL)locationChangeFlag
{
    return self.realLocationChangeFlag;
}

- (NSString *)familyDeviceCount
{
    return self.realFamilyDeviceCount;
}

- (NSInteger)memberType
{
    NSInteger mType = 2; // 默认普通成员
    NSString *currentUserId = [[[self userDomainProvider] provideUpUserDomain] oauthData].uc_user_id;
    for (id<UDFamilyMemberDelegate> mb in self.members) {
        if ([currentUserId isEqualToString:mb.memberInfo.ucUserId] || mb.memberInfo == nil) {
            UPLogInfo(@"UPUserDomain", @"found current user in family (%@-%@), memberType = %@", mb.memberName, self.realInfo.familyName, @(mb.memberType));
            mType = mb.memberType;
            break;
        }
    }
    return mType;
}

- (NSString *)joinTime
{
    NSString *time = @"";
    NSString *currentUserId = [[[self userDomainProvider] provideUpUserDomain] oauthData].uc_user_id;
    for (id<UDFamilyMemberDelegate> mb in self.members) {
        if ([currentUserId isEqualToString:mb.memberInfo.ucUserId] || mb.memberInfo == nil) {
            UPLogInfo(@"UPUserDomain", @"found current user in family (%@-%@), joinTime = %@", mb.memberName, self.realInfo.familyName, mb.joinTime);
            time = mb.joinTime;
            break;
        }
    }
    return time;
}

- (void)queryInfoSuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self.opManager operateWithClass:[UDUpdateFamilyDetailOp class] args:self.familyId success:success failure:failure];
}


- (void)queryRoomList:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (floorId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDRefreshRoomListOp class]
                                args:@{ @"familyId" : self.familyId,
                                        @"floorId" : floorId }
                             success:success
                             failure:failure];
}


- (void)removeDevices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (devices.count == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    BOOL checkDeviceResult = YES;
    for (id<UDDeviceDelegate> device in devices) {
        if (device.deviceId.length == 0 || device.deviceName.length == 0 || device.roomName.length == 0) {
            checkDeviceResult = NO;
            break;
        }
    }
    if (checkDeviceResult == NO) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDDeleteDevicesOp class]
                                args:@{ @"familyId" : self.familyId,
                                        @"devices" : devices }
                             success:success
                             failure:failure];
}

- (NSArray<id<UDFloorInfoDelegate>> *)floorInfos
{
    return self.realFloorInfos;
}

- (void)setDefaultFamily:(BOOL)defaultFamily
{
    self.realDefaultFamily = defaultFamily;
}


- (NSDictionary<NSString *, id<UDDeviceDelegate>> *)sharedDevices
{
    NSArray<Device *> *deviceList = self.userDomainProvider.provideUpUserDomainStore.deviceStore.deviceList;
    NSMutableDictionary *shareDeviceDict = [NSMutableDictionary dictionary];
    [deviceList enumerateObjectsUsingBlock:^(Device *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([obj.familyId isEqualToString:self.familyId]) {
          if (obj.isOwned) {
              [shareDeviceDict setValue:obj forKey:obj.deviceId ?: obj.barcode];
          }
      }
    }];
    return shareDeviceDict;
}


- (void)unBindDevices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (devices.count == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDUnbindDevicesOp class]
                                args:@{ @"devices" : devices,
                                        @"familyId" : self.familyId }
                             success:success
                             failure:failure];
}


- (void)updateFamilyInfo:(FamilyArgs *)familyArgs success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (familyArgs.name.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDEditFamilyInfoOp class]
                                args:@{ @"familyArgs" : familyArgs,
                                        @"familyId" : self.familyId }
                             success:success
                             failure:failure];
}


- (void)updateRoomName:(NSString *)roomName roomId:(NSString *)roomId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (roomName.length == 0 || roomId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    __block NSString *floorId = @"";
    [self.floorInfos enumerateObjectsUsingBlock:^(id<UDFloorInfoDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      for (id<UDRoomDelegate> room in obj.rooms) {
          NSString *roomIdOther = room.roomId;
          if ([roomIdOther isKindOfClass:[NSString class]] && [roomId isEqualToString:roomIdOther]) {
              floorId = obj.floorId;
              *stop = YES;
              break;
          }
      }
    }];
    [self.opManager operateWithClass:[UDEditRoomNameOp class]
                                args:@{ @"roomId" : roomId,
                                        @"familyId" : self.familyId,
                                        @"roomName" : roomName,
                                        @"floorId" : floorId
                                }
                             success:success
                             failure:failure];
}
- (void)moveDevicesToOtherFamily:(NSString *)newFamilyId devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (newFamilyId.length == 0 || devices.count == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDMoveDevicesToFamilyOp class]
                                args:@{ @"newFamilyId" : newFamilyId,
                                        @"familyId" : self.familyId,
                                        @"devices" : devices }
                             success:success
                             failure:failure];
}
- (void)createFloor:(FloorArg *)floorArg success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (floorArg.floorOrderId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:UDAddFloorOp.class
                                args:@{ @"familyId" : self.familyId,
                                        @"floorArg" : floorArg }
                             success:success
                             failure:failure];
}

- (void)deleteFloor:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (floorId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:UDDeleteFloorOp.class
                                args:@{ @"familyId" : self.familyId,
                                        @"floorId" : floorId }
                             success:success
                             failure:failure];
}
- (void)editFloor:(FloorArg *)floorArg success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (floorArg.floorId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:UDEditFloorOp.class
                                args:@{ @"familyId" : self.familyId,
                                        @"floorArg" : floorArg }
                             success:success
                             failure:failure];
}

- (void)queryFirstMemeberSuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self.opManager operateWithClass:UDQueryFirstMemeberOp.class
                                args:@{ @"familyId" : self.familyId }
                             success:success
                             failure:failure];
}

- (void)addVirtualMember:(NSString *)memberId memberName:(NSString *)memberName success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (memberId.length == 0 || memberName.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:UDAddVirtualMemberOp.class
                                args:@{ @"familyId" : self.familyId,
                                        @"memberId" : memberId,
                                        @"memberName" : memberName }
                             success:success
                             failure:failure];
}

- (void)modifyVirtualMember:(VirtualMemberArgs *)virtualMemberArgs success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (virtualMemberArgs.memberId.length == 0 || virtualMemberArgs.memberName.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:UDModifyVirtualMemberOp.class
                                args:@{ @"virtualMemberArgs" : virtualMemberArgs,
                                        @"familyId" : self.familyId }
                             success:success
                             failure:failure];
}

- (void)modifyMemberRole:(NSString *)memberId memberRole:(NSString *)memberRole success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (memberId.length == 0 || memberRole.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:UDModifyMemberRoleOp.class
                                args:@{ @"memberId" : memberId,
                                        @"familyId" : self.familyId,
                                        @"memberRole" : memberRole }
                             success:success
                             failure:failure];
}

- (void)modifyMemberType:(NSString *)memberId memberType:(NSInteger)memberType success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (memberId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:UDModifyMemberTypeOp.class
                                args:@{ @"memberId" : memberId,
                                        @"familyId" : self.familyId,
                                        @"memberType" : @(memberType) }
                             success:success
                             failure:failure];
}

- (void)modifyVirtualMemberRole:(NSString *)memberId memberRole:(NSString *)memberRole success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (memberId.length == 0 || memberRole.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:UDModifyVirtualMemberRoleOp.class
                                args:@{ @"memberId" : memberId,
                                        @"familyId" : self.familyId,
                                        @"memberRole" : memberRole }
                             success:success
                             failure:failure];
}

- (void)saveRoomsOrder:(NSArray<NSString *> *)rooms floorId:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (rooms.count == 0 || floorId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:UDSaveRoomsOrderOp.class
                                args:@{ @"rooms" : rooms,
                                        @"familyId" : self.familyId,
                                        @"floorId" : floorId }
                             success:success
                             failure:failure];
}

- (void)saveRoomsOrderNew:(NSArray<NSDictionary *> *)sortList success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (sortList.count == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }

    [self.opManager operateWithClass:[UDSaveRoomsOrderNewOp class]
                                args:@{ @"familyId" : self.familyId,
                                        @"sortList" : sortList }
                             success:success
                             failure:failure];
}

- (void)getGroupDeviceListSuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self.opManager operateWithClass:UDGetGroupDevicesOp.class
                                args:@{ @"familyId" : self.familyId }
                             success:success
                             failure:failure];
}

- (void)modifyDeviceCardStatus:(DeviceCardStatusArgs *)deviceCardStatus
                       success:(userDomainCallback)success
                       failure:(userDomainCallback)failure
{
    if ((deviceCardStatus.middleCardList && deviceCardStatus.bigCardList && deviceCardStatus.smallCardList) ||
        (!deviceCardStatus.middleCardList && !deviceCardStatus.bigCardList && !deviceCardStatus.smallCardList)) {
        [self.opManager operateWithClass:UDModifyDeviceCardListOp.class
                                    args:(NSObject *)deviceCardStatus
                                 success:success
                                 failure:failure];
        return;
    }


    if (failure) {
        NSError *error = [NSError errorWithDomain:@"com.uplus.userdomain" code:-1 userInfo:@{ NSLocalizedDescriptionKey : @"参数`bigCardList`, `middleCardList`和`smallCardList`必须同时为null或同时不为null." }];
        UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
        failure(result);
    }
}

- (void)modifyDeviceAggregation:(DeviceCardAggregationArgs *)deviceCardAgg
                        success:(userDomainCallback)success
                        failure:(userDomainCallback)failure
{
    if (deviceCardAgg.aggCard.count) {
        NSError *error = nil;
        for (AggCard *card in deviceCardAgg.aggCard) {
            if (card.aggType.length < 1 || !card.sortList) {
                error = [NSError errorWithDomain:@"com.uplus.userdomain" code:-1 userInfo:@{ NSLocalizedDescriptionKey : @"缺少必要参数`aggType`或`sortList`." }];
                break;
            }
        }
        if (error) {
            if (failure) {
                UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
                failure(result);
            }
            return;
        }
    }
    [self.opManager operateWithClass:UDModifyDeviceAggOp.class
                                args:(NSObject *)deviceCardAgg
                             success:success
                             failure:failure];
}

@end
