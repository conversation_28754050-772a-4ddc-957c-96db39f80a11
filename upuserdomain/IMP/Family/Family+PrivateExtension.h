//
//  Family+PrivateExtension.h
//  UserDomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "Family.h"
@protocol UDRoomDelegate
, UDDeviceDelegate, UDFamilyInfoDelegate, UDFamilyMemberDelegate, UDMemberInfoDelegate, UDFloorInfoDelegate;
NS_ASSUME_NONNULL_BEGIN

@interface Family ()
/**
家庭id
*/
@property (nonatomic, copy) NSString *realFamilyId;
/**
创建时间
*/
@property (nonatomic, copy) NSString *realCreateTime;
/**
appId
*/
@property (nonatomic, copy) NSString *realAppId;


/// 家庭信息
@property (nonatomic, strong) id<UDFamilyInfoDelegate> realInfo;

/// 获取成员列表
@property (nonatomic, strong) NSArray<id<UDFamilyMemberDelegate>> *realMembers;

/// 家庭管理员信息
@property (nonatomic, strong) id<UDMemberInfoDelegate> realOwner;

/// 家庭管理员信息ID
@property (nonatomic, strong) NSString *realOwnerId;

/// 家庭楼层列表
@property (nonatomic, strong) NSArray<id<UDFloorInfoDelegate>> *realFloorInfos;

/// 是否默认家庭
@property (nonatomic, assign) BOOL realDefaultFamily;
/**
 家庭位置更改标志位
 */
@property (assign, nonatomic) BOOL realLocationChangeFlag;
/**
 设备数量
 */
@property (copy, nonatomic) NSString *realFamilyDeviceCount;

@end

NS_ASSUME_NONNULL_END
