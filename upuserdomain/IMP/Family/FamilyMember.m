//
//  FamilyMember.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FamilyMember.h"
#import "FamilyMember+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation FamilyMember
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realFamilyId", @"realJoinTime", @"realMemberInfo", @"realMemberName", @"realShareDeviceCount", @"realMemberRole", @"realMemberType" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"familyId", @"joinTime", @"memberInfo", @"memberName", @"shareDeviceCount", @"memberRole", @"memberType" ];
}

+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"memberInfo"]) {
          [property.type setValue:NSClassFromString(@"MemberInfo") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}
- (NSString *)familyId
{
    return self.realFamilyId;
}

- (NSString *)joinTime
{
    return self.realJoinTime;
}

- (id<UDMemberInfoDelegate>)memberInfo
{
    return self.realMemberInfo;
}

- (NSString *)memberName
{
    return self.realMemberName;
}

- (NSInteger)shareDeviceCount
{
    return self.realShareDeviceCount;
}

- (NSString *)memberRole
{
    return self.realMemberRole;
}

- (NSInteger)memberType
{
    return self.realMemberType;
}

@end
