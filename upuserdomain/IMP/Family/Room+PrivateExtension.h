//
//  Room+PrivateExtension.h
//  UserDomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/23.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "Room.h"

NS_ASSUME_NONNULL_BEGIN

@interface Room ()
/**
 房间iD
 */
@property (nonatomic, copy) NSString *realRoomId;

/**
 房间名称
 */
@property (nonatomic, copy) NSString *realRoomName;

/**
 房间分类
 */
@property (nonatomic, copy) NSString *realRoomClass;

/**
 房间标签
 */
@property (nonatomic, copy) NSString *realRoomLabel;

/**
 房间logo
 */
@property (nonatomic, copy) NSString *realRoomLogo;

/**
 房间图片
 */
@property (nonatomic, copy) NSString *realRoomPicture;

/**
 房间排序码
 */
@property (nonatomic, copy) NSString *realSortCode;

@end

NS_ASSUME_NONNULL_END
