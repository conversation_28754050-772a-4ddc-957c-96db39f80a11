//
//  BatchProcessDeviceResult.h
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/27.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface BatchProcessDevice : NSObject
@property (strong, nonatomic) NSString *deviceId;
@property (strong, nonatomic) NSString *deviceName;
@end

@interface BatchProcessDeviceResult : NSObject
@property (strong, nonatomic) NSArray<BatchProcessDevice *> *successDevices;
@property (strong, nonatomic) NSArray<BatchProcessDevice *> *failureDevices;
@end

NS_ASSUME_NONNULL_END
