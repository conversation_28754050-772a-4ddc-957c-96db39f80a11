//
//  MemberInfo.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "MemberInfo.h"
#import "MemberInfo+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation MemberInfo
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realAvatarUrl", @"realMobile", @"realName", @"realUserId", @"realVirtualUserFlag", @"realUcUserId", @"realHostUserId", @"realBirthday" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"avatarUrl", @"mobile", @"name", @"userId", @"virtualUserFlag", @"ucUserId", @"hostUserId", @"birthday" ];
}

- (NSString *)avatarUrl
{
    return self.realAvatarUrl;
}

- (NSString *)mobile
{
    return self.realMobile;
}

- (NSString *)name
{
    return self.realName;
}

- (NSString *)userId
{
    return self.realUserId;
}
- (BOOL)virtualUserFlag
{
    return self.realVirtualUserFlag;
}

- (NSString *)ucUserId
{
    return self.realUcUserId;
}

- (NSString *)hostUserId
{
    return self.realHostUserId;
}

- (NSString *)birthday
{
    return self.realBirthday;
}
@end
