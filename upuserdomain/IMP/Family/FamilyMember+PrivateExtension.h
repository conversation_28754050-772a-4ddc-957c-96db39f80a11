//
//  FamilyMember+PrivateExtension.h
//  UserDomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/23.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FamilyMember.h"
@protocol UDMemberInfoDelegate;
NS_ASSUME_NONNULL_BEGIN

@interface FamilyMember ()
@property (nonatomic, strong) id<UDMemberInfoDelegate> realMemberInfo;

@property (nonatomic, copy) NSString *realMemberName;

@property (nonatomic, copy) NSString *realFamilyId;

@property (nonatomic, copy) NSString *realJoinTime;

@property (nonatomic, assign) NSInteger realShareDeviceCount;

@property (nonatomic, copy) NSString *realMemberRole;

@property (nonatomic, assign) NSInteger realMemberType;

@end

NS_ASSUME_NONNULL_END
