//
//  UDFloorInfo+PrivateExtension.h
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/3.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDFloorInfo.h"

NS_ASSUME_NONNULL_BEGIN

@interface UDFloorInfo ()
/**
 楼层名称
 */
@property (nonatomic, copy) NSString *realFloorName;
/**
 楼层Id
 */
@property (nonatomic, copy) NSString *realFloorId;
/**
 楼层次序（-3到5，没有0）
 */
@property (nonatomic, copy) NSString *realFloorOrderId;
/**
 楼层类型
 */
@property (nonatomic, copy) NSString *realFloorClass;
/**
 楼层标签
 */
@property (nonatomic, copy) NSString *realFloorLabel;
/**
 楼层logo url
 */
@property (nonatomic, copy) NSString *realFloorLogo;
/**
 楼层图片 url
 */
@property (nonatomic, copy) NSString *realFloorPicture;
/**
 楼层创建时间
 */
@property (nonatomic, copy) NSString *realFloorCreateTime;
/**
 房间列表
 */
@property (nonatomic, strong) NSArray<id<UDRoomDelegate>> *realRooms;
@end

NS_ASSUME_NONNULL_END
