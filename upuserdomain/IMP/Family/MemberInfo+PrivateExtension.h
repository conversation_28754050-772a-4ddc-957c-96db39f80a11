//
//  MemberInfo+PrivateExtension.h
//  UserDomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/23.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "MemberInfo.h"

NS_ASSUME_NONNULL_BEGIN

@interface MemberInfo ()
/**
 用户id
 */
@property (nonatomic, copy) NSString *realUserId;

/**
 用户昵称
 */
@property (nonatomic, copy) NSString *realName;

/**
 头像
 */
@property (nonatomic, copy) NSString *realAvatarUrl;

/**
 手机号
 */
@property (nonatomic, copy) NSString *realMobile;

/**
 是否虚拟成员
 */
@property (nonatomic, assign) BOOL realVirtualUserFlag;

/**
 用户中心userId
 */
@property (nonatomic, copy) NSString *realUcUserId;

/**
 宿主用户的IOT平台userId
 */
@property (nonatomic, copy) NSString *realHostUserId;

/**
 用户生日
 */
@property (nonatomic, copy) NSString *realBirthday;

@end

NS_ASSUME_NONNULL_END
