//
//  DeviceCardStatusArgs.h
//  upuserdomain
//
//  Created by lubiao on 2025/3/26.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface DeviceCardStatusArgs : NSObject

/// 家庭id
@property (nullable, nonatomic, copy) NSString *familyId;

/// 全量已排序设备id列表
@property (nullable, nonatomic, strong) NSArray<NSString *> *cardOrderList;

/// 全量大卡片设备id列表
@property (nullable, nonatomic, strong) NSArray<NSString *> *bigCardList;

/// 全量中卡片设备id列表
@property (nullable, nonatomic, strong) NSArray<NSString *> *middleCardList;

/// 全量小卡片设备id列表
@property (nullable, nonatomic, strong) NSArray<NSString *> *smallCardList;

@end

NS_ASSUME_NONNULL_END
