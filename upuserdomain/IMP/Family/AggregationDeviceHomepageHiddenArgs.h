//
//  AggregationDeviceHomepageHiddenArgs.h
//  upuserdomain
//
//  Created by gut<PERSON> on 2025/7/10.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 设置聚合设备在首页隐藏与否的参数模型
@interface AggregationDeviceHomepageHiddenArgs : NSObject

/// 家庭id
@property (nullable, nonatomic, copy) NSString *familyId;

/// 需要由"隐藏"变更为"展示"的设备id集合，参与灯光|窗帘|环境|摄像头聚合的deviceId
@property (nullable, nonatomic, strong) NSArray<NSString *> *devicesDisplayed;

/// 需要由"展示"变更为"隐藏"的设备id集合，参与灯光|窗帘|环境|摄像头聚合的deviceId
@property (nullable, nonatomic, strong) NSArray<NSString *> *devicesHidden;

@end

NS_ASSUME_NONNULL_END
