//
//  UDFloorInfo.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/3.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDFloorInfo.h"
#import "UDFloorInfo+PrivateExtension.h"
#import "Room.h"
#import <MJExtension/MJExtension.h>
@implementation UDFloorInfo
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realFloorId", @"realFloorLogo", @"realFloorName", @"realFloorClass", @"realFloorLabel", @"realFloorPicture", @"realFloorOrderId", @"realRooms", @"realFloorCreateTime" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"floorId", @"floorLogo", @"floorName", @"floorClass", @"floorLabel", @"floorPicture", @"floorOrderId", @"rooms", @"floorCreateTime" ];
}

+ (NSDictionary *)mj_objectClassInArray
{
    return @{ @"rooms" : Room.class };
}

- (NSString *)floorId
{
    return self.realFloorId;
}
- (NSString *)floorLogo
{
    return self.realFloorLogo;
}
- (NSString *)floorName
{
    return self.realFloorName;
}
- (NSString *)floorClass
{
    return self.realFloorClass;
}
- (NSString *)floorLabel
{
    return self.realFloorLabel;
}
- (NSString *)floorPicture
{
    return self.realFloorPicture;
}
- (NSString *)floorOrderId
{
    return self.realFloorOrderId;
}
- (NSArray<id<UDRoomDelegate>> *)rooms
{
    return self.realRooms;
}
- (NSString *)floorCreateTime
{
    return self.realFloorCreateTime;
}
@end
