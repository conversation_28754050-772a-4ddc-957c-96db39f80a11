//
//  BatchProcessDeviceResult.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/27.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "BatchProcessDeviceResult.h"
#import <MJExtension/MJExtension.h>

@implementation BatchProcessDevice

@end

@implementation BatchProcessDeviceResult
+ (NSDictionary *)mj_objectClassInArray
{
    return @{
        @"successDevices" : [BatchProcessDevice class],
        @"failureDevices" : [BatchProcessDevice class]
    };
}
@end
