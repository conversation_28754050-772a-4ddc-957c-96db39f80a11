//
//  UDServerCacheManager.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDServerCacheManager.h"
#import "NSObject+UD.h"
NSString *const kServerResponseUserInfo = @"kServerResponseUserInfo";
NSString *const kServerResponseUserAddressInfo = @"kServerResponseUserAddressInfo";
NSString *const kServerResponseLoginTerminalInfo = @"kServerResponseLoginTerminalInfo";
NSString *const kServerResponseDeviceListInfo = @"kServerResponseDeviceListInfo";
NSString *const kServerResponseFamilyListInfo = @"kServerResponseFamilyListInfo";
@implementation UDServerCacheManager
+ (UDServerCacheManager *)getInstance
{
    static UDServerCacheManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[UDServerCacheManager alloc] init];
    });
    return instance;
}
- (instancetype)init
{
    if (self = [super init]) {
        _serverResponseRecords = [NSMutableDictionary dictionary];
    }
    return self;
}
- (BOOL)needNotifyUserRefreshSuccess
{
    return ![self isLatest2ServerInfoRecordsEqualWithKey:kServerResponseUserInfo];
}

- (BOOL)needNotifyLoginTerminalRefreshSuccess
{
    return ![self isLatest2ServerInfoRecordsEqualWithKey:kServerResponseLoginTerminalInfo];
}

- (BOOL)needNotifyDeviceListRefreshSuccess
{
    return ![self isLatest2ServerInfoRecordsEqualWithKey:kServerResponseDeviceListInfo];
}

- (BOOL)needNotifyFamilyListRefreshSuccess
{
    return ![self isLatest2ServerInfoRecordsEqualWithKey:kServerResponseFamilyListInfo];
}
#pragma mark - Non-Public Methods
- (NSMutableArray *)getValidServerInfoRecordsArrayForKey:(NSString *)key
{
    if (![self.serverResponseRecords isKindOfClass:[NSMutableDictionary class]]) {
        self.serverResponseRecords = [NSMutableDictionary dictionary];
    }
    NSMutableArray *records = [self.serverResponseRecords valueForKey:key];
    if (![records isKindOfClass:[NSMutableArray class]]) {
        records = [NSMutableArray array];
    }
    return records;
}

- (BOOL)isLatest2ServerInfoRecordsEqualWithKey:(NSString *)key
{
    NSArray *records = [self getValidServerInfoRecordsArrayForKey:key];
    if (records.count != 2) {
        return NO;
    }
    NSObject *firstRecordInfo = [records firstObject];
    NSObject *secondRecordInfo = [records lastObject];
    return [firstRecordInfo ud_isEqualTo:secondRecordInfo];
}

#pragma mark - Public Methods
- (void)addServerResponseInfo:(NSObject *)info forKey:(NSString *)key
{
    if (![key isKindOfClass:[NSString class]] || info == nil || [info isKindOfClass:[NSNull class]]) {
        return;
    }
    @synchronized(self.serverResponseRecords)
    {
        NSMutableArray *records = [self getValidServerInfoRecordsArrayForKey:key];
        if (records.count == 2) {
            [records removeObjectAtIndex:0];
        }
        [records addObject:info];
        [self.serverResponseRecords setValue:records forKey:key];
    }
}

- (void)clearAllServerResponseCache
{
    [self.serverResponseRecords removeAllObjects];
}

@end
