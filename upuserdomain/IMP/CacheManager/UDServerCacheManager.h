//
//  UDServerCacheManager.h
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
extern NSString *const kServerResponseUserInfo;
extern NSString *const kServerResponseUserAddressInfo;
extern NSString *const kServerResponseLoginTerminalInfo;
extern NSString *const kServerResponseDeviceListInfo;
extern NSString *const kServerResponseFamilyListInfo;
@interface UDServerCacheManager : NSObject
@property (atomic, assign, readonly) BOOL needNotifyUserRefreshSuccess;
@property (atomic, assign, readonly) BOOL needNotifyLoginTerminalRefreshSuccess;
@property (atomic, assign, readonly) BOOL needNotifyDeviceListRefreshSuccess;
@property (atomic, assign, readonly) BOOL needNotifyFamilyListRefreshSuccess;
@property (atomic, strong) NSMutableDictionary<NSString *, NSMutableArray *> *serverResponseRecords;
+ (UDServerCacheManager *)getInstance;
- (void)addServerResponseInfo:(NSObject *)info forKey:(NSString *)key;
- (void)clearAllServerResponseCache;
@end

NS_ASSUME_NONNULL_END
