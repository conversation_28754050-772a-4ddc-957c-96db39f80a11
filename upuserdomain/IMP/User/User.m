//
//  User.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "User.h"
#import "UserDomainSampleResult.h"
#import "UDFamilyDelegate.h"
#import "CreateFamilyArgs.h"
#import "UpUserDomainStore.h"
#import "UDOperatorManager.h"
#import "UpUserStore.h"
#import "UDCreateFamilyOp.h"
#import "UserAddressArgs.h"
#import "UDCreateAddressOp.h"
#import "UDDeleteAddressOp.h"
#import "UPEditAddressOp.h"
#import "UDModifyUserInfoOp.h"
#import "UserInfoArgs.h"
#import <MJExtension/MJExtension.h>
#import "UserInfo.h"
#import "UserInfo+PrivateExtension.h"
#import "UserAddressInfo.h"
#import "UDPollqrCodeStateOp.h"
#import "UDQrCancleLoginOp.h"
#import "UDQrConfirmLoginOp.h"
#import "UDQrConfirmScanOp.h"
#import "UDQueryServiceOrderOp.h"
#import "UDQueryLoginLogsOp.h"
#import "UDRefreshAddressListOp.h"
#import "UDRefreshDeviceListOp.h"
#import "UDRefreshFamilyListOp.h"
#import "UDRefreshTerminalListOp.h"
#import "UDRefreshUserInfoOp.h"
#import "UDReplyFamilyInviteOp.h"
#import <uplog/UPLog.h>
#import "Family.h"
#import "UDSetCurrentFamilyOp.h"
#import "UDAuthDataDelegate.h"
#import "UDUpdateAvatarOp.h"
#import "Device.h"
#import "UDRefreshUserOp.h"
#import "UDReplyJoinFamilyOp.h"
#import "AggregationSwitchArgs.h"
#import "UDModifyAggSwitchOp.h"
#import "UDConfirmDeviceSharingRelationOp.h"
#import "UDCancelDeviceSharingRelationOp.h"

@interface User ()
@property (nonatomic, strong) id<UpUserDomainProvider> provider;
@property (nonatomic, strong) UDOperatorManager *opManager;
@end

@implementation User
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"provider", @"opManager" ];
}
+ (NSDictionary *)mj_objectClassInArray
{
    return @{ @"terminals" : @"UserTerminal" };
}
+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"extraInfo"]) {
          [property.type setValue:NSClassFromString(@"UserInfo") forKey:@"typeClass"];
      }
      if ([property.name isEqualToString:@"currentFamily"]) {
          [property.type setValue:NSClassFromString(@"Family") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}

- (instancetype)initUserWithProvider:(id<UpUserDomainProvider>)provider operateMamager:(UDOperatorManager *)manager
{
    if (self = [super init]) {
        self.provider = provider;
        self.opManager = manager;
    }
    return self;
}
#pragma mark - UDUserModelTransformerDelegate

- (NSDictionary *)up_keyValues
{
    NSMutableDictionary *dict = self.mj_keyValues;
    NSMutableDictionary *devices = [NSMutableDictionary dictionary];
    NSMutableDictionary *families = [NSMutableDictionary dictionary];
    [self.devices enumerateKeysAndObjectsUsingBlock:^(NSString *_Nonnull key, id<UDDeviceDelegate> _Nonnull obj, BOOL *_Nonnull stop) {
      if (key) {
          devices[key] = ((Device *)obj).mj_keyValues;
      }
    }];
    [self.families enumerateKeysAndObjectsUsingBlock:^(NSString *_Nonnull key, id<UDFamilyDelegate> _Nonnull obj, BOOL *_Nonnull stop) {
      if (key) {
          families[key] = ((Family *)obj).up_keyValues;
      }
    }];
    [dict setObject:families forKey:@"families"];
    [dict setObject:devices forKey:@"devices"];
    [dict setObject:((Family *)self.currentFamily).up_keyValues ?: @{} forKey:@"currentFamily"];
    return dict;
}
#pragma mark - UDUserDelegate


- (void)createFamily:(CreateFamilyArgs *)createFamilyArgs success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (![createFamilyArgs isKindOfClass:CreateFamilyArgs.class]) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    if (createFamilyArgs.name.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDCreateFamilyOp class] args:createFamilyArgs success:success failure:failure];
}

- (void)createNewAddress:(UserAddressArgs *)userAddressArgs success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (![userAddressArgs isKindOfClass:userAddressArgs.class]) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    if ([self checkAddressArgs:userAddressArgs]) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDCreateAddressOp class] args:userAddressArgs success:success failure:failure];
}
- (BOOL)checkAddressArgs:(UserAddressArgs *)userAddressArgs
{
    return userAddressArgs.receiver_name.length == 0 || userAddressArgs.receiver_mobile.length == 0 || userAddressArgs.province_id.length == 0 || userAddressArgs.province.length == 0 || userAddressArgs.city.length == 0 || userAddressArgs.city_id.length == 0 || userAddressArgs.district.length == 0 || userAddressArgs.district_id.length == 0 || userAddressArgs.source.length == 0;
}

- (id<UDFamilyDelegate>)currentFamily
{
    return self.provider.provideUpUserDomainStore.familyStore.currentFamliy;
}

- (void)deleteAddress:(NSString *)addressId success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (addressId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDDeleteAddressOp class] args:addressId success:success failure:failure];
}

- (NSDictionary<NSString *, id<UDDeviceDelegate>> *)devices
{
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    [self.provider.provideUpUserDomainStore.deviceStore.deviceList enumerateObjectsUsingBlock:^(Device *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      dict[obj.deviceId] = obj;
    }];
    return dict;
}

- (void)editAddress:(UserAddressArgs *)userAddressArgs success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (![userAddressArgs isKindOfClass:userAddressArgs.class]) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    if ([self checkAddressArgs:userAddressArgs] || userAddressArgs.addressId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UPEditAddressOp class] args:userAddressArgs success:success failure:failure];
}

- (id<UDUserInfoDelegate>)extraInfo
{
    @synchronized(self)
    {
        UserInfo *userInfo = self.provider.provideUpUserDomainStore.userStore.extraInfo;
        userInfo.realDefaultAddress = nil;
        userInfo.realAddresses = @[];
        if (self.provider.provideUpUserDomainStore.userStore.addressList.count == 0) {
            return userInfo;
        }
        NSArray *array = [NSArray arrayWithArray:self.provider.provideUpUserDomainStore.userStore.addressList];
        [array enumerateObjectsUsingBlock:^(UserAddressInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          if (obj.is_default) {
              userInfo.realDefaultAddress = obj;
              *stop = YES;
          }
        }];
        userInfo.realAddresses = array;
        return userInfo;
    }
}

- (NSDictionary<NSString *, id<UDFamilyDelegate>> *)families
{
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    [self.getFamilyList enumerateObjectsUsingBlock:^(id<UDFamilyDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      dict[obj.familyId] = obj;
    }];
    return dict;
}

- (id<UDDeviceDelegate>)getDeviceById:(NSString *)deviceId
{
    __block id<UDDeviceDelegate> device;
    [self.provider.provideUpUserDomainStore.deviceStore.deviceList enumerateObjectsUsingBlock:^(Device *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([obj.deviceId isEqualToString:deviceId]) {
          device = obj;
          *stop = YES;
      }
    }];
    return device;
}

- (NSArray<id<UDDeviceDelegate>> *)getDeviceList:(BOOL (^)(id<UDDeviceDelegate>))deviceFilter
{
    NSMutableArray *deviceList = [NSMutableArray array];
    [self.provider.provideUpUserDomainStore.deviceStore.deviceList enumerateObjectsUsingBlock:^(Device *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (deviceFilter) {
          if (deviceFilter(obj)) {
              [deviceList addObject:obj];
          }
      }
    }];
    return deviceList;
}

- (id<UDFamilyDelegate>)getFamilyById:(NSString *)familyId
{
    __block id<UDFamilyDelegate> family;
    [self.getFamilyList enumerateObjectsUsingBlock:^(id<UDFamilyDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([obj.familyId isEqualToString:familyId]) {
          family = obj;
          *stop = YES;
      }
    }];
    return family;
}

- (NSArray<id<UDFamilyDelegate>> *)getFamilyList
{
    return self.provider.provideUpUserDomainStore.familyStore.familyList;
}

- (void)modifyUserInfo:(UserInfoArgs *)userInfo success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (![userInfo isKindOfClass:UserInfoArgs.class]) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    if (userInfo.userId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    if ((self.provider.provideUpUserDomain.getSettings.getUserDomainPlatform == UPUserDomainPlatformHomeland || self.provider.provideUpUserDomain.getSettings.getUserDomainPlatform == UPUserDomainPlatformHomelandSYN) && ![self verifyUserInfoIsLegal:userInfo.mj_keyValues]) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDModifyUserInfoOp class] args:userInfo success:success failure:failure];
}

- (void)pollqrCodeState:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (uuid.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDPollqrCodeStateOp class] args:uuid success:success failure:failure];
}

- (void)qrCancleLogin:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (uuid.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDQrCancleLoginOp class] args:uuid success:success failure:failure];
}

- (void)qrConfirmLogin:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (uuid.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDQrConfirmLoginOp class] args:uuid success:success failure:failure];
}

- (void)qrConfirmScan:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (uuid.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDQrConfirmScanOp class] args:uuid success:success failure:failure];
}

- (void)qrWorkOrderInfo:(NSString *)phoneNumber success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (phoneNumber.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDQueryServiceOrderOp class] args:phoneNumber success:success failure:failure];
}

- (void)queryLoginLogs:(NSInteger)pageNo pageSize:(NSInteger)pageSize success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (pageNo <= 0 || pageSize <= 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDQueryLoginLogsOp class]
                                args:@{ @"pageNo" : @(pageNo),
                                        @"pageSize" : @(pageSize) }
                             success:success
                             failure:failure];
}

- (void)refreshAddressList:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self.opManager operateWithClass:[UDRefreshAddressListOp class] args:nil success:success failure:failure];
}

- (void)refreshDeviceList:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self.opManager operateWithClass:[UDRefreshDeviceListOp class] args:nil success:success failure:failure];
}

- (void)refreshFamilyList:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self.opManager operateWithClass:[UDRefreshFamilyListOp class] args:nil success:success failure:failure];
}

- (void)refreshTerminalList:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self.opManager operateWithClass:[UDRefreshTerminalListOp class] args:nil success:success failure:failure];
}
- (void)refreshUser:(BOOL)immediate callback:(userDomainCallback)callback
{
    if (self.provider.provideUpUserDomainStore.loginState != UpUserDomainStateDidLogin) {
        if (callback) {
            callback([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    if (immediate == NO && self.provider.provideUpUserDomain.isRefreshCompleted == YES) {
        [self.provider.provideEventManager sendEvent:NOTIFY_REFRESH_USER_SUCCESS];
        [self.provider.provideEventManager sendEvent:NOTIFY_REFRESH_DEVICE_LIST_SUCCESS];
        [self.provider.provideEventManager sendEvent:NOTIFY_REFRESH_FAMILY_LIST_SUCCESS];
        [self.provider.provideEventManager sendEvent:NOTIFY_REFRESH_COMPLETED];
        if (callback) {
            callback([UserDomainSampleResult defaultSuccessResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDRefreshUserOp class] args:nil success:callback failure:callback];
}
- (void)refreshUser:(userDomainCallback)callback
{
    [self refreshUser:YES callback:callback];
}

- (void)refreshUserInfo:(userDomainCallback)callback
{
    [self.opManager operateWithClass:[UDRefreshUserInfoOp class] args:nil success:callback failure:callback];
}

- (void)replyFamilyInvite:(NSString *)code familyId:(NSString *)familyId agree:(BOOL)agree success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [self replyFamilyInvite:code familyId:familyId memberName:self.extraInfo.nickname agree:agree success:success failure:failure];
}

- (void)replyFamilyInvite:(NSString *)code familyId:(NSString *)familyId memberName:(NSString *)memberName agree:(BOOL)agree success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (code.length == 0 || familyId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDReplyFamilyInviteOp class]
                                args:@{ @"code" : code,
                                        @"familyId" : familyId,
                                        @"memberName" : memberName ?: @"",
                                        @"agree" : @(agree) }
                             success:success
                             failure:failure];
}

- (void)replyJoinFamily:(NSString *)applicationId agree:(BOOL)agree success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (applicationId == nil || applicationId.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:UDReplyJoinFamilyOp.class
                                args:@{ @"applicationId" : applicationId,
                                        @"agree" : @(agree) }
                             success:success
                             failure:failure];
}
- (void)setCurrentFamily:(id<UDFamilyDelegate>)currentFamily
{
    if (currentFamily == nil || currentFamily.familyId.length == 0) {
        UPLogError(@"UPUserDomain", @"%s%d传入的当前家庭对象id为空", __func__, __LINE__);
        return;
    }
    NSString *familyId = currentFamily.familyId;
    [self.opManager operateWithClass:[UDSetCurrentFamilyOp class]
        args:familyId
        success:^(UserDomainSampleResult *_Nonnull result) {

        }
        failure:^(UserDomainSampleResult *_Nonnull result){

        }];
}

- (NSArray<id<UDUserTermDelegate>> *)terminals
{
    return self.provider.provideUpUserDomainStore.userStore.terminals;
}

- (NSString *)uHomeUserId
{
    return self.provider.provideUpUserDomainStore.oauthData.uhome_user_id;
}

- (void)updateAvatar:(UIImage *)image success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (image == nil) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDUpdateAvatarOp class] args:image success:success failure:failure];
}

- (NSString *)user_center_userId
{
    return self.provider.provideUpUserDomainStore.userStore.extraInfo.userId;
}

- (void)confirmDeviceSharingRelation:(NSString *)shareUuid success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (shareUuid == nil || shareUuid.length == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDConfirmDeviceSharingRelationOp class]
                                args:@{ @"shareUuid" : shareUuid }
                             success:success
                             failure:failure];
}
- (void)cancelDeviceSharingRelation:(NSArray<NSString *> *)shareUuids success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (shareUuids == nil || shareUuids.count == 0) {
        if (failure) {
            failure([UserDomainSampleResult defaultFailureResult]);
        }
        return;
    }
    [self.opManager operateWithClass:[UDCancelDeviceSharingRelationOp class]
                                args:@{ @"shareUuids" : shareUuids }
                             success:success
                             failure:failure];
}

- (void)modifyAggregationSwitch:(AggregationSwitchArgs *)aggSwitch
                        success:(userDomainCallback)success
                        failure:(userDomainCallback)failure
{
    NSError *error = nil;
    if (aggSwitch.source.length < 1 || !aggSwitch.familyAgg) {
        error = [NSError errorWithDomain:@"com.uplus.userdomain" code:-1 userInfo:@{ NSLocalizedDescriptionKey : @"缺少必要参数`source`或`familyAgg`." }];
        if (failure) {
            UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
            failure(result);
        }
        return;
    }

    if (aggSwitch.familyAgg.count) {
        for (FamilyAgg *agg in aggSwitch.familyAgg) {
            if (agg.familyId.length < 1) {
                error = [NSError errorWithDomain:@"com.uplus.userdomain" code:-1 userInfo:@{ NSLocalizedDescriptionKey : @"缺少必要参数`familyId`." }];
                break;
            }
        }
    }

    if (error) {
        if (failure) {
            UserDomainSampleResult *result = [[UserDomainSampleResult alloc] initWithResponseError:error];
            failure(result);
        }
        return;
    }
    [self.opManager operateWithClass:UDModifyAggSwitchOp.class
                                args:(NSObject *)aggSwitch
                             success:success
                             failure:failure];
}

#pragma mark PrivateMethods
- (BOOL)verifyUserInfoIsLegal:(NSDictionary *)extraInfo
{
    NSDictionary *verifyDic = @{ @"gender" : @[ @"male", @"female", @"" ],
                                 @"marriage" : @[ @"0", @"1", @"" ],
                                 @"education" : @[ @"10", @"11", @"12", @"13", @"14", @"15", @"16", @"17", @"" ],
                                 @"income" : @[ @"18", @"19", @"20", @"21", @"22", @"23", @"" ],
                                 @"familyNum" : @[ @"1", @"2", @"3", @"4", @"" ] };
    __block BOOL outStop = NO;
    __block NSArray *values = [NSArray array];
    [extraInfo enumerateKeysAndObjectsUsingBlock:^(id _Nonnull key, NSString *obj, BOOL *_Nonnull stop) {
      if (![obj isKindOfClass:NSString.class]) {
          *stop = YES;
          return;
      }
      if ([key isEqual:@"userId"]) {
          *stop = !obj.length;
      }
      if ([key isEqual:@"gender"]) {
          values = verifyDic[key];
          *stop = ![values containsObject:obj];
      }
      if ([key isEqual:@"marriage"]) {
          values = verifyDic[key];
          *stop = ![values containsObject:obj];
      }
      if ([key isEqual:@"education"]) {
          values = verifyDic[key];
          *stop = ![values containsObject:obj];
      }
      if ([key isEqual:@"income"]) {
          values = verifyDic[key];
          *stop = ![values containsObject:obj];
      }
      if ([key isEqual:@"familyNum"]) {
          values = verifyDic[key];
          *stop = ![values containsObject:obj];
      }
      if ([key isEqual:@"birthday"]) {
          if (obj.length > 0) {
              NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
              [dateFormatter setDateFormat:@"yyyy-MM-dd"];
              NSDate *destDate = [dateFormatter dateFromString:obj];
              BOOL isDate = destDate != nil;
              *stop = !isDate;
          }
      }
      outStop = *stop;
    }];
    return !outStop;
}

@end
