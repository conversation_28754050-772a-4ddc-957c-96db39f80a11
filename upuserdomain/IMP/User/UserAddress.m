//
//  UserAddress.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserAddress.h"
#import "UserAddress+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation UserAddress
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realCity", @"realCity_id", @"realCountry_code", @"realDistrict", @"realDistrict_id", @"realLine1", @"realLine2", @"realPostcode", @"realProvince", @"realProvince_id", @"realTown", @"realTown_id" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"city", @"city_id", @"country_code", @"district", @"district_id", @"line1", @"line2", @"postcode", @"province", @"province_id", @"town", @"town_id" ];
}

- (NSString *)city
{
    return self.realCity;
}

- (NSString *)city_id
{
    return self.realCity_id;
}

- (NSString *)country_code
{
    return self.realCountry_code;
}

- (NSString *)district
{
    return self.realDistrict;
}

- (NSString *)district_id
{
    return self.realDistrict_id;
}

- (NSString *)line1
{
    return self.realLine1;
}

- (NSString *)line2
{
    return self.realLine2;
}

- (NSString *)postcode
{
    return self.realPostcode;
}

- (NSString *)province
{
    return self.realProvince;
}

- (NSString *)province_id
{
    return self.realProvince_id;
}

- (NSString *)town
{
    return self.realTown;
}

- (NSString *)town_id
{
    return self.realTown_id;
}

@end
