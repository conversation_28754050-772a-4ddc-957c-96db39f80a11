//
//  AggregationSwitchArgs.h
//  upuserdomain
//
//  Created by l<PERSON><PERSON> on 2025/3/26.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface FamilyAgg : NSObject

/// 家庭id
@property (nonatomic, copy) NSString *familyId;

/// 灯光聚合开关, "0"-关闭, "1"-打开
@property (nonatomic, copy) NSString *lightAgg;

/// 窗帘聚合开关, "0"-关闭, "1"-打开
@property (nonatomic, copy) NSString *curtainAgg;

/// 环境聚合开关, "0"-关闭, "1"-打开
@property (nonatomic, copy) NSString *envAgg;

/// 长期离线设备聚合开关, "0"-关闭, "1"-打开
@property (nonatomic, copy) NSString *offlineAgg;

/// 非网器设备聚合开关, "0"-关闭, "1"-打开
@property (nonatomic, copy) NSString *nonnetAgg;

/// 摄像头聚合开关, "0"-关闭, "1"-打开
@property (nonatomic, copy) NSString *cameraAgg;

@end

@interface AggregationSwitchArgs : NSObject

/// 操作来源: "0"-引导页, "1"-手动设置
@property (nonatomic, copy) NSString *source;

@property (nonatomic, copy) NSArray<FamilyAgg *> *familyAgg;

@end

NS_ASSUME_NONNULL_END
