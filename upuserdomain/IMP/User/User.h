//
//  User.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UDUserDelegate.h"
#import "UDUserModelTransformer.h"
NS_ASSUME_NONNULL_BEGIN
@protocol UpUserDomainProvider;
@class UDOperatorManager;
@interface User : NSObject <UDUserDelegate, UDUserModelTransformer, NSCoding>
- (instancetype)initUserWithProvider:(id<UpUserDomainProvider>)provider operateMamager:(UDOperatorManager *)manager;
@end

NS_ASSUME_NONNULL_END
