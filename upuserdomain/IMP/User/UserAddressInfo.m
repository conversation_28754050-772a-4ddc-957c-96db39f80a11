//
//  UserAddressInfo.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/8.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserAddressInfo.h"
#import "UserAddressInfo+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation UserAddressInfo
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realAddress", @"realEmail", @"realReceiver_name", @"realReceiver_mobile", @"realAddressId", @"realIs_default", @"realIs_service", @"realTag", @"realUser_id", @"realSource" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"address", @"email", @"receiver_name", @"receiver_mobile", @"addressId", @"is_default", @"is_service", @"tag", @"user_id", @"source" ];
}

+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"address"]) {
          [property.type setValue:NSClassFromString(@"UserAddress") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}

- (id<UDUserAddressDelegate>)address
{
    return self.realAddress;
}
- (NSString *)email
{
    return self.realEmail;
}
- (NSString *)receiver_name
{
    return self.realReceiver_name;
}
- (NSString *)receiver_mobile
{
    return self.realReceiver_mobile;
}
- (NSString *)addressId
{
    return self.realAddressId;
}
- (NSInteger)is_default
{
    return self.realIs_default;
}
- (NSInteger)is_service
{
    return self.realIs_service;
}
- (NSString *)tag
{
    return self.realTag;
}
- (NSString *)user_id
{
    return self.realUser_id;
}
- (NSString *)source
{
    return self.realSource;
}

@end
