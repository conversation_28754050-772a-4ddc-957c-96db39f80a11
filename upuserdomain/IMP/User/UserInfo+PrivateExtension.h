//
//  UserInfo+PrivateExtension.h
//  UserDomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/23.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserInfo.h"
@protocol UDAddressDelegate;
NS_ASSUME_NONNULL_BEGIN

@interface UserInfo ()
/**
 用户iD
 */
@property (nonatomic, copy) NSString *realUserId;

/**
 手机号
 */
@property (nonatomic, copy) NSString *realMobile;

/**
 邮箱
 */
@property (nonatomic, copy) NSString *realEmail;

/**
 用户名
 */
@property (nonatomic, copy) NSString *realUsername;

/**
 真实姓名
 */
@property (nonatomic, copy) NSString *realGivenName;

/**
 昵称
 */
@property (nonatomic, copy) NSString *realNickname;

/**
 家庭数量
 */
@property (nonatomic, copy) NSString *realFamilyNum;

/**
 性别
 */
@property (nonatomic, copy) NSString *realGender;

/**
 婚姻状况
 */
@property (nonatomic, copy) NSString *realMarriage;

/**
 生日
 */
@property (nonatomic, copy) NSString *realBirthday;

/**
 学历
 */
@property (nonatomic, copy) NSString *realEducation;

/**
 头像
 */
@property (nonatomic, copy) NSString *realAvatarUrl;

/**
 备用手机号
 */
@property (nonatomic, copy) NSString *realExtraPhone;

/**
 收入状况
 */
@property (nonatomic, copy) NSString *realIncome;

/**
 身高
 */
@property (nonatomic, copy) NSString *realHeight;

/**
 体重
 */
@property (nonatomic, copy) NSString *realWeight;
/**
 国家代码
*/
@property (nonatomic, copy) NSString *realCountryCode;
/**
 国家隐私代码
*/
@property (nonatomic, copy) NSString *realPrivacyCountryCode;

/**
 地址列表
 @see UserAddressInfo
 */
@property (strong, nonatomic) NSArray<id<UDAddressDelegate>> *realAddresses;

/**
 默认地址
 @see UserAddressInfo
 */
@property (strong, nonatomic) id<UDAddressDelegate> realDefaultAddress;

/**
个性签名
*/
@property (copy, nonatomic) NSString *realSignature;

/**
 注册来源
 */
@property (copy, nonatomic) NSString *realRegClientId;
@end

NS_ASSUME_NONNULL_END
