//
//  UserAddress+PrivateExtension.h
//  UserDomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserAddress.h"

NS_ASSUME_NONNULL_BEGIN

@interface UserAddress ()
/**
 城市
 */
@property (nonatomic, copy) NSString *realCity;

/**
 城市编码
 */
@property (nonatomic, copy) NSString *realCity_id;

/**
 国家编码
 */
@property (nonatomic, copy) NSString *realCountry_code;

/**
 区域
 */
@property (nonatomic, copy) NSString *realDistrict;

/**
 区域编码
 */
@property (nonatomic, copy) NSString *realDistrict_id;
/**
地址行1
*/
@property (nonatomic, copy) NSString *realLine1;
/**
地址行2
*/
@property (nonatomic, copy) NSString *realLine2;

/**
 邮编
 */
@property (nonatomic, copy) NSString *realPostcode;

/**
 省
 */
@property (nonatomic, copy) NSString *realProvince;

/**
 省编码
 */
@property (nonatomic, copy) NSString *realProvince_id;

/**
 乡镇、街道
 */
@property (nonatomic, copy) NSString *realTown;

/**
 乡镇街道编码
 */
@property (nonatomic, copy) NSString *realTown_id;
@end

NS_ASSUME_NONNULL_END
