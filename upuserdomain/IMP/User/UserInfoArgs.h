//
//  UserInfoArgs.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/25.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@protocol UDUserInfoDelegate;
NS_ASSUME_NONNULL_BEGIN

@interface UserInfoArgs : NSObject
/**
 生日
 */
@property (nonatomic, copy) NSString *birthday;
/**
 头像地址
 */
@property (nonatomic, copy) NSString *avatarUrl;
/**
 学历
 */
@property (nonatomic, copy) NSString *education;
/**
 第二联系方式
 */
@property (nonatomic, copy) NSString *extraPhone;
/**
 性别
 */
@property (nonatomic, copy) NSString *gender;
/**
 姓氏
 */
@property (nonatomic, copy) NSString *givenName;
/**
 身高
 */
@property (nonatomic, copy) NSString *height;
/**
 体重
 */
@property (nonatomic, copy) NSString *weight;
/**
 昵称
 */
@property (nonatomic, copy) NSString *nickname;
/**
 是否已婚
 */
@property (nonatomic, copy) NSString *marriage;
/**
 收入
 */
@property (nonatomic, copy) NSString *income;
/**
 家庭数
 */
@property (nonatomic, copy) NSString *familyNum;
/**
 国家隐私编码
 */
@property (nonatomic, copy) NSString *privacyCountryCode;

/**
 用户id
 */
@property (nonatomic, copy, readonly) NSString *userId;
- (instancetype)initUserInfoArgs:(id<UDUserInfoDelegate>)userInfo;
@end

NS_ASSUME_NONNULL_END
