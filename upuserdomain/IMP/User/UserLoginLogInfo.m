//
//  UserLoginLogInfo.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserLoginLogInfo.h"
#import "UserLoginLogInfo+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation UserLoginLogInfo
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realAction_at", @"realCity", @"realClient_id", @"realLatitude", @"realLogId", @"realLongitude", @"realProvince", @"realUser_id", @"realUser_name" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"action_at", @"city", @"client_id", @"latitude", @"logId", @"longitude", @"province", @"user_id", @"user_name" ];
}

- (NSString *)action_at
{
    return self.realAction_at;
}

- (NSString *)city
{
    return self.realCity;
}

- (NSString *)client_id
{
    return self.realClient_id;
}

- (NSString *)latitude
{
    return self.realLatitude;
}

- (NSString *)logId
{
    return self.realLogId;
}

- (NSString *)longitude
{
    return self.realLongitude;
}

- (NSString *)province
{
    return self.realProvince;
}

- (NSString *)user_id
{
    return self.realUser_id;
}

- (NSString *)user_name
{
    return self.realUser_name;
}

@end
