//
//  UserAddressArgs.h
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@protocol UDAddressDelegate;
NS_ASSUME_NONNULL_BEGIN

@interface UserAddressArgs : NSObject
/**
 邮箱
 */
@property (copy, nonatomic) NSString *email;

/**
 地址编号
 */
@property (copy, nonatomic, readonly) NSString *addressId;

/**
 是否是默认地址
 */
@property (assign, nonatomic) NSInteger is_default;

/**
 是否有效
 */
@property (assign, nonatomic) NSInteger is_service;

/**
 手机号码
 */
@property (copy, nonatomic) NSString *receiver_mobile;

/**
 收货人姓名
 */
@property (copy, nonatomic) NSString *receiver_name;

@property (copy, nonatomic) NSString *source;
/**
 标签
 */
@property (copy, nonatomic) NSString *tag;
/**
 用户id
 */
@property (copy, nonatomic) NSString *user_id;
/**
 城市
 */
@property (copy, nonatomic) NSString *city;

/**
 城市编码
 */
@property (copy, nonatomic) NSString *city_id;

/**
 国家编码
 */
@property (copy, nonatomic) NSString *country_code;

/**
 区域
 */
@property (copy, nonatomic) NSString *district;

/**
 区域编码
 */
@property (copy, nonatomic) NSString *district_id;

@property (copy, nonatomic) NSString *line1;

@property (copy, nonatomic) NSString *line2;

/**
 邮编
 */
@property (copy, nonatomic) NSString *postcode;

/**
 省
 */
@property (copy, nonatomic) NSString *province;

/**
 省编码
 */
@property (copy, nonatomic) NSString *province_id;

/**
 乡镇、街道
 */
@property (copy, nonatomic) NSString *town;

/**
 乡镇街道编码
 */
@property (copy, nonatomic) NSString *town_id;
- (instancetype)initAddressArgs:(id<UDAddressDelegate>)addressInfo;
@end

NS_ASSUME_NONNULL_END
