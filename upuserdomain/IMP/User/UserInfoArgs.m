//
//  UserInfoArgs.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/25.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserInfoArgs.h"
#import "UDUserInfoDelegate.h"
@implementation UserInfoArgs
- (instancetype)initUserInfoArgs:(id<UDUserInfoDelegate>)userInfo
{
    if (self = [super init]) {
        _birthday = userInfo.birthday;
        _avatarUrl = userInfo.avatarUrl;
        _education = userInfo.education;
        _extraPhone = userInfo.extraPhone;
        _gender = userInfo.gender;
        _givenName = userInfo.givenName;
        _height = userInfo.height;
        _weight = userInfo.weight;
        _nickname = userInfo.nickname;
        _marriage = userInfo.marriage;
        _income = userInfo.income;
        _userId = userInfo.userId;
        _familyNum = userInfo.familyNum;
        _privacyCountryCode = userInfo.privacyCountryCode;
    }
    return self;
}
@end
