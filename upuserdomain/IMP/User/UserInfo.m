//
//  UserInfo.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/8.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserInfo.h"
#import "UserInfo+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation UserInfo
MJCodingImplementation;
+ (NSDictionary *)mj_objectClassInArray
{
    return @{ @"addresses" : @"UserAddressInfo" };
}
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realUserId", @"realMobile", @"realEmail", @"realUsername", @"realGivenName", @"realNickname", @"realFamilyNum", @"realGender", @"realMarriage", @"realBirthday", @"realEducation", @"realAvatarUrl", @"realExtraPhone", @"realIncome", @"realHeight", @"realWeight", @"realCountryCode", @"realAddresses", @"realDefaultAddress", @"realSignature", @"realRegClientId", @"realPrivacyCountryCode" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"userId", @"mobile", @"email", @"username", @"givenName", @"nickname", @"familyNum", @"gender", @"marriage", @"birthday", @"education", @"avatarUrl", @"extraPhone", @"income", @"height", @"weight", @"countryCode", @"privacyCountryCode", @"addresses", @"defaultAddress", @"signature", @"regClientId" ];
}

+ (void)mj_enumerateProperties:(MJPropertiesEnumeration)enumeration
{
    void (^UDEnumeration)(MJProperty *property, BOOL *stop) = ^(MJProperty *property, BOOL *stop) {
      if ([property.name isEqualToString:@"defaultAddress"]) {
          [property.type setValue:NSClassFromString(@"UserAddressInfo") forKey:@"typeClass"];
      }
      enumeration(property, stop);
    };
    [super mj_enumerateProperties:UDEnumeration];
}

- (NSString *)countryCode
{
    return self.realCountryCode;
}
- (NSString *)privacyCountryCode
{
    return self.realPrivacyCountryCode;
}
- (NSString *)avatarUrl
{
    return self.realAvatarUrl;
}

- (NSString *)birthday
{
    return self.realBirthday;
}

- (NSString *)education
{
    return self.realEducation;
}

- (NSString *)email
{
    return self.realEmail;
}

- (NSString *)extraPhone
{
    return self.realExtraPhone;
}

- (NSString *)familyNum
{
    return self.realFamilyNum;
}

- (NSString *)gender
{
    return self.realGender;
}

- (NSString *)givenName
{
    return self.realGivenName;
}

- (NSString *)height
{
    return self.realHeight;
}

- (NSString *)income
{
    return self.realIncome;
}

- (NSString *)marriage
{
    return self.realMarriage;
}

- (NSString *)mobile
{
    return self.realMobile;
}

- (NSString *)nickname
{
    return self.realNickname;
}

- (NSString *)userId
{
    return self.realUserId;
}

- (NSString *)username
{
    return self.realUsername;
}

- (NSString *)weight
{
    return self.realWeight;
}
- (NSArray<id<UDAddressDelegate>> *)addresses
{
    return self.realAddresses;
}
- (id<UDAddressDelegate>)defaultAddress
{
    return self.realDefaultAddress;
}
- (NSString *)regClientId
{
    return self.realRegClientId;
}
- (NSString *)signature
{
    return self.realSignature;
}

@end
