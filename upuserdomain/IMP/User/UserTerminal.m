//
//  UserTerminal.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserTerminal.h"
#import "UserTerminal+PrivateExtension.h"
#import <MJExtension/MJExtension.h>
@implementation UserTerminal
MJCodingImplementation;
+ (NSArray *)mj_ignoredPropertyNames
{
    return @[ @"realAction_at", @"realClient_id", @"realUser_id", @"realUser_name" ];
}

+ (NSArray *)mj_ignoredCodingPropertyNames
{
    return @[ @"action_at", @"client_id", @"user_id", @"user_name" ];
}

- (NSString *)action_at
{
    return self.realAction_at;
}

- (NSString *)client_id
{
    return self.realClient_id;
}

- (NSString *)user_id
{
    return self.realUser_id;
}

- (NSString *)user_name
{
    return self.realUser_name;
}

@end
