//
//  UserAddressArgs.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserAddressArgs.h"
#import "UDAddressDelegate.h"
#import "UDUserAddressDelegate.h"
@implementation UserAddressArgs
- (instancetype)initAddressArgs:(id<UDAddressDelegate>)addressInfo
{
    if (self = [super init]) {
        _addressId = addressInfo.addressId;
        _receiver_name = addressInfo.receiver_name;
        _receiver_mobile = addressInfo.receiver_mobile;
        _province = addressInfo.address.province;
        _province_id = addressInfo.address.province_id;
        _city = addressInfo.address.city;
        _city_id = addressInfo.address.city_id;
        _district = addressInfo.address.district;
        _district_id = addressInfo.address.district_id;
        _source = addressInfo.source;
        _email = addressInfo.email;
        _is_default = addressInfo.is_default;
        _is_service = addressInfo.is_service;
        _tag = addressInfo.tag;
        _user_id = addressInfo.user_id;
        _country_code = addressInfo.address.country_code;
        _line1 = addressInfo.address.line1;
        _line2 = addressInfo.address.line2;
        _postcode = addressInfo.address.postcode;
        _town = addressInfo.address.town;
        _town_id = addressInfo.address.town_id;
    }
    return self;
}
@end
