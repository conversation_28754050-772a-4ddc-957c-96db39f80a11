//
//  UserQRLoginStage.h
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
//二维码被创建(或刷新)    stage = 0    持续轮询
//手机端第一次扫过二维码    stage = 1    异步刷新页面提示用户手机端确认登录，并持续轮询
//手机端点击确认登录    stage = 2    根据并随stage提供的token获取信息并写登录状态
//手机端点击取消登录    stage = 3    刷新二维码，持续轮询
//手机端扫了一个过期码后点击重新扫码    stage=3    刷新二维码，持续轮询
typedef NS_ENUM(NSInteger, QRLoginStage) {
    UserQRLoginStageCreate = 0,
    UserQRLoginStageDidScan = 1,
    UserQRLoginStageConfirmLogin = 2,
    UserQRLoginStageCancleLoginOrOverdue = 3,
};
NS_ASSUME_NONNULL_BEGIN

@interface UserQRLoginStage : NSObject
@property (nonatomic, assign) QRLoginStage stage;
@property (nonatomic, strong) NSString *access_token;
@property (nonatomic, strong) NSString *expires_in;
@property (nonatomic, strong) NSString *scope;
@property (nonatomic, strong) NSString *token_type;
@property (nonatomic, strong) NSString *refresh_token;
@end

NS_ASSUME_NONNULL_END
