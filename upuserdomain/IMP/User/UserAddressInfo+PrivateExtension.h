//
//  UserAddressInfo+PrivateExtension.h
//  UserDomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/23.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserAddressInfo.h"
@protocol UDUserAddressDelegate;
NS_ASSUME_NONNULL_BEGIN

@interface UserAddressInfo ()
/**
 地址信息
 @see UserAddress
 */
@property (strong, nonatomic) id<UDUserAddressDelegate> realAddress;

/**
 邮箱
 */
@property (copy, nonatomic) NSString *realEmail;

/**
 地址编号
 */
@property (copy, nonatomic) NSString *realAddressId;

/**
 是否是默认地址
 */
@property (assign, nonatomic) NSInteger realIs_default;

/**
 是否有效
 */
@property (assign, nonatomic) NSInteger realIs_service;

/**
 手机号码
 */
@property (copy, nonatomic) NSString *realReceiver_mobile;

/**
 收货人姓名
 */
@property (copy, nonatomic) NSString *realReceiver_name;
/**
 来源
*/
@property (copy, nonatomic) NSString *realSource;

/**
 标签
 */
@property (copy, nonatomic) NSString *realTag;

/**
 用户id
 */
@property (copy, nonatomic) NSString *realUser_id;
@end

NS_ASSUME_NONNULL_END
