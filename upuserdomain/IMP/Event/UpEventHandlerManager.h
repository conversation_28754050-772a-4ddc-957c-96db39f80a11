//
//  UpEventHandlerManager.h
//  upuserdomain
//
//  Created by 闫达 on 2020/7/31.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpEventHandlerManagerDelegate.h"
NS_ASSUME_NONNULL_BEGIN
@protocol UpUserDomainProvider;
@interface UpEventHandlerManager : NSObject <UpEventHandlerManagerDelegate>
- (instancetype)initWithProvider:(id<UpUserDomainProvider>)userDomainProvider;
@end

NS_ASSUME_NONNULL_END
