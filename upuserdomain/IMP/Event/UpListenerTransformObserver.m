//
//  UpListenerTransformObserver.m
//  upuserdomain
//
//  Created by 闫达 on 2020/7/31.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpListenerTransformObserver.h"
#import "UpEvent.h"
#import "UpUserDomainObserver.h"
#import <uplog/UPLog.h>
@interface UpListenerTransformObserver ()
@property (nonatomic, strong) NSHashTable<id<UpUserDomainObserver>> *observers;
@end

@implementation UpListenerTransformObserver
- (instancetype)init
{
    if (self = [super init]) {
        self.observers = [NSHashTable weakObjectsHashTable];
    }
    return self;
}

- (void)addObserver:(id<UpUserDomainObserver>)observer
{
    if (!observer || ![observer conformsToProtocol:@protocol(UpUserDomainObserver)]) {
        return;
    }
    @synchronized(self.observers)
    {
        [self.observers addObject:observer];
    }
}

- (void)removeObserver:(id<UpUserDomainObserver>)observer
{
    if (!observer) {
        return;
    }
    @synchronized(self.observers)
    {
        [self.observers removeObject:observer];
    }
}
- (BOOL)notifyPQAction:(NSString *)action obj:(id<UpUserDomainObserver>)obj upuserdomain:(id<UpUserDomainDelegate>)domain
{
    BOOL isNotifyedAction = NO;
    if ([action isEqualToString:@"onCancelQrCodeLoginFailed"]) {
        if ([obj respondsToSelector:@selector(onCancleQrScanLoginFailure:)]) {
            [obj onCancleQrScanLoginFailure:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]取消扫码登录失败，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onCancelQrCodeLoginSuccess"]) {
        if ([obj respondsToSelector:@selector(onCancleQrScanLoginSuccess:)]) {
            [obj onCancleQrScanLoginSuccess:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]取消扫码登录成功，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onQrCodeLoginFailed"]) {
        if ([obj respondsToSelector:@selector(onQrScanLoginFailure:)]) {
            [obj onQrScanLoginFailure:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]扫码登录失败，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onQrCodeLoginSuccess"]) {
        if ([obj respondsToSelector:@selector(onQrScanLoginSuccess:)]) {
            [obj onQrScanLoginSuccess:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]扫码登录成功，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    return isNotifyedAction;
}
- (BOOL)notifyUserAddressAndTerminalListAction:(NSString *)action obj:(id<UpUserDomainObserver>)obj upuserdomain:(id<UpUserDomainDelegate>)domain
{
    BOOL isNotifyedAction = NO;
    if ([action isEqualToString:@"onRefreshTerminalListFailed"]) {
        if ([obj respondsToSelector:@selector(onRefreshLoginTerminalFaild:)]) {
            [obj onRefreshLoginTerminalFaild:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]登录终端刷新失败，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onRefreshTerminalListSuccess"]) {
        if ([obj respondsToSelector:@selector(onRefreshLoginTerminalSuccess:)]) {
            [obj onRefreshLoginTerminalSuccess:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]登录终端刷新成功，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onRefreshAddressListSuccess"]) {
        if ([obj respondsToSelector:@selector(onRefreshAddressListSuccess:)]) {
            [obj onRefreshAddressListSuccess:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]地址列表刷新成功，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onRefreshAddressListFailed"]) {
        if ([obj respondsToSelector:@selector(onRefreshAddressListFaild:)]) {
            [obj onRefreshAddressListFaild:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]地址列表刷新失败，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    return isNotifyedAction;
}
- (BOOL)notifyUserAction:(NSString *)action obj:(id<UpUserDomainObserver>)obj upuserdomain:(id<UpUserDomainDelegate>)domain
{
    BOOL isNotifyedAction = NO;
    if ([action isEqualToString:@"onRefreshFailed"] || [action isEqualToString:@"onRefreshUserFailed"]) {
        if ([obj respondsToSelector:@selector(onRefeshUserFaild:)]) {
            [obj onRefeshUserFaild:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]用户基础信息刷新失败，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onRefreshCompleted"]) {
        if ([obj respondsToSelector:@selector(onRefreshComplete:)]) {
            [obj onRefreshComplete:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]用户的相关数据刷新完成，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onRefreshUserSuccess"]) {
        if ([obj respondsToSelector:@selector(onRefeshUserSuccess:)]) {
            [obj onRefeshUserSuccess:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]刷新用户信息成功，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    return isNotifyedAction;
}
- (BOOL)notifyDeviceAction:(NSString *)action obj:(id<UpUserDomainObserver>)obj upuserdomain:(id<UpUserDomainDelegate>)domain
{
    BOOL isNotifyedAction = NO;
    if ([action isEqualToString:@"onRefreshDeviceListFailed"]) {
        if ([obj respondsToSelector:@selector(onRefreshDeviceListFaild:)]) {
            [obj onRefreshDeviceListFaild:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]用户设备列表刷新失败，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onRefreshDeviceListSuccess"]) {
        if ([obj respondsToSelector:@selector(onRefreshDeviceListSuccess:)]) {
            [obj onRefreshDeviceListSuccess:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]用户设备列表刷新成功，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onRefreshDeviceCacheList"]) {
        if ([obj respondsToSelector:@selector(onRefreshDeviceListCache:)]) {
            [obj onRefreshDeviceListCache:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]用户设备列表缓存刷新，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    return isNotifyedAction;
}
- (BOOL)notifyFamilyDetailAction:(NSString *)action obj:(id<UpUserDomainObserver>)obj upuserdomain:(id<UpUserDomainDelegate>)domain
{
    BOOL isNotifyedAction = NO;
    if ([action isEqualToString:@"onRefreshFamilyDetailFailed"]) {
        if ([obj respondsToSelector:@selector(onRefreshFamilyDetailFailure:)]) {
            isNotifyedAction = YES;
            [obj onRefreshFamilyDetailFailure:domain.user];
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]家庭刷新详情失败，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onRefreshFamilyDetailSuccess"]) {
        if ([obj respondsToSelector:@selector(onRefreshFamilyDetailSuccess:)]) {
            isNotifyedAction = YES;
            [obj onRefreshFamilyDetailSuccess:domain.user];
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]家庭刷新详情成功，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    return isNotifyedAction;
}
- (BOOL)notifyFamilyListAction:(NSString *)action obj:(id<UpUserDomainObserver>)obj upuserdomain:(id<UpUserDomainDelegate>)domain
{
    BOOL isNotifyedAction = NO;
    if ([action isEqualToString:@"onCurrentFamilyChanged"]) {
        if ([obj respondsToSelector:@selector(onCurrentFamilyDidChanged:)]) {
            isNotifyedAction = YES;
            [obj onCurrentFamilyDidChanged:domain.user];
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]当前家庭发生变化，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onRefreshFamilyListFailed"]) {
        if ([obj respondsToSelector:@selector(onRefreshFamilyListFaild:)]) {
            isNotifyedAction = YES;
            [obj onCurrentFamilyDidChanged:domain.user];
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]家庭列表刷新失败，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onRefreshFamilyListSuccess"]) {
        if ([obj respondsToSelector:@selector(onRefreshFamilyListSuccess:)]) {
            [obj onRefreshFamilyListSuccess:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]家庭列表刷新成功，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onCurrentFamilyRoomListChanged"]) {
        if ([obj respondsToSelector:@selector(onCurrentFamilyRoomListChanged:)]) {
            [obj onCurrentFamilyRoomListChanged:domain.user];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]家庭房间刷新成功，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    return isNotifyedAction;
}
- (BOOL)notifyTokenAction:(NSString *)action obj:(id<UpUserDomainObserver>)obj upuserdomain:(id<UpUserDomainDelegate>)domain
{
    BOOL isNotifyedAction = NO;
    if ([action isEqualToString:@"onTokenInvalid"]) {
        if ([obj respondsToSelector:@selector(onInvalidRefreshToken)]) {
            [obj onInvalidRefreshToken];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]自动登录鉴权信息刷新失败，参数refreshToken已过期，用户登出。已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onCachedAuthDataLoaded"]) {
        if ([obj respondsToSelector:@selector(onGetCacheTokenSuccess:)]) {
            [obj onGetCacheTokenSuccess:domain.oauthData];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]读取缓存中的Token成功，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onAuthDataRefreshedSuccess"]) {
        if ([obj respondsToSelector:@selector(onRefreshTokenSuccess:)]) {
            [obj onRefreshTokenSuccess:domain.oauthData];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]刷新Token成功，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onAuthDataRefreshedFailed"]) {
        if ([obj respondsToSelector:@selector(onInvalidRefreshToken)]) {
            [obj onInvalidRefreshToken];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]刷新Token失败，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onTokenMismatchDevice"]) {
        if ([obj respondsToSelector:@selector(onTokenMismatchDevice)]) {
            [obj onTokenMismatchDevice];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]Token与设备不匹配，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    return isNotifyedAction;
}
- (BOOL)notifyLoginAction:(NSString *)action obj:(id<UpUserDomainObserver>)obj upuserdomain:(id<UpUserDomainDelegate>)domain
{
    BOOL isNotifyedAction = NO;
    if ([action isEqualToString:@"onLogOut"]) {
        if ([obj respondsToSelector:@selector(onLogOut:)]) {
            [obj onLogOut:domain];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]用户登出，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onWillLogOut"]) {
        if ([obj respondsToSelector:@selector(onWillLogOut:)]) {
            [obj onWillLogOut:domain];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]用户将要登出，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"cancelLogin"]) {
        if ([obj respondsToSelector:@selector(onCancleLogin)]) {
            [obj onCancleLogin];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]用户取消登录，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    return isNotifyedAction;
}
- (BOOL)notifyUserDomainAction:(NSString *)action obj:(id<UpUserDomainObserver>)obj upuserdomain:(id<UpUserDomainDelegate>)domain
{
    BOOL isNotifyedAction = NO;
    if ([action isEqualToString:@"onPlannedRefreshTokenFailed"]) {
        if ([obj respondsToSelector:@selector(onPlannedRefreshTokenFailure)]) {
            [obj onPlannedRefreshTokenFailure];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]定时刷新Token失败，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    else if ([action isEqualToString:@"onPlannedRefreshTokenSuccess"]) {
        if ([obj respondsToSelector:@selector(onPlannedRefreshTokenSuccess:)]) {
            [obj onPlannedRefreshTokenSuccess:domain.oauthData];
            isNotifyedAction = YES;
        }
        UPLogInfo(@"UPUserDomain", @"%s[%d]定时刷新Token成功，已通知监听者:%@", __PRETTY_FUNCTION__, __LINE__, obj);
    }
    return isNotifyedAction;
}

- (void)onReceivedAction:(NSString *)action upuserdomain:(id<UpUserDomainDelegate>)domain
{
    NSArray<id<UpUserDomainObserver>> *copyObservers = [self.observers.allObjects copy];
    for (NSInteger i = 0; i < copyObservers.count; i++) {
        id<UpUserDomainObserver> obj = copyObservers[i];
        BOOL isOnRecevie = [self notifyPQAction:action obj:obj upuserdomain:domain];
        if (isOnRecevie) {
            continue;
        }
        isOnRecevie = [self notifyUserAction:action obj:obj upuserdomain:domain];
        if (isOnRecevie) {
            continue;
        }
        isOnRecevie = [self notifyUserAddressAndTerminalListAction:action obj:obj upuserdomain:domain];
        if (isOnRecevie) {
            continue;
        }
        isOnRecevie = [self notifyFamilyListAction:action obj:obj upuserdomain:domain];
        if (isOnRecevie) {
            continue;
        }
        isOnRecevie = [self notifyFamilyDetailAction:action obj:obj upuserdomain:domain];
        if (isOnRecevie) {
            continue;
        }
        isOnRecevie = [self notifyDeviceAction:action obj:obj upuserdomain:domain];
        if (isOnRecevie) {
            continue;
        }
        isOnRecevie = [self notifyUserDomainAction:action obj:obj upuserdomain:domain];
        if (isOnRecevie) {
            continue;
        }
        isOnRecevie = [self notifyTokenAction:action obj:obj upuserdomain:domain];
        if (isOnRecevie) {
            continue;
        }
        isOnRecevie = [self notifyLoginAction:action obj:obj upuserdomain:domain];
    }
}
@end
