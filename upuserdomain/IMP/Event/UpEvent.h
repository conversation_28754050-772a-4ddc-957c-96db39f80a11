//
//  UpEvent.h
//  upuserdomain
//
//  Created by 闫达 on 2020/7/29.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
extern NSString *const REFRESH_TOKEN;
extern NSString *const REFRESH_USER;
extern NSString *const CLEAR_OPERATORS;
extern NSString *const REFRESH_FAMILY_DELAYED;
extern NSString *const LOAD_CACHED_TOKEN;
extern NSString *const REFRESH_TOKEN_SUCCESS;
extern NSString *const REFRESH_TOKEN_FAILURE;
extern NSString *const TOKEN_INVALID;
extern NSString *const REFRESH_TERMINAL_LIST_SUCCESS;
extern NSString *const REFRESH_TERMINAL_LIST_FAILURE;
extern NSString *const REFRESH_USER_SUCCESS;
extern NSString *const REFRESH_USER_FAILURE;
extern NSString *const REFRESH_FAMILY_LIST_SUCCESS;
extern NSString *const REFRESH_FAMILY_LIST_FAILURE;
extern NSString *const REFRESH_DEVICE_LIST_SUCCESS;
extern NSString *const REFRESH_DEVICE_LIST_FAILURE;
extern NSString *const REFRESH_COMPLETED;
extern NSString *const REFRESH_FAILED;
extern NSString *const CURRENT_FAMILY_CHANGED;
extern NSString *const NOTIFY_CANCEL_LOGIN;
extern NSString *const REFRESH_FAMILY_DETAIL_SUCCESS;
extern NSString *const REFRESH_FAMILY_DETAIL_FAILURE;
//操作事件
extern NSString *const OPERATE_REFRESH_FAMILY_LIST;
extern NSString *const OPERATE_REFRESH_DEVICE_LIST;
extern NSString *const OPERATE_REFRESH_ADDRESS_LIST;
extern NSString *const OPERATE_REFRESH_ALL_FAMILY_DETAIL;
extern NSString *const OPERATE_REFRESH_FAMILY_DETAIL;
extern NSString *const OPERATE_REFRESH_ROOM_LIST;
extern NSString *const OPERATE_REFRESH_TOKEN_SCHEDULED_TASK;
extern NSString *const OPERATE_SET_CURRENT_FAMILY;
extern NSString *const OPERATE_REFRESH_TERMINAL_LIST;
extern NSString *const OPERATE_REFRESH_USER;

//通知事件
extern NSString *const NOTIFY_REFRESH_USER_SUCCESS;
extern NSString *const NOTIFY_REFRESH_USER_FAILED;
extern NSString *const NOTIFY_CANCEL_QR_CODE_LOGIN_FAILED;
extern NSString *const NOTIFY_CANCEL_QR_CODE_LOGIN_SUCCESS;
extern NSString *const NOTIFY_QR_CODE_LOGIN_FAILED;
extern NSString *const NOTIFY_QR_CODE_LOGIN_SUCCESS;
extern NSString *const NOTIFY_REFRESH_ADDRESS_LIST_FAILED;
extern NSString *const NOTIFY_REFRESH_ADDRESS_LIST_SUCCESS;
extern NSString *const NOTIFY_REFRESH_DEVICE_LIST_FAILED;
extern NSString *const NOTIFY_REFRESH_DEVICE_LIST_SUCCESS;
extern NSString *const NOTIFY_REFRESH_FAMILY_LIST_FAILED;
extern NSString *const NOTIFY_REFRESH_FAMILY_LIST_SUCCESS;
extern NSString *const NOTIFY_REFRESH_TERMINAL_LIST_FAILED;
extern NSString *const NOTIFY_REFRESH_TERMINAL_LIST_SUCCESS;
extern NSString *const NOTIFY_CURRENT_FAMILY_CHANGED;
extern NSString *const NOTIFY_DEVICE_INFO_UPDATE_SUCCESS;
extern NSString *const NOTIFY_CACHED_AUTH_DATA_LOADED;
extern NSString *const NOTIFY_AUTH_DATA_REFRESHED_SUCCESS;
extern NSString *const NOTIFY_AUTH_DATA_REFRESHED_FAILED;
extern NSString *const NOTIFY_PLANNED_REFRESH_TOKEN_SUCCESS;
extern NSString *const NOTIFY_PLANNED_REFRESH_TOKEN_FAILED;
extern NSString *const NOTIFY_REFRESH_FAMILY_DETAIL_SUCCESS;
extern NSString *const NOTIFY_REFRESH_FAMILY_DETAIL_FAILED;
extern NSString *const NOTIFY_REFRESH_COMPLETED;
extern NSString *const NOTIFY_REFRESH_FAILED;
extern NSString *const NOTIFY_LOG_OUT;
extern NSString *const NOTIFY_WILL_LOG_OUT;
extern NSString *const NOTIFY_TOKEN_INVALID;
extern NSString *const NOTIFY_LOG_IN_ELSEWHERE;
extern NSString *const NOTIFY_TOKEN_MISMATCHDEVICE;
extern NSString *const NOTIFY_REFRESH_DEVICE_CACHE_LIST;
extern NSString *const NOTIFY_CURRENT_FAMILY_ROOM_LIST_CHANGED;
@interface UpEvent : NSObject

@end

NS_ASSUME_NONNULL_END
