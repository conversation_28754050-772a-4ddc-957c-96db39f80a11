//
//  UpNotifyEventHandler.m
//  upuserdomain
//
//  Created by 闫达 on 2020/7/29.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpNotifyEventHandler.h"
#import "UpUserDomainListenerDelegate.h"
#import <uplog/UPLog.h>
#import "UpEvent.h"
#import "UpEventHandlerManagerDelegate.h"
@interface UpNotifyEventHandler ()
@property (nonatomic, strong) NSDictionary *notifyDict;
@end
@implementation UpNotifyEventHandler

- (instancetype)init
{
    if (self = [super init]) {
        self.notifyDict = @{ NOTIFY_CACHED_AUTH_DATA_LOADED : @"onCachedAuthDataLoaded",
                             NOTIFY_AUTH_DATA_REFRESHED_SUCCESS : @"onAuthDataRefreshedSuccess",
                             NOTIFY_AUTH_DATA_REFRESHED_FAILED : @"onAuthDataRefreshedFailed",
                             NOTIFY_PLANNED_REFRESH_TOKEN_SUCCESS : @"onPlannedRefreshTokenSuccess",
                             NOTIFY_PLANNED_REFRESH_TOKEN_FAILED : @"onPlannedRefreshTokenFailed",
                             NOTIFY_REFRESH_USER_SUCCESS : @"onRefreshUserSuccess",
                             NOTIFY_REFRESH_USER_FAILED : @"onRefreshUserFailed",
                             NOTIFY_REFRESH_ADDRESS_LIST_SUCCESS : @"onRefreshAddressListSuccess",
                             NOTIFY_REFRESH_ADDRESS_LIST_FAILED : @"onRefreshAddressListFailed",
                             NOTIFY_REFRESH_TERMINAL_LIST_SUCCESS : @"onRefreshTerminalListSuccess",
                             NOTIFY_REFRESH_TERMINAL_LIST_FAILED : @"onRefreshTerminalListFailed",
                             NOTIFY_REFRESH_FAMILY_LIST_SUCCESS : @"onRefreshFamilyListSuccess",
                             NOTIFY_REFRESH_FAMILY_LIST_FAILED : @"onRefreshFamilyListFailed",
                             NOTIFY_REFRESH_FAMILY_DETAIL_SUCCESS : @"onRefreshFamilyDetailSuccess",
                             NOTIFY_REFRESH_FAMILY_DETAIL_FAILED : @"onRefreshFamilyDetailFailed",
                             NOTIFY_CURRENT_FAMILY_CHANGED : @"onCurrentFamilyChanged",
                             NOTIFY_REFRESH_DEVICE_LIST_SUCCESS : @"onRefreshDeviceListSuccess",
                             NOTIFY_REFRESH_DEVICE_LIST_FAILED : @"onRefreshDeviceListFailed",
                             NOTIFY_QR_CODE_LOGIN_SUCCESS : @"onQrCodeLoginSuccess",
                             NOTIFY_QR_CODE_LOGIN_FAILED : @"onQrCodeLoginFailed",
                             NOTIFY_CANCEL_QR_CODE_LOGIN_SUCCESS : @"onCancelQrCodeLoginSuccess",
                             NOTIFY_CANCEL_QR_CODE_LOGIN_FAILED : @"onCancelQrCodeLoginFailed",
                             NOTIFY_REFRESH_COMPLETED : @"onRefreshCompleted",
                             NOTIFY_REFRESH_FAILED : @"onRefreshFailed",
                             NOTIFY_LOG_OUT : @"onLogOut",
                             NOTIFY_WILL_LOG_OUT : @"onWillLogOut",
                             NOTIFY_TOKEN_INVALID : @"onTokenInvalid",
                             NOTIFY_CANCEL_LOGIN : @"cancelLogin",
                             NOTIFY_TOKEN_MISMATCHDEVICE : @"onTokenMismatchDevice",
                             NOTIFY_REFRESH_DEVICE_CACHE_LIST : @"onRefreshDeviceCacheList",
                             NOTIFY_CURRENT_FAMILY_ROOM_LIST_CHANGED : @"onCurrentFamilyRoomListChanged",
        };
    }
    return self;
}
- (void)handler:(NSString *)action userDomainPrvider:(id<UpUserDomainProvider>)userDomainPrvider
{
    NSArray<id<UpUserDomainListenerDelegate>> *listeners = [userDomainPrvider.provideEventManager cloneUserDomainListeners];
    NSString *actionName = self.notifyDict[action];
    for (id<UpUserDomainListenerDelegate> listener in listeners) {
        if ([listener conformsToProtocol:@protocol(UpUserDomainListenerDelegate)] && [listener respondsToSelector:@selector(onReceivedAction:upuserdomain:)]) {
            [listener onReceivedAction:actionName upuserdomain:userDomainPrvider.provideUpUserDomain];
            UPLogInfo(@"UPUserDomain", @"%s,%d----事件名称为%@", __func__, __LINE__, action);
        }
        else {
            UPLogError(@"UPUserDomain", @"%s,%d----事件发送失败%@", __func__, __LINE__, action);
        }
    }
}

@end
