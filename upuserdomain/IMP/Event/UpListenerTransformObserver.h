//
//  UpListenerTransformObserver.h
//  upuserdomain
//
//  Created by 闫达 on 2020/7/31.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpUserDomainListenerDelegate.h"
NS_ASSUME_NONNULL_BEGIN
@interface UpListenerTransformObserver : NSObject <UpUserDomainListenerDelegate>
- (void)addObserver:(id<UpUserDomainObserver>)observer;
- (void)removeObserver:(id<UpUserDomainObserver>)observer;
@end

NS_ASSUME_NONNULL_END
