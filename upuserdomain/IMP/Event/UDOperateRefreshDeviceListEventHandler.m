//
//  UDOperateRefreshDeviceListEventHandler.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/20.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDOperateRefreshDeviceListEventHandler.h"
#import "UDOperatorManager.h"
#import "UDRefreshDeviceListOp.h"
@implementation UDOperateRefreshDeviceListEventHandler
- (void)handler:(NSString *)action userDomainPrvider:(id<UpUserDomainProvider>)userDomainPrvider
{
    [userDomainPrvider.provideOperatorManager operateWithClass:UDRefreshDeviceListOp.class args:nil success:nil failure:nil];
}
@end
