//
//  UpEvent.m
//  upuserdomain
//
//  Created by 闫达 on 2020/7/29.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEvent.h"

NSString *const REFRESH_TOKEN = @"refreshToken";
/*
 *  刷新用户信息，包括用户的详细信息，家庭列表和设备信息
 */
NSString *const REFRESH_USER = @"refreshUser";
/*
 *  清除所有工作中的Operator
 */
NSString *const CLEAR_OPERATORS = @"clearOperators";
/*
 *  延时刷新家庭
 */
NSString *const REFRESH_FAMILY_DELAYED = @"refreshFamilyDelayed";
/*
 *  获取缓存OauthData成功
 */
NSString *const LOAD_CACHED_TOKEN = @"loadCachedToken";
/*
 *  刷新OauthData成功
 */
NSString *const REFRESH_TOKEN_SUCCESS = @"refreshTokenSuccess";
/*
 *  刷新OauthData失败
 */
NSString *const REFRESH_TOKEN_FAILURE = @"refreshTokenFailure";
/*
 *  Token失效
 */
NSString *const TOKEN_INVALID = @"tokenInvalid";
/*
 *  查询登录终端列表成功
 */
NSString *const REFRESH_TERMINAL_LIST_SUCCESS = @"refreshTerminalListSuccess";
/*
 *  查询登录终端列表失败
 */
NSString *const REFRESH_TERMINAL_LIST_FAILURE = @"refreshTerminalListFailure";
/*
 *  查询用户信息成功
 */
NSString *const REFRESH_USER_SUCCESS = @"refreshUserSuccess";
/*
 *  查询用户信息失败
 */
NSString *const REFRESH_USER_FAILURE = @"refreshUserFailure";
/*
 *  查询家庭列表成功
 */
NSString *const REFRESH_FAMILY_LIST_SUCCESS = @"refreshFamilyListSuccess";
/*
 *  查询家庭列表失败
 */
NSString *const REFRESH_FAMILY_LIST_FAILURE = @"refreshFamilyListFailure";
/*
 *  查询设备列表成功
 */
NSString *const REFRESH_DEVICE_LIST_SUCCESS = @"refreshDeviceListSuccess";
/*
 *  查询设备列表失败
 */
NSString *const REFRESH_DEVICE_LIST_FAILURE = @"refreshDeviceListFailure";
/*
 *  用户全部信息刷新完成
 */
NSString *const REFRESH_COMPLETED = @"refreshCompleted";
/*
 *  用户全部信息刷新失败
 */
NSString *const REFRESH_FAILED = @"refreshFailed";
/*
 *  当前家庭发生变化
 */
NSString *const CURRENT_FAMILY_CHANGED = @"onCurrentFamilyChanged";
/*
 *  用户取消登录
 */
NSString *const NOTIFY_CANCEL_LOGIN = @"cancelLogin";
/*
 *  查询家庭详情成功
 */
NSString *const REFRESH_FAMILY_DETAIL_SUCCESS = @"refreshFamilyDetailSuccess";
/*
 *  查询家庭详情失败
 */
NSString *const REFRESH_FAMILY_DETAIL_FAILURE = @"refreshFamilyDetailFailure";
//操作类事件
NSString *const OPERATE_REFRESH_FAMILY_LIST = @"operateRefreshFamilyList";
NSString *const OPERATE_REFRESH_DEVICE_LIST = @"operateRefreshDeviceList";
NSString *const OPERATE_REFRESH_ADDRESS_LIST = @"operateRefreshAddressList";
NSString *const OPERATE_REFRESH_ALL_FAMILY_DETAIL = @"operateRefreshAllFamilyDetail";
NSString *const OPERATE_REFRESH_TOKEN_SCHEDULED_TASK = @"operateRefreshTokenScheduledTask";
NSString *const OPERATE_SET_CURRENT_FAMILY = @"operateSetCurrentFamily";
NSString *const OPERATE_REFRESH_TERMINAL_LIST = @"operateRefreshTerminalList";
NSString *const OPERATE_REFRESH_USER = @"operateRefreshUser";

//通知类事件
NSString *const NOTIFY_REFRESH_USER_SUCCESS = @"notifyRefreshUserSuccess";
NSString *const NOTIFY_REFRESH_USER_FAILED = @"notifyRefreshUserFailed";
NSString *const NOTIFY_CANCEL_QR_CODE_LOGIN_FAILED = @"notifyCancelQrCodeLoginFailed";
NSString *const NOTIFY_CANCEL_QR_CODE_LOGIN_SUCCESS = @"notifyCancelQrCodeLoginSuccess";
NSString *const NOTIFY_QR_CODE_LOGIN_FAILED = @"notifyQrCodeLoginFailed";
NSString *const NOTIFY_QR_CODE_LOGIN_SUCCESS = @"notifyQrCodeLoginSuccess";
NSString *const NOTIFY_REFRESH_ADDRESS_LIST_FAILED = @"notifyRefreshAddressListFailed";
NSString *const NOTIFY_REFRESH_ADDRESS_LIST_SUCCESS = @"notifyRefreshAddressListSuccess";
NSString *const NOTIFY_REFRESH_DEVICE_LIST_FAILED = @"notifyRefreshDeviceListFailed";
NSString *const NOTIFY_REFRESH_DEVICE_LIST_SUCCESS = @"notifyRefreshDeviceListSuccess";
NSString *const NOTIFY_REFRESH_FAMILY_LIST_FAILED = @"notifyRefreshFamilyListFailed";
NSString *const NOTIFY_REFRESH_FAMILY_LIST_SUCCESS = @"notifyRefreshFamilyListSuccess";
NSString *const NOTIFY_REFRESH_TERMINAL_LIST_FAILED = @"notifyRefreshTerminalListFailed";
NSString *const NOTIFY_REFRESH_TERMINAL_LIST_SUCCESS = @"notifyRefreshTerminalListSuccess";
NSString *const NOTIFY_CURRENT_FAMILY_CHANGED = @"notifyCurrentFamilyChanged";
NSString *const NOTIFY_DEVICE_INFO_UPDATE_SUCCESS = @"notifyDeviceInfoUpdateSuccess";
NSString *const NOTIFY_CACHED_AUTH_DATA_LOADED = @"notifyCachedAuthDataLoaded";
NSString *const NOTIFY_AUTH_DATA_REFRESHED_SUCCESS = @"notifyAuthDataRefreshedSuccess";
NSString *const NOTIFY_AUTH_DATA_REFRESHED_FAILED = @"notifyAuthDataRefreshedFailed";
NSString *const NOTIFY_PLANNED_REFRESH_TOKEN_SUCCESS = @"notifyPlannedRefreshTokenSuccess";
NSString *const NOTIFY_PLANNED_REFRESH_TOKEN_FAILED = @"notifyPlannedRefreshTokenFailed";
NSString *const NOTIFY_REFRESH_FAMILY_DETAIL_SUCCESS = @"notifyRefreshFamilyDetailSuccess";
NSString *const NOTIFY_REFRESH_FAMILY_DETAIL_FAILED = @"notifyRefreshFamilyDetailFailed";
NSString *const NOTIFY_REFRESH_COMPLETED = @"notifyRefreshCompleted";
NSString *const NOTIFY_REFRESH_FAILED = @"notifyRefreshFailed";
NSString *const NOTIFY_LOG_OUT = @"notifyLogOut";
NSString *const NOTIFY_WILL_LOG_OUT = @"notifyWillLogOut";
NSString *const NOTIFY_TOKEN_INVALID = @"notifyTokenInvalid";
NSString *const NOTIFY_LOG_IN_ELSEWHERE = @"notifyLogInElsewhere";
NSString *const NOTIFY_TOKEN_MISMATCHDEVICE = @"notifyTokenMismatchDevice";
NSString *const NOTIFY_REFRESH_DEVICE_CACHE_LIST = @"notifyRefreshDeviceCacheList";
NSString *const NOTIFY_CURRENT_FAMILY_ROOM_LIST_CHANGED = @"notifyCurrentFamilyRoomListChanged";
@implementation UpEvent

@end
