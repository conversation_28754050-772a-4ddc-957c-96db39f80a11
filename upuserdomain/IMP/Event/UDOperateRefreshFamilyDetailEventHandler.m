//
//  UDOperateRefreshFamilyDetailEventHandler.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/20.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDOperateRefreshFamilyDetailEventHandler.h"
#import "UDOperatorManager.h"
#import "UDRefreshAllFamilyDetailOp.h"
@implementation UDOperateRefreshFamilyDetailEventHandler
- (void)handler:(NSString *)action userDomainPrvider:(id<UpUserDomainProvider>)userDomainPrvider
{
    NSArray<Family *> *familyList = userDomainPrvider.provideUpUserDomainStore.familyStore.familyList;
    [userDomainPrvider.provideOperatorManager operateWithClass:UDRefreshAllFamilyDetailOp.class args:familyList success:nil failure:nil];
}
@end
