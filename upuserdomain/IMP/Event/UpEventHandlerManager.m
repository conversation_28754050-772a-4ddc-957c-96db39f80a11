//
//  UpEventHandlerManager.m
//  upuserdomain
//
//  Created by 闫达 on 2020/7/31.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEventHandlerManager.h"
#import "UpEvent.h"
#import "UpUserDomainProvider.h"
#import "UpEventHandlerDelegate.h"
#import "UpNotifyEventHandler.h"
#import "UDOperatePlannedRefreshTokenEventHandler.h"
#import "UpUserDomainListenerDelegate.h"
#import "UDOperateRefreshAddressListEventHandler.h"
#import "UDOperateRefreshTerminalListEventHandler.h"
#import "UDOperateRefreshDeviceListEventHandler.h"
#import "UDOperateRefreshFamilyListEventHandler.h"
#import "UDOperateRefreshFamilyDetailEventHandler.h"
#import "UDOpertateRefreshUserEventHandler.h"
@interface UpEventHandlerManager ()
@property (nonatomic, strong) NSMutableDictionary<UpEvent *, id<UpEventHandlerDelegate>> *handleMap;
@property (nonatomic, strong) id<UpUserDomainProvider> userDomainProvider;
@property (nonatomic, strong) id<UpEventHandlerDelegate> notifyEventHandler;
@property (nonatomic, strong) NSMutableSet *listeners;
@end

@implementation UpEventHandlerManager


- (instancetype)initWithProvider:(id<UpUserDomainProvider>)userDomainProvider
{
    if (self = [super init]) {
        self.handleMap = [NSMutableDictionary dictionary];
        self.listeners = [NSMutableSet set];
        self.userDomainProvider = userDomainProvider;
        UDOperatePlannedRefreshTokenEventHandler *refreshTokenHandler = [[UDOperatePlannedRefreshTokenEventHandler alloc] init];
        [_handleMap setObject:refreshTokenHandler forKey:OPERATE_REFRESH_TOKEN_SCHEDULED_TASK];


        UDOperateRefreshAddressListEventHandler *refreshAddressList = [[UDOperateRefreshAddressListEventHandler alloc] init];
        [_handleMap setValue:refreshAddressList forKey:OPERATE_REFRESH_ADDRESS_LIST];

        UDOperateRefreshTerminalListEventHandler *refreshTerminalList = [[UDOperateRefreshTerminalListEventHandler alloc] init];
        [_handleMap setValue:refreshTerminalList forKey:OPERATE_REFRESH_TERMINAL_LIST];

        UDOperateRefreshDeviceListEventHandler *refreshDeviceList = [[UDOperateRefreshDeviceListEventHandler alloc] init];
        [_handleMap setValue:refreshDeviceList forKey:OPERATE_REFRESH_DEVICE_LIST];

        UDOperateRefreshFamilyListEventHandler *refreshFamilyList = [[UDOperateRefreshFamilyListEventHandler alloc] init];
        [_handleMap setValue:refreshFamilyList forKey:OPERATE_REFRESH_FAMILY_LIST];

        UDOperateRefreshFamilyDetailEventHandler *refreshFamilyDetailList = [[UDOperateRefreshFamilyDetailEventHandler alloc] init];
        [_handleMap setValue:refreshFamilyDetailList forKey:OPERATE_REFRESH_ALL_FAMILY_DETAIL];

        UDOpertateRefreshUserEventHandler *refreshUser = [[UDOpertateRefreshUserEventHandler alloc] init];
        [_handleMap setValue:refreshUser forKey:OPERATE_REFRESH_USER];

        self.notifyEventHandler = [[UpNotifyEventHandler alloc] init];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_CACHED_AUTH_DATA_LOADED];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_AUTH_DATA_REFRESHED_SUCCESS];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_AUTH_DATA_REFRESHED_FAILED];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_PLANNED_REFRESH_TOKEN_SUCCESS];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_PLANNED_REFRESH_TOKEN_FAILED];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_USER_SUCCESS];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_USER_FAILED];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_ADDRESS_LIST_SUCCESS];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_ADDRESS_LIST_FAILED];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_TERMINAL_LIST_SUCCESS];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_TERMINAL_LIST_FAILED];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_FAMILY_LIST_SUCCESS];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_FAMILY_LIST_FAILED];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_FAMILY_DETAIL_SUCCESS];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_FAMILY_DETAIL_FAILED];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_CURRENT_FAMILY_CHANGED];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_DEVICE_LIST_SUCCESS];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_DEVICE_LIST_FAILED];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_QR_CODE_LOGIN_SUCCESS];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_QR_CODE_LOGIN_FAILED];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_CANCEL_QR_CODE_LOGIN_SUCCESS];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_CANCEL_QR_CODE_LOGIN_FAILED];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_COMPLETED];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_FAILED];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_LOG_OUT];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_WILL_LOG_OUT];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_TOKEN_INVALID];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_LOG_IN_ELSEWHERE];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_TOKEN_MISMATCHDEVICE];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_CANCEL_LOGIN];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_REFRESH_DEVICE_CACHE_LIST];
        [_handleMap setObject:self.notifyEventHandler forKey:NOTIFY_CURRENT_FAMILY_ROOM_LIST_CHANGED];
    }
    return self;
}

- (void)registerListener:(id<UpUserDomainListenerDelegate>)listener
{
    if ([listener conformsToProtocol:@protocol(UpUserDomainListenerDelegate)]) {
        @synchronized(self.listeners)
        {
            [self.listeners addObject:listener];
        }
    }
}

- (void)unregisterListener:(id<UpUserDomainListenerDelegate>)listener
{
    if ([listener conformsToProtocol:@protocol(UpUserDomainListenerDelegate)]) {
        @synchronized(self.listeners)
        {
            [self.listeners removeObject:listener];
        }
    }
}
- (NSArray<id<UpUserDomainListenerDelegate>> *)cloneUserDomainListeners
{
    return self.listeners.allObjects;
}

- (void)sendEvent:(NSString *)event
{
    if (event == nil || event.length == 0) {
        return;
    }
    id<UpEventHandlerDelegate> eventHandler = [self.handleMap valueForKey:event];
    if (eventHandler == nil) {
        return;
    }
    if ([NSThread isMainThread]) {
        [eventHandler handler:event userDomainPrvider:self.userDomainProvider];
    }
    else {
        dispatch_async(dispatch_get_main_queue(), ^{
          [eventHandler handler:event userDomainPrvider:self.userDomainProvider];
        });
    }
}


@end
