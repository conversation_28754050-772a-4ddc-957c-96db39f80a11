//
//  UDOperateRefreshTokenEventHandler.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/18.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDOperatePlannedRefreshTokenEventHandler.h"
#import "UpUserDomainStore.h"
#import "UDPlannedRefreshAuthDataOp.h"
#import "UDAuthDataDelegate.h"
#import "UDOperatorManager.h"

@implementation UDOperatePlannedRefreshTokenEventHandler
- (void)handler:(NSString *)action userDomainPrvider:(id<UpUserDomainProvider>)userDomainPrvider
{
    UpUserDomainStore *store = userDomainPrvider.provideUpUserDomainStore;
    [userDomainPrvider.provideOperatorManager operateWithClass:[UDPlannedRefreshAuthDataOp class]
                                                          args:store.oauthData.refresh_token
                                                       success:nil
                                                       failure:nil];
}
@end
