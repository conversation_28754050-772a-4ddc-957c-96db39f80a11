//
//  UDOpertateRefreshUserEventHandler.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/25.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDOpertateRefreshUserEventHandler.h"
#import "UDOperatorManager.h"
#import "UDRefreshUserOp.h"
@implementation UDOpertateRefreshUserEventHandler
- (void)handler:(NSString *)action userDomainPrvider:(id<UpUserDomainProvider>)userDomainPrvider
{
    [userDomainPrvider.provideOperatorManager operateWithClass:[UDRefreshUserOp class]
                                                          args:nil
                                                       success:nil
                                                       failure:nil];
}
@end
