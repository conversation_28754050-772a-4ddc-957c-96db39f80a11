//
//  UDQrConfirmLoginOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/12.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDQrConfirmLoginOp.h"
#import "UpUserDataSource.h"
@implementation UDQrConfirmLoginOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSString *uuid = (NSString *)args;
    [[self.userDomainProvider provideUserDataSource] qrConfirmLogin:uuid success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_QR_CODE_LOGIN_SUCCESS];
    }
    return [super onSuccess:result];
}
- (BOOL)onFailure:(id<UpUserDomainResult>)result
{
    [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_QR_CODE_LOGIN_FAILED];
    return [super onFailure:result];
}
@end
