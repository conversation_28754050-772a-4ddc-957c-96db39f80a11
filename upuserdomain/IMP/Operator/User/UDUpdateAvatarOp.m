//
//  UDUpdateAvatarOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/13.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDUpdateAvatarOp.h"
#import "UpUserDataSource.h"
@implementation UDUpdateAvatarOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UIImage *image = (UIImage *)args;
    [[self.userDomainProvider provideUserDataSource] updateAvatar:image success:success failure:failure];
}
@end
