//
//  UDPollqrCodeStateOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/12.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDPollqrCodeStateOp.h"
#import "UpUserDataSource.h"
@implementation UDPollqrCodeStateOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSString *uuid = (NSString *)args;
    [[self.userDomainProvider provideUserDataSource] pollqrCodeState:uuid success:success failure:failure];
}
@end
