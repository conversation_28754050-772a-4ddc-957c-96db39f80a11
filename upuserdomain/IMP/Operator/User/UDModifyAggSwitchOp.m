//
//  UDModifyAggSwitchOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/25.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDModifyAggSwitchOp.h"
#import <MJExtension/MJExtension.h>
#import "AggregationSwitchArgs.h"
#import "UpUserDataSource.h"
#import "UpEventHandlerManagerDelegate.h"

@implementation UDModifyAggSwitchOp

- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    AggregationSwitchArgs *params = (AggregationSwitchArgs *)args;
    NSMutableArray *familyAggs = [NSMutableArray array];
    for (FamilyAgg *agg in params.familyAgg) {
        [familyAggs addObject:[agg mj_keyValues]];
    }
    id<UpUserDataSource> dataSouce = [self.userDomainProvider provideUserDataSource];
    [dataSouce modifyAggregationSwitch:familyAggs
                                source:params.source
                               success:success
                               failure:failure];
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_DEVICE_LIST];
    }
    return [super onSuccess:result];
}

@end
