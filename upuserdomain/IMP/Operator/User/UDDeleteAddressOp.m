//
//  UDDeleteAddressOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/12.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDDeleteAddressOp.h"
#import "UpUserDataSource.h"
@implementation UDDeleteAddressOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSString *addressId = (NSString *)args;
    [[self.userDomainProvider provideUserDataSource] deleteAddress:addressId success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        [[self.userDomainProvider provideUpUserDomainStore].userStore deleteAddressId:self.args];
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_USER_SUCCESS];
        [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_ADDRESS_LIST];
    }
    return [super onSuccess:result];
}
@end
