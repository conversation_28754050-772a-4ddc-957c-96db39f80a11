//
//  UDTerminalListOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/13.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDRefreshTerminalListOp.h"
#import "UpUserDataSource.h"
#import "UDServerCacheManager.h"
#import <MJExtension/MJExtension.h>
@implementation UDRefreshTerminalListOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [[self.userDomainProvider provideUserDataSource] queryLoginTerminalSuccess:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        [[self.userDomainProvider provideUpUserDomainStore].userStore updateTerminalsList:result.retData];
        NSArray<UserTerminal *> *terminals = result.retData;
        NSArray *array = [NSArray mj_keyValuesArrayWithObjectArray:terminals];
        [[UDServerCacheManager getInstance] addServerResponseInfo:array forKey:kServerResponseLoginTerminalInfo];
        if ([[UDServerCacheManager getInstance] needNotifyLoginTerminalRefreshSuccess]) {
            [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_TERMINAL_LIST_SUCCESS];
        }
    }
    return [super onSuccess:result];
}
- (BOOL)onFailure:(id<UpUserDomainResult>)result
{
    [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_TERMINAL_LIST_FAILED];
    return [super onFailure:result];
}
- (BOOL)supportMultiCallback
{
    return YES;
}
@end
