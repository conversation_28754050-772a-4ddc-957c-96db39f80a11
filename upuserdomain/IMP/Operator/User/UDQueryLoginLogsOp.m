//
//  UDQueryLoginLogsOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/12.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDQueryLoginLogsOp.h"
#import "UpUserDataSource.h"
@implementation UDQueryLoginLogsOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    [[self.userDomainProvider provideUserDataSource] queryLoginLogs:[dict[@"pageNo"] integerValue] pageSize:[dict[@"pageSize"] integerValue] success:success failure:failure];
}
@end
