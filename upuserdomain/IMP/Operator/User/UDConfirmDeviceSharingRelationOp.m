//
//  UDConfirmDeviceSharingRelationOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/3/10.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDConfirmDeviceSharingRelationOp.h"
#import "UpUserDataSource.h"
@implementation UDConfirmDeviceSharingRelationOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    [[self.userDomainProvider provideUserDataSource] confirmDeviceSharingRelation:dict[@"shareUuid"] success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_DEVICE_LIST];
    }
    return [super onSuccess:result];
}
@end
