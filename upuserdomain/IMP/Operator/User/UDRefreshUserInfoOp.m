//
//  UDRefreshUserInfoOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/13.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDRefreshUserInfoOp.h"
#import "UpUserDataSource.h"
#import "UDServerCacheManager.h"
#import <MJExtension/MJExtension.h>
#import "UserInfo.h"
@implementation UDRefreshUserInfoOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [[self.userDomainProvider provideUserDataSource] queryUserInfosuccess:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        [[self.userDomainProvider provideUpUserDomainStore].userStore updateUserInfo:result.retData];
        UserInfo *userInfo = result.retData;
        [[UDServerCacheManager getInstance] addServerResponseInfo:[userInfo mj_keyValues] forKey:kServerResponseUserInfo];
        if ([[UDServerCacheManager getInstance] needNotifyUserRefreshSuccess]) {
            [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_USER_SUCCESS];
        }
    }
    [self.userDomainProvider provideUpUserDomainStore].userStore.isRefreshUserCompleted = YES;
    return [super onSuccess:result];
}
- (BOOL)onFailure:(id<UpUserDomainResult>)result
{
    [self.userDomainProvider provideUpUserDomainStore].userStore.isRefreshUserCompleted = [result.retCode isEqualToString:@"99999"];
    [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_USER_FAILED];
    return [super onFailure:result];
}
- (BOOL)supportMultiCallback
{
    return YES;
}
@end
