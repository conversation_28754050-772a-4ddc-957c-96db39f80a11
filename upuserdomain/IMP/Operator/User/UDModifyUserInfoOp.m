//
//  UDModifyUserInfoOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/12.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDModifyUserInfoOp.h"
#import "UpUserDataSource.h"
#import <MJExtension/MJExtension.h>
#import "UserInfoArgs.h"
#import "UserInfo.h"
@implementation UDModifyUserInfoOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UserInfoArgs *userInfoArgs = (UserInfoArgs *)args;
    NSDictionary *dict = [userInfoArgs mj_keyValues];
    [[self.userDomainProvider provideUserDataSource] modifyUserInfo:dict success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        [[self.userDomainProvider provideUpUserDomainStore].userStore updateUserInfoWithArgs:(UserInfoArgs *)self.args];
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_USER_SUCCESS];
        [[self.userDomainProvider provideUserDataSource] uHomeSearchUserInfo:^(UserDomainSampleResult *_Nonnull result) {
        }
            failure:^(UserDomainSampleResult *_Nonnull result){
            }];
    }
    return [super onSuccess:result];
}
@end
