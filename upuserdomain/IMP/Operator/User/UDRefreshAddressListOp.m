//
//  UDRereshAddressListOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/12.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDRefreshAddressListOp.h"
#import "UpUserDataSource.h"
#import "UDServerCacheManager.h"
#import <MJExtension/MJExtension.h>
@implementation UDRefreshAddressListOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [[self.userDomainProvider provideUserDataSource] queryUserAddresssuccess:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        [[self.userDomainProvider provideUpUserDomainStore].userStore updateAddressList:result.retData];
        NSArray<UserAddressInfo *> *addressList = result.retData;
        NSArray *array = [NSArray mj_keyValuesArrayWithObjectArray:addressList];
        [[UDServerCacheManager getInstance] addServerResponseInfo:array forKey:kServerResponseUserAddressInfo];
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_ADDRESS_LIST_SUCCESS];
    }
    return [super onSuccess:result];
}
- (BOOL)onFailure:(id<UpUserDomainResult>)result
{
    [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_ADDRESS_LIST_FAILED];
    return [super onFailure:result];
}
- (BOOL)supportMultiCallback
{
    return YES;
}
@end
