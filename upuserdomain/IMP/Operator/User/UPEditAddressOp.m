//
//  UPEditAddressOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/12.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPEditAddressOp.h"
#import "UpUserDataSource.h"
#import <MJExtension/MJExtension.h>
#import "UserAddressArgs.h"
@implementation UPEditAddressOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UserAddressArgs *addressArgs = (UserAddressArgs *)args;
    UserAddressArgs *adds = [[UserAddressArgs alloc] init];
    adds.city = addressArgs.city;
    adds.city_id = addressArgs.city_id;
    adds.district = addressArgs.district;
    adds.district_id = addressArgs.district_id;
    adds.line1 = addressArgs.line1;
    adds.province = addressArgs.province;
    adds.province_id = addressArgs.province_id;
    adds.town = addressArgs.town;
    adds.town_id = addressArgs.town_id;
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    dict[@"address"] = [adds mj_keyValues];
    dict[@"id"] = addressArgs.addressId;
    dict[@"is_default"] = @(addressArgs.is_default);
    dict[@"is_service"] = @(addressArgs.is_service);
    dict[@"receiver_mobile"] = addressArgs.receiver_mobile;
    dict[@"receiver_name"] = addressArgs.receiver_name;
    dict[@"source"] = addressArgs.source;
    [[self.userDomainProvider provideUserDataSource] editAddress:dict success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        [[self.userDomainProvider provideUpUserDomainStore].userStore updateAddress:self.args];
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_USER_SUCCESS];
    }
    return [super onSuccess:result];
}
@end
