//
//  UDModifyDeviceNameOp.m
//  upuserdomain
//
//  Created by Apple on 2020/7/29.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDModifyDeviceNameOp.h"
#import "UpDeviceListDataSource.h"
@implementation UDModifyDeviceNameOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    [[self.userDomainProvider provideDeviceDataSource] updateDeviceNameWithDeviceId:dict[@"deviceId"] oldName:dict[@"oldName"] newName:dict[@"name"] familyId:dict[@"familyId"] deviceNetType:dict[@"deviceNetType"] prodNo:dict[@"prodNo"] success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result) {
        NSDictionary *dict = (NSDictionary *)self.args;
        [[self.userDomainProvider provideUpUserDomainStore].deviceStore updateDevice:dict[@"deviceId"] deviceName:dict[@"name"]];
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_DEVICE_LIST_SUCCESS];
    }
    return [super onSuccess:result];
}
@end
