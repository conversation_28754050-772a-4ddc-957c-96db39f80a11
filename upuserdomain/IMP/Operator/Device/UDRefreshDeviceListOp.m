//
//  UDRefreshDeviceListOp.m
//  upuserdomain
//
//  Created by Apple on 2020/7/30.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDRefreshDeviceListOp.h"
#import "UpDeviceListDataSource.h"
#import "UDServerCacheManager.h"
#import <MJExtension/MJExtension.h>
@implementation UDRefreshDeviceListOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [[self.userDomainProvider provideDeviceDataSource] queryDeviceList:success failure:failure];
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSArray<Device *> *deviceList = result.retData;
        NSArray *array = [NSArray mj_keyValuesArrayWithObjectArray:deviceList];
        [[UDServerCacheManager getInstance] addServerResponseInfo:array forKey:kServerResponseDeviceListInfo];
        [[self.userDomainProvider provideUpUserDomainStore].deviceStore updateDeviceList:result.retData];

        if ([[UDServerCacheManager getInstance] needNotifyDeviceListRefreshSuccess]) {
            [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_DEVICE_LIST_SUCCESS];
        }
    }
    [self.userDomainProvider provideUpUserDomainStore].deviceStore.isRefreshDeviceListCompleted = YES;
    return [super onSuccess:result];
}
- (BOOL)onFailure:(id<UpUserDomainResult>)result
{
    [self.userDomainProvider provideUpUserDomainStore].deviceStore.isRefreshDeviceListCompleted = [result.retCode isEqualToString:@"99999"];
    [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_DEVICE_LIST_FAILED];
    return [super onFailure:result];
}

- (BOOL)supportMultiCallback
{
    return YES;
}

@end
