//
//  UDModifyDeviceNameNewOp.m
//  upuserdomain
//
//  Created by whenwe on 2023/9/20.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDModifyDeviceNameNewOp.h"
#import "UpDeviceListDataSource.h"

@implementation UDModifyDeviceNameNewOp

- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    [[self.userDomainProvider provideDeviceDataSource] updateDeviceNameWithDeviceId:dict[@"deviceId"] oldName:dict[@"oldName"] newName:dict[@"name"] familyId:dict[@"familyId"] type:dict[@"type"] checkLevel:[dict[@"checkLevel"] boolValue] success:success failure:failure];
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result) {
        NSDictionary *dict = (NSDictionary *)self.args;
        [[self.userDomainProvider provideUpUserDomainStore].deviceStore updateDevice:dict[@"deviceId"] deviceName:dict[@"name"]];
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_DEVICE_LIST_SUCCESS];
    }
    return [super onSuccess:result];
}

@end
