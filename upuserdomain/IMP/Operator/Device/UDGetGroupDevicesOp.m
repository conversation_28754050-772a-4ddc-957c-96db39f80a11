//
//  UDGetGroupDevicesOp.m
//  upuserdomain
//
//  Created by 冉东军 on 2022/11/4.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDGetGroupDevicesOp.h"
#import "UpDeviceListDataSource.h"
#import <MJExtension/MJExtension.h>
@implementation UDGetGroupDevicesOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    [[self.userDomainProvider provideDeviceDataSource] getGroupDeviceList:familyId filterFlag:NO success:success failure:failure];
}

@end
