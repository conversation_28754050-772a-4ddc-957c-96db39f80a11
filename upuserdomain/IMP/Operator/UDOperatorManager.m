//
//  UDOperatorManager.m
//  upuserdomain
//
//  Created by 闫达 on 2020/7/28.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDOperatorManager.h"
#import "UDOperator.h"
#import <uplog/UPLog.h>
@interface UDOperatorManager ()
@property (nonatomic, strong) NSMutableDictionary<NSString *, CreateOpBlock> *creatorMap;
@property (nonatomic, strong) NSMutableDictionary<NSString *, UDOperator *> *workingQueue;
@property (nonatomic, strong) NSMutableDictionary<NSString *, UDOperator *> *pendingQueue;
@property (nonatomic, strong) NSMutableSet<UDOperator *> *operators;
@property (nonatomic, strong) id<UpUserDomainProvider> userDomainProvider;
@property (nonatomic, strong) dispatch_semaphore_t queueSemaphore;
@property (nonatomic, strong) NSMutableDictionary *mutilCallBackMap;
@end

@implementation UDOperatorManager

- (instancetype)initWithProvider:(id<UpUserDomainProvider>)provider
{
    self = [super init];
    if (self) {
        _creatorMap = [NSMutableDictionary dictionary];
        _workingQueue = [NSMutableDictionary dictionaryWithCapacity:DEFAULT_CAPACITY];
        _pendingQueue = [NSMutableDictionary dictionary];
        _operators = [NSMutableSet set];
        _userDomainProvider = provider;
        _queueSemaphore = dispatch_semaphore_create(1);
        _mutilCallBackMap = [NSMutableDictionary dictionary];
        [self initAuthOps];
    }
    return self;
}


- (void)initAuthOps
{
    NSArray *classArray = @[ @"UDDeleteDevicesOp", @"UDMoveDevicesToRoomOp", @"UDUnbindDevicesOp", @"UDDeleteMemberOp", @"UDInviteFamilyMemberOp", @"UDEditRoomNameOp", @"UDDeleteRoomOp", @"UDCreateRoomOp", @"UDUpdateFamilyDetailOp", @"UDModifyDeviceNameOp", @"UDUpdateAvatarOp", @"UDSetCurrentFamilyOp", @"UDReplyFamilyInviteOp", @"UDRefreshUserInfoOp", @"UDRefreshTerminalListOp", @"UDRefreshFamilyListOp", @"UDRefreshDeviceListOp", @"UDRefreshAddressListOp", @"UDQueryLoginLogsOp", @"UDQueryServiceOrderOp", @"UDQrConfirmScanOp", @"UDQrConfirmLoginOp", @"UDQrCancleLoginOp", @"UDPollqrCodeStateOp", @"UDModifyUserInfoOp", @"UPEditAddressOp", @"UDDeleteAddressOp", @"UDCreateAddressOp", @"UDCreateFamilyOp", @"UDLogoutOp", @"UDRefreshTokenOp", @"UDMoveDevicesToFamilyOp", @"UDExitFamilyAsAdminOp", @"UDDeleteFamilyAsAdminOp", @"UDChangeFamilyAdminOp", @"UDEditFamilyInfoOp", @"UDRefreshRoomListOp", @"UDExitFamilyAsMemberOp", @"UDSingleClientCheckOp", @"UDRefreshUserOp", @"UDRefreshAllFamilyDetailOp", @"UDPlannedRefreshAuthDataOp", @"UDAddFloorOp", @"UDEditFloorOp", @"UDDeleteFloorOp", @"UDReplyJoinFamilyOp", @"UDQueryFirstMemeberOp", @"UDAddVirtualMemberOp", @"UDModifyVirtualMemberOp", @"UDModifyMemberRoleOp", @"UDModifyVirtualMemberRoleOp", @"UDSaveRoomsOrderOp", @"UDGetGroupDevicesOp", @"UDModifyDeviceNameNewOp", @"UDConfirmDeviceSharingRelationOp", @"UDCancelDeviceSharingRelationOp", @"UDModifyDeviceCardListOp", @"UDModifyDeviceAggOp", @"UDModifyAggSwitchOp", @"UDModifyMemberTypeOp", @"UDCreateRoomNewOp", @"UDSaveRoomsOrderNewOp" ];

    for (NSString *clazz in classArray) {
        CreateOpBlock deleteDevicesOp = ^(void) {
          return [[NSClassFromString(clazz) alloc] init];
        };
        [self.creatorMap setValue:deleteDevicesOp forKey:clazz];
    }
}

- (nullable UDOperator *)searchByOperatorKey:(NSString *)operatorKey
{
    if (operatorKey.length == 0) {
        return nil;
    }
    NSArray *keyArray = [operatorKey componentsSeparatedByString:@"|"];
    NSString *actualKey = operatorKey;
    if (keyArray.count == 2) {
        actualKey = keyArray.firstObject;
    }
    __block UDOperator *op;
    [self.operators enumerateObjectsUsingBlock:^(UDOperator *_Nonnull operator, BOOL *_Nonnull stop) {
      if ([self isNotNullAndEqual:operator actualKey:actualKey] && [operator canAppendCallback]) {
          if (keyArray.count == 2) {
                NSString *opKey = [NSString stringWithFormat:@"%@|%@",actualKey,operator.args];
                if ([opKey isEqualToString:operatorKey])
                    op = operator;
          }
          else {
              op = operator;
          }
      }
    }];
    if (op) {
        return op;
    }
    CreateOpBlock operatorBlock = [self.creatorMap objectForKey:actualKey];
    if (operatorBlock == nil) {
        UPLogError(@"UPUserdomain", @"%s%d--未查找到key==%@对应的operator", __func__, __LINE__, operatorKey);
        return nil;
    }
    UDOperator *operator= operatorBlock();
    @synchronized(self.operators)
    {
        [self.operators addObject:operator];
    }
    UPLogInfo(@"UPUserdomain", @"operateAdd---%@ ", actualKey);
    return operator;
}
- (BOOL)isNotNullAndEqual:(UDOperator *) operator actualKey:(NSString *)actualKey
{
    return operator!= nil && actualKey != nil && [NSStringFromClass(operator.class) isEqualToString:actualKey];
}
- (void)operateWithClass:(Class)clas args:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSString *key = NSStringFromClass(clas);
    if (key.length == 0) {
        UPLogError(@"UPUserdomain", @"传入的类名有错误--%@", clas);
        return;
    }
    if ([key isEqualToString:@"UDUpdateFamilyDetailOp"] || [key isEqualToString:@"UDRefreshRoomListOp"]) {
        key = [NSString stringWithFormat:@"%@|%@", key, args];
    }
    dispatch_semaphore_wait(self.queueSemaphore, DISPATCH_TIME_FOREVER);
    UDOperator *operator= [self searchByOperatorKey:key];
    dispatch_semaphore_signal(self.queueSemaphore);
    if (operator!= nil) {
        [operator setProvider:self.userDomainProvider];
        [operator setArgs:args];
        [operator appendSuccess:success failure:failure];
        [operator operate];
    }
}
- (void)removeOperator:(UDOperator *)op
{
    @synchronized(self.operators)
    {
        [self.operators removeObject:op];
    }
}
- (void)dispose
{
    @synchronized(self.operators)
    {
        [self.operators removeAllObjects];
    }
    UPLogInfo(@"UPUserDomain", @"%s%d清空所有operators", __func__, __LINE__);
}

@end
