//
//  UDModifyDeviceCardListOp.m
//  upuserdomain
//
//  Created by lubiao on 2025/3/25.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDModifyDeviceCardListOp.h"
#import "UpFamilyDataSource.h"
#import "DeviceCardStatusArgs.h"
#import "UpDeviceStore.h"
#import "Device.h"
#import "UDDeviceInfo.h"
#import "UDDeviceInfo+PrivateExtension.h"
#import "UDShareDeviceCardInfoDelegate.h"
#import "DeviceShareDeviceCardInfo.h"
#import "DeviceShareDeviceCardInfo+PrivateExtension.h"

@implementation UDModifyDeviceCardListOp

- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    DeviceCardStatusArgs *params = (DeviceCardStatusArgs *)args;

    // 不关心接口结果,先更新缓存
    [self updateDeviceListCacheAndNotify:params];

    id<UpFamilyDataSource> dataSource = [self.userDomainProvider provideFamilyDataSource];
    [dataSource modifyDeviceCardList:params.familyId
                           orderList:params.cardOrderList
                         bigCardList:params.bigCardList
                      middleCardList:params.middleCardList
                       smallCardList:params.smallCardList
                             success:success
                             failure:failure];
}

- (void)updateDeviceListCacheAndNotify:(DeviceCardStatusArgs *)cardStatus
{
    UpDeviceStore *store = [[self.userDomainProvider provideUpUserDomainStore] deviceStore];
    NSArray<Device *> *devices = store.deviceList;
    for (Device *device in devices) {
        UDDeviceInfo *deviceInfo = (UDDeviceInfo *)[device getInfo];

        if (device.sharedDeviceFlag) {
            // 共享设备逻辑
            for (id<UDShareDeviceCardInfoDelegate> deviceCardInfo in device.shareDeviceCardInfo) {
                if (![cardStatus.familyId isEqualToString:deviceCardInfo.familyId]) {
                    continue;
                }

                [self updateShareDevice:deviceCardInfo withDeviceId:device.deviceId cardStatus:cardStatus];
                break;
            }
        }
        else if ([cardStatus.familyId isEqualToString:device.familyId]) {
            [self updateDevice:deviceInfo withCardStatus:cardStatus];
        }
    }

    // 更新本地缓存
    [store updateDeviceList:devices];

    [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_DEVICE_CACHE_LIST];
}

- (void)updateShareDevice:(DeviceShareDeviceCardInfo *)device
             withDeviceId:(NSString *)deviceId
               cardStatus:(DeviceCardStatusArgs *)cardStatus
{
    NSInteger index = [cardStatus.cardOrderList indexOfObject:deviceId];
    if (index != NSNotFound) {
        device.realCardSort = index;
    }

    if ([cardStatus.middleCardList containsObject:deviceId]) {
        device.realCardStatus = 1;
        return;
    }

    if ([cardStatus.bigCardList containsObject:deviceId]) {
        device.realCardStatus = 2;
        return;
    }

    if ([cardStatus.smallCardList containsObject:deviceId]) {
        device.realCardStatus = 0;
    }
}

- (void)updateDevice:(UDDeviceInfo *)device withCardStatus:(DeviceCardStatusArgs *)cardStatus
{
    NSInteger index = [cardStatus.cardOrderList indexOfObject:device.deviceId];
    if (index != NSNotFound) {
        device.realCardSort = index;
    }

    if ([cardStatus.middleCardList containsObject:device.deviceId]) {
        device.realCardStatus = 1;
        return;
    }

    if ([cardStatus.bigCardList containsObject:device.deviceId]) {
        device.realCardStatus = 2;
        return;
    }

    if ([cardStatus.smallCardList containsObject:device.deviceId]) {
        device.realCardStatus = 0;
    }
}

@end
