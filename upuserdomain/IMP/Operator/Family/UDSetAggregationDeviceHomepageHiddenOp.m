//
//  UDSetAggregationDeviceHomepageHiddenOp.m
//  upuserdomain
//
//  Created by gut<PERSON> on 2025/7/10.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDSetAggregationDeviceHomepageHiddenOp.h"
#import "UpFamilyDataSource.h"
#import "AggregationDeviceHomepageHiddenArgs.h"

@implementation UDSetAggregationDeviceHomepageHiddenOp

- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    AggregationDeviceHomepageHiddenArgs *params = (AggregationDeviceHomepageHiddenArgs *)args;
    [self updateDeviceListCacheAndNotify:params];
    id<UpFamilyDataSource> dataSource = [self.userDomainProvider provideFamilyDataSource];
    [dataSource setAggregationDeviceHomepageHidden:params.familyId
                                  devicesDisplayed:params.devicesDisplayed
                                     devicesHidden:params.devicesHidden
                                           success:success
                                           failure:failure];
}

- (void)updateDeviceListCacheAndNotify:(AggregationDeviceHomepageHiddenArgs *)args
{
    UpDeviceStore *store = [[self.userDomainProvider provideUpUserDomainStore] deviceStore];
    NSArray<Device *> *devices = store.deviceList;

    for (Device *device in devices) {
        
    }
    [store updateDeviceList:devices];
    [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_DEVICE_CACHE_LIST];
}

@end
