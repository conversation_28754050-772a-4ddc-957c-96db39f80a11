//
//  UDSetAggregationDeviceHomepageHiddenOp.m
//  upuserdomain
//
//  Created by AI Assistant on 2025/7/10.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDSetAggregationDeviceHomepageHiddenOp.h"
#import "UpFamilyDataSource.h"
#import "AggregationDeviceHomepageHiddenArgs.h"

@implementation UDSetAggregationDeviceHomepageHiddenOp

- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    AggregationDeviceHomepageHiddenArgs *params = (AggregationDeviceHomepageHiddenArgs *)args;
    
    id<UpFamilyDataSource> dataSource = [self.userDomainProvider provideFamilyDataSource];
    [dataSource setAggregationDeviceHomepageHidden:params.familyId
                                   devicesDisplayed:params.devicesDisplayed
                                      devicesHidden:params.devicesHidden
                                            success:success
                                            failure:failure];
}

@end
