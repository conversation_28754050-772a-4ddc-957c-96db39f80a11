//
//  UDQueryFirstMemeberOp.m
//  upuserdomain
//
//  Created by 冉东军 on 2022/1/18.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDQueryFirstMemeberOp.h"
#import "UpFamilyDataSource.h"

@implementation UDQueryFirstMemeberOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    [[self.userDomainProvider provideFamilyDataSource] queryFirstMemeber:familyId success:success failure:failure];
}

@end
