//
//  UDDeleteMemberOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/14.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDDeleteMemberOp.h"
#import "UpFamilyDataSource.h"
@implementation UDDeleteMemberOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *userId = dict[@"userId"];
    NSString *familyId = dict[@"familyId"];
    [[self.userDomainProvider provideFamilyDataSource] deleteFamilyMemberAsAdmin:familyId memberId:userId success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        NSString *userId = dict[@"userId"];
        NSString *familyId = dict[@"familyId"];
        [[self.userDomainProvider provideUpUserDomainStore].familyStore deleteMember:familyId userId:userId];
        [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_FAMILY_LIST];
        [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_DEVICE_LIST];
    }
    return [super onSuccess:result];
}
@end
