//
//  UDExitFamilyAsMemberOp.m
//  upuserdomain
//
//  Created by 闫达 on 2020/8/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDExitFamilyAsMemberOp.h"
#import "UpFamilyDataSource.h"
#import "Device.h"
#import "Family.h"
@implementation UDExitFamilyAsMemberOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSString *familyId = (NSString *)args;
    [[self.userDomainProvider provideFamilyDataSource] exitFamilyAsMember:familyId success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSString *familyId = self.args;
        NSMutableArray<Device *> *deviceList = [[self.userDomainProvider provideUpUserDomainStore].deviceStore.deviceList mutableCopy];
        NSMutableArray *deleteDeviceList = [NSMutableArray array];
        [deviceList enumerateObjectsUsingBlock:^(Device *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          if ([familyId isEqualToString:obj.familyId]) {
              [deleteDeviceList addObject:obj];
          }
        }];
        [deviceList removeObjectsInArray:deleteDeviceList];
        [[self.userDomainProvider provideUpUserDomainStore].deviceStore updateDeviceList:deviceList];
        NSMutableArray<Family *> *familyList = [[self.userDomainProvider provideUpUserDomainStore].familyStore.familyList mutableCopy];
        NSMutableArray *deleteFamilyList = [NSMutableArray array];
        [familyList enumerateObjectsUsingBlock:^(Family *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          if ([familyId isEqualToString:obj.familyId]) {
              [deleteFamilyList addObject:obj];
          }
        }];
        [familyList removeObjectsInArray:deleteFamilyList];
        [[self.userDomainProvider provideUpUserDomainStore].familyStore updateFamilyList:familyList];
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_FAMILY_LIST_SUCCESS];
        [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_DEVICE_LIST];
    }
    return [super onSuccess:result];
}
@end
