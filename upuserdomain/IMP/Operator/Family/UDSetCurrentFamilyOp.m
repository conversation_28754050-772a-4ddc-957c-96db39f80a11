//
//  UDSetCurrentFamilyOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/13.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDSetCurrentFamilyOp.h"
#import "UpFamilyDataSource.h"
#import "Family.h"
@implementation UDSetCurrentFamilyOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSString *familyId = (NSString *)args;
    NSArray<Family *> *familyList = self.userDomainProvider.provideUpUserDomainStore.familyStore.familyList;
    Family *family;
    for (Family *fam in familyList) {
        if ([fam.familyId isEqualToString:familyId]) {
            family = fam;
            break;
        }
    }
    if (family) {
        if (family.members.count) {
            [self.userDomainProvider.provideUpUserDomainStore.familyStore updateCurrentFamily:family];
        }
        else {
            [family queryInfoSuccess:^(UserDomainSampleResult *_Nonnull result) {
              [self.userDomainProvider.provideUpUserDomainStore.familyStore updateCurrentFamily:result.retData];
            }
                failure:^(UserDomainSampleResult *_Nonnull result) {
                  [self.userDomainProvider.provideUpUserDomainStore.familyStore updateCurrentFamily:family];
                }];
        }
    }
    [[self.userDomainProvider provideFamilyDataSource] setDefaultFamily:familyId success:success failure:failure];
}
@end
