//
//  UDInviteFamilyMemberOp.m
//  upuserdomain
//
//  Created by 闫达 on 2020/8/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDInviteFamilyMemberOp.h"
#import "UpFamilyDataSource.h"
#import "AdminInviteMemberArgs.h"

@implementation UDInviteFamilyMemberOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    AdminInviteMemberArgs *params = dict[@"adminInviteMemberArgs"];
    NSString *familyId = dict[@"familyId"];

    [[self.userDomainProvider provideFamilyDataSource] adminInvitateMember:params familyId:familyId success:success failure:failure];
}

@end
