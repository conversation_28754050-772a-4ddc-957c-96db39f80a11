//
//  UDRefreshAllFamilyDetailOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/20.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDRefreshAllFamilyDetailOp.h"
#import "UpFamilyDataSource.h"
#import "Family.h"
#import "UDUpdateFamilyDetailOp.h"
#import "UDOperatorManager.h"
#import <uplog/UPLog.h>
@interface UDRefreshAllFamilyDetailOp ()
@property (atomic, assign) NSUInteger isFamilyCountNeedToRefreshInfoCount;
@property (atomic, assign) BOOL isFamilyDetailInfoRefreshingFailed;
@property (atomic, strong) NSArray<Family *> *familyList;
@end
@implementation UDRefreshAllFamilyDetailOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    self.familyList = (NSArray *)args;
    self.isFamilyDetailInfoRefreshingFailed = NO;
    self.isFamilyCountNeedToRefreshInfoCount = self.familyList.count;
    [self.familyList enumerateObjectsUsingBlock:^(Family *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [[self.userDomainProvider provideOperatorManager] operateWithClass:UDUpdateFamilyDetailOp.class args:obj.familyId success:success failure:failure];
    }];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    return [self reduceCountDownAndCheckToSendEventNotificationIfNeed];
}
- (BOOL)onFailure:(id<UpUserDomainResult>)result
{
    self.isFamilyDetailInfoRefreshingFailed = YES;
    [self reduceCountDownAndCheckToSendEventNotificationIfNeed];
    return [super onFailure:result];
}
- (BOOL)supportMultiCallback
{
    return YES;
}
- (BOOL)reduceCountDownAndCheckToSendEventNotificationIfNeed
{
    @synchronized(self)
    {
        self.isFamilyCountNeedToRefreshInfoCount--;
    }
    if (self.isFamilyCountNeedToRefreshInfoCount > 0) {
        UPLogInfo(@"UPUserDomain", @"%s[%d]剩余待统计的家庭详情刷新结果的个数为:%ld", __PRETTY_FUNCTION__, __LINE__, self.isFamilyCountNeedToRefreshInfoCount);
        return YES;
    }
    UPLogInfo(@"UPUserDomain", @"%s[%d]家庭详情刷新完毕", __PRETTY_FUNCTION__, __LINE__);
    if (!self.isFamilyDetailInfoRefreshingFailed) {
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_FAMILY_DETAIL_SUCCESS];
    }
    else {
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_FAMILY_DETAIL_FAILED];
    }
    return NO;
}
@end
