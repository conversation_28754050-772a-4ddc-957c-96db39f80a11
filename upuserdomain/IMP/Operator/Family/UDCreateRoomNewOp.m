//
//  UDCreateRoomNewOp.m
//  upuserdomain
//
//  Created by l<PERSON><PERSON> on 2025/4/10.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDCreateRoomNewOp.h"
#import "UpFamilyDataSource.h"
#import "RoomNewArgs.h"
#import "UDOperatorManager.h"
#import "UDRefreshRoomListOp.h"

@implementation UDCreateRoomNewOp

- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *params = (NSDictionary *)args;
    RoomNewArgs *roomArgs = params[@"roomNewArgs"];
    NSString *familyId = params[@"familyId"];
    [[self.userDomainProvider provideFamilyDataSource] addRoomForFamilyNew:roomArgs familyId:familyId success:success failure:failure];
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *args = (NSDictionary *)self.args;
        NSString *familyId = args[@"familyId"];
        NSString *floorId = result.retData[@"roomInfo"][@"floorId"] ?: @"";

        NSDictionary *params = @{
            @"familyId" : familyId,
            @"floorId" : floorId
        };
        [[self.userDomainProvider provideOperatorManager] operateWithClass:UDRefreshRoomListOp.class args:params success:nil failure:nil];
    }
    return [super onSuccess:result];
}

@end
