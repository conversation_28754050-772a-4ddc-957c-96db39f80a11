//
//  UDAddFloorOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDAddFloorOp.h"
#import "FloorArg.h"
#import "UpFamilyDataSource.h"
@implementation UDAddFloorOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    FloorArg *floorArg = dict[@"floorArg"];
    [[self.userDomainProvider provideFamilyDataSource] addFloorOfFamily:familyId floorArg:floorArg success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_FAMILY_LIST];
    }
    return [super onSuccess:result];
}
@end
