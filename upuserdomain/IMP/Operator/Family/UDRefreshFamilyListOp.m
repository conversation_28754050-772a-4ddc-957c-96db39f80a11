//
//  UDRefreshFamilyListOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/13.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDRefreshFamilyListOp.h"
#import "UpFamilyDataSource.h"
#import "UDServerCacheManager.h"
#import <MJExtension/MJExtension.h>
@implementation UDRefreshFamilyListOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    [[self.userDomainProvider provideFamilyDataSource] queryFamilyListsuccess:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSArray<Family *> *familyList = result.retData;
        if (familyList.count) {
            [[self.userDomainProvider provideUpUserDomainStore].familyStore updateFamilyList:familyList];
        }
        NSArray *array = [NSArray mj_keyValuesArrayWithObjectArray:familyList];
        [[UDServerCacheManager getInstance] addServerResponseInfo:array forKey:kServerResponseFamilyListInfo];
        if ([[UDServerCacheManager getInstance] needNotifyFamilyListRefreshSuccess]) {
            [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_FAMILY_LIST_SUCCESS];
        }
        [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_ALL_FAMILY_DETAIL];
        result.retData = [self.userDomainProvider provideUpUserDomainStore].familyStore.familyList;
    }
    [self.userDomainProvider provideUpUserDomainStore].familyStore.isRefreshFamilyListCompleted = YES;
    return [super onSuccess:result];
}
- (BOOL)onFailure:(id<UpUserDomainResult>)result
{
    [self.userDomainProvider provideUpUserDomainStore].familyStore.isRefreshFamilyListCompleted = [result.retCode isEqualToString:@"99999"];
    [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_FAMILY_LIST_FAILED];
    return [super onFailure:result];
}
- (BOOL)supportMultiCallback
{
    return YES;
}
@end
