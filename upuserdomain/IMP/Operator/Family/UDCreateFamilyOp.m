//
//  UDCreateFamilyOp.m
//  upuserdomain
//
//  Created by 闫达 on 2020/8/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDCreateFamilyOp.h"
#import "UpFamilyDataSource.h"
@implementation UDCreateFamilyOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    CreateFamilyArgs *createFamilyArgs = (CreateFamilyArgs *)args;
    [[self.userDomainProvider provideFamilyDataSource] createFamily:createFamilyArgs success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_FAMILY_LIST];
    }
    return [super onSuccess:result];
}
@end
