//
//  UDCreateRoomByInfoOp.m
//  upuserdomain
//
//  Created by 闫达 on 2020/8/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDCreateRoomOp.h"
#import "UpFamilyDataSource.h"
#import "RoomArgs.h"
#import "UDOperatorManager.h"
#import "UDRefreshRoomListOp.h"
@implementation UDCreateRoomOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    RoomArgs *roomArgs = dict[@"roomArgs"];
    NSString *familyId = dict[@"familyId"];
    [[self.userDomainProvider provideFamilyDataSource] addRoomForFamily:roomArgs familyId:familyId success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        NSString *familyId = dict[@"familyId"];
        if (familyId == nil) {
            familyId = @"";
        }
        RoomArgs *roomArgs = dict[@"roomArgs"];
        if (roomArgs.floorId == nil) {
            roomArgs.floorId = @"";
        }
        [[self.userDomainProvider provideOperatorManager] operateWithClass:UDRefreshRoomListOp.class
                                                                      args:@{ @"familyId" : familyId,
                                                                              @"floorId" : roomArgs.floorId }
                                                                   success:nil
                                                                   failure:nil];
    }
    return [super onSuccess:result];
}
@end
