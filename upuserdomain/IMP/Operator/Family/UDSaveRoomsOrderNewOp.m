//
//  UDSaveRoomsOrderNewOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON> on 2025/4/21.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDSaveRoomsOrderNewOp.h"
#import "UpFamilyDataSource.h"
#import "UpFamilyStore.h"
#import "Family.h"
#import "User.h"
@implementation UDSaveRoomsOrderNewOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    NSArray *sortList = dict[@"sortList"];
    [[self.userDomainProvider provideFamilyDataSource] saveRoomsOrderNewForFamily:familyId sortList:sortList success:success failure:failure];
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        NSString *familyId = dict[@"familyId"];
        NSArray *sortList = dict[@"sortList"];
        [[self.userDomainProvider provideUpUserDomainStore].familyStore updateFamilyRoomsOrderNew:familyId floorRooms:sortList];
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_FAMILY_LIST_SUCCESS];
        Family *currFamily = [self.userDomainProvider provideUpUserDomain].user.currentFamily;
        if ([currFamily.familyId isEqualToString:familyId]) {
            [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_CURRENT_FAMILY_ROOM_LIST_CHANGED];
        }
    }
    return [super onSuccess:result];
}

@end
