//
//  UDModifyMemberTypeOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON> on 2025/4/11.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDModifyMemberTypeOp.h"
#import "UpFamilyDataSource.h"
#import "UDUserDelegate.h"

@implementation UDModifyMemberTypeOp

- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    NSString *memberId = dict[@"memberId"];
    NSInteger memberType = [dict[@"memberType"] integerValue];
    [[self.userDomainProvider provideFamilyDataSource] modifyMemberTypeOfFamily:familyId memberId:memberId memberType:memberType success:success failure:failure];
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        NSInteger memberType = [dict[@"memberType"] integerValue];
        NSString *memberId = dict[@"memberId"];
        NSString *familyId = dict[@"familyId"];
        [[self.userDomainProvider provideUpUserDomainStore].familyStore updateFamilyMemberInfo:familyId memberId:memberId memberType:memberType];
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_FAMILY_LIST_SUCCESS];
    }
    return [super onSuccess:result];
}

@end
