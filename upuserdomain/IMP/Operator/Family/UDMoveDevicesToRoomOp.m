//
//  UDMoveDevicesToRoomOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/14.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDMoveDevicesToRoomOp.h"
#import "UpFamilyDataSource.h"
#import "UDDeviceDelegate.h"
#import "Room.h"
#import "BatchProcessDeviceResult.h"
#import "CloudBatchProcessDevicesResponse.h"
#import <MJExtension/MJExtension.h>
@implementation UDMoveDevicesToRoomOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    NSArray *devices = dict[@"devices"];
    Room *room = dict[@"room"];
    [[self.userDomainProvider provideFamilyDataSource] moveDevicesToOtherRoom:familyId newRoom:room devices:devices success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_DEVICE_LIST];
        CloudBatchProcessDevicesResponse *reponse = result.retData;
        BatchProcessDeviceResult *processDeviceResult = [BatchProcessDeviceResult mj_objectWithKeyValues:reponse.mj_keyValues];
        result.retData = processDeviceResult;
    }
    return [super onSuccess:result];
}

@end
