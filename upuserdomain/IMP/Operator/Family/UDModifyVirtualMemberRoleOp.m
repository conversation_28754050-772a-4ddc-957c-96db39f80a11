//
//  UDModifyVirtualMemberRoleOp.m
//  upuserdomain
//
//  Created by 冉东军 on 2022/8/24.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDModifyVirtualMemberRoleOp.h"
#import "UpFamilyDataSource.h"
#import "UDUserDelegate.h"

@implementation UDModifyVirtualMemberRoleOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    NSString *memberId = dict[@"memberId"];
    NSString *memberRole = dict[@"memberRole"];
    [[self.userDomainProvider provideFamilyDataSource] modifyVirtualMemberRoleOfFamily:familyId memberId:memberId memberRole:memberRole success:success failure:failure];
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        NSString *memberRole = dict[@"memberRole"];
        NSString *memberId = dict[@"memberId"];
        NSString *familyId = dict[@"familyId"];
        [[self.userDomainProvider provideUpUserDomainStore].familyStore updateFamilyVirtualMemberInfo:familyId memberId:memberId memberRole:memberRole];
    }
    return [super onSuccess:result];
}
@end
