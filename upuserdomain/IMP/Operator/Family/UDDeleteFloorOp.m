//
//  UDDeleteFloorOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDDeleteFloorOp.h"
#import "FloorArg.h"
#import "UpFamilyDataSource.h"
@implementation UDDeleteFloorOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    NSString *floorId = dict[@"floorId"];
    [[self.userDomainProvider provideFamilyDataSource] deleteFloorOfFamily:familyId floorId:floorId success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        NSString *familyId = dict[@"familyId"];
        NSString *floorId = dict[@"floorId"];
        [[self.userDomainProvider provideUpUserDomainStore].familyStore deleteFloorInfo:familyId floorId:floorId];
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_FAMILY_LIST_SUCCESS];
    }
    return [super onSuccess:result];
}
@end
