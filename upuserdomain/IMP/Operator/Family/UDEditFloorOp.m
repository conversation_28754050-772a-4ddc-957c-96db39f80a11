//
//  UDEditFloorOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDEditFloorOp.h"
#import "FloorArg.h"
#import "UpFamilyDataSource.h"
@implementation UDEditFloorOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    FloorArg *floorArg = dict[@"floorArg"];
    [[self.userDomainProvider provideFamilyDataSource] editFloorOfFamily:familyId floorArg:floorArg success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        FloorArg *floorArg = dict[@"floorArg"];
        NSString *familyId = dict[@"familyId"];
        [[self.userDomainProvider provideUpUserDomainStore].familyStore updateFloorInfo:familyId floorArg:floorArg];
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_FAMILY_LIST_SUCCESS];
    }
    return [super onSuccess:result];
}
@end
