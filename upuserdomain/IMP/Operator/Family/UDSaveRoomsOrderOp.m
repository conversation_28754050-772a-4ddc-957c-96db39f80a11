//
//  UDSaveRoomsOrderOp.m
//  upuserdomain
//
//  Created by 冉东军 on 2022/11/14.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDSaveRoomsOrderOp.h"
#import "UpFamilyDataSource.h"
@implementation UDSaveRoomsOrderOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    NSArray *rooms = dict[@"rooms"];
    NSString *floorId = dict[@"floorId"];
    [[self.userDomainProvider provideFamilyDataSource] saveRoomsOrderOfFamily:familyId rooms:rooms floorId:floorId success:success failure:failure];
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        NSString *familyId = dict[@"familyId"];
        NSArray *rooms = dict[@"rooms"];
        NSString *floorId = dict[@"floorId"];
        [[self.userDomainProvider provideUpUserDomainStore].familyStore updateFamilyRoomsOrder:familyId rooms:rooms floorId:floorId];
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_FAMILY_LIST_SUCCESS];
    }
    return [super onSuccess:result];
}
@end
