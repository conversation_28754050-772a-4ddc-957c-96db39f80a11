//
//  UDDeleteRoomByIdOp.m
//  upuserdomain
//
//  Created by 闫达 on 2020/8/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDDeleteRoomOp.h"
#import "UpFamilyDataSource.h"
#import "User.h"
#import "Family.h"
#import "Room.h"
#import "UDFloorInfo.h"
@implementation UDDeleteRoomOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *roomId = dict[@"roomId"];
    NSString *familyId = dict[@"familyId"];
    [[self.userDomainProvider provideFamilyDataSource] removeRoomFromFamily:familyId roomId:roomId success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        NSString *roomId = dict[@"roomId"];
        NSString *familyId = dict[@"familyId"];
        Family *family = (Family *)[[self.userDomainProvider provideUpUserDomain].user getFamilyById:familyId];
        __block NSString *floorId = @"";
        [family.floorInfos enumerateObjectsUsingBlock:^(id<UDFloorInfoDelegate> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          for (id<UDRoomDelegate> room in obj.rooms) {
              NSString *roomIdOther = room.roomId;
              if ([roomIdOther isKindOfClass:[NSString class]] && [roomId isEqualToString:roomIdOther]) {
                  floorId = obj.floorId;
                  *stop = YES;
                  break;
              }
          }
        }];
        [[self.userDomainProvider provideUpUserDomainStore].familyStore deleteRoom:familyId floorId:floorId roomId:roomId];
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_FAMILY_LIST_SUCCESS];
    }
    return [super onSuccess:result];
}
@end
