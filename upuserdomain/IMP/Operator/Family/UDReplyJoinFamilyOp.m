//
//  UDReplyJoinFamilyOp.m
//  upuserdomain
//
//  Created by 冉东军 on 2021/3/4.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDReplyJoinFamilyOp.h"
#import "UpFamilyDataSource.h"

@implementation UDReplyJoinFamilyOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    [[self.userDomainProvider provideFamilyDataSource] replyJoinFamily:dict[@"applicationId"] agree:[dict[@"agree"] boolValue] success:success failure:failure];
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        if ([dict[@"agree"] boolValue]) {
            [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_FAMILY_LIST];
            [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_DEVICE_LIST];
        }
    }
    return [super onSuccess:result];
}

@end
