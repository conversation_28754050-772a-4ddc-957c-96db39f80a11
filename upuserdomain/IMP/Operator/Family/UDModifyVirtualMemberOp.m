//
//  UDModifyVirtualMemberOp.m
//  upuserdomain
//
//  Created by 冉东军 on 2022/1/18.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDModifyVirtualMemberOp.h"
#import "UpFamilyDataSource.h"
#import "UserInfo.h"
#import "UserInfo+PrivateExtension.h"
#import "VirtualMemberArgs.h"

@implementation UDModifyVirtualMemberOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    VirtualMemberArgs *virtualMemberArgs = dict[@"virtualMemberArgs"];
    [[self.userDomainProvider provideFamilyDataSource] modifyVirtualMember:virtualMemberArgs success:success failure:failure];
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        VirtualMemberArgs *virtualMemberArgs = dict[@"virtualMemberArgs"];
        NSString *familyId = dict[@"familyId"];
        [[self.userDomainProvider provideUpUserDomainStore].familyStore updateFamilyVirtualMemberInfo:familyId virtualMemberArgs:virtualMemberArgs];
    }
    return [super onSuccess:result];
}
@end
