//
//  UDUnbindDevicesOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/14.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDUnbindDevicesOp.h"
#import "UpFamilyDataSource.h"
#import "UDDeviceDelegate.h"
#import "Device.h"
#import "CloudBatchProcessDevicesResponse.h"
#import "BatchProcessDeviceResult.h"
#import <MJExtension/MJExtension.h>
@implementation UDUnbindDevicesOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    NSArray<id<UDDeviceDelegate>> *devices = dict[@"devices"];
    [[self.userDomainProvider provideFamilyDataSource] unBindFamilyDevices:familyId devices:devices success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSMutableArray<Device *> *deviceList = [[self.userDomainProvider provideUpUserDomainStore].deviceStore.deviceList mutableCopy];
        NSMutableArray *deleteDeviceList = [NSMutableArray array];
        CloudBatchProcessDevicesResponse *reponse = result.retData;
        [deviceList enumerateObjectsUsingBlock:^(Device *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          for (CloudBatchProcessDevice *cloudDevice in reponse.successDevices) {
              if ([cloudDevice.deviceId isEqualToString:obj.deviceId]) {
                  [deleteDeviceList addObject:obj];
              }
          }
        }];
        [deviceList removeObjectsInArray:deleteDeviceList];
        [[self.userDomainProvider provideUpUserDomainStore].deviceStore updateDeviceList:deviceList];
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_DEVICE_LIST_SUCCESS];
        BatchProcessDeviceResult *processDeviceResult = [BatchProcessDeviceResult mj_objectWithKeyValues:reponse.mj_keyValues];
        result.retData = processDeviceResult;
    }
    return [super onSuccess:result];
}

@end
