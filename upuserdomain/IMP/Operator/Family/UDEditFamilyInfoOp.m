//
//  UDEditFamilyInfoOp.m
//  upuserdomain
//
//  Created by 闫达 on 2020/8/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDEditFamilyInfoOp.h"
#import "UpFamilyDataSource.h"
#import "Family.h"
#import "FamilyArgs.h"
#import "Family+PrivateExtension.h"
#import "FamilyInfo.h"
#import "FamilyInfo+PrivateExtension.h"
#import "FamilyLocation.h"
#import "FamilyLocation+PrivateExtension.h"
@implementation UDEditFamilyInfoOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    FamilyArgs *familyArgs = dict[@"familyArgs"];
    [[self.userDomainProvider provideFamilyDataSource] updateFamilyInfo:familyArgs familyId:familyId success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        NSString *familyId = dict[@"familyId"];
        FamilyArgs *familyArgs = dict[@"familyArgs"];
        NSArray<Family *> *familyList = [self.userDomainProvider provideUpUserDomainStore].familyStore.familyList;
        __block Family *family;
        [familyList enumerateObjectsUsingBlock:^(Family *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          if ([familyId isEqualToString:obj.familyId]) {
              family = obj;
              *stop = YES;
          }
        }];
        FamilyInfo *familyInfo = family.info;
        familyInfo.realFamilyName = familyArgs.name;
        familyInfo.realFamilyPosition = familyArgs.position;
        FamilyLocation *location = familyInfo.familyLocation;
        location.realCityCode = familyArgs.cityCode ?: location.cityCode;
        location.realLatitude = familyArgs.latitude ?: location.latitude;
        location.realLongitude = familyArgs.longitude ?: location.longitude;
        familyInfo.realFamilyLocation = location;
        if (family) {

            [[self.userDomainProvider provideUpUserDomainStore].familyStore updateFamliy:family];
        }
        [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_FAMILY_LIST_SUCCESS];
    }
    return [super onSuccess:result];
}
@end
