//
//  UDEditRoomNameOp.m
//  upuserdomain
//
//  Created by 闫达 on 2020/8/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDEditRoomNameOp.h"
#import "UpFamilyDataSource.h"
@implementation UDEditRoomNameOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *roomId = dict[@"roomId"];
    NSString *roomName = dict[@"roomName"];
    NSString *familyId = dict[@"familyId"];
    NSString *floorId = dict[@"floorId"];
    [[self.userDomainProvider provideFamilyDataSource] updateFamilyRoomName:familyId roomId:roomId roomName:roomName floorId:floorId success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        NSString *roomId = dict[@"roomId"];
        NSString *roomName = dict[@"roomName"];
        NSString *familyId = dict[@"familyId"];
        NSString *floorId = dict[@"floorId"];
        [[self.userDomainProvider provideUpUserDomainStore].familyStore updateRoomName:familyId floorId:floorId roomId:roomId roomName:roomName];
    }
    return [super onSuccess:result];
}
@end
