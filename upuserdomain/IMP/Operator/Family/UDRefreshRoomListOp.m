//
//  UDCreateFamilyAndShareDeviceOp.m
//  upuserdomain
//
//  Created by 闫达 on 2020/8/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDRefreshRoomListOp.h"
#import "UpFamilyDataSource.h"
#import "Family.h"
#import "Family+PrivateExtension.h"
#import "UDFloorInfo.h"
#import "UDFloorInfo+PrivateExtension.h"
@implementation UDRefreshRoomListOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    NSString *floorId = dict[@"floorId"];
    [[self.userDomainProvider provideFamilyDataSource] queryRoomListOfFamily:familyId floorId:floorId success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        NSString *familyId = dict[@"familyId"];
        NSString *floorId = dict[@"floorId"];
        NSArray<Family *> *familyList = [self.userDomainProvider provideUpUserDomainStore].familyStore.familyList;
        Family *findFamily;
        for (Family *family in familyList) {
            if ([family.familyId isEqualToString:familyId])
                findFamily = family;
        }
        for (UDFloorInfo *floorInfo in findFamily.floorInfos) {
            if ([floorInfo.floorId isEqualToString:floorId])
                floorInfo.realRooms = result.retData;
        }
        [self.userDomainProvider.provideUpUserDomainStore.familyStore updateFamilyList:familyList];
    }
    return [super onSuccess:result];
}
@end
