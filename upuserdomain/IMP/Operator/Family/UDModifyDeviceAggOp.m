//
//  UDModifyDeviceAggOp.m
//  upuserdomain
//
//  Created by l<PERSON><PERSON> on 2025/3/25.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDModifyDeviceAggOp.h"
#import <MJExtension/MJExtension.h>
#import "UpFamilyDataSource.h"
#import "DeviceCardAggregationArgs.h"
#import "Device.h"
#import "UDDeviceInfo.h"
#import "UDDeviceInfo+PrivateExtension.h"

@implementation UDModifyDeviceAggOp

- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    DeviceCardAggregationArgs *params = (DeviceCardAggregationArgs *)args;

    // 不关心接口结果,先更新缓存
    [self updateDeviceListCacheAndNotify:params];

    NSMutableArray<NSDictionary *> *aggCards = [NSMutableArray array];
    for (AggCard *card in params.aggCard) {
        NSDictionary *info = [card mj_keyValues];
        [aggCards addObject:info];
    }
    id<UpFamilyDataSource> dataSouce = [self.userDomainProvider provideFamilyDataSource];
    [dataSouce modifyDeviceAggregation:params.familyId
                      aggregationCards:aggCards
                               success:success
                               failure:failure];
}

- (void)updateDeviceListCacheAndNotify:(DeviceCardAggregationArgs *)cardAgg
{
    UpDeviceStore *store = [[self.userDomainProvider provideUpUserDomainStore] deviceStore];
    NSArray<Device *> *devices = store.deviceList;

    for (Device *device in devices) {
        if (![cardAgg.familyId isEqualToString:device.familyId]) {
            continue;
        }

        UDDeviceInfo *deviceInfo = (UDDeviceInfo *)[device getInfo];
        BOOL isAggDevice = NO;
        deviceInfo.realAggregationParentId = @"";

        for (AggCard *card in cardAgg.aggCard) {
            if ([card.sortList containsObject:device.deviceId]) {
                NSString *parentId = [self getParentIdWithAggType:card.aggType familyId:cardAgg.familyId];
                isAggDevice = parentId.length > 0;
                deviceInfo.realAggregationParentId = parentId;
                break;
            }
        }
    }

    // 更新本地缓存
    [store updateDeviceList:devices];

    [[self.userDomainProvider provideEventManager] sendEvent:NOTIFY_REFRESH_DEVICE_CACHE_LIST];
}

- (NSString *)getParentIdWithAggType:(NSString *)aggType familyId:(NSString *)familyId
{
    static NSDictionary *_parentIdPrefix = nil;
    if (!_parentIdPrefix) {
        _parentIdPrefix = @{
            @"0" : @"lightAggId",
            @"1" : @"curtainAggId",
            @"2" : @"envAggId",
            @"3" : @"nonnetAggId",
            @"4" : @"offlineAggId",
            @"5" : @"cameraAggId",
        };
    }
    NSString *prefix = _parentIdPrefix[aggType];
    if (!prefix) {
        return @"";
    }
    return [NSString stringWithFormat:@"%@%@", prefix, familyId];
}

@end
