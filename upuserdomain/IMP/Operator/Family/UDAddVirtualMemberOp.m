//
//  UDAddVirtualMemberOp.m
//  upuserdomain
//
//  Created by 冉东军 on 2022/1/18.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDAddVirtualMemberOp.h"
#import "UpFamilyDataSource.h"

@implementation UDAddVirtualMemberOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    NSString *memberId = dict[@"memberId"];
    NSString *memberName = dict[@"memberName"];
    [[self.userDomainProvider provideFamilyDataSource] addVirtualMember:familyId memberId:memberId memberName:memberName success:success failure:failure];
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_ALL_FAMILY_DETAIL];
    }
    return [super onSuccess:result];
}
@end
