//
//  UDChangeFamilyAdminOp.m
//  upuserdomain
//
//  Created by 闫达 on 2020/7/30.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDChangeFamilyAdminOp.h"
#import "UpFamilyDataSource.h"
#import "Device.h"
#import "Family.h"
@implementation UDChangeFamilyAdminOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    NSString *familyId = dict[@"familyId"];
    NSString *userId = dict[@"userId"];
    [[self.userDomainProvider provideFamilyDataSource] changeFamilyAdmin:familyId userId:userId success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_ALL_FAMILY_DETAIL];
    }
    return [super onSuccess:result];
}

@end
