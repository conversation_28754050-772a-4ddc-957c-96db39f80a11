//
//  UDUpdateFamilyDetailOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/14.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDUpdateFamilyDetailOp.h"
#import "UpFamilyDataSource.h"
#import "Family+PrivateExtension.h"
#import "Family.h"
#import "UDUserDelegate.h"
@implementation UDUpdateFamilyDetailOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSString *familyId = (NSString *)args;
    [[self.userDomainProvider provideFamilyDataSource] queryFamilyInfo:familyId success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        Family *family = result.retData;
        id<UDFamilyDelegate> cacheFamily = [[self.userDomainProvider provideUpUserDomain].user getFamilyById:family.familyId];
        family.realDefaultFamily = cacheFamily.defaultFamily;
        [[self.userDomainProvider provideUpUserDomainStore].familyStore updateFamliy:result.retData];
    }
    return [super onSuccess:result];
}
- (BOOL)supportMultiCallback
{
    return YES;
}
@end
