//
//  UDReplyFamilyInviteOp.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/13.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDReplyFamilyInviteOp.h"
#import "UpFamilyDataSource.h"
@implementation UDReplyFamilyInviteOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSDictionary *dict = (NSDictionary *)args;
    [[self.userDomainProvider provideFamilyDataSource] replyFamilyInviteWithCode:dict[@"code"] familyId:dict[@"familyId"] memberName:dict[@"memberName"] agree:[dict[@"agree"] boolValue] success:success failure:failure];
}
- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    if (result.success) {
        NSDictionary *dict = (NSDictionary *)self.args;
        if ([dict[@"agree"] boolValue]) {
            [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_FAMILY_LIST];
            [[self.userDomainProvider provideEventManager] sendEvent:OPERATE_REFRESH_DEVICE_LIST];
        }
    }
    return [super onSuccess:result];
}
@end
