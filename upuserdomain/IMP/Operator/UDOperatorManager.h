//
//  UDOperatorManager.h
//  upuserdomain
//
//  Created by 闫达 on 2020/7/28.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpUserDomainProvider.h"
#import "UserDomainSampleResult.h"
@class UDOperator;
NS_ASSUME_NONNULL_BEGIN
static NSInteger const DEFAULT_CAPACITY = 10;
@interface UDOperatorManager : NSObject
- (instancetype)initWithProvider:(id<UpUserDomainProvider>)provider;
- (void)operateWithClass:(Class)clas args:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure;
- (nullable UDOperator *)searchByOperatorKey:(NSString *)operatorKey;
- (void)removeOperator:(UDOperator *)op;
- (void)dispose;
@end

NS_ASSUME_NONNULL_END
