//
//  UDLogoutOp.m
//  upuserdomain
//
//  Created by 闫达 on 2020/7/29.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDLogoutOp.h"
#import "UpUserDataSource.h"
@implementation UDLogoutOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSString *token = (NSString *)args;
    [self.userDomainProvider.provideUpUserDomainStore setLoginState:UpUserDomainStateLogingOut];
    [self.userDomainProvider.provideUserDataSource logout:token success:success failure:failure];
}

- (BOOL)supportMultiCallback
{
    return YES;
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    [self.userDomainProvider.provideEventManager sendEvent:NOTIFY_LOG_OUT];
    [self.userDomainProvider.provideUpUserDomainStore clearUserData];
    return [super onSuccess:result];
}

- (BOOL)onFailure:(id<UpUserDomainResult>)result
{
    [self.userDomainProvider.provideEventManager sendEvent:NOTIFY_LOG_OUT];
    [self.userDomainProvider.provideUpUserDomainStore clearUserData];
    result.success = YES;
    return [super onFailure:result];
}
@end
