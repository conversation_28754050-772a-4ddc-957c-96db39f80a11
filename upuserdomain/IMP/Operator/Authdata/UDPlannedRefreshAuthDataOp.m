//
//  UDPlannedRefreshAuthDataOp.m
//  upuserdomain
//
//  Created by 闫达 on 2020/8/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDPlannedRefreshAuthDataOp.h"
#import "UpUserDomainStore.h"
#import "UpUserDomain.h"
@implementation UDPlannedRefreshAuthDataOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSTimeInterval times = [self.userDomainProvider.provideUpUserDomainStore caculatePlanRefreshTokenExpresIn];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(times * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self.userDomainProvider.provideUpUserDomain planRefreshTokenTask:success failure:failure];
    });
}
- (BOOL)supportMultiCallback
{
    return YES;
}

@end
