//
//  UDRefreshUserOp.m
//  upuserdomain
//
//  Created by 闫达 on 2020/8/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDRefreshUserOp.h"
#import "UpEvent.h"
#import "UserDomainSampleResult.h"
#import "User.h"
#import "Device.h"
#import "DeviceOwnerInfo.h"
#import "DeviceOwnerInfo+PrivateExtension.h"
#import "Family+PrivateExtension.h"
#import "Room.h"
#import "UDDeviceInfo.h"
#import "UDDeviceInfo+PrivateExtension.h"
#import "UDServerCacheManager.h"
@implementation UDRefreshUserOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    dispatch_group_t group = dispatch_group_create();
    __block UserDomainSampleResult *refresh_result = [UserDomainSampleResult defaultSuccessResult];
    dispatch_group_enter(group);
    [self.userDomainProvider.provideUpUserDomain.user refreshUserInfo:^(UserDomainSampleResult *_Nonnull result) {
      if (result.success == NO) {
          refresh_result = result;
      }
      dispatch_group_leave(group);
    }];

    if (self.userDomainProvider.provideUpUserDomain.getSettings.isRefreshDeviceListEnable && ![self.userDomainProvider provideUpUserDomainStore].deviceStore.isPreRefreshDeviceList) {
        dispatch_group_enter(group);
        [self.userDomainProvider.provideUpUserDomain.user refreshDeviceList:^(UserDomainSampleResult *_Nonnull result) {
          dispatch_group_leave(group);
        }
            failure:^(UserDomainSampleResult *_Nonnull result) {
              refresh_result = result;
              dispatch_group_leave(group);
            }];
    }
    else {
        if (!self.userDomainProvider.provideUpUserDomain.getSettings.isRefreshDeviceListEnable) {
            [self.userDomainProvider provideUpUserDomainStore].deviceStore.isRefreshDeviceListCompleted = YES;
        }
        [self.userDomainProvider provideUpUserDomainStore].deviceStore.isPreRefreshDeviceList = NO;
        if ([UDServerCacheManager getInstance].serverResponseRecords[kServerResponseDeviceListInfo]) {
            [self.userDomainProvider.provideEventManager sendEvent:NOTIFY_REFRESH_DEVICE_LIST_SUCCESS];
        }
    }

    if (self.userDomainProvider.provideUpUserDomain.getSettings.isRefreshFamilyListEnable) {
        dispatch_group_enter(group);
        [self.userDomainProvider.provideUpUserDomain.user refreshFamilyList:^(UserDomainSampleResult *_Nonnull result) {
          dispatch_group_leave(group);
        }
            failure:^(UserDomainSampleResult *_Nonnull result) {
              refresh_result = result;
              dispatch_group_leave(group);
            }];
    }
    else {
        [self.userDomainProvider provideUpUserDomainStore].familyStore.isRefreshFamilyListCompleted = YES;
    }

    dispatch_async(dispatch_get_main_queue(), ^{
      dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        if (refresh_result.success) {
            if (success) {
                success(refresh_result);
            }
        }
        else {
            if (failure) {
                failure(refresh_result);
            }
        }
      });
    });
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    [self.userDomainProvider.provideEventManager sendEvent:NOTIFY_REFRESH_COMPLETED];
    //    [self productRealDevice];
    return [super onSuccess:result];
}

- (void)productRealDevice
{
    //    NSArray<Device *> *deviceList = self.userDomainProvider.provideUpUserDomainStore.deviceStore.deviceList;
    //    NSArray<Family *> *familyList = self.userDomainProvider.provideUpUserDomainStore.familyStore.familyList;
    //    NSString *uHomeUserId = self.userDomainProvider.provideUpUserDomain.user.uHomeUserId;
    //    for (Device *device in deviceList) {
    //        UDDeviceInfo *deviceInfoModel = (UDDeviceInfo *)device.getInfo;
    //        deviceInfoModel.realIsOwned = [uHomeUserId isEqualToString:deviceInfoModel.ownerId];
    //        Family *family = [self.userDomainProvider.provideUpUserDomain.user getFamilyById:device.familyId];
    //        family.floorInfos
    //        for (Room *room in family.rooms) {
    //            if ([room.roomId isEqualToString:device.roomId])
    //                deviceInfoModel.realRoom = room;
    //        }
    //        [device setDeviceInfo:deviceInfoModel];
    //    }
}


- (BOOL)onFailure:(id<UpUserDomainResult>)result
{
    [self.userDomainProvider.provideEventManager sendEvent:NOTIFY_REFRESH_FAILED];
    return [super onFailure:result];
}
- (BOOL)supportMultiCallback
{
    return YES;
}
@end
