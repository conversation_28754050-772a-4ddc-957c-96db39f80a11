//
//  UDRefreshTokenOp.m
//  upuserdomain
//
//  Created by 闫达 on 2020/7/28.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDRefreshTokenOp.h"
#import "UpUserDomainStore.h"
#import "UpEventHandlerManagerDelegate.h"
#import "UpUserDataSource.h"
#import "UpUserDomain.h"
#import "UpEvent.h"
#import <uplog/UPLog.h>
#import "ApplicationOauthData.h"
#import "ApplicationOauthData+PrivateExtension.h"
#import "UPTimeMillisDelegate.h"
NSString *const EXTRA_CODE_TOKEN_NOT_EXISTS = @"AT0001";
NSString *const EXTRA_INFO_TOKEN_NOT_EXISTS = @"访问令牌不存在！";
NSString *const EXTRA_CODE_ACCESS_TOKEN_CHECK_FAILED = @"AT0003";
NSString *const EXTRA_INFO_ACCESS_TOKEN_CHECK_FAILED = @"服务器返回AccessToken非法或失效！";
NSString *const EXTRA_CODE_REFRESH_TOKEN_CHECK_FAILED = @"AT0004";
NSString *const EXTRA_INFO_REFRESH_TOKEN_CHECK_FAILED = @"服务器返回RefreshToken非法！";
@interface UDRefreshTokenOp ()
@property (nonatomic, assign) BOOL isPlaned;
@end

@implementation UDRefreshTokenOp
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    NSString *token;
    if ([args isKindOfClass:NSDictionary.class]) {
        NSDictionary *dict = (NSDictionary *)args;
        token = dict[@"token"];
        self.isPlaned = [dict[@"isPlaned"] boolValue];
    }
    else {
        token = (NSString *)args;
    }
    if (self.isPlaned == NO) {
        self.userDomainProvider.provideUpUserDomainStore.loginState = UpUserDomainStateLogining;
    }
    [[self.userDomainProvider provideUserDataSource] refreshToken:token success:success failure:failure];
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    id<UDAuthDataDelegate> authData = result.retData;
    UpUserDomainStore *userDomainStore = [self.userDomainProvider provideUpUserDomainStore];
    id<UpEventHandlerManagerDelegate> eventManager = [self.userDomainProvider provideEventManager];
    if ([userDomainStore verifyOauthData:authData] == NO) {
        if (self.isPlaned) {
            [eventManager sendEvent:NOTIFY_PLANNED_REFRESH_TOKEN_FAILED];
        }
        else {
            [eventManager sendEvent:NOTIFY_TOKEN_INVALID];
        }
        [userDomainStore clearUserData];
        result.retCode = kUserDomainInvalid_token;
        result.retInfo = @"无效token";
        result.success = NO;
    }
    else {
        ApplicationOauthData *oauthData = result.retData;
        oauthData.realCreateTime = [NSDate dateWithTimeIntervalSince1970:self.userDomainProvider.providerTimeDelegate.currentTimeMillis];
        userDomainStore.oauthData = oauthData;
        [userDomainStore updateCacheOauthData:oauthData];
        [userDomainStore loadDataWithCache];
        if (self.isPlaned) {
            [eventManager sendEvent:NOTIFY_AUTH_DATA_REFRESHED_SUCCESS];
            [eventManager sendEvent:OPERATE_REFRESH_TOKEN_SCHEDULED_TASK];
        }
        else {
            [eventManager sendEvent:NOTIFY_AUTH_DATA_REFRESHED_SUCCESS];
            [eventManager sendEvent:OPERATE_REFRESH_TOKEN_SCHEDULED_TASK];
            [eventManager sendEvent:OPERATE_REFRESH_USER];
        }
    }
    return [super onSuccess:result];
}
- (BOOL)onFailure:(id<UpUserDomainResult>)result
{
    UpUserDomainStore *userDomainStore = [self.userDomainProvider provideUpUserDomainStore];
    id<UpEventHandlerManagerDelegate> eventManager = [self.userDomainProvider provideEventManager];
    if ([@"43005" isEqualToString:result.retCode] || [kUserDomainInvalid_grant isEqualToString:result.retCode] || [kUserDomainInvalid_token isEqualToString:result.retCode]) {
        [eventManager sendEvent:NOTIFY_LOG_OUT];
        [userDomainStore clearUserData];
        [eventManager sendEvent:NOTIFY_TOKEN_INVALID];
    }
    else {
        result = [self handleResult:result];
    }
    return [super onFailure:result];
}
- (BOOL)supportMultiCallback
{
    return YES;
}
- (id<UpUserDomainResult>)handleResult:(id<UpUserDomainResult>)result
{
    UpUserDomainStore *userDomainStore = [self.userDomainProvider provideUpUserDomainStore];
    id<UpEventHandlerManagerDelegate> eventManager = [self.userDomainProvider provideEventManager];
    NSTimeInterval times = [userDomainStore caculateOauthDataExpire];
    if (times > 0) {
        if (self.isPlaned) {
            [eventManager sendEvent:NOTIFY_AUTH_DATA_REFRESHED_SUCCESS];
            [eventManager sendEvent:OPERATE_REFRESH_TOKEN_SCHEDULED_TASK];
        }
        else {
            [eventManager sendEvent:NOTIFY_AUTH_DATA_REFRESHED_SUCCESS];
            [eventManager sendEvent:OPERATE_REFRESH_TOKEN_SCHEDULED_TASK];
            [eventManager sendEvent:OPERATE_REFRESH_USER];
        }
        [userDomainStore loadDataWithCache];
        userDomainStore.loginState = UpUserDomainStateDidLogin;
        result.success = YES;
        result.retData = userDomainStore.oauthData;
    }
    else {
        [eventManager sendEvent:NOTIFY_AUTH_DATA_REFRESHED_FAILED];
        [userDomainStore clearUserData];
    }
    return result;
}
@end
