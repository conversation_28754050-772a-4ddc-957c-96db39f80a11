//
//  UDOperator.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/26.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UserDomainSampleResult.h"
#import "UpUserDomainProvider.h"
#import "UpEventHandlerManagerDelegate.h"
#import "UpEvent.h"
#import "UpUserDomainStore.h"

NS_ASSUME_NONNULL_BEGIN
@interface UDOperator : NSObject
typedef UDOperator *_Nonnull (^CreateOpBlock)(void);
@property (nonatomic, copy, readonly) NSString *uuid;
@property (nonatomic, assign, readonly) NSInteger hashCode;
@property (nonatomic, strong, readonly) id<UpUserDomainProvider> userDomainProvider;
@property (nonatomic, strong, readonly) NSObject *args;
@property (nonatomic, assign) BOOL noNeedLogin;
- (void)appendSuccess:(userDomainCallback)success failure:(userDomainCallback)failure;
- (BOOL)canAppendCallback;
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure;
- (BOOL)equals:(NSObject *)objc;
- (void)dispose;
- (BOOL)onFailure:(id<UpUserDomainResult>)result;
- (BOOL)onSuccess:(id<UpUserDomainResult>)result;
- (void)operate;
- (void)setArgs:(NSObject *)operatorArgs;
- (BOOL)supportMultiCallback;
- (void)setProvider:(id<UpUserDomainProvider>)provider;
@end

NS_ASSUME_NONNULL_END
