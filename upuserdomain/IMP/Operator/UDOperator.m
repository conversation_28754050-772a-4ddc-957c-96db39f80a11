//
//  UDOperator.m
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/26.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UDOperator.h"
#import "UpUserDomain.h"
#import "UpUserDomainResult.h"
#import <uplog/UPLog.h>
#import "UDOperatorManager.h"
#import "UDRefreshTokenOp.h"
#import "ApplicationOauthData.h"
static NSInteger const ST_CREATED = 0;
static NSInteger const ST_FINISHED = 9;
static NSInteger const ST_RUNNING = 1;

@interface UDOperator ()
@property (atomic, assign) NSInteger status;
@property (nonatomic, strong) NSMutableArray<userDomainCallback> *successCallbacks;
@property (nonatomic, strong) NSMutableArray<userDomainCallback> *failureCallbacks;
@end
@implementation UDOperator
- (instancetype)init
{
    if (self = [super init]) {
        _uuid = [self uuidString];
        _status = ST_CREATED;
        _successCallbacks = [NSMutableArray array];
        _failureCallbacks = [NSMutableArray array];
    }
    return self;
}
- (void)setProvider:(id<UpUserDomainProvider>)provider
{
    _userDomainProvider = provider;
}
- (void)appendSuccess:(userDomainCallback)success failure:(userDomainCallback)failure
{
    if (success) {
        [self.successCallbacks addObject:success];
    }

    if (failure) {
        [self.failureCallbacks addObject:failure];
    }
}

- (BOOL)canAppendCallback
{
    if (_status == ST_FINISHED) {
        return NO;
    }
    if (self.supportMultiCallback) {
        return YES;
    }
    return self.successCallbacks.count == 0 && self.failureCallbacks.count == 0;
}

- (BOOL)equals:(NSObject *)objc
{
    return NO;
}

- (void)dispose
{
}
- (BOOL)onFailure:(id<UpUserDomainResult>)result
{
    if ([kUserDomainInvalid_token isEqualToString:result.retCode] && ![NSStringFromClass(self.class) isEqualToString:@"UDLogoutOp"] && ![NSStringFromClass(self.class) isEqualToString:@"UDRefreshTokenOp"]) {
        [self.userDomainProvider.provideOperatorManager operateWithClass:[UDRefreshTokenOp class]
            args:self.userDomainProvider.provideUpUserDomainStore.oauthData.refresh_token
            success:^(UserDomainSampleResult *_Nonnull result) {
              [self operateArgs:self.args
                  success:^(UserDomainSampleResult *_Nonnull result) {
                    [self handleSuccess:result];
                  }
                  failure:^(UserDomainSampleResult *_Nonnull result) {
                    [self handleFailure:result];
                  }];
            }
            failure:^(UserDomainSampleResult *_Nonnull result){

            }];
        return YES;
    }
    if ([result.retCode isEqualToString:@"21016"]) {
        [self.userDomainProvider.provideEventManager sendEvent:NOTIFY_TOKEN_MISMATCHDEVICE];
        [self.userDomainProvider.provideUpUserDomainStore clearUserData];
    }
    return NO;
}

- (BOOL)onSuccess:(id<UpUserDomainResult>)result
{
    return NO;
}

- (void)operate
{
    if (self.status == ST_CREATED) {
        self.status = ST_RUNNING;
        if ([self noNeedLogin] || [self checkState]) {
            [self operateArgs:self.args
                success:^(UserDomainSampleResult *_Nonnull result) {
                  [self handleSuccess:result];
                }
                failure:^(UserDomainSampleResult *_Nonnull result) {
                  [self handleFailure:result];
                }];
        }
        else {
            UPLogError(@"UPUserdomain", @"当前未登陆%s%d", __func__, __LINE__);
            [self handleFailure:[UserDomainSampleResult defaultFailureResult]];
        }
    }
    else {
        UPLogInfo(@"UPUserdomain", @"operate---%@正在执行", NSStringFromClass(self.class));
    }
}
- (void)operateArgs:(NSObject *)args success:(userDomainCallback)success failure:(userDomainCallback)failure
{
    UPLogInfo(@"UPUserdomain", @"%s%d", __func__, __LINE__);
}
- (void)setArgs:(NSObject *)args
{
    if (_status == ST_CREATED) {
        _args = args;
    }
}

- (BOOL)supportMultiCallback
{
    return NO;
}

- (BOOL)checkState
{
    return self.userDomainProvider.provideUpUserDomain.state == UpUserDomainStateDidLogin;
}

- (NSString *)uuidString
{
    return [NSUUID UUID].UUIDString;
}

- (void)handleSuccess:(id<UpUserDomainResult>)result
{
    if ([self onSuccess:result]) {
        return;
    }
    @synchronized(self.successCallbacks)
    {
        for (NSInteger i = 0; i < self.successCallbacks.count; i++) {
            self.successCallbacks[i](result);
        }
        [self.successCallbacks removeAllObjects];
    }
    self.status = ST_FINISHED;
    [[self.userDomainProvider provideOperatorManager] removeOperator:self];
}

- (void)handleFailure:(id<UpUserDomainResult>)result
{
    if ([self onFailure:result]) {
        return;
    }
    @synchronized(self.failureCallbacks)
    {
        for (NSInteger i = 0; i < self.failureCallbacks.count; i++) {
            self.failureCallbacks[i](result);
        }
        [self.failureCallbacks removeAllObjects];
    }
    self.status = ST_FINISHED;
    [[self.userDomainProvider provideOperatorManager] removeOperator:self];
}


@end
