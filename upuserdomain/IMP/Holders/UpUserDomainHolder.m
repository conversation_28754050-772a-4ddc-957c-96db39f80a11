//
//  UpUserDomainHolder.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/3/13.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UpUserDomainHolder.h"
#import "UserDataSource.h"
#import "DeviceListDataSource.h"
#import "FamilyDataSource.h"
#import "UDCacheIMP.h"
#import "UDTimeIMP.h"
#import <uplog/UPLog.h>
#import "SEUserDataSource.h"
#import "SEFamilyDataSource.h"
#import "SEDeviceListDataSource.h"

@interface UpUserDomainHolder ()
@property (nonatomic, strong) UpUserDomain *realUserDomain;
@end

@implementation UpUserDomainHolder
#pragma mark - Property Methods
- (UpUserDomain *)userDomain
{
    return self.realUserDomain;
}

#pragma mark - Public Methods
+ (instancetype)instance
{
    static UpUserDomainHolder *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[[self class] alloc] init];
    });
    return instance;
}

- (void)initializeUserDomainForHomeland
{
    id<UpUserDataSource> userDataSource = [UserDataSource new];
    id<UpDeviceListDataSource> deviceDataSource = [DeviceListDataSource new];
    id<UpFamilyDataSource> familyDataSource = [FamilyDataSource new];
    id<UDCache> cache = [UDCacheIMP new];
    id<UPTimeMillisDelegate> time = [[UDTimeIMP alloc] init];
    self.realUserDomain = [[UpUserDomain alloc] initDomain:userDataSource familyDataSource:familyDataSource deviceDataSource:deviceDataSource cache:cache time:time];
    [self.realUserDomain.getSettings setUserDomainPlatform:UPUserDomainPlatformHomeland];
}

- (void)initializeUserDomainForHomelandSYN
{
    id<UpUserDataSource> userDataSource = [UserDataSource new];
    id<UpDeviceListDataSource> deviceDataSource = [DeviceListDataSource new];
    id<UpFamilyDataSource> familyDataSource = [FamilyDataSource new];
    id<UDCache> cache = [UDCacheIMP new];
    id<UPTimeMillisDelegate> time = [[UDTimeIMP alloc] init];
    self.realUserDomain = [[UpUserDomain alloc] initDomain:userDataSource familyDataSource:familyDataSource deviceDataSource:deviceDataSource cache:cache time:time];
    [self.realUserDomain.getSettings setUserDomainPlatform:UPUserDomainPlatformHomelandSYN];
}

- (void)initializeUserDomainForSouthEastAsia
{
    id<UpUserDataSource> userDataSource = [SEUserDataSource new];
    id<UpDeviceListDataSource> deviceDataSource = [SEDeviceListDataSource new];
    id<UpFamilyDataSource> familyDataSource = [SEFamilyDataSource new];
    id<UDCache> cache = [UDCacheIMP new];
    id<UPTimeMillisDelegate> time = [[UDTimeIMP alloc] init];
    self.realUserDomain = [[UpUserDomain alloc] initDomain:userDataSource familyDataSource:familyDataSource deviceDataSource:deviceDataSource cache:cache time:time];
    [self.realUserDomain.getSettings setUserDomainPlatform:UPUserDomainPlatformSouthEastAsia];
}

- (void)initializeUserDomainWithMyOwnUserDataSource:(id<UpUserDataSource>)userDataSource deviceDataSource:(id<UpDeviceListDataSource>)deviceDataSource familyDataSource:(id<UpFamilyDataSource>)familyDataSource platform:(UPUserDomainPlatform)platform
{
    if (![userDataSource conformsToProtocol:@protocol(UpUserDataSource)]) {
        UPLogError(@"UPUserDomain", @"%s[%d]传入的自有用户数据源不符合要求，UserDomain组件初始化失败，后续功能将出现异常，请对比检查入参及其接口说明！", __PRETTY_FUNCTION__, __LINE__);
    }
    if (![deviceDataSource conformsToProtocol:@protocol(UpDeviceListDataSource)]) {
        UPLogError(@"UPUserDomain", @"%s[%d]传入的自有设备数据源不符合要求，UserDomain组件初始化失败，后续功能将出现异常，请对比检查入参及其接口说明！", __PRETTY_FUNCTION__, __LINE__);
    }
    if (![familyDataSource conformsToProtocol:@protocol(UpFamilyDataSource)]) {
        UPLogError(@"UPUserDomain", @"%s[%d]传入的自有家庭数据源不符合要求，UserDomain组件初始化失败，后续功能将出现异常，请对比检查入参及其接口说明！", __PRETTY_FUNCTION__, __LINE__);
    }

    id<UDCache> cache = [UDCacheIMP new];
    id<UPTimeMillisDelegate> time = [[UDTimeIMP alloc] init];
    self.realUserDomain = [[UpUserDomain alloc] initDomain:userDataSource familyDataSource:familyDataSource deviceDataSource:deviceDataSource cache:cache time:time];
    [self.realUserDomain.getSettings setUserDomainPlatform:platform];
}

@end
