//
//  UpUserDomainHolder.h
//  upuserdomain
//
//  Created by 振兴郑 on 2019/3/13.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpUserDataSource.h"
#import "UpDeviceListDataSource.h"
#import "UpFamilyDataSource.h"
#import "UpUserDomain.h"
#import "UDFamilyDelegate.h"
#import "UDFamilyInfoDelegate.h"
#import "UDFamilyLocationDelegate.h"
#import "UDFamilyMemberDelegate.h"
#import "UDMemberInfoDelegate.h"
#import "UDRoomDelegate.h"
#import "UDAddressDelegate.h"
#import "UDLastLoginInfoDelegate.h"
#import "UDUserDelegate.h"
#import "UDUserAddressDelegate.h"
#import "UDUserInfoDelegate.h"
#import "UDUserTermDelegate.h"
#import "UDUserLoginLogInfoDelegate.h"
#import "UDDeviceDelegate.h"
#import "UDDeviceAuthDelegate.h"
#import "UDDeviceInfoDelegate.h"
#import "UDDeviceOwnerInfoDelegate.h"
#import "UDDevicePermissionDelegate.h"
#import "UDAuthDataDelegate.h"
#import "UpUserDomainResult.h"
#import "UpUserDomainDelegate.h"
#import "UpUserDomainObserver.h"
#import "UPUserDomainSettings.h"
#import "UserAddressArgs.h"
#import "UserInfoArgs.h"
#import "UserQRLoginStage.h"
#import "CreateFamilyArgs.h"
#import "RoomArgs.h"
#import "FamilyArgs.h"
#import "Family.h"
#import "Device.h"
#import "User.h"
#import "ApplicationOauthData.h"
#import "BatchProcessDeviceResult.h"
#import "UDFloorInfo.h"
#import "UDFloorInfoDelegate.h"
#import "FloorArg.h"
#import "VirtualMemberArgs.h"

NS_ASSUME_NONNULL_BEGIN
/**
 用户基础信息组件实例持有者（单例子），有默认的userdomain，如果需要定制userdomain，调用initializeUserDomainWithUserDataSource接口进行初始化
 */
@interface UpUserDomainHolder : NSObject

/**
 * 用户基础信息组件实例(UpUserDomain)
 */
@property (nonatomic, strong, readonly) UpUserDomain *userDomain;

/**
 单例

 @return （UpUserDomainHolder）
 */
+ (instancetype)instance;

/**
 * @brief 初始化国内版本的UserDomain。
 * @since 3.0.0
 * @warning 该方法会初始化国内版本的UserDomain相关数据源及服务器请求源。请勿他用。
 */
- (void)initializeUserDomainForHomeland;

/**
 * @brief 初始化国内三翼鸟版本的UserDomain。
 * @since 3.0.0
 * @warning 该方法会初始化国内三翼鸟版本的UserDomain相关数据源及服务器请求源。请勿他用。
 */
- (void)initializeUserDomainForHomelandSYN;

/**
 * @brief 初始化东南亚版本的UserDomain。
 * @since 3.0.0
 * @warning 该方法会初始化东南亚版本的UserDomain相关数据源及服务器请求源。请勿他用。
 */
- (void)initializeUserDomainForSouthEastAsia;

/**
 * @brief 初始化自有数据版本的UserDomain。
 * @param userDataSource 自有的用户数据源接口协议实现对象，不能为nil。
 * @param deviceDataSource 自有的设备数据源接口协议实现对象，不能为nil。
 * @param familyDataSource 自有的家庭数据源接口协议实现对象，不能为nil。
 * @param platform 运行平台
 * @since 3.0.0
 * @warning 该方法会根据传入的自有数据源相关对象。请在清楚并了解的基础下使用，切勿乱用。若入参不符合接口注释所示要求，则UserDomain初始化失败，相关功能无法保证。
 */
- (void)initializeUserDomainWithMyOwnUserDataSource:(id<UpUserDataSource>)userDataSource deviceDataSource:(id<UpDeviceListDataSource>)deviceDataSource familyDataSource:(id<UpFamilyDataSource>)familyDataSource platform:(UPUserDomainPlatform)platform;
@end

NS_ASSUME_NONNULL_END
