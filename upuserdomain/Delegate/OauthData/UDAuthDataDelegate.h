//
//  UDAuthDataDelegate.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/7.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol UDAuthDataDelegate <NSObject>

/**
 * 用户中心个人token
 */
@property (nonatomic, copy, readonly) NSString *access_token;
/**
 * 用户中心access_token 过期剩余时间：秒
 */
@property (nonatomic, copy, readonly) NSString *expires_in;

/**
 * 用户中心刷新token
 */
@property (nonatomic, copy, readonly) NSString *refresh_token;

/**
 * 用户中心access_token作用域
 */
@property (nonatomic, copy, readonly) NSString *scope;

/**
 * 用户中心access_token 类型
 */
@property (nonatomic, copy, readonly) NSString *token_type;

/**
 * 云平台用户token
 */
@property (nonatomic, copy, readonly) NSString *uhome_access_token;

/**
 * 云平台用户ID
 */
@property (nonatomic, copy, readonly) NSString *uhome_user_id;

/**
 * 创建时间
 */
@property (nonatomic, strong, readonly) NSDate *createTime;

/**
 用户中心userID
 */
@property (nonatomic, copy, readonly) NSString *uc_user_id;
@end

NS_ASSUME_NONNULL_END
