//
//  UDPlanRefreshTokenTaskDelegate.h
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/16.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UserDomainSampleResult.h"
NS_ASSUME_NONNULL_BEGIN

@protocol UDPlanRefreshTokenTaskDelegate <NSObject>
@optional
/// 定时刷新token
/// @param success 成功回调
/// @param failure 失败回调
- (void)planRefreshTokenTask:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 进入前台时检查刷新token
- (void)applicationWillEnterForegroundCheckRefreshToken;
@end

NS_ASSUME_NONNULL_END
