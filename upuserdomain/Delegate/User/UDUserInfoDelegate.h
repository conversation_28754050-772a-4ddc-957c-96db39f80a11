//
//  UDUserInfoDelegate.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/8.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@protocol UDAddressDelegate;
NS_ASSUME_NONNULL_BEGIN

@protocol UDUserInfoDelegate <NSObject>
/**
 用户iD
 */
@property (nonatomic, copy, readonly) NSString *userId;

/**
 手机号
 */
@property (nonatomic, copy, readonly) NSString *mobile;

/**
 邮箱
 */
@property (nonatomic, copy, readonly) NSString *email;

/**
 用户名
 */
@property (nonatomic, copy, readonly) NSString *username;

/**
 真实姓名
 */
@property (nonatomic, copy, readonly) NSString *givenName;

/**
 昵称
 */
@property (nonatomic, copy, readonly) NSString *nickname;

/**
 家庭数量
 */
@property (nonatomic, copy, readonly) NSString *familyNum;

/**
 性别
 */
@property (nonatomic, copy, readonly) NSString *gender;

/**
 婚姻状况
 */
@property (nonatomic, copy, readonly) NSString *marriage;

/**
 生日
 */
@property (nonatomic, copy, readonly) NSString *birthday;

/**
 学历
 */
@property (nonatomic, copy, readonly) NSString *education;

/**
 头像
 */
@property (nonatomic, copy, readonly) NSString *avatarUrl;

/**
 备用手机号
 */
@property (nonatomic, copy, readonly) NSString *extraPhone;

/**
 收入状况
 */
@property (nonatomic, copy, readonly) NSString *income;

/**
 身高
 */
@property (nonatomic, copy, readonly) NSString *height;

/**
 体重
 */
@property (nonatomic, copy, readonly) NSString *weight;
/**
 国家代码
*/
@property (nonatomic, copy, readonly) NSString *countryCode;
/**
 国家隐私代码
*/
@property (nonatomic, copy, readonly) NSString *privacyCountryCode;

/**
 地址列表
 @see UserAddressInfo
 */
@property (strong, nonatomic, readonly) NSArray<id<UDAddressDelegate>> *addresses;

/**
 默认地址
 @see UserAddressInfo
 */
@property (strong, nonatomic, readonly) id<UDAddressDelegate> defaultAddress;

/**
个性签名
*/
@property (copy, nonatomic, readonly) NSString *signature;

/**
 注册来源
 */
@property (copy, nonatomic, readonly) NSString *regClientId;


@end

NS_ASSUME_NONNULL_END
