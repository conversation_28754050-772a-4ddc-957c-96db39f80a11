//
//  UDUserAddressDelegate.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京)有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol UDUserAddressDelegate <NSObject>
/**
 城市
 */
@property (nonatomic, copy, readonly) NSString *city;

/**
 城市编码
 */
@property (nonatomic, copy, readonly) NSString *city_id;

/**
 国家编码
 */
@property (nonatomic, copy, readonly) NSString *country_code;

/**
 区域
 */
@property (nonatomic, copy, readonly) NSString *district;

/**
 区域编码
 */
@property (nonatomic, copy, readonly) NSString *district_id;
/**
地址行1
*/
@property (nonatomic, copy, readonly) NSString *line1;
/**
地址行2
*/
@property (nonatomic, copy, readonly) NSString *line2;

/**
 邮编
 */
@property (nonatomic, copy, readonly) NSString *postcode;

/**
 省
 */
@property (nonatomic, copy, readonly) NSString *province;

/**
 省编码
 */
@property (nonatomic, copy, readonly) NSString *province_id;

/**
 乡镇、街道
 */
@property (nonatomic, copy, readonly) NSString *town;

/**
 乡镇街道编码
 */
@property (nonatomic, copy, readonly) NSString *town_id;
@end

NS_ASSUME_NONNULL_END
