//
//  UDAddressDelegate.h
//  UserDomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/23.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@protocol UDUserAddressDelegate;
NS_ASSUME_NONNULL_BEGIN

@protocol UDAddressDelegate <NSObject>
/**
 地址信息
 @see UserAddress
 */
@property (strong, nonatomic, readonly) id<UDUserAddressDelegate> address;

/**
 邮箱
 */
@property (copy, nonatomic, readonly) NSString *email;

/**
 地址编号
 */
@property (copy, nonatomic, readonly) NSString *addressId;

/**
 是否是默认地址
 */
@property (assign, nonatomic, readonly) NSInteger is_default;

/**
 是否有效
 */
@property (assign, nonatomic, readonly) NSInteger is_service;

/**
 手机号码
 */
@property (copy, nonatomic, readonly) NSString *receiver_mobile;

/**
 收货人姓名
 */
@property (copy, nonatomic, readonly) NSString *receiver_name;
/**
 来源
*/
@property (copy, nonatomic, readonly) NSString *source;

/**
 标签
 */
@property (copy, nonatomic, readonly) NSString *tag;

/**
 用户id
 */
@property (copy, nonatomic, readonly) NSString *user_id;
@end

NS_ASSUME_NONNULL_END
