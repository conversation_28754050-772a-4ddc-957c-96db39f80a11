//
//  UDUserDelegate.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/7.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "UserDomainSampleResult.h"
@protocol UDUserInfoDelegate
, UDFamilyDelegate, UDDeviceDelegate, UDUserTermDelegate;
@class CreateFamilyArgs, UserInfoArgs, UserAddressArgs, AggregationSwitchArgs;
NS_ASSUME_NONNULL_BEGIN

@protocol UDUserDelegate <NSObject>

/**
 取消扫码登陆

 @param uuid 扫码得到的uuid，必填
 @param success 成功回调
 @param failure 失败回调
 @see userDomainCallback
 */
- (void)qrCancleLogin:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 确认登陆，扫码成功后

 @param uuid 扫码得到的uuid，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)qrConfirmLogin:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure;


/**
 确认扫码登录
 @param uuid 扫码得到的uuid，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)qrConfirmScan:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 扫码登录状态

 @param uuid 扫码得到的uuid，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)pollqrCodeState:(NSString *)uuid success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 刷新用户：信息、家庭、设备列表
 @param callback 回调
 */
- (void)refreshUser:(userDomainCallback)callback;

/**
 刷新用户：信息、家庭、设备列表，传YES时从服务器请求数据，false时取本地数据，当为false时会根据缓存数据是否已经过期来判断
 若已经过期则从服务器请求数据
 @param callback 回调
 @param immediate 是否立即
 */
- (void)refreshUser:(BOOL)immediate callback:(userDomainCallback)callback;

/// 刷新用户基本信息，地址，登录终端信息
/// @param callback  刷新回调
- (void)refreshUserInfo:(userDomainCallback)callback;
/**
 创建新的收货地址

 @param userAddressArgs 地址信息(userAddressArgs)，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)createNewAddress:(UserAddressArgs *)userAddressArgs success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 创建家庭
 
 @param createFamilyArgs 创建家庭参数对象
 @param success 成功回调
 @param failure 失败回调
 */
- (void)createFamily:(CreateFamilyArgs *)createFamilyArgs success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 删除用户地址

 @param addressId 地址ID，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)deleteAddress:(NSString *)addressId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 编辑用户地址
 @param userAddressArgs 地址信息(userAddressArgs)
 @param success 成功回调
 @param failure 失败回调
 */
- (void)editAddress:(UserAddressArgs *)userAddressArgs success:(userDomainCallback)success failure:(userDomainCallback)failure;


/// 通过过滤器获取指定的设备列表
/// @param deviceFilter block中过滤
- (NSArray<id<UDDeviceDelegate>> *)getDeviceList:(BOOL (^)(id<UDDeviceDelegate> device))deviceFilter;

/// 通过设备ID获取指定设备对象
/// @param deviceId 设备id
- (id<UDDeviceDelegate>)getDeviceById:(NSString *)deviceId;

/// 通过家庭ID获取指定家庭对象
/// @param familyId 家庭id
- (id<UDFamilyDelegate>)getFamilyById:(NSString *)familyId;
/**
  设备列表
*/
@property (strong, nonatomic, readonly) NSDictionary<NSString *, id<UDDeviceDelegate>> *devices;
/**
  家庭列表，返回值字典，key是家庭id,value是实现家庭协议的家庭对象
*/
@property (strong, nonatomic, readonly) NSDictionary<NSString *, id<UDFamilyDelegate>> *families;

/// 获取家庭列表，返回数组
- (NSArray<id<UDFamilyDelegate>> *)getFamilyList;

/// 获取用户详细信息
@property (strong, nonatomic, readonly) id<UDUserInfoDelegate> extraInfo;

/// 登录终端列表
@property (nonatomic, strong, readonly) NSArray<id<UDUserTermDelegate>> *terminals;

/// 获取优家的UserID
@property (nonatomic, copy, readonly) NSString *uHomeUserId;
/// 获取用户中心的UserID
@property (nonatomic, copy, readonly) NSString *user_center_userId;
/**
 获取当前家庭
*/
@property (nonatomic, strong, readonly) id<UDFamilyDelegate> currentFamily;
/**
 修改用户信息
 @param userInfo 用户信息
 @param success 成功回调
 @param failure 失败回调
 
 */
- (void)modifyUserInfo:(UserInfoArgs *)userInfo success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
查询用户电子工单（未登录可用）,内部自动获取应用级Token
@param phoneNumber 手机号码，必填
@param success 成功回调
@param failure 失败回调
*/
- (void)qrWorkOrderInfo:(NSString *)phoneNumber success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 分页查询用户登录日志
 @param pageNo 起始页，必填
 @param pageSize 单页数量，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)queryLoginLogs:(NSInteger)pageNo pageSize:(NSInteger)pageSize success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 刷新用户地址列表
/// @param success 成功回调
/// @param failure 失败回调
- (void)refreshAddressList:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 刷新设备列表
/// @param success 成功回调
/// @param failure 失败回调
- (void)refreshDeviceList:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 刷新家庭列表
/// @param success 成功回调
/// @param failure 失败回调
- (void)refreshFamilyList:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 刷新终端列表
/// @param success 成功回调
/// @param failure 失败回调
- (void)refreshTerminalList:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 回复家庭邀请，同意家庭邀请操作成功以后，订阅者会收到家庭列表更新通知
 @param code 邀请码，必填
 @param familyId 家庭ID，必填
 @param agree 是否同意，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)replyFamilyInvite:(NSString *)code familyId:(NSString *)familyId agree:(BOOL)agree success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 回复家庭邀请，同意家庭邀请操作成功以后，订阅者会收到家庭列表更新通知
 @param code 邀请码，必填
 @param familyId 家庭ID，必填
 @param memberName 成员名称
 @param agree 是否同意，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)replyFamilyInvite:(NSString *)code familyId:(NSString *)familyId memberName:(NSString *)memberName agree:(BOOL)agree success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 设置当前家庭，进行家庭切换的时候，要将当前浏览的家庭设置为当前家庭，下次登录的时候会自动将此家庭设置为当前家庭

 @param currentFamily Family
 */
- (void)setCurrentFamily:(id<UDFamilyDelegate>)currentFamily;

/**
 更新用户头像
 @param image 头像图片，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)updateAvatar:(UIImage *)image success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 管理员同意/拒绝用户加入家庭
/// @param applicationId 申请ID，必填
/// @param agree BOOL，必填
/// @param success 成功回调
/// @param failure 失败回调
- (void)replyJoinFamily:(NSString *)applicationId agree:(BOOL)agree success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 打开/关闭设备聚合开关
///
/// - Parameters:
///   - aggSwitch: 聚合开关模型
- (void)modifyAggregationSwitch:(AggregationSwitchArgs *)aggSwitch
                        success:(userDomainCallback)success
                        failure:(userDomainCallback)failure;

/**
 @param shareUuid  分享关系id，不可为空
 @param success 成功回调
 @param failure 失败回调
 */
- (void)confirmDeviceSharingRelation:(NSString *)shareUuid success:(userDomainCallback)success failure:(userDomainCallback)failure;
/**
 @param shareUuids  分享关系数组id，不可为空
 @param success 成功回调
 @param failure 失败回调
 */
- (void)cancelDeviceSharingRelation:(NSArray<NSString *> *)shareUuids success:(userDomainCallback)success failure:(userDomainCallback)failure;

@end

NS_ASSUME_NONNULL_END
