//
//  UpUserDomainProvider.h
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@protocol UpFamilyDataSource
, UpUserDataSource, UpDeviceListDataSource, UpEventHandlerManagerDelegate, UpUserDomainDelegate, UPTimeMillisDelegate;
@class UDOperatorManager, UpUserDomainStore;
NS_ASSUME_NONNULL_BEGIN

@protocol UpUserDomainProvider <NSObject>
- (id<UpEventHandlerManagerDelegate>)provideEventManager;
- (UDOperatorManager *)provideOperatorManager;
- (id<UpUserDomainDelegate>)provideUpUserDomain;
- (UpUserDomainStore *)provideUpUserDomainStore;
- (id<UpFamilyDataSource>)provideFamilyDataSource;
- (id<UpUserDataSource>)provideUserDataSource;
- (id<UpDeviceListDataSource>)provideDeviceDataSource;
- (id<UPTimeMillisDelegate>)providerTimeDelegate;
@end

NS_ASSUME_NONNULL_END
