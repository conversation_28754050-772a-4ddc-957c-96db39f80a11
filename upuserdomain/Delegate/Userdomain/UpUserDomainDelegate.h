//
//  UpUserDomainDelegate.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/7.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UserDomainSampleResult.h"
#import "UDPlanRefreshTokenTaskDelegate.h"
@protocol UpUserDomainObserver
, UDUserDelegate, UDAuthDataDelegate, UDLastLoginInfoDelegate, UpUserDomainListenerDelegate;

NS_ASSUME_NONNULL_BEGIN
/**
 * 用户基础信息组件登录状态
 */
typedef NS_ENUM(NSInteger, UpUserDomainState) {
    /**
     *未登录
     */
    UpUserDomainStateUnLogin = -1,
    /**
     *正在登录
     */
    UpUserDomainStateLogining = 0,
    /**
     *已登录
     */
    UpUserDomainStateDidLogin = 1,
    /**
     *正在登出
     */
    UpUserDomainStateLogingOut = 2

};
typedef enum : NSUInteger {
    //国内智家app
    UPUserDomainPlatformHomeland = 0,
    //东南亚
    UPUserDomainPlatformSouthEastAsia,
    //国内三翼鸟app
    UPUserDomainPlatformHomelandSYN,
    //国内商城app
    UPUserDomainPlatformHomelandShop,
    //未知
    UPUserDomainPlatformUnknow
} UPUserDomainPlatform;

@protocol UPUserDomainSettingsDelegate <NSObject>

/// 获取客户端id
- (NSString *)getClientId;

/// 客户端ID，不能为null
/// @param clientId 设置客户端ID
- (void)setClientId:(NSString *)clientId;
/// 获取请求HTTP接口失败时重试的延时，返回当前延时（秒）
- (NSInteger)getHttpRequestRetryDelay;

/// 设置请求HTTP接口失败时重试的延时
/// @param delay  延时（秒），需大于等于0，否则无效
- (void)setHttpRequestRetryDelay:(NSInteger)delay;

/// 设置平台
/// @param platform 平台值
- (void)setUserDomainPlatform:(UPUserDomainPlatform)platform;

/// 获取当前平台
- (UPUserDomainPlatform)getUserDomainPlatform;


/// 是否开启家庭列表刷新
/// @param enabled true - 开启，false - 关闭
- (void)setRefreshFamilyListEnable:(BOOL)enabled;

/// 获取是否开启家庭列表刷新
- (BOOL)isRefreshFamilyListEnable;

/// 是否开启设备列表刷新
/// @param enabled true - 开启，false - 关闭
- (void)setRefreshDeviceListEnable:(BOOL)enabled;

/// 获取是否开启设备列表刷新
- (BOOL)isRefreshDeviceListEnable;

@property (nonatomic, assign) NSTimeInterval planRefreshTokenTime;
@end

@protocol UpUserDomainDelegate <NSObject, UDPlanRefreshTokenTaskDelegate>

/// 自动刷新用户令牌，通过回调接口返回刷新结果 此接口支持多次调用
- (void)autoRefreshToken;

- (void)autoRefreshToken:(userDomainCallback)callback;

/// 获取用户鉴权信息 当内存引用的鉴权信息为null时，会尝试从本地缓存读取
- (id<UDAuthDataDelegate>)oauthData;


/// 获取用户的登录状态
- (UpUserDomainState)state;

/// 获取用户基础信息的设置对象
- (id<UPUserDomainSettingsDelegate>)getSettings;

/// 获取用户对象 此接口不会返回null，但在未登录状态，User对象中的数据不可用
- (id<UDUserDelegate>)user;

/// 是否登录
- (BOOL)isLogin NS_DEPRECATED(1.1.3, 1.1.3, 1.1.3, 1.1.3, "请调用state 判断登录状态");

/// 获取数据刷新完成标识 当用户的详细信息，地址和登录终端列表，家庭列表，设备列表全部刷新完时返回true
- (BOOL)isRefreshCompleted;

/// 获取设备列表数据刷新完成标识 当用户的设备列表全部刷新完时返回true
- (BOOL)isRefreshDeviceListCompleted;

/// 获取家庭列表数据刷新完成标识 当用户的家庭列表刷新完时返回true
- (BOOL)isRefreshFamilyListCompleted;

/// 获取用户详细数据刷新完成标识 当用户的详细信息，地址和登录终端列表刷新完时返回true
- (BOOL)isRefreshUserCompleted;

/// 用户登出 调用服务端登出接口，无论接口返回成功还是失败，均清除内存和本地缓存
- (void)logOut:(userDomainCallback)callback;

/// 刷新用户信息，包括用户的详细信息，家庭列表和设备信息
/// @param callback 回调
- (void)refreshUser:(userDomainCallback)callback;

/**
 刷新用户：信息、家庭、设备列表，传YES时从服务器请求数据，false时取本地数据，当为false时会根据缓存数据是否已经过期来判断
 若已经过期则从服务器请求数据
 @param callback 回调
 @param immediate 是否立即
 */
- (void)refreshUser:(BOOL)immediate callback:(userDomainCallback)callback;

/**
 添加组件订阅者，订阅基础组件的各种事件

 @param observer (UpUserDomainObserver)，必填
 */
- (void)addObserver:(id<UpUserDomainObserver>)observer;

/**
 移除订阅者

 @param observer (UpUserDomainObserver)，必填
 */
- (void)removeObserver:(id<UpUserDomainObserver>)observer;

/**
 更新用户鉴权信息,更新成功以后组件会缓存用户鉴权信息，在下次应用启动时调用autoRefreshToken可实现自动登录

 @param access_token 用户中心个人级别token，必填
 @param refreshToken 用户中心刷新token，用于刷新个人token，必填
 @param uhome_access_token 云平台个人token，必填
 @param expires_in 用户中心个人token有效期，单位秒，必填
 @param scope 用户中心个人token作用域，必填
 @param token_type 用户中心个人token类型，必填
 @param uhome_user_id 云平台用户id，必填
 @param uc_user_id 用户中心userId
 @return 是否更新鉴权信息成功
 */
- (BOOL)updateOauthData:(NSString *)access_token refreshToken:(NSString *)refreshToken uhome_access_token:(NSString *)uhome_access_token expires_in:(NSString *)expires_in scope:(NSString *)scope token_type:(NSString *)token_type uhome_user_id:(NSString *)uhome_user_id uc_user_id:(NSString *)uc_user_id;

//
// 从老版本（未使用用户基础信息组件）更新用户鉴权信息，实现自动登录
//
// @param access_token 用户中心个人级别token，必填
// @param refreshToken 用户中心刷新token，用于刷新个人token，必填
// @param uhome_access_token 云平台个人token，必填
// @param expires_in 用户中心个人token有效期，单位秒，必填
// @param scope 用户中心个人token作用域，必填
// @param token_type 用户中心个人token类型，必填
// @param uhome_user_id 云平台用户id，必填
// @return 是否更新鉴权信息成功

- (BOOL)updateOauthDataWithCache:(NSString *)access_token refreshToken:(NSString *)refreshToken uhome_access_token:(NSString *)uhome_access_token expires_in:(NSString *)expires_in scope:(NSString *)scope token_type:(NSString *)token_type uhome_user_id:(NSString *)uhome_user_id;
/**
 查询用户电子工单（未登录可用）,内部自动获取应用级Token

 @param phoneNumber 手机号码，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)queryServiceOrder:(NSString *)phoneNumber success:(userDomainCallback)success failure:(userDomainCallback)failure;
/**
 取消登录
 */
- (void)cancleLogin;

@end

NS_ASSUME_NONNULL_END
