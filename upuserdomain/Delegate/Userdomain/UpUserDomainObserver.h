//
//  UpUserDomainObserver.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/8.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
@protocol UDUserDelegate
, UpUserDomainDelegate, UDAuthDataDelegate;
@protocol UpUserDomainObserver <NSObject>
@optional

/**
 获取缓存鉴权信息（鉴权信息未过期）oauthdata
 @param oauthData 鉴权信息对象
 */
- (void)onGetCacheTokenSuccess:(id<UDAuthDataDelegate>)oauthData;
/**
 刷新Token成功 oauthdata
  @param oauthData 鉴权信息对象
 */
- (void)onRefreshTokenSuccess:(id<UDAuthDataDelegate>)oauthData;

/**
 通过定时任务刷新Token成功
 @see ApplicationOauthData
 @since 1.0.0
 
 */
- (void)onPlannedRefreshTokenSuccess:(id<UDAuthDataDelegate>)oauthData;
/**
 通过定时任务刷新Token失败
 @since 1.0.0
 */
- (void)onPlannedRefreshTokenFailure;
/**
 RefreshToken 过期
 */
- (void)onInvalidRefreshToken;
/**
 刷新用户信息成功
 
 @param user user对象
 */
- (void)onRefeshUserSuccess:(id<UDUserDelegate>)user;
/**
 刷新用户信息失败
 @param user user对象
 */
- (void)onRefeshUserFaild:(id<UDUserDelegate>)user;
/**
 刷新家庭列表成功
 @param user user对象
 */
- (void)onRefreshFamilyListSuccess:(id<UDUserDelegate>)user;
/**
 刷新家庭列表失败
 @param user user对象
 */
- (void)onRefreshFamilyListFaild:(id<UDUserDelegate>)user;

/**
 刷新地址列表成功
 @param user user对象
 */
- (void)onRefreshAddressListSuccess:(id<UDUserDelegate>)user;
/**
 刷新地址列表失败
 @param user user对象
 */
- (void)onRefreshAddressListFaild:(id<UDUserDelegate>)user;

/**
 * @brief 用户所有家庭列表的详细信息刷新完成。
 *  @param user user对象
 */
- (void)onRefreshFamilyDetailSuccess:(id<UDUserDelegate>)user;

/**
 * @brief 用户所有家庭列表的详细信息刷新失败。
 *  @param user user对象
 */
- (void)onRefreshFamilyDetailFailure:(id<UDUserDelegate>)user;

/**
 刷新设备列表成功
 @param user user对象
 */
- (void)onRefreshDeviceListSuccess:(id<UDUserDelegate>)user;
/**
 刷新设备列表失败
 @param user user对象
 */
- (void)onRefreshDeviceListFaild:(id<UDUserDelegate>)user;

/**
 刷新设备列表缓存
 @param user user对象
 */
- (void)onRefreshDeviceListCache:(id<UDUserDelegate>)user;

/**
 刷新登录终端成功
 @param user user对象
 */
- (void)onRefreshLoginTerminalSuccess:(id<UDUserDelegate>)user;
/**
 刷新设备登录终端失败
 @param user user对象
 */
- (void)onRefreshLoginTerminalFaild:(id<UDUserDelegate>)user;
/**
用户刷新完成
 @param user user对象
 */
- (void)onRefreshComplete:(id<UDUserDelegate>)user;
/**
 用户注销
 @param userDomain userdomain对象
 */
- (void)onLogOut:(id<UpUserDomainDelegate>)userDomain;

/**
 用户将要注销
 @param userDomain userdomain对象
 */
- (void)onWillLogOut:(id<UpUserDomainDelegate>)userDomain;
/**
 当前家庭发生变化
 @param user 用户

 */
- (void)onCurrentFamilyDidChanged:(id<UDUserDelegate>)user;

/**
 扫码授权登录成功

 @param user 用户

 */
- (void)onQrScanLoginSuccess:(id<UDUserDelegate>)user;

/**
 扫码授权登录失败
 
 @param user 用户

 */
- (void)onQrScanLoginFailure:(id<UDUserDelegate>)user;

/**
 取消扫码授权登录成功
 
 @param user 用户

 */
- (void)onCancleQrScanLoginSuccess:(id<UDUserDelegate>)user;

/**
 取消扫码授权登录失败
 
 @param user 用户

 */
- (void)onCancleQrScanLoginFailure:(id<UDUserDelegate>)user;

/**
 取消登录--打开登录页面 未登录 点击关闭按钮时触发-
 */
- (void)onCancleLogin;

/**
 * @brief 用户在其他地方登录事件
 */
- (void)onLogInElsewhere;

/// token与设备不匹配
- (void)onTokenMismatchDevice;
/**
   当前家庭房间列表发生变化
 */
- (void)onCurrentFamilyRoomListChanged:(id<UDUserDelegate>)user;
@end

NS_ASSUME_NONNULL_END
