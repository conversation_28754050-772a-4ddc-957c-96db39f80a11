//
//  UDFamilyDelegate.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/8.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UserDomainSampleResult.h"

@class RoomArgs, RoomNewArgs, FamilyArgs, FloorArg, VirtualMemberArgs, DeviceCardStatusArgs, DeviceCardAggregationArgs, AdminInviteMemberArgs;

@protocol UDRoomDelegate
, UDDeviceDelegate, UDFamilyInfoDelegate, UDFamilyMemberDelegate, UDMemberInfoDelegate, UDFloorInfoDelegate;

NS_ASSUME_NONNULL_BEGIN

@protocol UDFamilyDelegate <NSObject>
/**
 家庭管理员删除家庭成员，成功后组件订阅者会收到家庭列表更新回调

 @param userId userId，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)adminDeleteMember:(NSString *)userId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 家庭管理员邀请新成员

 @param args 邀请参数对象, 必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)adminInvitateMember:(AdminInviteMemberArgs *)args success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 更换家庭管理员，成功以后订阅者会收到家庭列表更新的回调
 
 @param userId 新管理员ID,必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)changeFamilyAdminUserId:(NSString *)userId success:(userDomainCallback)success failure:(userDomainCallback)failure;


/**
 为家庭添加房间，成功以后组件订阅者会收到家庭列表更新的回调

 @param roomArgs 房间对象，必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)addRoom:(RoomArgs *)roomArgs success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 在楼层添加房间,成功后组件订阅者会收到家庭列表更新的回调
 
 @param roomArgs 房间参数对象, 必填(RoomNewArgs)
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)addRoomNew:(RoomNewArgs *)roomArgs success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 解散家庭，成功以后订阅者会收到家庭列表更新的回调
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)destoryFamilyAsAdminSuccess:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 删除家庭房间，成功以后组件订阅者会收到家庭列表更新的回调

 @param roomId 房间ID必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)removeRoom:(NSString *)roomId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 更新家庭信息，成功以后订阅者会收到家庭列表更新的回调
 
 @param familyArgs 家庭参数
 @param success 成功回调
 @param failure 失败回调
 */
- (void)updateFamilyInfo:(FamilyArgs *)familyArgs success:(userDomainCallback)success failure:(userDomainCallback)failure;
/**
 更新房间名称

 @param roomName 新的房间名称，必填
 @param roomId 房间ID，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)updateRoomName:(NSString *)roomName roomId:(NSString *)roomId success:(userDomainCallback)success failure:(userDomainCallback)failure;
/**
 管理员退出家庭，成功以后订阅者会收到家庭列表和设备列表更新的回调
 
 @param userId 新管理员ID，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)exitFamilyAsAdmin:(NSString *)userId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 家庭成员退出家庭，成功以后订阅者会收到家庭列表更新的回调
 @param success 成功回调
 @param failure 失败回调
 */
- (void)exitFamilyAsMemberSuccess:(userDomainCallback)success failure:(userDomainCallback)failure;
/**
家庭id
*/
@property (nonatomic, copy, readonly) NSString *familyId;
/**
创建时间
*/
@property (nonatomic, copy, readonly) NSString *createTime;
/**
appId
*/
@property (nonatomic, copy, readonly) NSString *appId;

/// 家庭设备列表
@property (nonatomic, strong, readonly) NSDictionary<NSString *, id<UDDeviceDelegate>> *devices;

/// 获取此家庭的设备
@property (nonatomic, strong, readonly) NSArray<id<UDDeviceDelegate>> *getDeviceList;

/// 家庭信息
@property (nonatomic, strong, readonly) id<UDFamilyInfoDelegate> info;

/// 获取成员列表
@property (nonatomic, strong, readonly) NSArray<id<UDFamilyMemberDelegate>> *members;

/// 第一个成员
@property (nonatomic, strong, readonly) id<UDFamilyMemberDelegate> firstMember;

/// 家庭管理员信息
@property (nonatomic, strong, readonly) id<UDMemberInfoDelegate> owner;

/// 家庭管理员信息ID
@property (nonatomic, strong, readonly) NSString *ownerId;

/// 家庭楼层列表
@property (nonatomic, strong, readonly) NSArray<id<UDFloorInfoDelegate>> *floorInfos;
/**
 当前用户分享的设备
 */
@property (nonatomic, strong, readonly) NSDictionary<NSString *, id<UDDeviceDelegate>> *sharedDevices;

/// 是否默认家庭
@property (nonatomic, assign, readonly) BOOL defaultFamily;
/**
 家庭位置更改标志位
 */
@property (assign, nonatomic, readonly) BOOL locationChangeFlag;

/// 家庭设备数量，包括网器+非网器设备的数量
@property (nonatomic, copy, readonly) NSString *familyDeviceCount;

/// 当前用户在家庭中的角色类型,0-创建者,1-管理员,2-普通成员
@property (nonatomic, readonly) NSInteger memberType;

/// 当前用户加入该家庭的时间
@property (nonatomic, readonly) NSString *joinTime;

/**
 批量移动设备到新的房间，成功以后订阅者会收到家庭列表和设备列表更新的回调
 @param newRoom 新的房间
 @param devices 要移动的设备
 @param success 成功回调
 @param failure 失败回调
 */
- (void)moveDevicesToOtherRoom:(id<UDRoomDelegate>)newRoom devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 查询家庭详细信息

 @param success 成功回调
 @param failure 失败回调
 */
- (void)queryInfoSuccess:(userDomainCallback)success failure:(userDomainCallback)failure;

/*
 查询家庭房间列表
 @param 楼层id
 @param success 成功回调
 @param failure 失败回
 */
- (void)queryRoomList:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 家庭批量移除设备，成功以后订阅者会收到家庭列表和设备列表更新的回调

 @param devices 设备，必填
 @param success 成功回调
 @param failure 失败回调
 */
- (void)removeDevices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 设置默认家庭
/// @param defaultFamily YES是 NO否
- (void)setDefaultFamily:(BOOL)defaultFamily;

/**
家庭管理员家庭批量解绑设备，解绑成功后组件订阅者会收到家庭列表和设备列表的更新回调

@param devices 设备，必填
@param success 成功回调，非必填
@param failure 失败回调，非必填
 */
- (void)unBindDevices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 批量移动设备到新的家庭，成功以后订阅者会收到家庭列表和设备列表更新的回调
 
 @param newFamilyId 新的家庭ID，必填
 @param devices Device,必填
 @param success 成功回调，非必填
 @param failure 失败回调，非必填
 */
- (void)moveDevicesToOtherFamily:(NSString *)newFamilyId devices:(NSArray<id<UDDeviceDelegate>> *)devices success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 添加楼层
/// @param floorArg 楼层参数
/// @param success 成功回调
/// @param failure 失败回调
- (void)createFloor:(FloorArg *)floorArg success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 删除楼层
/// @param floorId 楼层id
/// @param success 成功回调
/// @param failure 失败回调
- (void)deleteFloor:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 编辑楼层
/// @param floorArg 楼层参数
/// @param success 成功回调
/// @param failure 失败回调
- (void)editFloor:(FloorArg *)floorArg success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
  查询第一个加入家庭的家庭成员
 @param success 成功回调
 @param failure 失败回调
 */
- (void)queryFirstMemeberSuccess:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
   虚拟用户加入家庭
 @param memberId 虚拟用户用户中心ID
 @param memberName 用户加入家庭附属参数,为用户在家庭中昵称
 @param success 成功回调
 @param failure 失败回调
 */
- (void)addVirtualMember:(NSString *)memberId memberName:(NSString *)memberName success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
  编辑虚拟用户
 @param virtualMemberArgs 虚拟成员对象
 @param success 成功回调
 @param failure 失败回调
 */
- (void)modifyVirtualMember:(VirtualMemberArgs *)virtualMemberArgs success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
  编辑家庭成员身份
 @param memberId 成员ID（用户中心userID）
 @param memberRole 成员身份
 @param success 成功回调
 @param failure 失败回调
 */
- (void)modifyMemberRole:(NSString *)memberId memberRole:(NSString *)memberRole success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
 编辑家庭成员角色(权限)类型
 @param memberId 成员ID（用户中心userID）
 @param memberType 角色(权限)类型:0-创建者,1-管理员,2-成员
 @param success 成功回调
 @param failure 失败回调
 */
- (void)modifyMemberType:(NSString *)memberId memberType:(NSInteger)memberType success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
  编辑家庭虚拟成员身份
 @param memberId 虚拟成员ID（IOT的userID）
 @param memberRole 成员身份
 @param success 成功回调
 @param failure 失败回调
 */
- (void)modifyVirtualMemberRole:(NSString *)memberId memberRole:(NSString *)memberRole success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
  保存房间顺序
 @param rooms 房间名称列表
 @param floorId 房间所属楼层ID
 @param success 成功回调
 @param failure 失败回调
 */
- (void)saveRoomsOrder:(NSArray<NSString *> *)rooms floorId:(NSString *)floorId success:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 保存房间顺序New
/// @param sortList 楼层+房间名称列表
///   [
///     {"floorId": "123", "rooms": ["1", "2", ...]},
///     {"floorId": "abc", "rooms": ["a", "b", ...]},
///   ]
/// @param success 成功回调
/// @param failure 失败回调
- (void)saveRoomsOrderNew:(NSArray<NSDictionary *> *)sortList success:(userDomainCallback)success failure:(userDomainCallback)failure;

/**
  获取组设备列表
 @param success 成功回调
 @param failure 失败回调
 */
- (void)getGroupDeviceListSuccess:(userDomainCallback)success failure:(userDomainCallback)failure;

/// 编辑设备卡片顺序和切换设备卡片大小状态。
///
/// - Parameters:
///   - deviceCardStatus: DeviceCardStatusArgs参数模型
///   - success: 成功回调
///   - failure: 失败回调
///
- (void)modifyDeviceCardStatus:(DeviceCardStatusArgs *)deviceCardStatus
                       success:(userDomainCallback)success
                       failure:(userDomainCallback)failure;

/// 设备聚合卡片操作
///
/// - Parameters:
///   - deviceCardAgg: 设备聚合卡片参数模型
///   - success: 成功回调
///   - failure: 失败回调
- (void)modifyDeviceAggregation:(DeviceCardAggregationArgs *)deviceCardAgg
                        success:(userDomainCallback)success
                        failure:(userDomainCallback)failure;

@end

NS_ASSUME_NONNULL_END
