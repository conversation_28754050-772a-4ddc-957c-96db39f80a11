//
//  FloorArg.h
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/3.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@protocol UDFloorInfoDelegate;
NS_ASSUME_NONNULL_BEGIN

@interface FloorArg : NSObject
/**
楼层id,编辑时--不可为空
 */
@property (nonatomic, copy, readonly) NSString *floorId;
/**
 楼层次序（-3到5，没有0）不可为空
 */
@property (nonatomic, copy) NSString *floorOrderId;
/**
楼层名称
*/
@property (nonatomic, copy) NSString *floorName;
/**
 楼层类型
 */
@property (nonatomic, copy) NSString *floorClass;
/**
楼层标签
 */
@property (nonatomic, copy) NSString *floorLabel;
/**
 楼层logo url
*/
@property (nonatomic, copy) NSString *floorLogo;
/**
楼层图片 url
*/
@property (nonatomic, copy) NSString *floorPicture;

- (instancetype)initFloorArg:(id<UDFloorInfoDelegate>)floorArg;
@end

NS_ASSUME_NONNULL_END
