//
//  UDMemberInfoDelegate.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol UDMemberInfoDelegate <NSObject>
/**
 用户id
 */
@property (nonatomic, copy, readonly) NSString *userId;

/**
 用户昵称
 */
@property (nonatomic, copy, readonly) NSString *name;

/**
 头像
 */
@property (nonatomic, copy, readonly) NSString *avatarUrl;

/**
 手机号
 */
@property (nonatomic, copy, readonly) NSString *mobile;

/**
 是否虚拟成员
 */
@property (nonatomic, assign, readonly) BOOL virtualUserFlag;

/**
 用户中心userId
 */
@property (nonatomic, copy, readonly) NSString *ucUserId;

/**
 宿主用户的IOT平台userId
 */
@property (nonatomic, copy, readonly) NSString *hostUserId;

/**
 用户生日
 */
@property (nonatomic, copy, readonly) NSString *birthday;

@end

NS_ASSUME_NONNULL_END
