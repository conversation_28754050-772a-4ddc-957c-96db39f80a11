//
//  FloorArg.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/3.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FloorArg.h"
#import "UDFloorInfoDelegate.h"
@implementation FloorArg
- (instancetype)initFloorArg:(id<UDFloorInfoDelegate>)floorArg
{
    if (self = [super init]) {
        _floorId = floorArg.floorId;
        _floorLogo = floorArg.floorLogo;
        _floorLabel = floorArg.floorLabel;
        _floorName = floorArg.floorName;
        _floorPicture = floorArg.floorPicture;
        _floorOrderId = floorArg.floorOrderId;
        _floorClass = floorArg.floorClass;
    }
    return self;
}
@end
