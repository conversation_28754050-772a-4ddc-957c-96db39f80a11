//
//  UDRoomDelegate.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol UDRoomDelegate <NSObject>
/**
 房间iD
 */
@property (nonatomic, copy, readonly) NSString *roomId;

/**
 房间名称
 */
@property (nonatomic, copy, readonly) NSString *roomName;

/**
 房间分类
 */
@property (nonatomic, copy, readonly) NSString *roomClass;

/**
 房间标签
 */
@property (nonatomic, copy, readonly) NSString *roomLabel;

/**
 房间logo
 */
@property (nonatomic, copy, readonly) NSString *roomLogo;

/**
 房间图片
 */
@property (nonatomic, copy, readonly) NSString *roomPicture;

/**
 房间排序码, 0, 1, 2,...
 */
@property (nonatomic, copy, readonly) NSString *sortCode;

@end

NS_ASSUME_NONNULL_END
