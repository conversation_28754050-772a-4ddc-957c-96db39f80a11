//
//  UDFamilyMemberDelegate.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@protocol UDMemberInfoDelegate;
NS_ASSUME_NONNULL_BEGIN

@protocol UDFamilyMemberDelegate <NSObject>
@property (nonatomic, strong, readonly) id<UDMemberInfoDelegate> memberInfo;

@property (nonatomic, copy, readonly) NSString *memberName;

@property (nonatomic, copy, readonly) NSString *familyId;

@property (nonatomic, copy, readonly) NSString *joinTime;

@property (nonatomic, assign, readonly) NSInteger shareDeviceCount;

@property (nonatomic, copy, readonly) NSString *memberRole;

/// 角色类型: 0-创建者, 1-管理员, 2-成员
@property (nonatomic, assign, readonly) NSInteger memberType;

@end

NS_ASSUME_NONNULL_END
