//
//  UpEventHandlerManagerDelegate.h
//  upuserdomain
//
//  Created by 闫达 on 2020/7/29.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN
@class UpEvent;
@protocol UpUserDomainListenerDelegate;
@protocol UpEventHandlerManagerDelegate <NSObject>
- (void)sendEvent:(NSString *)event;
- (void)registerListener:(id<UpUserDomainListenerDelegate>)listener;
- (void)unregisterListener:(id<UpUserDomainListenerDelegate>)listener;
- (NSArray<id<UpUserDomainListenerDelegate>> *)cloneUserDomainListeners;

@end

NS_ASSUME_NONNULL_END
