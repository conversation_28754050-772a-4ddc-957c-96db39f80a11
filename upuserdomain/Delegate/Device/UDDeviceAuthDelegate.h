//
//  UDDeviceAuthDelegate.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol UDDeviceAuthDelegate <NSObject>
/**
 * 控制
 */
/**
 *  权限信息(DevicePermission)
 */
@property (nonatomic, assign, readonly) BOOL control;
/**
 * 设置
 */
@property (nonatomic, assign, readonly) BOOL set;
/**
 * 查看
 */
@property (nonatomic, assign, readonly) BOOL view;

@end

NS_ASSUME_NONNULL_END
