//
//  UDDevicePermissionDelegate.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@protocol UDDeviceAuthDelegate;
NS_ASSUME_NONNULL_BEGIN

@protocol UDDevicePermissionDelegate <NSObject>
/**
 权限信息
 */
@property (nonatomic, strong, readonly) id<UDDeviceAuthDelegate> auth;

/**
 权限类型
 */
@property (nonatomic, copy, readonly) NSString *authType;
@end

NS_ASSUME_NONNULL_END
