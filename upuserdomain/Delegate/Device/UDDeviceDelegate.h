//
//  UDDeviceDelegate.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/8.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UserDomainSampleResult.h"
@protocol UDDeviceInfoDelegate
, UDDevicePermissionDelegate, UDDeviceOwnerInfoDelegate, UDRoomDelegate, UDShareDeviceCardInfoDelegate;
NS_ASSUME_NONNULL_BEGIN

@protocol UDDeviceDelegate <NSObject>
/**
 * 设备mac
 */
@property (nonatomic, copy, readonly) NSString *deviceId;

/**
 *  设备名称
 */
@property (nonatomic, copy, readonly) NSString *deviceName;

/**
 *  家庭下设备名称
 */
@property (nonatomic, copy, readonly) NSString *devName;

/**
 *  设备类型
 */
@property (nonatomic, copy, readonly) NSString *deviceType;

/**
 * 设备家庭id
 */
@property (nonatomic, copy, readonly) NSString *familyId;

/**
 *  设备拥有者ID
 */
@property (nonatomic, copy, readonly) NSString *ownerId;

/**
 *  权限信息(DevicePermission)
 */
@property (nonatomic, strong, readonly) id<UDDevicePermissionDelegate> permission;

/**
 *  typeId
 */
@property (nonatomic, copy, readonly) NSString *wifiType;
/**
设备绑定时间
*/
@property (nonatomic, copy, readonly) NSString *bindTime;

/**
 *  是否在线
 */
@property (nonatomic, assign, readonly) BOOL isOnline;

/**
 *  拥有者信息(DeviceOwnerInfo)
 */
@property (nonatomic, strong, readonly) id<UDDeviceOwnerInfoDelegate> ownerInfo;
/**
 子设备ID列表
 */
@property (nonatomic, strong, readonly) NSArray *subDevIds;
/**
 父设备ID
 */
@property (nonatomic, copy, readonly) NSString *parentId;
/**
 1 普通设备; 2 网关设备; 3 附件设备; 4 子设备;
 */
@property (nonatomic, copy, readonly) NSString *deviceRole;
/**
 主设备/子设备/无主从关系，0 无主设备，1 主设备，2 子设备
 */
@property (nonatomic, copy, readonly) NSString *deviceRoleType;

/**
 *  应用分类
 */
@property (nonatomic, copy, readonly) NSString *apptypeName;
/**
 *  应用分类
 */
@property (nonatomic, copy, readonly) NSString *apptypeCode;

/**
 *  一级应用分组
 */
@property (nonatomic, copy, readonly) NSString *categoryGrouping;
/**
 *  二级应用分组
 */
@property (nonatomic, copy, readonly) NSString *twoGroupingName;

/**
 *  机编码
 */
@property (nonatomic, copy, readonly) NSString *barcode;

/**
 *  绑定方式（SmartLink SmartAP SoftAP Scancode bt DirectLink Nolink BLE&SoftAP）
 */
@property (nonatomic, copy, readonly) NSString *bindType;

/**
 *  品牌
 */
@property (nonatomic, copy, readonly) NSString *brand;

/**
 *  实物图1
 */
@property (nonatomic, copy, readonly) NSString *imageAddr1;

/**
 *  实物图2，（备用字段，暂时无用）
 */
@property (nonatomic, copy, readonly) NSString *imageAddr2;

/**
 *  大卡图
 */
@property (nonatomic, copy, readonly) NSString *cardPageIcon;

/**
 *  小卡图
 */
@property (nonatomic, copy, readonly) NSString *cardPageImg;
/**
 小卡排序码
 */
@property (nonatomic, assign, readonly) NSInteger smallCardSort;

/**
 大卡排序码
 */
@property (nonatomic, assign, readonly) NSInteger largeCardSort;

/**
 卡片排序码
 */
@property (nonatomic, assign, readonly) NSInteger cardSort;
/**
 卡片状态 0:小卡 1:中卡 2:大卡
 */
@property (nonatomic, assign, readonly) NSInteger cardStatus;
/**
 聚合设备父节点id。灯聚合id:light_aggregation_id 窗帘聚合id:curtain_aggregation_id
 */
@property (nonatomic, copy, readonly) NSString *aggregationParentId;
/**
 1:支持灯光聚合 2:支持窗帘聚合 0： 不支持聚合。
 */
@property (nonatomic, assign, readonly) NSInteger supportAggregationFlag;
/**
 默认为null或空字符串，作为普通设备 灯光聚合为lightAgg 窗帘聚合为curtainAgg 非网器聚合id:"nonnetAgg 摄像头聚合id:"cameraAgg 长期离线聚合id:"offlineAgg 环境聚合id: "envAgg
 */
@property (nonatomic, copy, readonly) NSString *deviceAggregateType;

/**
 *  型号
 */
@property (nonatomic, copy, readonly) NSString *model;

/**
 *  产品编码
 */
@property (nonatomic, copy, readonly) NSString *prodNo;

/**
 *  房间名称
 */
@property (nonatomic, copy, readonly) NSString *roomName;

/**
 *  房间ID
 */
@property (nonatomic, copy, readonly) NSString *roomId;

/**
 *  房间(Room)
 */
@property (nonatomic, strong, readonly) id<UDRoomDelegate> room;

/**
 *  是否归当前用户所有
 */
@property (nonatomic, assign, readonly) BOOL isOwned;

/**
 联网类型
 13:WIFI, 14:SDK_LINUX, 15:SDK_RTOS,31:SDK_ANDROID,90:NB-IoT, 93:Wi-Fi&BLE, 98:DEVICE_CLOUD
 */
@property (nonatomic, copy, readonly) NSString *accessType;

/**
 app端是否支持绑定
 0 app端支持配网绑定；1 app端不支持配网绑定
 */
@property (nonatomic, copy, readonly) NSString *configType;
/**
 通讯协议
 */
@property (nonatomic, copy, readonly) NSString *comunicationMode;
/**
 楼层ID
 */
@property (nonatomic, copy, readonly) NSString *devFloorId;
/**
 在家庭中的楼层序列（-3到5，没有0）
 */
@property (nonatomic, copy, readonly) NSString *devFloorOrderId;
/**
 楼层名
 */
@property (nonatomic, copy, readonly) NSString *devFloorName;
/**
 应用分类图片
 */
@property (nonatomic, copy, readonly) NSString *apptypeIcon;
/**
 网器：netDevice，非网器 ：nonNetDevice，
 */
@property (nonatomic, copy, readonly) NSString *deviceNetType;
/**
 组设备ID
 */
@property (nonatomic, copy, readonly) NSString *deviceGroupId;
/**
 组设备类型
 */
@property (nonatomic, copy, readonly) NSString *deviceGroupType;
/**
 0:保活  1：非保活
 */
@property (nonatomic, assign, readonly) NSInteger noKeepAlive;

/**
 分享的设备信息数组
 */
@property (nonatomic, strong, readonly) NSArray<id<UDShareDeviceCardInfoDelegate>> *shareDeviceCardInfo;
/**
 *  是否共享设备 true：是 false:否
 */
@property (nonatomic, assign, readonly) BOOL sharedDeviceFlag;

/**
 *  是否可以共享
 */
@property (nonatomic, assign, readonly) BOOL supportShared;
/**
 *  附件设备排序码
 */
@property (nonatomic, assign, readonly) NSInteger attachmentSortCode;

/**
 *  首页展示状态：0:未设置过 1:首页展示 2:首页隐藏
 */
@property (nonatomic, assign, readonly) NSInteger displayedInHomePage;

/**
 按照品类是否支持   true：支持  false:不支持
 */
@property (nonatomic, assign, readonly) BOOL deviceShareSupportFlag;

/// 是否支持二次绑定。默认为true,高安全设备(部分摄像头、门锁等)之类的为false
@property (nonatomic, assign, readonly) BOOL reBind;

/// 获取设备详细信息
- (id<UDDeviceInfoDelegate>)getInfo;

/**
 更新设备名称，成功后组件订阅者会收到设备列表更新的回调
 @param name 设备名称
 @param success 成功回调
 @param failure 失败回调
 */
- (void)updateDeviceName:(NSString *)name success:(userDomainCallback)success failure:(userDomainCallback)failure NS_DEPRECATED(2.0, 2.0, 2.0, 2.0, "请调用Device中的 updateDeviceName:checkLevel:type:success:failure:");
/**
 更新房间，成功后组件订阅者会收到设备列表更新的回调
 
 @param name 房间名称
 @param success 成功回调
 @param failure 失败回调
 */
- (void)updateRoomName:(NSString *)name success:(userDomainCallback)success failure:(userDomainCallback)failure NS_DEPRECATED(2.0, 2.0, 2.0, 2.0, "请调用Family中的  moveDevicesToOtherRoom:");

/// 设备改名带名称校验功能
/// @param name 设备名称
/// @param checkLevel 是否开启二级验证
/// @param type 1:设备绑定成功 2:设备编辑管理
/// @param success 成功回调
/// @param failure 失败回调
- (void)updateDeviceName:(NSString *)name checkLevel:(BOOL)checkLevel type:(NSString *)type success:(userDomainCallback)success failure:(userDomainCallback)failure;
@end

NS_ASSUME_NONNULL_END
