//
//  UDDeviceShareDeviceCardInfo.h
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/3/11.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol UDShareDeviceCardInfoDelegate <NSObject>
/**
 * 家庭id
 */
@property (nonatomic, copy, readonly) NSString *familyId;

/**
 *  卡片排序码
 */
@property (nonatomic, assign, readonly) NSInteger cardSort;

/**
 *  卡片状态 0:小卡 1:中卡 2:大卡
 */
@property (nonatomic, assign, readonly) NSInteger cardStatus;
@end

NS_ASSUME_NONNULL_END
