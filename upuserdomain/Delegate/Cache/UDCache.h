//
//  UDCache.h
//  UPUserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/26.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol UDCache <NSObject>
/*
 归档对象

 @param object 符合NSCoding协议的可归档对象
 @param key 归档key
 @return 是否归档成功
 */
- (BOOL)putObject:(id<NSCoding>)object key:(NSString *)key;

/*
 获取归档对象

 @param key 归档key
 @return 归档对象
 */
- (id<NSCoding>)getObject:(NSString *)key;

/// 清空缓存
- (void)clear;

@end

NS_ASSUME_NONNULL_END
