//
//  UpUserDomainCacheDelegate.h
//  upuserdomain
//
//  Created by 闫达 on 2020/7/29.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
@protocol UDAuthDataDelegate
, UDUserInfoDelegate, UDFamilyDelegate, UDDeviceDelegate, UDAddressDelegate, UDUserTermDelegate, UDLastLoginInfoDelegate;
@protocol UpUserDomainCacheDelegate <NSObject>

/// 设置地址列表
/// @param addressList 地址列表
- (void)setAddressList:(NSArray<id<UDAddressDelegate>> *)addressList;
/**
 * 清除全部缓存
 */
- (void)clearAllData;
/**
 * 读取鉴权信息
 *
 * @return 鉴权信息，没有时返回null
 */
- (id<UDAuthDataDelegate>)getAuthData;

/**
 * 存储鉴权信息
 *
 * @param authData 鉴权信息，传null时为删除
 */
- (void)setAuthData:(id<UDAuthDataDelegate>)authData;

/**
 * 读取用户信息
 *
 * @return 用户信息，没有时返回null
 */
- (id<UDUserInfoDelegate>)getUserInfo;
/**
 * 存储用户信息
 *
 * @param userInfo 用户信息，传null时为删除
 */
- (void)setUserInfo:(id<UDUserInfoDelegate>)userInfo;

/**
 * 读取家庭信息列表
 *
 * @return 家庭信息列表，没有时返回null
 */
- (NSArray<id<UDFamilyDelegate>> *)getFamilyList;

/**
 * 存储家庭信息列表
 *
 * @param infoList 家庭信息列表，传null时为删除
 */
- (void)setFamilyList:(NSArray<id<UDFamilyDelegate>> *)infoList;
/**
 * 读取设备信息列表
 *
 * @return 设备信息列表，没有时返回null
 */
- (NSArray<id<UDDeviceDelegate>> *)getDeviceList;

/**
 * 存储设备信息列表
 *
 * @param infoList 设备信息列表，传null时为删除
 */
- (void)setDeviceList:(NSArray<id<UDDeviceDelegate>> *)infoList;
/**
 * 存储终端列表
 *
 * @param infoList 终端列表，传null时为删除
 */
- (void)setTerminalList:(NSArray<id<UDUserTermDelegate>> *)infoList;
/**
 * 读取终端列表
 *
 * @return 终端列表，没有时返回null
 */
- (NSArray<id<UDUserTermDelegate>> *)getTerminalList;

/**
 * 获取地址列表
 */
- (NSArray<id<UDAddressDelegate>> *)getAddressList;
@end

NS_ASSUME_NONNULL_END
