Pod::Spec.new do |s|
  s.name             = 'upuserdomain'
  s.version          = '3.20.2.**********'
  s.summary          = 'Uplus App User basic information library'
  s.homepage         = 'https://git.haier.net/uplus/ios/upuserdomain.git'
  s.license          = 'MIT'
  s.author           = { '<PERSON><PERSON><PERSON><PERSON>' => '<PERSON><PERSON><PERSON><PERSON>@haier.com','<PERSON>'=>'<EMAIL>' }
  s.source              = { :git => "https://git.haier.net/uplus/ios-kernel/upuserdomain.git", :tag => s.version.to_s }
  s.platform         = :ios, '12.0'
  s.requires_arc     = true
  s.frameworks       = 'Foundation'
  s.module_name      = 'UPUserDomain'
  s.subspec 'UserDomainAPIs' do |ss|
    ss.source_files = 'UserDomainAPIs/**/*.{h,m}'
  end
  s.subspec 'upuserdomain' do |ss|
    ss.source_files = 'upuserdomain/**/*.{h,m}'
  end
  s.subspec 'UserDomainDataSource' do |ss|
    ss.source_files = 'UserDomainDataSource/**/*.{h,m}'
  end
  s.resources           = ['doc/readme.md','doc/release.md']
  s.dependency 'upnetwork','>= 3.2.1'
  s.dependency 'MJExtension','3.2.1'
  s.dependency 'uplog','>= 1.1.8'
  
  s.pod_target_xcconfig = {
    'DEFINES_MODULE' => 'YES'
  }
end
