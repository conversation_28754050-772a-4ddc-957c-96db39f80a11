//
//  UserdomainHolderTest.m
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserdomainHolderTest.h"
#import "UDServerCacheManager.h"

#import "UPTimeMillisDelegate.h"

@implementation UserdomainHolderTest
+ (UserdomainHolderTest *)getInstance
{
    static UserdomainHolderTest *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[UserdomainHolderTest alloc] init];
    });
    return instance;
}

- (void)initializationUserDomain
{
    _userdomain = [[UpUserDomain alloc] initDomain:_userDateSource familyDataSource:_familyDataSource deviceDataSource:_deviceDataSource cache:_cache time:_timeDelegate];
    [_userdomain.getSettings setUserDomainPlatform:UPUserDomainPlatformHomeland];
    [[UDServerCacheManager getInstance] clearAllServerResponseCache];
}

@end
