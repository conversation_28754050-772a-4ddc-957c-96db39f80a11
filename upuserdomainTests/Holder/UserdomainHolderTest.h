//
//  UserdomainHolderTest.h
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpUserDomain.h"
#import "UpUserDomainStore.h"
#import "UDCache.h"
#import "UPTimeMillisDelegate.h"
NS_ASSUME_NONNULL_BEGIN

@interface UserdomainHolderTest : NSObject
@property (nonatomic, strong, readonly) UpUserDomain *userdomain;
@property (nonatomic, strong) id<UDCache> cache;
@property (nonatomic, strong) id<UPTimeMillisDelegate> timeDelegate;
@property (nonatomic, strong) id<UpUserDataSource> userDateSource;
@property (nonatomic, strong) id<UpFamilyDataSource> familyDataSource;
@property (nonatomic, strong) id<UpDeviceListDataSource> deviceDataSource;

+ (UserdomainHolderTest *)getInstance;
- (void)initializationUserDomain;
@end

NS_ASSUME_NONNULL_END
