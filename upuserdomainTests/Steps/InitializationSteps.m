//
//  InitializationSteps.m
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "InitializationSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "UserdomainHolderTest.h"
#import <OCMock/OCMock.h>
@implementation InitializationSteps
- (void)defineStepsAndHocks
{
    Given(@"缓存数据代理使用\"([^\"]*)\"", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *state = args[0];
      if ([state isEqualToString:@"模拟的"]) {
          [UserdomainHolderTest getInstance].cache = OCMProtocolMock(@protocol(UDCache));
      }
    });

    Given(@"^时间代理使用\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      if ([result isEqualToString:@"模拟的"]) {
          [UserdomainHolderTest getInstance].timeDelegate = OCMProtocolMock(@protocol(UPTimeMillisDelegate));
      }
    });

    Given(@"^用户数据源代理使用\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *userResult = args[0];
      if ([userResult isEqualToString:@"模拟的"]) {
          [UserdomainHolderTest getInstance].userDateSource = OCMProtocolMock(@protocol(UpUserDataSource));
      }
    });
    Given(@"^家庭数据源代理使用\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyResult = args[0];
      if ([familyResult isEqualToString:@"模拟的"]) {
          [UserdomainHolderTest getInstance].familyDataSource = OCMProtocolMock(@protocol(UpFamilyDataSource));
      }
    });
    Given(@"^设备数据源代理使用\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceResult = args[0];
      if ([deviceResult isEqualToString:@"模拟的"]) {
          [UserdomainHolderTest getInstance].deviceDataSource = OCMProtocolMock(@protocol(UpDeviceListDataSource));
      }
    });
    Given(@"^组件已正常初始化$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[UserdomainHolderTest getInstance] initializationUserDomain];
    });
}
@end
