//
//  UserdomainSteps.m
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/19.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserdomainSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "UserdomainHolderTest.h"
#import "ApplicationOauthData.h"
#import "StepsUtils.h"
#import "User.h"
#import "UPUserDomainSettings.h"
@interface UserdomainSteps ()
@property (nonatomic, assign) BOOL updateOauthDataResult;
@property (nonatomic, assign) BOOL logoutResult;
@property (nonatomic, assign) BOOL refreshUserResult;
@property (nonatomic, strong) NSMutableDictionary *mutiCallBackDic;

@end

@implementation UserdomainSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.updateOauthDataResult = NO;
      self.logoutResult = NO;
      self.refreshUserResult = NO;
      self.mutiCallBackDic = [NSMutableDictionary dictionary];

    });
    Given(@"刷新家庭列表配置为\"([^\"]*)\"", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[UserdomainHolderTest getInstance].userdomain.getSettings setRefreshFamilyListEnable:[args[0] isEqualToString:@"YES"]];
    });
    Given(@"刷新设备列表配置为\"([^\"]*)\"", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[UserdomainHolderTest getInstance].userdomain.getSettings setRefreshDeviceListEnable:[args[0] isEqualToString:@"YES"]];
    });
    When(@"^使用者\"([^\"]*)\"更新用户鉴权信息,参数为空对象$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.updateOauthDataResult = [[UserdomainHolderTest getInstance].userdomain updateOauthData:nil refreshToken:nil uhome_access_token:nil expires_in:nil scope:nil token_type:nil uhome_user_id:nil uc_user_id:nil];
    });
    Then(@"^使用者\"([^\"]*)\"收到更新用户鉴权信息接口的返回值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[1];
      CCIAssert(self.updateOauthDataResult == [result isEqualToString:@"YES"], @"更新用户鉴权信息接口的返回值与实际不一致");
    });
    When(@"^使用者\"([^\"]*)\"更新用户鉴权信息,参数为$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ApplicationOauthData *oauthData = getOauthDataFromExpectUserInfo(userInfo);
      self.updateOauthDataResult = [[UserdomainHolderTest getInstance].userdomain updateOauthData:oauthData.access_token refreshToken:oauthData.refresh_token uhome_access_token:oauthData.uhome_access_token expires_in:oauthData.expires_in scope:oauthData.scope token_type:oauthData.token_type uhome_user_id:oauthData.uhome_user_id uc_user_id:oauthData.uc_user_id];
    });
    Then(@"^使用者获取缓存的用户鉴权信息为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert([UserdomainHolderTest getInstance].userdomain.oauthData == nil, @"获取缓存的用户鉴权信息与实际不一致");
    });
    Then(@"^使用者获取缓存的用户鉴权信息为$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      ApplicationOauthData *oauthData = getOauthDataFromExpectUserInfo(userInfo);
      CCIAssert(isEqualAuthDataInfo([UserdomainHolderTest getInstance].userdomain.oauthData, oauthData), @"获取缓存的用户鉴权信息与实际不一致");
    });
    When(@"^使用者调用用户登出方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[UserdomainHolderTest getInstance]
              .userdomain logOut:^(UserDomainSampleResult *_Nonnull result) {
        self.logoutResult = result.success;
      }];
    });
    Then(@"^使用者收到用户登出接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.logoutResult == [result isEqualToString:@"成功"], @"用户登出接口的回调结果与实际不一致");
    });
    Then(@"^查询用户相关数据为空$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      CCIAssert([UserdomainHolderTest getInstance].userdomain.oauthData == nil, @"鉴权信息应该为空");
      CCIAssert([UserdomainHolderTest getInstance].userdomain.user.extraInfo == nil, @"用户信息应该为空");
      CCIAssert([UserdomainHolderTest getInstance].userdomain.user.getFamilyList == nil, @"家庭列表应该为空");
      CCIAssert([UserdomainHolderTest getInstance].userdomain.user.devices.allKeys.count == 0, @"设备列表应该为空");
    });
    Given(@"^设置登录状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *loginStateStr = args[0];
      UpUserDomainState state = UpUserDomainStateLogingOut;
      if ([loginStateStr isEqualToString:@"正在登出中"]) {
          state = UpUserDomainStateLogingOut;
      }
      else if ([loginStateStr isEqualToString:@"已登出"]) {
          state = UpUserDomainStateUnLogin;
      }
      else if ([loginStateStr isEqualToString:@"正在登录中"]) {
          state = UpUserDomainStateLogining;
      }
      [[UserdomainHolderTest getInstance].userdomain provideUpUserDomainStore].loginState = state;
    });
    When(@"^使用者\"([^\"]*)\"执行刷新用户方法,参数为:\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      BOOL immediate = [args[1] isEqualToString:@"YES"];
      [[UserdomainHolderTest getInstance]
              .userdomain refreshUser:immediate
                             callback:^(UserDomainSampleResult *_Nonnull result) {
                               self.refreshUserResult = result.success;
                             }];
    });
    Then(@"^使用者\"([^\"]*)\"收到刷新用户接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[1];
      CCIAssert(self.refreshUserResult == [result isEqualToString:@"成功"], @"收到刷新用户接口的回调结果与实际不一致");
    });
    When(@"^线程\"([^\"]*)\",执行刷新用户方法,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      BOOL immediate = [args[1] isEqualToString:@"YES"];
      dispatch_async(dispatch_get_global_queue(0, 0), ^{
        [[UserdomainHolderTest getInstance]
                .userdomain refreshUser:immediate
                               callback:^(UserDomainSampleResult *_Nonnull result) {
                                 self.mutiCallBackDic[user] = result;
                               }];
      });
    });
    Then(@"^线程\"([^\"]*)\",收到刷新用户接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      NSString *resultStr = args[1];
      UserDomainSampleResult *result = self.mutiCallBackDic[user];
      CCIAssert(result.success == [resultStr isEqualToString:@"成功"], @"执行刷新用户信息结果与实际不一致");
    });
    Then(@"^使用者获取刷新是否完成结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert([UserdomainHolderTest getInstance]
                        .userdomain.isRefreshCompleted == [result isEqualToString:@"YES"],
                @"获取刷新是否完成结果与预期不一致");
    });
    Then(@"^使用者获取刷新用户是否完成结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert([UserdomainHolderTest getInstance]
                        .userdomain.isRefreshUserCompleted == [result isEqualToString:@"YES"],
                @"获取刷新用户是否完成结果与预期不一致");
    });
    Then(@"^使用者获取家庭列表刷新是否完成结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert([UserdomainHolderTest getInstance]
                        .userdomain.isRefreshFamilyListCompleted == [result isEqualToString:@"YES"],
                @"获取家庭列表刷新是否完成结果与预期不一致");
    });
    Then(@"^使用者获取设备列表刷新是否完成结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert([UserdomainHolderTest getInstance]
                        .userdomain.isRefreshDeviceListCompleted == [result isEqualToString:@"YES"],
                @"获取设备列表刷新是否完成结果与预期不一致");
    });
    Then(@"^获取用户信息中的地址列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UserAddressInfo *> *addressList = getAddressListFromExpectUserInfo(userInfo);
      NSArray<id<UDAddressDelegate>> *userDomainUserAddressList = [UserdomainHolderTest getInstance].userdomain.user.extraInfo.addresses;
      CCIAssert(isEqualUserAddressList(addressList, (NSArray<UserAddressInfo *> *)userDomainUserAddressList), @"用户信息中的地址列表结果与实际不一致");
    });
    Then(@"^获取用户信息中的终端列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UserTerminal *> *terminalList = getTerminalListFromExpectUserInfo(userInfo);
      NSArray<id<UDUserTermDelegate>> *userDomainUserTerminalList = [UserdomainHolderTest getInstance].userdomain.user.terminals;
      CCISAssert(isEqualUserTerminalList(terminalList, (NSArray<UserTerminal *> *)userDomainUserTerminalList), @"用户信息中的终端列表结果与实际不一致");
    });
}
@end
