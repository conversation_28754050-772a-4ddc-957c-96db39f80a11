//
//  UserSteps.m
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UserSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "StepsUtils.h"
#import "UserdomainHolderTest.h"
#import "UpUserDataSource.h"
#import "User.h"
#import "CreateFamilyArgs.h"
#import "UserAddressArgs.h"
#import "UserInfoArgs.h"
#import "Family+PrivateExtension.h"
#import "UserInfo+PrivateExtension.h"
#import "UserAddressInfo+PrivateExtension.h"
#import "UserAddress.h"
#import "UserAddress+PrivateExtension.h"
#import <OCMock/OCMock.h>

#define UserDataSource [UserdomainHolderTest getInstance].userdomain.provideUserDataSource

@interface UserSteps ()
@property (nonatomic, assign) BOOL createAddressResult;
@property (nonatomic, assign) BOOL deleteAddressResult;
@property (nonatomic, assign) BOOL editAddressResult;
@property (nonatomic, assign) BOOL modifyUserInfoResult;
@property (nonatomic, assign) BOOL pollqrCodeStateResult;
@property (nonatomic, assign) BOOL cancelQRLoginResult;
@property (nonatomic, assign) BOOL confirmQRScanResult;
@property (nonatomic, assign) BOOL confirmQRLoginResult;
@property (nonatomic, assign) BOOL queryWorkOrderResult;
@property (nonatomic, assign) BOOL queryLoginLogsResult;
@property (nonatomic, strong) NSArray<UserLoginLogInfo *> *userLoginLogInfoList;
@property (nonatomic, assign) BOOL refreshAddressListResult;
@property (nonatomic, strong) NSArray<UserAddressInfo *> *addressList;
@property (nonatomic, assign) BOOL refreshTerminalListResult;
@property (nonatomic, strong) NSArray<UserTerminal *> *terminalList;
@property (nonatomic, assign) BOOL refreshUserInfoResult;
@property (nonatomic, assign) BOOL updateAvatarResult;
@property (nonatomic, copy) NSString *avatarUrl;
@property (nonatomic, strong) UserInfo *userInfo;
@property (nonatomic, strong) NSMutableDictionary *mutiCallBackDic;
@property (nonatomic, strong) NSDictionary *actualDict;
@property (nonatomic, strong) UIImage *actualImage;
@property (nonatomic, assign) int invoCount;
@property (nonatomic, strong) NSMutableDictionary *conditionVariableMap;

@end
@implementation UserSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.createAddressResult = NO;
      self.deleteAddressResult = NO;
      self.modifyUserInfoResult = NO;
      self.pollqrCodeStateResult = NO;
      self.cancelQRLoginResult = NO;
      self.confirmQRScanResult = NO;
      self.confirmQRLoginResult = NO;
      self.queryWorkOrderResult = NO;
      self.queryLoginLogsResult = NO;
      self.userLoginLogInfoList = nil;
      self.refreshAddressListResult = NO;
      self.addressList = nil;
      self.refreshTerminalListResult = NO;
      self.terminalList = nil;
      self.refreshUserInfoResult = NO;
      self.userInfo = nil;
      self.avatarUrl = nil;
      self.updateAvatarResult = NO;
      self.mutiCallBackDic = [NSMutableDictionary dictionary];
      self.actualDict = [NSDictionary dictionary];
      self.actualImage = nil;
      self.invoCount = 0;
      self.conditionVariableMap = [NSMutableDictionary dictionary];
    });

    Given(@"^用户触发自动登录$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[UserdomainHolderTest getInstance].userdomain autoRefreshToken];
    });

    Given(@"^用户数据源的用户信息\"([^\"]*)\"数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *keyStr = args[0];
      UserInfo *info = getUserInfoFromExpectUserInfo(userInfo);
      [self.conditionVariableMap setObject:info forKey:keyStr];
    });

    When(@"^执行创建地址方法,参数地址对象为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *createAddressStr = args[0];
      UserAddressArgs *addressArgs;
      if (![createAddressStr isEqualToString:@"空对象"]) {
          NSMutableDictionary *dict = [NSMutableDictionary dictionary];
          NSArray<NSString *> *argsList = [createAddressStr componentsSeparatedByString:@","];
          for (NSString *str in argsList) {
              NSArray<NSString *> *dataList = [str componentsSeparatedByString:@"="];
              dict[dataList.firstObject] = dataList.lastObject;
          }
          UserAddressInfo *addressInfo = createUserAddressInfo(dict);
          addressArgs = [[UserAddressArgs alloc] initAddressArgs:addressInfo];
      }
      [[UserdomainHolderTest getInstance]
              .userdomain.user createNewAddress:addressArgs
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.createAddressResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.createAddressResult = result.success;
          }];
    });
    Then(@"^用户数据源创建地址接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource createNewAddress:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^地址创建回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.createAddressResult == [result isEqualToString:@"成功"], @"地址创建结果与实际不一致");
    });
    Given(@"^用户数据源创建地址接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([UserDataSource createNewAddress:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        [invocation getArgument:&finishBlockPointer atIndex:2];
        NSDictionary *address = (__bridge NSDictionary *)finishBlockPointer;
        self.actualDict = address;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:3];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    Then(@"^用户数据源创建地址接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource createNewAddress:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
      NSString *createAddressArgsStr = args[1];
      NSDictionary *dict = self.actualDict;
      NSString *actualCreateAddressArgsStr = [NSString stringWithFormat:@"receiverName=%@,receiverMobile=%@,provinceId=%@,province=%@,cityId=%@,city=%@,districtId=%@,district=%@,source=%@", dict[@"receiver_name"], dict[@"receiver_mobile"], dict[@"address"][@"province_id"], dict[@"address"][@"province"], dict[@"address"][@"city_id"], dict[@"address"][@"city"], dict[@"address"][@"district_id"], dict[@"address"][@"district"], dict[@"source"]];
      CCIAssert([actualCreateAddressArgsStr isEqualToString:createAddressArgsStr], @"创建地址参数为%@实际为%@", createAddressArgsStr, actualCreateAddressArgsStr);

    });
    When(@"^执行删除地址方法,参数地址id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *addressId = args[0];
      if ([addressId isEqualToString:@"空对象"]) {
          addressId = nil;
      }
      else if ([addressId isEqualToString:@"空字符串"]) {
          addressId = @"";
      }
      [[UserdomainHolderTest getInstance]
              .userdomain.user deleteAddress:addressId
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.deleteAddressResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.deleteAddressResult = result.success;
          }];

    });
    Then(@"^用户数据源删除地址接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource deleteAddress:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);

    });
    Then(@"^用户数据源删除地址接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *addressId = args[1];
      OCMVerify(times(expectedInvocationTimes), [UserDataSource deleteAddress:addressId success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^删除地址回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.deleteAddressResult == [result isEqualToString:@"成功"], @"删除地址结果与实际不一致");
    });
    Given(@"^用户数据源删除地址接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([UserDataSource deleteAddress:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:3 retCode:nil retData:nil]);
    });
    When(@"^执行编辑地址方法,参数地址对象为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *createAddressStr = args[0];
      UserAddressArgs *addressArgs;
      if (![createAddressStr isEqualToString:@"空对象"]) {
          NSMutableDictionary *dict = [NSMutableDictionary dictionary];
          NSArray<NSString *> *argsList = [createAddressStr componentsSeparatedByString:@","];
          for (NSString *str in argsList) {
              NSArray<NSString *> *dataList = [str componentsSeparatedByString:@"="];
              dict[dataList.firstObject] = dataList.lastObject;
          }
          UserAddressInfo *addInfo = createUserAddressInfo(dict);
          UserAddressInfo *addressInfo;
          for (UserAddressInfo *add in [UserdomainHolderTest getInstance].userdomain.provideUpUserDomainStore.userStore.addressList) {
              if ([add.addressId isEqualToString:addInfo.addressId]) {
                  addressInfo = add;
                  break;
              }
          }
          addressInfo.realReceiver_name = addInfo.receiver_name;
          addressInfo.realReceiver_mobile = addInfo.realReceiver_mobile;
          addressInfo.realSource = addInfo.realSource;
          UserAddress *address = (UserAddress *)addressInfo.address;
          address.realProvince_id = addInfo.address.province_id;
          address.realProvince = addInfo.address.province;
          address.realCity = addInfo.address.city;
          address.realCity_id = addInfo.address.city_id;
          address.realDistrict = addInfo.address.district;
          address.realDistrict_id = addInfo.address.district_id;
          addressArgs = [[UserAddressArgs alloc] initAddressArgs:addressInfo];
      }
      [[UserdomainHolderTest getInstance]
              .userdomain.user editAddress:addressArgs
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.editAddressResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.editAddressResult = result.success;
          }];

    });
    Then(@"^用户数据源编辑地址接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource editAddress:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^用户数据源编辑地址接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource editAddress:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
      NSString *createAddressArgsStr = args[1];
      NSDictionary *dict = self.actualDict;
      NSString *actualCreateAddressArgsStr = [NSString stringWithFormat:@"receiverName=%@,receiverMobile=%@,provinceId=%@,province=%@,cityId=%@,city=%@,districtId=%@,district=%@,source=%@,addressId=%@", dict[@"receiver_name"], dict[@"receiver_mobile"], dict[@"address"][@"province_id"], dict[@"address"][@"province"], dict[@"address"][@"city_id"], dict[@"address"][@"city"], dict[@"address"][@"district_id"], dict[@"address"][@"district"], dict[@"source"], dict[@"id"]];
      CCIAssert([actualCreateAddressArgsStr isEqualToString:createAddressArgsStr], @"创建地址参数为%@实际为%@", createAddressArgsStr, actualCreateAddressArgsStr);
    });
    Then(@"^编辑地址回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.editAddressResult == [result isEqualToString:@"成功"], @"编辑地址回调结果与实际不一致");
    });
    Given(@"^用户数据源编辑地址接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([UserDataSource editAddress:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        [invocation getArgument:&finishBlockPointer atIndex:2];
        NSDictionary *address = (__bridge NSDictionary *)finishBlockPointer;
        self.actualDict = address;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:3];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    When(@"^执行修改用户信息方法,参数用户信息对象为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *userInfoStr = args[0];
      UserInfoArgs *userInfoArgs;
      if (![userInfoStr isEqualToString:@"空对象"]) {
          NSMutableDictionary *dict = [NSMutableDictionary dictionary];
          NSArray<NSString *> *argsList = [userInfoStr componentsSeparatedByString:@","];
          for (NSString *str in argsList) {
              NSArray<NSString *> *dataList = [str componentsSeparatedByString:@"="];
              dict[dataList.firstObject] = dataList.lastObject;
          }
          UserInfo *userInfo = createUserInfo(dict);
          userInfoArgs = [[UserInfoArgs alloc] initUserInfoArgs:userInfo];
      }
      [[UserdomainHolderTest getInstance]
              .userdomain.user modifyUserInfo:userInfoArgs
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.modifyUserInfoResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.modifyUserInfoResult = result.success;
          }];

    });
    Then(@"^用户数据源修改用户信息接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource modifyUserInfo:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^修改用户信息回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.modifyUserInfoResult == [result isEqualToString:@"成功"], @"编辑用户信息回调结果与实际不一致");
    });
    Given(@"^用户数据源修改用户信息接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([UserDataSource modifyUserInfo:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        [invocation getArgument:&finishBlockPointer atIndex:2];
        NSDictionary *address = (__bridge NSDictionary *)finishBlockPointer;
        self.actualDict = address;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:3];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    Then(@"^用户数据源修改用户信息接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource modifyUserInfo:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
      NSString *modifyUserInfoStr = args[1];
      NSDictionary *dict = self.actualDict;
      NSString *actualModifyUserInfoStr = [NSString stringWithFormat:@"userId=%@,gender=%@,marriage=%@,education=%@,income=%@,familyNum=%@,birthday=%@", dict[@"userId"], dict[@"gender"], dict[@"marriage"], dict[@"education"], dict[@"income"], dict[@"familyNum"], dict[@"birthday"]];
      CCIAssert([actualModifyUserInfoStr isEqualToString:modifyUserInfoStr], @"编辑用户信息参数为%@实际为%@", modifyUserInfoStr, actualModifyUserInfoStr);
    });
    When(@"^执行轮询二维码登录状态方法,参数UUID为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *uuid = args[0];
      if ([uuid isEqualToString:@"空对象"]) {
          uuid = nil;
      }
      else if ([uuid isEqualToString:@"空字符串"]) {
          uuid = @"";
      }
      [[UserdomainHolderTest getInstance]
              .userdomain.user pollqrCodeState:uuid
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.pollqrCodeStateResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.pollqrCodeStateResult = result.success;
          }];

    });
    Then(@"^用户数据源轮询二维码接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource pollqrCodeState:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^用户数据源轮询二维码接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *pollQRStateStr = args[1];
      OCMVerify(times(expectedInvocationTimes), [UserDataSource pollqrCodeState:pollQRStateStr success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^轮询二维码登录状态回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.pollqrCodeStateResult == [result isEqualToString:@"成功"], @"轮询二维码登录状态回调结果与实际不一致");
    });
    Given(@"^用户数据源轮询二维码接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([UserDataSource pollqrCodeState:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:3 retCode:nil retData:nil]);
    });
    When(@"^执行二维码取消登录方法,参数UUID为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *uuid = args[0];
      if ([uuid isEqualToString:@"空对象"]) {
          uuid = nil;
      }
      else if ([uuid isEqualToString:@"空字符串"]) {
          uuid = @"";
      }
      [[UserdomainHolderTest getInstance]
              .userdomain.user qrCancleLogin:uuid
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.cancelQRLoginResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.cancelQRLoginResult = result.success;
          }];

    });
    Then(@"^用户数据源二维码取消登录接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource qrCancleLogin:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^二维码取消登录回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.cancelQRLoginResult == [result isEqualToString:@"成功"], @"二维码取消登录回调结果与实际不一致");
    });
    Given(@"^用户数据源二维码取消登录接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([UserDataSource qrCancleLogin:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:3 retCode:nil retData:nil]);
    });
    Then(@"^用户数据源二维码取消登录接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *cancelQRLoginStr = args[1];
      OCMVerify(times(expectedInvocationTimes), [UserDataSource qrCancleLogin:cancelQRLoginStr success:[OCMArg any] failure:[OCMArg any]]);
    });
    When(@"^执行二维码确认扫码方法,参数UUID为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *uuid = args[0];
      if ([uuid isEqualToString:@"空对象"]) {
          uuid = nil;
      }
      else if ([uuid isEqualToString:@"空字符串"]) {
          uuid = @"";
      }
      [[UserdomainHolderTest getInstance]
              .userdomain.user qrConfirmScan:uuid
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.confirmQRScanResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.confirmQRScanResult = result.success;
          }];
    });
    Then(@"^用户数据源二维码确认扫码接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource qrConfirmScan:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^二维码确认扫码回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.confirmQRScanResult == [result isEqualToString:@"成功"], @"二维码确认扫码回调结果为与实际不一致");
    });
    Given(@"^用户数据源二维码确认扫码接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([UserDataSource qrConfirmScan:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:3 retCode:nil retData:nil]);
    });
    Then(@"^用户数据源二维码确认扫码接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *confirmQRScanStr = args[1];
      OCMVerify(times(expectedInvocationTimes), [UserDataSource qrConfirmScan:confirmQRScanStr success:[OCMArg any] failure:[OCMArg any]]);
    });
    When(@"^执行二维码确认登录方法,参数UUID为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *uuid = args[0];
      if ([uuid isEqualToString:@"空对象"]) {
          uuid = nil;
      }
      else if ([uuid isEqualToString:@"空字符串"]) {
          uuid = @"";
      }
      [[UserdomainHolderTest getInstance]
              .userdomain.user qrConfirmLogin:uuid
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.confirmQRLoginResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.confirmQRLoginResult = result.success;
          }];
    });
    Then(@"^用户数据源二维码确认登录接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource qrConfirmLogin:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^二维码确认登录回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.confirmQRLoginResult == [result isEqualToString:@"成功"], @"二维码确认登录回调结果与实际不一致");
    });
    Given(@"^用户数据源二维码确认登录接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([UserDataSource qrConfirmLogin:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:3 retCode:nil retData:nil]);
    });
    Then(@"^用户数据源二维码确认登录接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *confirmQRLoginStr = args[1];
      OCMVerify(times(expectedInvocationTimes), [UserDataSource qrConfirmLogin:confirmQRLoginStr success:[OCMArg any] failure:[OCMArg any]]);
    });
    When(@"^执行查询用户电子工单方法,参数手机号为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *phoneNum = args[0];
      if ([phoneNum isEqualToString:@"空对象"]) {
          phoneNum = nil;
      }
      else if ([phoneNum isEqualToString:@"空字符串"]) {
          phoneNum = @"";
      }
      [[UserdomainHolderTest getInstance]
              .userdomain.user qrWorkOrderInfo:phoneNum
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.queryWorkOrderResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.queryWorkOrderResult = result.success;
          }];
    });
    Then(@"^用户数据源查询用户电子工单接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource queryServiceOrder:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^查询用户电子工单回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.queryWorkOrderResult == [result isEqualToString:@"成功"], @"查询用户电子工单回调结果与实际不一致");
    });
    Given(@"^用户数据源查询用户电子工单接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([UserDataSource queryServiceOrder:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:3 retCode:nil retData:nil]);
    });
    Then(@"^用户数据源查询用户电子工单接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *queryWorkorderStr = args[1];
      OCMVerify(times(expectedInvocationTimes), [UserDataSource queryServiceOrder:queryWorkorderStr success:[OCMArg any] failure:[OCMArg any]]);
    });
    When(@"^执行查询用户登录日志方法,参数页码\"([^\"]*)\",数量为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *pageNo = args[0];
      NSString *size = args[1];
      [[UserdomainHolderTest getInstance]
              .userdomain.user queryLoginLogs:pageNo.integerValue
          pageSize:size.integerValue
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.queryLoginLogsResult = result.success;
            self.userLoginLogInfoList = result.retData;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.queryLoginLogsResult = result.success;
          }];

    });
    Then(@"^用户数据源查询用户登录日志接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource queryLoginLogs:[OCMArg any] pageSize:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^查询用户登录日志回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.queryLoginLogsResult == [result isEqualToString:@"成功"], @"查询用户登录日志回调结果与实际不一致");
    });
    Given(@"^用户数据源查询用户登录日志接口返回\"([^\"]*)\",返回登录日志如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UserLoginLogInfo *> *userLoginLogInfoList = getUserLoginLogInfoListFromExpectUserInfo(userInfo);
      OCMStub([UserDataSource queryLoginLogs:[OCMArg any] pageSize:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:4 retCode:nil retData:userLoginLogInfoList]).ignoringNonObjectArgs;
    });
    Then(@"^用户数据源查询用户登录日志接口被调用\"([^\"]*)\"次,参数页码\"([^\"]*)\",数量为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSInteger pageNo = args[1].integerValue;
      NSInteger size = args[2].integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource queryLoginLogs:pageNo pageSize:size success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^查询用户登录日志回调结果为\"([^\"]*)\",登录日志列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.queryLoginLogsResult == [result isEqualToString:@"成功"], @"查询用户登录日志回调结果与实际不一致");
      NSArray<UserLoginLogInfo *> *userLoginLogInfoList = getUserLoginLogInfoListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserLoginLogInfoList(self.userLoginLogInfoList, userLoginLogInfoList), @"登录日志应该相等，实际不相等");
    });
    Given(@"^用户数据源刷新地址列表接口返回\"([^\"]*)\",地址列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UserAddressInfo *> *addressList = getAddressListFromExpectUserInfo(userInfo);
      OCMStub([UserDataSource queryUserAddresssuccess:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:2 retCode:nil retData:addressList]);
    });
    When(@"^执行刷新地址列表方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[UserdomainHolderTest getInstance]
              .userdomain.user refreshAddressList:^(UserDomainSampleResult *_Nonnull result) {
        self.refreshAddressListResult = result.success;
        self.addressList = result.retData;
      }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.refreshAddressListResult = result.success;
          }];
    });
    Then(@"^刷新地址列表回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.refreshAddressListResult == [result isEqualToString:@"成功"], @"刷新地址列表回调结果与实际不一致");
    });
    Then(@"^用户数据源刷新地址列表接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource queryUserAddresssuccess:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^刷新地址列表回调结果为\"([^\"]*)\",地址列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.refreshAddressListResult == [result isEqualToString:@"成功"], @"刷新地址列表回调结果与实际不一致");
      NSArray<UserAddressInfo *> *addressList = getAddressListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserAddressList(self.addressList, addressList), @"地址列表应该相同，实际不一致");
    });
    Given(@"^用户数据源刷新地址列表接口等待\"([^\"]*)\"秒,返回地址列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSTimeInterval waitTime = [args[0] floatValue];
      NSArray<UserAddressInfo *> *addressList = getAddressListFromExpectUserInfo(userInfo);
      [OCMStub([UserDataSource queryUserAddresssuccess:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        if (waitTime) {
            [NSThread sleepForTimeInterval:waitTime];
        }
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        [invocation getArgument:&finishBlockPointer atIndex:2];
        userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
        UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
        result.retData = addressList;
        userDomainCallback(result);
      }];
    });
    When(@"^线程\"([^\"]*)\"中\"([^\"]*)\"执行刷新地址列表方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[1];
      dispatch_async(dispatch_get_global_queue(0, 0), ^{
        [[UserdomainHolderTest getInstance]
                .userdomain.user refreshAddressList:^(UserDomainSampleResult *_Nonnull result) {
          self.mutiCallBackDic[user] = result;
        }
            failure:^(UserDomainSampleResult *_Nonnull result) {
              self.mutiCallBackDic[user] = result;
            }];
      });
    });
    Then(@"^\"([^\"]*)\"收到刷新地址列表回调结果为\"([^\"]*)\",地址列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      NSString *resultStr = args[1];
      UserDomainSampleResult *result = self.mutiCallBackDic[user];
      CCIAssert(result.success == [resultStr isEqualToString:@"成功"], @"刷新地址列表回调结果与实际不一致");
      NSArray<UserAddressInfo *> *addressList = getAddressListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserAddressList(result.retData, addressList), @"地址列表应该相同，实际不一致");
    });
    Given(@"^用户数据源刷新终端列表接口返回\"([^\"]*)\",返回终端列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UserTerminal *> *terminalList = getTerminalListFromExpectUserInfo(userInfo);
      OCMStub([UserDataSource queryLoginTerminalSuccess:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:2 retCode:nil retData:terminalList]);
    });
    When(@"^执行刷新终端列表方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[UserdomainHolderTest getInstance]
              .userdomain.user refreshTerminalList:^(UserDomainSampleResult *_Nonnull result) {
        self.refreshTerminalListResult = result.success;
        self.terminalList = result.retData;
      }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.refreshTerminalListResult = result.success;
          }];

    });
    Then(@"^刷新终端列表回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.refreshTerminalListResult == [result isEqualToString:@"成功"], @"刷新终端列表回调结果与实际不一致");
    });
    Then(@"^用户数据源刷新终端列表接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource queryLoginTerminalSuccess:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^刷新终端列表回调结果为\"([^\"]*)\",终端列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.refreshTerminalListResult == [result isEqualToString:@"成功"], @"刷新终端列表回调结果与实际不一致");
      NSArray<UserTerminal *> *terminalList = getTerminalListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserTerminalList(self.terminalList, terminalList), @"终端列表应该相同，实际不一致");
    });
    Given(@"^用户数据源刷新终端列表接口等待\"([^\"]*)\"秒,返回终端列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSTimeInterval waitTime = [args[0] floatValue];
      NSArray<UserTerminal *> *terminalList = getTerminalListFromExpectUserInfo(userInfo);
      OCMStub([UserDataSource queryLoginTerminalSuccess:[OCMArg any] failure:[OCMArg any]]).andDo([self actionSuccessHandler:args location:2 retData:terminalList waitTime:waitTime]);
    });
    When(@"^线程\"([^\"]*)\"中\"([^\"]*)\"执行刷新终端列表方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[1];
      dispatch_async(dispatch_get_global_queue(0, 0), ^{
        [[UserdomainHolderTest getInstance]
                .userdomain.user refreshTerminalList:^(UserDomainSampleResult *_Nonnull result) {
          self.mutiCallBackDic[user] = result;
        }
            failure:^(UserDomainSampleResult *_Nonnull result) {
              self.mutiCallBackDic[user] = result;
            }];
      });
    });
    Then(@"^\"([^\"]*)\"收到刷新终端列表回调结果为\"([^\"]*)\",终端列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      NSString *resultStr = args[1];
      UserDomainSampleResult *result = self.mutiCallBackDic[user];
      CCIAssert(result.success == [resultStr isEqualToString:@"成功"], @"刷新终端列表回调结果与实际不一致");
      NSArray<UserTerminal *> *terminalList = getTerminalListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserTerminalList(result.retData, terminalList), @"终端列表应该相同，实际不一致");
    });
    Given(@"^用户数据源刷新用户信息接口返回\"([^\"]*)\",返回的用户信息为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *keyStr = args[1];
      OCMStub([UserDataSource queryUserInfosuccess:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:2 retCode:nil retData:self.conditionVariableMap[keyStr]]);
    });
    When(@"^执行刷新用户信息方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[UserdomainHolderTest getInstance]
              .userdomain.user refreshUserInfo:^(UserDomainSampleResult *_Nonnull result) {
        self.refreshUserInfoResult = result.success;
        self.userInfo = result.retData;
      }];

    });
    Then(@"^刷新用户信息回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.refreshUserInfoResult == [result isEqualToString:@"成功"], @"刷新用户信息回调结果与实际不一致");
    });
    Then(@"^用户数据源刷新用户信息接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource queryUserInfosuccess:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^刷新用户信息回调结果为\"([^\"]*)\",用户信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.refreshUserInfoResult == [result isEqualToString:@"成功"], @"刷新用户信息回调结果与实际不一致");
      UserInfo *info = getUserInfoFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserInfo(info, self.userInfo), @"刷新用户信息应该相同，实际不一致");
    });
    Given(@"^用户数据源刷新用户信息接口等待\"([^\"]*)\"秒,返回结果为\"([^\"]*)\",返回的用户信息为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSTimeInterval waitTime = [args[0] floatValue];
      NSString *keyStr = args[2];
      [OCMStub([UserDataSource queryUserInfosuccess:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        if (waitTime) {
            [NSThread sleepForTimeInterval:waitTime];
        }
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        if ([args[1] isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:2];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            result.retData = self.conditionVariableMap[keyStr];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:3];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    When(@"^线程\"([^\"]*)\"中\"([^\"]*)\"执行刷新用户信息方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[1];
      dispatch_async(dispatch_get_global_queue(0, 0), ^{
        [[UserdomainHolderTest getInstance]
                .userdomain.user refreshUserInfo:^(UserDomainSampleResult *_Nonnull result) {
          self.mutiCallBackDic[user] = result;
        }];
      });
    });
    Then(@"^\"([^\"]*)\"收到刷新用户信息回调结果为\"([^\"]*)\",用户信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      NSString *resultStr = args[1];
      UserDomainSampleResult *result = self.mutiCallBackDic[user];
      CCIAssert(result.success == [resultStr isEqualToString:@"成功"], @"执行刷新用户信息结果与实际不一致");
      UserInfo *info = getUserInfoFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserInfo(result.retData, info), @"用户信息应该相同，实际不一致");
    });
    When(@"^调用设置默认家庭方法,参数家庭id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      if ([familyId isEqualToString:@"空对象"]) {
          familyId = nil;
      }
      else if ([familyId isEqualToString:@"空字符串"]) {
          familyId = @"";
      }
      Family *family = [[Family alloc] init];
      family.realFamilyId = familyId;
      [[UserdomainHolderTest getInstance]
              .userdomain.user setCurrentFamily:family];
    });
    Then(@"^当前家庭的家庭id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      CCIAssert([[UserdomainHolderTest getInstance]
                        .userdomain.user.currentFamily.familyId isEqualToString:familyId],
                @"当前家庭id与实际不相同");
    });
    When(@"^调用上传用户头像方法,参数图片地址为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *imagePath = args[0];
      if ([imagePath isEqualToString:@"空对象"]) {
          imagePath = nil;
      }
      else if ([imagePath isEqualToString:@"空字符串"]) {
          imagePath = @"";
      }
      UIImage *image;
      if (imagePath.length) {
          image = [[UIImage alloc] init];
      }

      [[UserdomainHolderTest getInstance]
              .userdomain.user updateAvatar:image
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.updateAvatarResult = result.success;
            self.avatarUrl = result.retData;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.updateAvatarResult = result.success;
          }];
    });
    Then(@"^上传用户头像回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.updateAvatarResult == [result isEqualToString:@"成功"], @"上传用户头像回调结果与实际不一致");
    });
    Then(@"^用户数据源上传用户头像接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource updateAvatar:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^用户数据源上传头像接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([UserDataSource updateAvatar:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        [invocation getArgument:&finishBlockPointer atIndex:2];
        self.actualImage = (__bridge UIImage *)finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        if ([resultString isEqualToString:@"失败"]) {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    Then(@"^用户数据源上传用户头像接口被调用\"([^\"]*)\"次,参数图片地址为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource updateAvatar:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
      CCIAssert(self.actualImage != nil, @"图片对象应不为空，实际为空");

    });
    Given(@"^用户数据源上传头像接口返回的头像地址为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *imageUrl = args[0];
      [OCMStub([UserDataSource updateAvatar:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        [invocation getArgument:&finishBlockPointer atIndex:2];
        self.actualImage = (__bridge UIImage *)finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        [invocation getArgument:&finishBlockPointer atIndex:3];
        userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
        UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
        result.retData = imageUrl;
        userDomainCallback(result);
      }];
    });
    Then(@"^上传用户头像回调结果为\"([^\"]*)\",图片地址为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.updateAvatarResult == [result isEqualToString:@"成功"], @"上传用户头像回调结果与实际不一致");
      NSString *imageUrl = args[1];
      CCIAssert([imageUrl isEqualToString:self.avatarUrl], @"图片地址应该相同，实际不相同");
    });
    Given(@"^用户数据源的刷新用户鉴权信息接口\"([^\"]*)\"秒后返回鉴权信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSTimeInterval waitTime = [args[0] floatValue];
      ApplicationOauthData *oauthData = getOauthDataFromExpectUserInfo(userInfo);
      OCMStub([UserDataSource refreshToken:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionSuccessHandler:args location:3 retData:oauthData waitTime:waitTime]);
    });
    Given(@"^用户数据源的用户鉴权信息数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ApplicationOauthData *oauthData = getOauthDataFromExpectUserInfo(userInfo);
      OCMStub([[UserdomainHolderTest getInstance].userdomain.provideUserDataSource refreshToken:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionSuccessHandler:args location:3 retData:oauthData waitTime:0]);
    });
    Then(@"^用户数据源的刷新用户鉴权信息接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource refreshToken:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^用户数据源的用户登出接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([UserDataSource logout:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]])
              .ignoringNonObjectArgs andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:3];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    Then(@"^用户数据源的用户登出接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [UserDataSource logout:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });

    Given(@"^用户数据源调用\"([^\"]*)\"接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStr = args[1];
      if ([args[0] isEqualToString:@"刷新用户鉴权信息"]) {
          OCMStub([UserDataSource refreshToken:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStr retCode2:nil retData:nil]);
      }
      else if (([args[0] isEqualToString:@"修改用户信息"])) {
          OCMStub([UserDataSource modifyUserInfo:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStr retCode2:nil retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"创建地址"]))) {
          OCMStub([UserDataSource createNewAddress:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStr retCode2:nil retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"编辑地址"]))) {
          OCMStub([UserDataSource editAddress:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStr retCode2:nil retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"删除地址"]))) {
          OCMStub([UserDataSource deleteAddress:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStr retCode2:nil retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"刷新地址列表"]))) {
          OCMStub([UserDataSource queryUserAddresssuccess:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:3 retCode:resultStr retCode2:nil retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"刷新用户信息"]))) {
          OCMStub([UserDataSource queryUserInfosuccess:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:3 retCode:resultStr retCode2:nil retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"刷新终端列表"]))) {
          OCMStub([UserDataSource queryLoginTerminalSuccess:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:3 retCode:resultStr retCode2:nil retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"轮询二维码"]))) {
          OCMStub([UserDataSource pollqrCodeState:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStr retCode2:nil retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"二维码确认扫码"]))) {
          OCMStub([UserDataSource qrConfirmScan:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStr retCode2:nil retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"二维码确认登录"]))) {
          OCMStub([UserDataSource qrConfirmLogin:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStr retCode2:nil retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"二维码取消登录"]))) {
          OCMStub([UserDataSource qrCancleLogin:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStr retCode2:nil retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"查询用户登录日志"]))) {
          OCMStub([UserDataSource queryLoginLogs:[OCMArg any] pageSize:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:5 retCode:resultStr retCode2:nil retData:nil]).ignoringNonObjectArgs;
      }
      else if ((([args[0] isEqualToString:@"查询用户电子工单"]))) {
          OCMStub([UserDataSource queryServiceOrder:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStr retCode2:nil retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"上传头像"]))) {
          OCMStub([UserDataSource updateAvatar:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerUpdateAvatar:args location:4 retCode:resultStr retCode2:nil retData:nil]);
      }

    });
    Given(@"^用户数据源调用\"([^\"]*)\"接口第一次返回\"([^\"]*)\",第二次返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStrFail = args[1];
      NSString *resultSuccess = args[2];
      if (([args[0] isEqualToString:@"修改用户信息"])) {
          OCMStub([UserDataSource modifyUserInfo:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStrFail retCode2:resultSuccess retData:nil]);
      }
      else if (([args[0] isEqualToString:@"创建地址"])) {
          OCMStub([UserDataSource createNewAddress:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStrFail retCode2:resultSuccess retData:nil]);
      }
      else if (([args[0] isEqualToString:@"编辑地址"])) {
          OCMStub([UserDataSource editAddress:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStrFail retCode2:resultSuccess retData:nil]);
      }
      else if (([args[0] isEqualToString:@"删除地址"])) {
          OCMStub([UserDataSource deleteAddress:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStrFail retCode2:resultSuccess retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"轮询二维码"]))) {
          OCMStub([UserDataSource pollqrCodeState:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStrFail retCode2:resultSuccess retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"二维码确认扫码"]))) {
          OCMStub([UserDataSource qrConfirmScan:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStrFail retCode2:resultSuccess retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"二维码确认登录"]))) {
          OCMStub([UserDataSource qrConfirmLogin:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStrFail retCode2:resultSuccess retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"二维码取消登录"]))) {
          OCMStub([UserDataSource qrCancleLogin:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStrFail retCode2:resultSuccess retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"查询用户电子工单"]))) {
          OCMStub([UserDataSource queryServiceOrder:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:4 retCode:resultStrFail retCode2:resultSuccess retData:nil]);
      }
      else if ((([args[0] isEqualToString:@"上传头像"]))) {
          OCMStub([UserDataSource updateAvatar:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerUpdateAvatar:args location:4 retCode:resultStrFail retCode2:resultSuccess retData:nil]);
      }

    });

    Given(@"^用户数据源调用\"([^\"]*)\"接口第一次返回\"([^\"]*)\",第二次返回\"([^\"]*)\",返回\"([^\"]*)\"如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStrFail = args[1];
      NSString *resultSuccess = args[2];
      if ((([args[0] isEqualToString:@"刷新地址列表"]))) {
          NSArray<UserAddressInfo *> *addressList = getAddressListFromExpectUserInfo(userInfo);
          OCMStub([UserDataSource queryUserAddresssuccess:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:3 retCode:resultStrFail retCode2:resultSuccess retData:addressList]);
      }
      else if ((([args[0] isEqualToString:@"查询用户登录日志"]))) {
          NSArray<UserLoginLogInfo *> *userLoginLogInfoList = getUserLoginLogInfoListFromExpectUserInfo(userInfo);
          OCMStub([UserDataSource queryLoginLogs:[OCMArg any] pageSize:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:5 retCode:resultStrFail retCode2:resultSuccess retData:userLoginLogInfoList]).ignoringNonObjectArgs;
      }
      else if ((([args[0] isEqualToString:@"刷新用户信息"]))) {
          UserInfo *info = getUserInfoFromExpectUserInfo(userInfo);
          OCMStub([UserDataSource queryUserInfosuccess:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:3 retCode:resultStrFail retCode2:resultSuccess retData:info]);
      }
      else if ((([args[0] isEqualToString:@"刷新终端列表"]))) {
          NSArray<UserTerminal *> *terminalList = getTerminalListFromExpectUserInfo(userInfo);
          OCMStub([UserDataSource queryLoginTerminalSuccess:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerCallBackTwice:args location:3 retCode:resultStrFail retCode2:resultSuccess retData:terminalList]);
      }
    });

    Given(@"^用户数据源调用\"([^\"]*)\"接口第一次返回\"([^\"]*)\",第二次返回\"([^\"]*)\",返回的头像地址为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStrFail = args[1];
      NSString *resultSuccess = args[2];
      NSString *imageUrl = args[3];
      if ((([args[0] isEqualToString:@"上传头像"]))) {
          OCMStub([UserDataSource updateAvatar:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([self actionHandlerUpdateAvatar:args location:4 retCode:resultStrFail retCode2:resultSuccess retData:imageUrl]);
      }
    });

    Given(@"^用户数据源的刷新用户鉴权信息接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStr = args[0];
      [OCMStub([UserDataSource refreshToken:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        if ([resultStr isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:3];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            result.retCode = resultStr;
            userDomainCallback(result);
        }
      }];
    });
}

#pragma mark - private
- (void (^)(NSInvocation *))actionSuccessHandler:(NSArray *)args location:(NSInteger)location retData:(id)retData waitTime:(NSTimeInterval)waitTime
{
    return ^(NSInvocation *invocation) {
      if (waitTime) {
          [NSThread sleepForTimeInterval:waitTime];
      }
      [invocation retainArguments];
      void *finishBlockPointer;
      [invocation getArgument:&finishBlockPointer atIndex:location];
      void (^userDomainCallback)(UserDomainSampleResult *result);
      userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
      dispatch_async(dispatch_get_global_queue(0, 0), ^{
        UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
        result.retData = retData;
        userDomainCallback(result);
      });
    };
}

- (void (^)(NSInvocation *))actionHandlerCallBackTwice:(NSArray *)args location:(NSInteger)location retCode:(id)retCode retCode2:(id)retCode2 retData:(id)retData
{
    return ^(NSInvocation *invocation) {
      [invocation retainArguments];
      void *finishBlockPointer;
      void (^userDomainCallback)(UserDomainSampleResult *result);
      if (location == 4) {
          [invocation getArgument:&finishBlockPointer atIndex:2];
          NSDictionary *dict = (__bridge NSDictionary *)finishBlockPointer;
          self.actualDict = [dict isKindOfClass:[NSDictionary class]] ? dict : @{};
      }
      self.invoCount++;
      if ([retCode2 isEqualToString:@"成功"] && self.invoCount >= 2) {
          [invocation getArgument:&finishBlockPointer atIndex:location - 1];
          userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
          UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
          result.retData = retData;
          userDomainCallback(result);
      }
      else {
          [invocation getArgument:&finishBlockPointer atIndex:location];
          userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
          UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
          result.retCode = self.invoCount >= 2 ? retCode2 : retCode;
          userDomainCallback(result);
      }

    };
}

- (void (^)(NSInvocation *))actionHandlerUpdateAvatar:(NSArray *)args location:(NSInteger)location retCode:(id)retCode retCode2:(id)retCode2 retData:(id)retData
{
    return ^(NSInvocation *invocation) {
      [invocation retainArguments];
      void *finishBlockPointer;
      void (^userDomainCallback)(UserDomainSampleResult *result);
      if (location == 4) {
          [invocation getArgument:&finishBlockPointer atIndex:2];
          self.actualImage = (__bridge UIImage *)finishBlockPointer;
      }
      self.invoCount++;
      if ([retCode2 isEqualToString:@"成功"] && self.invoCount >= 2) {
          [invocation getArgument:&finishBlockPointer atIndex:location - 1];
          userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
          UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
          result.retData = retData;
          userDomainCallback(result);
      }
      else {
          [invocation getArgument:&finishBlockPointer atIndex:location];
          userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
          UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
          result.retCode = self.invoCount >= 2 ? retCode2 : retCode;
          userDomainCallback(result);
      }
    };
}
@end
