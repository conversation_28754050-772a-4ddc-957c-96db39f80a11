//
//  FamilySteps.m
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FamilySteps.h"
#import <Cucumberish/Cucumberish.h>
#import "UserdomainHolderTest.h"
#import "StepsUtils.h"
#import "CreateFamilyArgs.h"
#import "UserdomainHolderTest.h"
#import "User.h"
#import "UpFamilyDataSource.h"
#import "Family+PrivateExtension.h"
#import "RoomArgs.h"
#import "Room+PrivateExtension.h"
#import "UDDeviceInfo.h"
#import "UDDeviceInfo+PrivateExtension.h"
#import "FamilyArgs.h"
#import <MJExtension/MJExtension.h>
#import "FloorArg.h"
#import "UDFloorInfo.h"
#import "UDFloorInfo+PrivateExtension.h"
#import "FamilyMember.h"
#import "MemberInfo.h"
#import "VirtualMemberArgs.h"
#import <OCMock/OCMock.h>
#import "CloudBatchProcessDevicesResponse.h"

#define FamilyDataSource [UserdomainHolderTest getInstance].userdomain.provideFamilyDataSource

@interface FamilySteps ()
@property (nonatomic, strong) NSMutableDictionary *mutiCallBackDic;
@property (nonatomic, assign) BOOL refreshFamilyListResult;
@property (nonatomic, strong) NSArray<Family *> *familyList;
@property (nonatomic, assign) BOOL createFamilyResult;
@property (nonatomic, copy) NSString *createFamilyId;
@property (nonatomic, assign) BOOL replyFamilyResult;
@property (nonatomic, assign) BOOL refreshFamliyDetailResult;
@property (nonatomic, assign) BOOL createRoomResult;
@property (nonatomic, assign) BOOL deleteRoomResult;
@property (nonatomic, assign) BOOL editRoomResult;
@property (nonatomic, assign) BOOL invitateMemberResult;
@property (nonatomic, assign) BOOL deleteMemberResult;
@property (nonatomic, assign) BOOL unbindDevicesResult;
@property (nonatomic, assign) BOOL moveDevicesToRoomResult;
@property (nonatomic, assign) BOOL removeDevicesResult;
@property (nonatomic, assign) BOOL moveDevicesToFamilyResult;
@property (nonatomic, assign) BOOL exitFamilyAsAdminResult;
@property (nonatomic, assign) BOOL deleteFamilyResult;
@property (nonatomic, assign) BOOL changeFamilyAdminResult;
@property (nonatomic, assign) BOOL editFamilyInfoResult;
@property (nonatomic, assign) BOOL refreshRoomListResult;
@property (nonatomic, assign) BOOL exitFamilyAsMemberResult;
@property (nonatomic, assign) BOOL createFloorResult;
@property (nonatomic, assign) BOOL deleteFloorResult;
@property (nonatomic, assign) BOOL editFloorResult;
@property (nonatomic, assign) BOOL replyJoinFamilyResult;
@property (nonatomic, assign) BOOL queryFirstMemebeResult;
@property (nonatomic, assign) BOOL addVirtualMemberResult;
@property (nonatomic, assign) BOOL modifyVirtualMemberResult;
@property (nonatomic, assign) BOOL modifyMemberRoleResult;
@property (nonatomic, assign) BOOL modifyVirtualMemberRoleResult;
@property (nonatomic, assign) BOOL saveRoomOrderResult;
@property (nonatomic, strong) FamilyMember *firstMember;
@property (nonatomic, strong) Family *familyInfo;
@property (nonatomic, strong) RoomArgs *actualRoomArgs;
@property (nonatomic, strong) NSMutableArray *actualDeviceList;
@property (nonatomic, strong) Room *actualRoom;
@property (nonatomic, strong) FamilyArgs *actualFamilyArgs;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSArray<UDFloorInfo *> *> *relationFloorDic;
@property (nonatomic, strong) FloorArg *actualFloorArg;
@property (nonatomic, strong) NSMutableDictionary<NSString *, FamilyMember *> *relationFirstMemberDic;
@property (nonatomic, strong) VirtualMemberArgs *actualAirtualMemberArgs;
@property (nonatomic, strong) NSArray<NSString *> *actualRoomList;
@property (nonatomic, strong) CreateFamilyArgs *actualCreateFamilyArgs;
@property (nonatomic, strong) NSMutableDictionary *conditionVariableMap;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSArray<FamilyMember *> *> *relationFirstMemberListDict;
@end

@implementation FamilySteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.mutiCallBackDic = [NSMutableDictionary dictionary];
      self.createFamilyResult = NO;
      self.createFamilyId = nil;
      self.familyList = nil;
      self.refreshFamilyListResult = NO;
      self.replyFamilyResult = NO;
      self.refreshFamliyDetailResult = NO;
      self.createRoomResult = NO;
      self.deleteRoomResult = NO;
      self.editRoomResult = NO;
      self.invitateMemberResult = NO;
      self.deleteMemberResult = NO;
      self.unbindDevicesResult = NO;
      self.moveDevicesToRoomResult = NO;
      self.removeDevicesResult = NO;
      self.moveDevicesToFamilyResult = NO;
      self.exitFamilyAsAdminResult = NO;
      self.exitFamilyAsMemberResult = NO;
      self.deleteFamilyResult = NO;
      self.changeFamilyAdminResult = NO;
      self.editFamilyInfoResult = NO;
      self.refreshRoomListResult = NO;
      self.createFloorResult = NO;
      self.deleteFloorResult = NO;
      self.editFloorResult = NO;
      self.replyJoinFamilyResult = NO;
      self.queryFirstMemebeResult = NO;
      self.addVirtualMemberResult = NO;
      self.modifyVirtualMemberResult = NO;
      self.modifyMemberRoleResult = NO;
      self.modifyVirtualMemberRoleResult = NO;
      self.saveRoomOrderResult = NO;
      self.firstMember = nil;
      self.familyInfo = nil;
      self.actualRoomArgs = nil;
      self.actualDeviceList = [NSMutableArray array];
      self.actualRoom = nil;
      self.actualFamilyArgs = nil;
      self.relationFloorDic = [NSMutableDictionary dictionary];
      self.actualFloorArg = nil;
      self.relationFirstMemberDic = [NSMutableDictionary dictionary];
      self.actualAirtualMemberArgs = nil;
      self.actualRoomList = [NSArray array];
      self.actualCreateFamilyArgs = nil;
      self.conditionVariableMap = [NSMutableDictionary dictionary];
      self.relationFirstMemberListDict = [NSMutableDictionary dictionary];
    });

    Given(@"^家庭数据源的家庭列表\"([^\"]*)\"数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *keyStr = args[0];
      NSArray<Family *> *familyList = getFamilyListFromExpectUserInfo(userInfo);
      [self.conditionVariableMap setObject:familyList forKey:keyStr];
    });
    Given(@"^家庭\"([^\"]*)\"所关联的楼层信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSArray<UDFloorInfo *> *floorInfos = getFloorInfoListFromExpectUserInfo(userInfo);
      [self.relationFloorDic setValue:floorInfos forKey:familyId];
    });

    Given(@"^家庭\"([^\"]*)\"的家庭成员信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSArray<FamilyMember *> *memberList = getFamilyMemberInfoFromExpectUserInfo(userInfo);
      [self.relationFirstMemberListDict setObject:memberList forKey:familyId];
    });

    Given(@"^家庭数据源的家庭列表\"([^\"]*)\"中\"([^\"]*)\"的楼层列表数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *keyStr = args[0];
      NSString *familyId = args[1];
      NSArray<Family *> *familyList = self.conditionVariableMap[keyStr];
      NSArray<UDFloorInfo *> *floorInfos = getFloorInfoListFromExpectUserInfo(userInfo);
      for (Family *family in familyList) {
          if ([family.familyId isEqualToString:familyId]) {
              family.realFloorInfos = floorInfos;
          }
      }
    });

    Given(@"^家庭数据源的家庭列表\"([^\"]*)\"中\"([^\"]*)\"的成员信息数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *keyStr = args[0];
      NSString *familyId = args[1];
      NSArray<Family *> *familyList = self.conditionVariableMap[keyStr];
      NSArray<FamilyMember *> *memberList = getFamilyMemberInfoFromExpectUserInfo(userInfo);
      for (Family *family in familyList) {
          if ([family.familyId isEqualToString:familyId]) {
              family.realMembers = memberList;
          }
      }
    });

    Given(@"^缓存数据代理的家庭列表信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<Family *> *familyList = getFamilyListFromExpectUserInfo(userInfo);
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        for (Family *family in familyList) {
            if ([family.familyId isEqualToString:self.relationFirstMemberListDict.allKeys.firstObject]) {
                family.realMembers = self.relationFirstMemberListDict[family.familyId];
            }
            if ([family.familyId isEqualToString:self.relationFloorDic.allKeys.firstObject]) {
                family.realFloorInfos = self.relationFloorDic[family.familyId];
            }
        }
        NSArray *result = familyList;
        [invocation setReturnValue:&result];
      };
      [[[(OCMockObject *)[UserdomainHolderTest getInstance].cache stub] andDo:proxyBlock] getObject:@"familyList"];
    });

    When(@"^执行创建家庭方法,参数家庭对象为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyArgsStr = args[0];
      CreateFamilyArgs *familyArgs;
      if (![familyArgsStr isEqualToString:@"空对象"]) {
          familyArgs = [[CreateFamilyArgs alloc] init];
          NSArray<NSString *> *argsList = [familyArgsStr componentsSeparatedByString:@","];
          for (NSString *str in argsList) {
              NSArray<NSString *> *dataList = [str componentsSeparatedByString:@"="];
              if ([dataList.firstObject isEqualToString:@"familyName"]) {
                  familyArgs.name = dataList.lastObject;
              }
              else if ([dataList.firstObject isEqualToString:@"familyPosition"]) {
                  familyArgs.position = dataList.lastObject;
              }
              else {
                  familyArgs.latitude = dataList.lastObject;
                  familyArgs.longitude = dataList.lastObject;
              }
          }
      }
      [[UserdomainHolderTest getInstance]
              .userdomain.user createFamily:familyArgs
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.createFamilyResult = result.success;
            self.createFamilyId = result.retData;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.createFamilyResult = result.success;
          }];

    });
    Then(@"^家庭创建回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.createFamilyResult == [result isEqualToString:@"成功"], @"家庭创建结果与实际不一致");
    });
    Then(@"^家庭创建回调结果为\"([^\"]*)\",家庭id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      NSString *createFamilyId = args[1];
      CCIAssert(self.createFamilyResult == [result isEqualToString:@"成功"] && [createFamilyId isEqualToString:self.createFamilyId], @"家庭创建结果与实际不一致");
    });
    Then(@"^家庭数据源创建家庭接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource createFamily:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^家庭数据源创建家庭接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource createFamily:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
      NSString *familyArgsStr = args[1];
      NSString *actualFamilyArgsStr = [NSString stringWithFormat:@"familyName=%@,familyPosition=%@,familyLocation=%@", self.actualCreateFamilyArgs.name, self.actualCreateFamilyArgs.position, self.actualCreateFamilyArgs.latitude];
      CCIAssert([familyArgsStr isEqualToString:actualFamilyArgsStr], @"创建家庭参数应一致实际不一致。期望为%@,实际为%@", familyArgsStr, actualFamilyArgsStr);
    });
    Given(@"^家庭数据源创建家庭接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([FamilyDataSource createFamily:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        [invocation getArgument:&finishBlockPointer atIndex:2];
        self.actualCreateFamilyArgs = (__bridge CreateFamilyArgs *)finishBlockPointer;
        if ([resultString isEqualToString:@"失败"]) {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    Given(@"^家庭数据源创建家庭接口返回家庭id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      [OCMStub([FamilyDataSource createFamily:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        [invocation getArgument:&finishBlockPointer atIndex:2];
        self.actualCreateFamilyArgs = (__bridge CreateFamilyArgs *)finishBlockPointer;
        [invocation getArgument:&finishBlockPointer atIndex:3];
        userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
        UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
        result.retData = familyId;
        userDomainCallback(result);
      }];
    });
    Given(@"^家庭数据源刷新家庭列表接口返回\"([^\"]*)\",返回的家庭列表为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *keyStr = args[1];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        if ([args[0] isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:2];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            result.retData = self.conditionVariableMap[keyStr];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:3];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      };
      [[[(OCMockObject *)FamilyDataSource stub] andDo:proxyBlock] queryFamilyListsuccess:[OCMArg any] failure:[OCMArg any]];
    });
    Given(@"^家庭数据源刷新家庭列表接口返回\"([^\"]*)\",错误码为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *errorCode = args[1];
      OCMStub([FamilyDataSource queryFamilyListsuccess:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:2 retCode:errorCode retData:nil]);
    });
    When(@"^执行刷新家庭列表方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[UserdomainHolderTest getInstance]
              .userdomain.user refreshFamilyList:^(UserDomainSampleResult *_Nonnull result) {
        self.refreshFamilyListResult = result.success;
        self.familyList = result.retData;
      }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.refreshFamilyListResult = result.success;
          }];
    });
    Then(@"^刷新家庭列表回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.refreshFamilyListResult == [result isEqualToString:@"成功"], @"刷新家庭列表回调结果与实际不一致");
    });
    Then(@"^家庭数据源刷新家庭列表接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource queryFamilyListsuccess:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^刷新家庭列表回调结果为\"([^\"]*)\",家庭列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.refreshFamilyListResult == [result isEqualToString:@"成功"], @"刷新家庭列表回调结果与实际不一致");
      NSArray<Family *> *familyList = getFamilyListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserFamilyList(self.familyList, familyList), @"家庭列表应该相同，实际不一致");
    });
    Given(@"^家庭数据源刷新家庭列表接口等待\"([^\"]*)\"秒,返回的家庭列表为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSTimeInterval waitTime = [args[0] floatValue];
      NSString *keyStr = args[1];
      OCMStub([FamilyDataSource queryFamilyListsuccess:[OCMArg any] failure:[OCMArg any]]).andDo([self actionSuccessHandler:2 retData:self.conditionVariableMap[keyStr] waitTime:waitTime]);
    });
    When(@"^线程\"([^\"]*)\"中\"([^\"]*)\"执行刷新家庭列表方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[1];
      dispatch_async(dispatch_get_global_queue(0, 0), ^{
        [[UserdomainHolderTest getInstance]
                .userdomain.user refreshFamilyList:^(UserDomainSampleResult *_Nonnull result) {
          self.mutiCallBackDic[user] = result;
        }
            failure:^(UserDomainSampleResult *_Nonnull result) {
              self.mutiCallBackDic[user] = result;
            }];
      });
    });
    Then(@"^\"([^\"]*)\"收到刷新家庭列表回调结果为\"([^\"]*)\",家庭列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      NSString *resultStr = args[1];
      UserDomainSampleResult *result = self.mutiCallBackDic[user];
      CCIAssert(result.success == [resultStr isEqualToString:@"成功"], @"刷新家庭列表回调结果与实际不一致");
      NSArray<Family *> *familyList = getFamilyListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserFamilyList(result.retData, familyList), @"家庭列表应该相同，实际不一致");
    });
    When(@"^执行回复家庭邀请方法,参数邀请码为\"([^\"]*)\",家庭id为\"([^\"]*)\",成员名称\"([^\"]*)\",是否同意\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *code = args[0];
      if ([code isEqualToString:@"空对象"]) {
          code = nil;
      }
      else if ([code isEqualToString:@"空字符串"]) {
          code = @"";
      }
      NSString *familyId = args[1];
      if ([familyId isEqualToString:@"空对象"]) {
          familyId = nil;
      }
      else if ([familyId isEqualToString:@"空字符串"]) {
          familyId = @"";
      }
      NSString *memberName = args[2];
      if ([memberName isEqualToString:@"空对象"]) {
          memberName = nil;
      }
      else if ([memberName isEqualToString:@"空字符串"]) {
          memberName = @"";
      }
      BOOL isAgree = [args[3] isEqualToString:@"是"];
      [[UserdomainHolderTest getInstance]
              .userdomain.user replyFamilyInvite:code
          familyId:familyId
          memberName:memberName
          agree:isAgree
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.replyFamilyResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.replyFamilyResult = result.success;
          }];

    });
    Then(@"^回复家庭邀请回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.replyFamilyResult == [result isEqualToString:@"成功"], @"回复家庭邀请回调结果与实际不一致");
    });
    Then(@"^家庭数据源回复家庭邀请接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource replyFamilyInviteWithCode:[OCMArg any] familyId:[OCMArg any] memberName:[OCMArg any] agree:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源回复家庭邀请接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource replyFamilyInviteWithCode:[OCMArg any] familyId:[OCMArg any] memberName:[OCMArg any] agree:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:6 retCode:nil retData:nil]).ignoringNonObjectArgs();
    });
    Then(@"^家庭数据源回复家庭邀请接口被调用\"([^\"]*)\"次,参数邀请码为\"([^\"]*)\",家庭id为\"([^\"]*)\",成员名称\"([^\"]*)\",是否同意\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *code = args[1];
      NSString *familyId = args[2];
      NSString *memberName = args[3];
      BOOL isAgree = [args[4] isEqualToString:@"是"];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource replyFamilyInviteWithCode:code familyId:familyId memberName:memberName agree:isAgree success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^家庭数据源的设置默认家庭接口被调用\"([^\"]*)\"次,参数家庭id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *familyId = args[1];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource setDefaultFamily:familyId success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的刷新家庭详细信息接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource queryFamilyInfo:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:3 retCode:nil retData:nil]);
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的刷新家庭详细信息接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family queryInfoSuccess:^(UserDomainSampleResult *_Nonnull result) {
        self.refreshFamliyDetailResult = result.success;
        self.familyInfo = result.retData;
      }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.refreshFamliyDetailResult = result.success;
          }];
    });
    Then(@"^使用者收到刷新家庭详细信息接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.refreshFamliyDetailResult == [result isEqualToString:@"成功"], @"刷新家庭详细信息回调结果与实际不一致");
    });
    Then(@"^使用者收到刷新家庭详细信息接口的回调结果为\"([^\"]*)\",家庭详细信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.refreshFamliyDetailResult == [result isEqualToString:@"成功"], @"刷新家庭详细信息回调结果与实际不一致");
      NSArray<Family *> *familyList = getFamilyListFromExpectUserInfo(userInfo);
      NSArray *arr = [NSArray arrayWithObject:self.familyInfo];
      CCIAssert(isEqualUserFamilyList(arr, familyList), @"家庭详情应该相同，实际不一致");
    });
    Then(@"^家庭数据源的刷新家庭详细信息接口被调用\"([^\"]*)\"次,入参家庭ID的值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *familyId = args[1];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource queryFamilyInfo:familyId success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的刷新家庭详细信息接口返回的数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<Family *> *familyList = getFamilyListFromExpectUserInfo(userInfo);
      [OCMStub([FamilyDataSource queryFamilyInfo:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        [invocation getArgument:&finishBlockPointer atIndex:3];
        void (^userDomainCallback)(UserDomainSampleResult *result);
        userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
        UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
        result.retData = familyList.firstObject;
        userDomainCallback(result);
      }];

    });
    Given(@"^家庭数据源的刷新家庭详细信息接口\"([^\"]*)\"秒后返回的数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSTimeInterval waitTime = [args[0] floatValue];
      NSArray<Family *> *familyList = getFamilyListFromExpectUserInfo(userInfo);
      [OCMStub([FamilyDataSource queryFamilyInfo:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        if (waitTime) {
            [NSThread sleepForTimeInterval:waitTime];
        }
        [invocation retainArguments];
        void *finishBlockPointer;
        [invocation getArgument:&finishBlockPointer atIndex:3];
        void (^userDomainCallback)(UserDomainSampleResult *result);
        userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
        UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
        result.retData = familyList.firstObject;
        userDomainCallback(result);
      }];
    });
    When(@"^线程\"([^\"]*)\",调用ID为\"([^\"]*)\"的家庭对象的刷新家庭详细信息接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      NSString *familyId = args[1];
      dispatch_async(dispatch_get_global_queue(0, 0), ^{
        id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
        [family queryInfoSuccess:^(UserDomainSampleResult *_Nonnull result) {
          self.mutiCallBackDic[user] = result;
        }
            failure:^(UserDomainSampleResult *_Nonnull result) {
              self.mutiCallBackDic[user] = result;
            }];
      });
    });
    Then(@"^使用者\"([^\"]*)\"收到刷新家庭详细信息接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      NSString *resultStr = args[1];
      UserDomainSampleResult *result = self.mutiCallBackDic[user];
      CCIAssert(result.success == [resultStr isEqualToString:@"成功"], @"刷新家庭详细信息回调结果与实际不一致");
    });

    Then(@"^使用者\"([^\"]*)\"收到刷新家庭详细信息接口的回调结果为\"([^\"]*)\",家庭详情信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      NSString *resultStr = args[1];
      UserDomainSampleResult *result = self.mutiCallBackDic[user];
      CCIAssert(result.success == [resultStr isEqualToString:@"成功"], @"刷新家庭详细信息回调结果与实际不一致");
      NSArray<Family *> *familyList = getFamilyListFromExpectUserInfo(userInfo);
      NSArray *arr = [NSArray arrayWithObject:result.retData];
      CCIAssert(isEqualUserFamilyList(arr, familyList), @"家庭详情应该相同，实际不一致");
    });

    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的添加家庭房间接口,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      RoomArgs *roomArgs = [[RoomArgs alloc] init];
      roomArgs.floorId = array.firstObject[0];
      roomArgs.name = array.firstObject[1];
      roomArgs.type = array.firstObject[2];
      roomArgs.label = array.firstObject[3];
      roomArgs.logo = array.firstObject[4];
      roomArgs.image = array.firstObject[5];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family addRoom:roomArgs
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.createRoomResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.createRoomResult = result.success;
          }];
    });
    Then(@"^使用者收到添加家庭房间接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.createRoomResult == [result isEqualToString:@"成功"], @"添加家庭房间接口的回调结果与实际不一致");
    });
    Then(@"^家庭数据源的添加家庭房间接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource addRoomForFamily:[OCMArg any] familyId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的添加房间接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([FamilyDataSource addRoomForFamily:[OCMArg any] familyId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        [invocation getArgument:&finishBlockPointer atIndex:2];
        RoomArgs *args = (__bridge RoomArgs *)finishBlockPointer;
        self.actualRoomArgs = args;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:5];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    Then(@"^家庭数据源的添加家庭房间接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;

      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      RoomArgs *roomArgs = [[RoomArgs alloc] init];
      roomArgs.floorId = array.firstObject[1];
      roomArgs.name = array.firstObject[2];
      roomArgs.type = array.firstObject[3];
      roomArgs.label = array.firstObject[4];
      roomArgs.logo = array.firstObject[5];
      roomArgs.image = array.firstObject[6];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource addRoomForFamily:[OCMArg any] familyId:familyId success:[OCMArg any] failure:[OCMArg any]]);
      CCIAssert(isEqualRoomArgsInfo(roomArgs, self.actualRoomArgs), @"房间参数应该相同，实际不相同");
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的删除家庭房间接口,删除的房间ID为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSString *roomId = args[1];
      if ([roomId isEqualToString:@"空对象"]) {
          roomId = nil;
      }
      else if ([roomId isEqualToString:@"空字符串"]) {
          roomId = @"";
      }
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family removeRoom:roomId
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.deleteRoomResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.deleteRoomResult = result.success;
          }];
    });
    Then(@"^使用者收到删除房间接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.deleteRoomResult == [result isEqualToString:@"成功"], @"删除房间接口的回调结果与实际不一致");
    });
    Then(@"^家庭数据源的删除房间接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource removeRoomFromFamily:[OCMArg any] roomId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的删除房间接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource removeRoomFromFamily:[OCMArg any] roomId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:4 retCode:nil retData:nil]);
    });
    Then(@"^家庭数据源的删除房间接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      NSString *roomId = array.firstObject[1];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource removeRoomFromFamily:familyId roomId:roomId success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^家庭ID为\"([^\"]*)\"的家庭对象房间列表数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      NSDictionary<NSString *, NSArray<Room *> *> *roomList = getRoomListFromExpectUserInfo(userInfo);
      [roomList enumerateKeysAndObjectsUsingBlock:^(NSString *_Nonnull key, NSArray<Room *> *_Nonnull obj, BOOL *_Nonnull stop) {
        for (UDFloorInfo *info in family.floorInfos) {
            if ([info.floorId isEqualToString:key]) {
                CCIAssert(isEqualRoomList(info.rooms, obj), @"家庭对象房间列表数据应该相等，实际不相等");
            }
        }
      }];
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的编辑家庭房间名称接口,编辑房间ID为\"([^\"]*)\"的房间名称为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSString *roomId = args[1];
      NSString *roomName = args[2];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family updateRoomName:roomName
          roomId:roomId
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.editRoomResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.editRoomResult = result.success;
          }];
    });
    Then(@"^使用者收到编辑房间名称接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.editRoomResult == [result isEqualToString:@"成功"], @"编辑房间名称接口的回调结果与实际不一致");
    });
    Then(@"^家庭数据源的编辑房间名称接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource updateFamilyRoomName:[OCMArg any] roomId:[OCMArg any] roomName:[OCMArg any] floorId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的编辑房间名称接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource updateFamilyRoomName:[OCMArg any] roomId:[OCMArg any] roomName:[OCMArg any] floorId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:6 retCode:nil retData:nil]);
    });
    Then(@"^家庭数据源的编辑房间名称接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      NSString *floorId = array.firstObject[1];
      NSString *roomId = array.firstObject[2];
      NSString *roomName = array.firstObject[3];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource updateFamilyRoomName:familyId roomId:roomId roomName:roomName floorId:floorId success:[OCMArg any] failure:[OCMArg any]]);
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的邀请家庭成员接口,邀请手机号为\"([^\"]*)\"的用户加入家庭$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSString *phoneNum = args[1];
      if ([phoneNum isEqualToString:@"空对象"]) {
          phoneNum = nil;
      }
      else if ([phoneNum isEqualToString:@"空字符串"]) {
          phoneNum = @"";
      }
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family adminInvitateMember:phoneNum
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.invitateMemberResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.invitateMemberResult = result.success;
          }];
    });
    Then(@"^使用者收到邀请家庭成员接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.invitateMemberResult == [result isEqualToString:@"成功"], @"编辑房间名称接口的回调结果与实际不一致");
    });
    Then(@"^家庭数据源的邀请家庭成员接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource adminInvitateMember:[OCMArg any] familyId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的邀请家庭成员接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource adminInvitateMember:[OCMArg any] familyId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:4 retCode:nil retData:nil]);
    });
    Then(@"^家庭数据源的邀请家庭成员接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      NSString *phoneNum = array.firstObject[1];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource adminInvitateMember:phoneNum familyId:familyId success:[OCMArg any] failure:[OCMArg any]]);
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的删除家庭成员接口,删除ID为\"([^\"]*)\"的家庭成员$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSString *memberId = args[1];
      if ([memberId isEqualToString:@"空对象"]) {
          memberId = nil;
      }
      else if ([memberId isEqualToString:@"空字符串"]) {
          memberId = @"";
      }
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family adminDeleteMember:memberId
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.deleteMemberResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.deleteMemberResult = result.success;
          }];
    });
    Then(@"^使用者收到删除家庭成员接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.deleteMemberResult == [result isEqualToString:@"成功"], @"删除家庭成员接口的回调结果与实际不一致");
    });
    Then(@"^家庭数据源的删除家庭成员接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource deleteFamilyMemberAsAdmin:[OCMArg any] memberId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的删除家庭成员接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource deleteFamilyMemberAsAdmin:[OCMArg any] memberId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:4 retCode:nil retData:nil]);
    });
    Then(@"^家庭数据源的删除家庭成员接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      NSString *memberId = array.firstObject[1];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource deleteFamilyMemberAsAdmin:familyId memberId:memberId success:[OCMArg any] failure:[OCMArg any]]);
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的批量解绑设备接口,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSArray<Device *> *array = getDeviceListFromExpectUserInfo(userInfo);
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family unBindDevices:array
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.unbindDevicesResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.unbindDevicesResult = result.success;
          }];
    });
    Then(@"^使用者收到批量解绑设备接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.unbindDevicesResult == [result isEqualToString:@"成功"], @"批量解绑设备接口的回调结果与实际不一致");
    });
    Then(@"^家庭数据源的批量解绑设备接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource unBindFamilyDevices:[OCMArg any] devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的批量解绑设备接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([FamilyDataSource unBindFamilyDevices:[OCMArg any] devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        [invocation getArgument:&finishBlockPointer atIndex:3];
        NSArray *devices = (__bridge NSArray *)finishBlockPointer;
        [self.actualDeviceList addObjectsFromArray:devices];
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            CloudBatchProcessDevicesResponse *res = [[CloudBatchProcessDevicesResponse alloc] init];
            NSMutableArray *array = [NSMutableArray array];
            for (id<UDDeviceDelegate> device in devices) {
                CloudBatchProcessDevice *cloudDevice = [[CloudBatchProcessDevice alloc] init];
                cloudDevice.deviceId = device.deviceId;
                cloudDevice.deviceName = device.deviceName;
                [array addObject:cloudDevice];
            }
            res.successDevices = array;
            result.retData = res;
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:5];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    Then(@"^家庭数据源的批量解绑设备接口被调用\"([^\"]*)\"次,参数家庭id为\"([^\"]*)\",设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *familyId = args[1];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource unBindFamilyDevices:familyId devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
      NSArray<Device *> *deviceList = getDeviceListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserDeviceList(deviceList, self.actualDeviceList), @"家庭数据源的批量解绑设备接口参数与实际不一致");
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的批量移动设备到新房间接口,房间ID为\"([^\"]*)\",设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSString *roomId = args[1];
      NSArray<Device *> *devices = getDeviceListFromExpectUserInfo(userInfo);
      Room *room = [[Room alloc] init];
      room.realRoomId = roomId;

      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family moveDevicesToOtherRoom:room
          devices:devices
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.moveDevicesToRoomResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.moveDevicesToRoomResult = result.success;
          }];
    });
    Then(@"^使用者收到批量移动设备到新房间接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.moveDevicesToRoomResult == [result isEqualToString:@"成功"], @"批量移动设备到新房间结果与实际不一致");
    });
    Then(@"^家庭数据源的批量移动设备到新房间接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource moveDevicesToOtherRoom:[OCMArg any] newRoom:[OCMArg any] devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的批量移动设备到新房间接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([FamilyDataSource moveDevicesToOtherRoom:[OCMArg any] newRoom:[OCMArg any] devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        [invocation getArgument:&finishBlockPointer atIndex:4];
        NSArray *devices = (__bridge NSArray *)finishBlockPointer;
        [self.actualDeviceList addObjectsFromArray:devices];
        [invocation getArgument:&finishBlockPointer atIndex:3];
        Room *room = (__bridge Room *)finishBlockPointer;
        self.actualRoom = room;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:5];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:6];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    Then(@"^家庭数据源的批量移动设备到新房间接口被调用\"([^\"]*)\"次,参数家庭id为\"([^\"]*)\",房间ID为\"([^\"]*)\",设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<Device *> *deviceList = getDeviceListFromExpectUserInfo(userInfo);
      NSString *familyId = args[1];
      NSString *roomId = args[2];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource moveDevicesToOtherRoom:familyId newRoom:[OCMArg any] devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
      CCIAssert([roomId isEqualToString:self.actualRoom.roomId] && isEqualUserDeviceList(deviceList, self.actualDeviceList), @"家庭数据源的批量移动设备到新房间接口参数应该相等实际不相等");
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的批量移除设备接口,设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSArray *devices = getDeviceListFromExpectUserInfo(userInfo);
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family removeDevices:devices
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.removeDevicesResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.removeDevicesResult = result.success;
          }];
    });
    Then(@"^使用者收到批量移除设备接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.removeDevicesResult == [result isEqualToString:@"成功"], @"批量移除设备接口的回调结果与实际不一致");
    });
    Then(@"^家庭数据源的批量移除设备接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource removeDevicesFromFamily:[OCMArg any] devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的批量移除设备接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([FamilyDataSource removeDevicesFromFamily:[OCMArg any] devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        [invocation getArgument:&finishBlockPointer atIndex:3];
        NSArray *devices = (__bridge NSArray *)finishBlockPointer;
        [self.actualDeviceList addObjectsFromArray:devices];
        void (^userDomainCallback)(UserDomainSampleResult *result);
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:5];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    Then(@"^家庭数据源的批量移除设备接口被调用\"([^\"]*)\"次,参数家庭id为\"([^\"]*)\",参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *familyId = args[1];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource removeDevicesFromFamily:familyId devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
      NSArray<Device *> *array = getDeviceListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserDeviceList(array, self.actualDeviceList), @"家庭数据源的批量移除设备接口参数应该相等实际不相等");
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的移动设备到新的家庭接口,参数家庭id为\"([^\"]*)\",设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSArray *devices = getDeviceListFromExpectUserInfo(userInfo);
      NSString *newFamilyId = args[1];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family moveDevicesToOtherFamily:newFamilyId
          devices:devices
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.moveDevicesToFamilyResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.moveDevicesToFamilyResult = result.success;
          }];
    });
    Then(@"^使用者收到批量移动设备到新的家庭接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.moveDevicesToFamilyResult == [result isEqualToString:@"成功"], @"批量移动设备到新的家庭接口的回调结果与实际不一致");
    });
    Then(@"^家庭数据源的批量移动设备到新的家庭接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource moveDevicesToOtherFamily:[OCMArg any] oldFamilyId:[OCMArg any] devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的批量移动设备到新的家庭接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([FamilyDataSource moveDevicesToOtherFamily:[OCMArg any] oldFamilyId:[OCMArg any] devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        [invocation getArgument:&finishBlockPointer atIndex:4];
        NSArray *devices = (__bridge NSArray *)finishBlockPointer;
        [self.actualDeviceList addObjectsFromArray:devices];
        void (^userDomainCallback)(UserDomainSampleResult *result);
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:5];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:6];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    Then(@"^家庭数据源的批量移动设备到新的家庭接口被调用\"([^\"]*)\"次,原家庭id为\"([^\"]*)\",新家庭id为\"([^\"]*)\",设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSString *newFamilyId = args[2];
      NSString *oldFamilyId = args[1];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource moveDevicesToOtherFamily:newFamilyId oldFamilyId:oldFamilyId devices:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
      NSArray *deviceList = getDeviceListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserDeviceList(deviceList, self.actualDeviceList), @"家庭数据源的批量移动设备到新的家庭接口参数应该相等实际不相等");
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的管理员退出家庭接口,指定新管理员的用户ID为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSString *userId = args[1];
      if ([userId isEqualToString:@"空对象"]) {
          userId = nil;
      }
      else if ([userId isEqualToString:@"空字符串"]) {
          userId = @"";
      }
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family exitFamilyAsAdmin:userId
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.exitFamilyAsAdminResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.exitFamilyAsAdminResult = result.success;
          }];
    });
    Then(@"^使用者收到管理员退出家庭接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.exitFamilyAsAdminResult == [result isEqualToString:@"成功"], @"管理员退出家庭接口的回调结果与实际不一致");
    });
    Then(@"^家庭数据源的管理员退出家庭接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource exitFamilyAsAdmin:[OCMArg any] userId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的管理员退出家庭接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource exitFamilyAsAdmin:[OCMArg any] userId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:4 retCode:nil retData:nil]);
    });
    Then(@"^家庭数据源的管理员退出家庭接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      NSString *userId = array.firstObject[1];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource exitFamilyAsAdmin:familyId userId:userId success:[OCMArg any] failure:[OCMArg any]]);

    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的家庭管理员删除家庭接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family destoryFamilyAsAdminSuccess:^(UserDomainSampleResult *_Nonnull result) {
        self.deleteFamilyResult = result.success;
      }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.deleteFamilyResult = result.success;
          }];
    });
    Then(@"^使用者收到家庭管理员删除家庭接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.deleteFamilyResult == [result isEqualToString:@"成功"], @"家庭管理员删除家庭接口的回调结果与实际不一致");
    });
    Given(@"^家庭数据源的管理员删除家庭接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource destoryFamilyAsAdmin:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:3 retCode:nil retData:nil]);
    });
    Then(@"^家庭数据源的管理员删除家庭接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource destoryFamilyAsAdmin:familyId success:[OCMArg any] failure:[OCMArg any]]);
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的更换家庭管理员接口,指定新管理员的用户ID为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSString *userId = args[1];
      if ([userId isEqualToString:@"空对象"]) {
          userId = nil;
      }
      else if ([userId isEqualToString:@"空字符串"]) {
          userId = @"";
      }
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family changeFamilyAdminUserId:userId
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.changeFamilyAdminResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.changeFamilyAdminResult = result.success;
          }];
    });
    Then(@"^使用者收到更换家庭管理员接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.changeFamilyAdminResult == [result isEqualToString:@"成功"], @"更换家庭管理员接口的回调结果与实际不一致");
    });
    Then(@"^家庭数据源的更换家庭管理员接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource changeFamilyAdmin:[OCMArg any] userId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的更换家庭管理员接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource changeFamilyAdmin:[OCMArg any] userId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:4 retCode:nil retData:nil]);
    });
    Then(@"^家庭数据源的更换家庭管理员接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      NSString *userId = array.firstObject[1];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource changeFamilyAdmin:familyId userId:userId success:[OCMArg any] failure:[OCMArg any]]);
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的编辑家庭信息接口,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      FamilyArgs *familyArgs = [[FamilyArgs alloc] init];
      familyArgs.name = array.firstObject[0];
      NSDictionary *dict = [array.firstObject[1] mj_JSONObject];
      familyArgs.cityCode = dict[@"cityCode"];
      familyArgs.latitude = dict[@"lat"];
      familyArgs.longitude = dict[@"lon"];
      familyArgs.position = array.firstObject[2];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family updateFamilyInfo:familyArgs
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.editFamilyInfoResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.editFamilyInfoResult = result.success;
          }];
    });
    Then(@"^使用者收到编辑家庭信息接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.editFamilyInfoResult == [result isEqualToString:@"成功"], @"编辑家庭信息接口的回调结果为与实际不一致");
    });
    Then(@"^家庭数据源的编辑家庭信息接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource updateFamilyInfo:[OCMArg any] familyId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的编辑家庭信息接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([FamilyDataSource updateFamilyInfo:[OCMArg any] familyId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        [invocation getArgument:&finishBlockPointer atIndex:2];
        FamilyArgs *familyArgs = (__bridge FamilyArgs *)finishBlockPointer;
        self.actualFamilyArgs = familyArgs;
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:5];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    Then(@"^家庭数据源的编辑家庭信息接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource updateFamilyInfo:[OCMArg any] familyId:familyId success:[OCMArg any] failure:[OCMArg any]]);
      FamilyArgs *familyArgs = [[FamilyArgs alloc] init];
      familyArgs.name = array.firstObject[1];
      NSDictionary *dict = [array.firstObject[2] mj_JSONObject];
      familyArgs.cityCode = dict[@"cityCode"];
      familyArgs.latitude = dict[@"lat"];
      familyArgs.longitude = dict[@"lon"];
      familyArgs.position = array.firstObject[3];
      CCIAssert(isEqualFamilyArgsInfo(familyArgs, self.actualFamilyArgs), @"家庭数据源的编辑家庭信息接口应该相等实际不相等");
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的刷新家庭房间列表接口,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSString *floorId = args[1];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family queryRoomList:floorId
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.refreshRoomListResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.refreshRoomListResult = result.success;
          }];
    });
    Then(@"^使用者收到刷新家庭房间列表接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.refreshRoomListResult == [result isEqualToString:@"成功"], @"刷新家庭房间列表接口的回调结果与实际不一致");
    });
    Given(@"^家庭数据源的刷新家庭房间列表接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([FamilyDataSource queryRoomListOfFamily:[OCMArg any] floorId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        [invocation getArgument:&finishBlockPointer atIndex:2];
        NSString *familyId = (__bridge NSString *)finishBlockPointer;
        [invocation getArgument:&finishBlockPointer atIndex:3];
        NSString *floorId = (__bridge NSString *)finishBlockPointer;
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            NSArray<UDFloorInfo *> *floors = self.relationFloorDic[familyId];
            for (UDFloorInfo *floorInfo in floors) {
                if ([floorInfo.floorId isEqualToString:floorId]) {
                    result.retData = floorInfo.rooms;
                    break;
                }
            }
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:5];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    Then(@"^家庭数据源的刷新家庭房间列表接口被调用\"([^\"]*)\"次,家庭ID为\"([^\"]*)\",楼层ID为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *familyId = args[1];
      NSString *floorId = args[2];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource queryRoomListOfFamily:familyId floorId:floorId success:[OCMArg any] failure:[OCMArg any]]);
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的成员退出家庭接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family exitFamilyAsMemberSuccess:^(UserDomainSampleResult *_Nonnull result) {
        self.exitFamilyAsMemberResult = result.success;
      }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.exitFamilyAsMemberResult = result.success;
          }];
    });
    Then(@"^使用者收到成员退出家庭接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.exitFamilyAsMemberResult == [result isEqualToString:@"成功"], @"成员退出家庭接口的回调结果与实际不一致");
    });
    Given(@"^家庭数据源的家庭成员退出家庭接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource exitFamilyAsMember:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:3 retCode:nil retData:nil]);
    });
    Then(@"^家庭数据源的家庭成员退出家庭接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource exitFamilyAsMember:familyId success:[OCMArg any] failure:[OCMArg any]]);
    });

    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的创建楼层接口,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      FloorArg *floorArg = [[FloorArg alloc] init];
      floorArg.floorOrderId = array.firstObject[0];
      floorArg.floorName = array.firstObject[1];
      floorArg.floorClass = array.firstObject[2];
      floorArg.floorLabel = array.firstObject[3];
      floorArg.floorLogo = array.firstObject[4];
      floorArg.floorPicture = array.firstObject[5];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];

      [family createFloor:floorArg
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.createFloorResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.createFloorResult = result.success;
          }];
    });
    Then(@"^使用者收到创建楼层接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.createFloorResult == [result isEqualToString:@"成功"], @"家庭对象的创建楼层接口的回调结果与实际不一致");
    });
    Then(@"^家庭数据源的创建楼层接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource addFloorOfFamily:[OCMArg any] floorArg:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的创建楼层接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([FamilyDataSource addFloorOfFamily:[OCMArg any] floorArg:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        [invocation getArgument:&finishBlockPointer atIndex:3];
        self.actualFloorArg = (__bridge FloorArg *)finishBlockPointer;
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:5];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    Then(@"^家庭数据源的创建楼层接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource addFloorOfFamily:familyId floorArg:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
      FloorArg *floorArg = [[FloorArg alloc] init];
      floorArg.floorOrderId = array.firstObject[1];
      floorArg.floorName = array.firstObject[2];
      floorArg.floorClass = array.firstObject[3];
      floorArg.floorLabel = array.firstObject[4];
      floorArg.floorLogo = array.firstObject[5];
      floorArg.floorPicture = array.firstObject[6];
      CCIAssert(isEqualFloorArgInfo(floorArg, self.actualFloorArg), @"楼层参数应该相同，实际不相同");
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的删除楼层接口,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *floorId = array.firstObject[0];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family deleteFloor:floorId
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.deleteFloorResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.deleteFloorResult = result.success;
          }];
    });
    Then(@"^使用者收到删除楼层接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.deleteFloorResult == [result isEqualToString:@"成功"], @"删除楼层接口的回调结果与实际不一致");
    });
    Then(@"^家庭数据源的删除楼层接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource deleteFloorOfFamily:[OCMArg any] floorId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的删除楼层接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource deleteFloorOfFamily:[OCMArg any] floorId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:4 retCode:nil retData:nil]);
    });
    Then(@"^家庭数据源的删除楼层接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      NSString *floorId = array.firstObject[1];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource deleteFloorOfFamily:familyId floorId:floorId success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^家庭ID为\"([^\"]*)\"的家庭对象楼层列表数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      NSArray *array = getFloorInfoListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualFloorList(array, family.floorInfos), @"家庭对象楼层列表数据应该相同，实际不相同");
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的编辑楼层接口,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      UDFloorInfo *floorInfo = [[UDFloorInfo alloc] init];
      floorInfo.realFloorId = array.firstObject[0];
      floorInfo.realFloorOrderId = array.firstObject[1];
      floorInfo.realFloorName = array.firstObject[2];
      floorInfo.realFloorClass = array.firstObject[3];
      floorInfo.realFloorLabel = array.firstObject[4];
      floorInfo.realFloorLogo = array.firstObject[5];
      floorInfo.realFloorPicture = array.firstObject[6];
      FloorArg *floorArg = [[FloorArg alloc] initFloorArg:floorInfo];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];

      [family editFloor:floorArg
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.editFloorResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.editFloorResult = result.success;
          }];
    });
    Then(@"^使用者收到编辑楼层接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.editFloorResult == [result isEqualToString:@"成功"], @"家庭对象的编辑楼层接口的回调结果为与实际不一致");
    });
    Then(@"^家庭数据源的编辑楼层接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource editFloorOfFamily:[OCMArg any] floorArg:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的编辑楼层接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([FamilyDataSource editFloorOfFamily:[OCMArg any] floorArg:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        [invocation getArgument:&finishBlockPointer atIndex:3];
        self.actualFloorArg = (__bridge FloorArg *)finishBlockPointer;
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:5];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    Then(@"^家庭数据源的编辑楼层接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource editFloorOfFamily:familyId floorArg:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
      FloorArg *floorArg = [[FloorArg alloc] init];
      [floorArg setValue:array.firstObject[1] forKey:@"floorId"];
      floorArg.floorOrderId = array.firstObject[2];
      floorArg.floorName = array.firstObject[3];
      floorArg.floorClass = array.firstObject[4];
      floorArg.floorLabel = array.firstObject[5];
      floorArg.floorLogo = array.firstObject[6];
      floorArg.floorPicture = array.firstObject[7];
      CCIAssert(isEqualFloorArgInfo(floorArg, self.actualFloorArg), @"楼层参数应该相同，实际不相同");
    });

    When(@"^执行回复加入家庭申请方法,参数申请码为\"([^\"]*)\",是否同意\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *codeStr = args[0];
      if ([codeStr isEqualToString:@"空对象"]) {
          codeStr = nil;
      }
      else if ([codeStr isEqualToString:@"空字符串"]) {
          codeStr = @"";
      }
      BOOL isAgree = [args[1] isEqualToString:@"是"];
      [[UserdomainHolderTest getInstance]
              .userdomain.user replyJoinFamily:codeStr
          agree:isAgree
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.replyJoinFamilyResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.replyJoinFamilyResult = result.success;
          }];
    });
    Then(@"^回复加入家庭申请回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.replyJoinFamilyResult == [result isEqualToString:@"成功"], @"执行结果与实际不一致");
    });
    Then(@"^家庭数据源回复加入家庭申请接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource replyJoinFamily:[OCMArg any] agree:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^家庭数据源回复加入家庭申请接口被调用\"([^\"]*)\"次,参数申请码为\"([^\"]*)\",是否同意\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *code = args[1];
      BOOL isAgree = [args[2] isEqualToString:@"是"];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource replyJoinFamily:code agree:isAgree success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源回复加入家庭申请接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource replyJoinFamily:[OCMArg any] agree:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]])
          .andDo([StepsUtils actionHandler:args location:4 retCode:nil retData:nil])
          .ignoringNonObjectArgs;
    });

    Given(@"^家庭数据源的查询第一个加入家庭的成员接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([FamilyDataSource queryFirstMemeber:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        [invocation getArgument:&finishBlockPointer atIndex:2];
        NSString *familyId = (__bridge NSString *)finishBlockPointer;
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:3];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            FamilyMember *memberModel = self.relationFirstMemberDic[familyId];
            result.retData = memberModel;
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的查询第一个加入家庭的成员接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family queryFirstMemeberSuccess:^(UserDomainSampleResult *_Nonnull result) {
        self.queryFirstMemebeResult = result.success;
        self.firstMember = result.retData;
      }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.queryFirstMemebeResult = result.success;
          }];
    });
    Then(@"^使用者收到查询第一个加入家庭的成员接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.queryFirstMemebeResult == [result isEqualToString:@"成功"], @"家庭对象的查询第一个加入家庭的成员接口的回调结果为与实际不一致");
    });
    Then(@"^家庭数据源的查询第一个加入家庭的成员接口被调用\"([^\"]*)\"次,参数家庭id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *familyId = args[1];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource queryFirstMemeber:familyId success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭\"([^\"]*)\"的第一个加入家庭的成员信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      FamilyMember *firstMember = getFamilyFirstMemberFromExpectUserInfo(userInfo);
      [self.relationFirstMemberDic setValue:firstMember forKey:familyId];
    });
    Then(@"^家庭ID为\"([^\"]*)\"的第一个加入家庭的成员信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      FamilyMember *expecteFirstMember = getFamilyFirstMemberFromExpectUserInfo(userInfo);
      CCIAssert([expecteFirstMember.memberName isEqualToString:self.firstMember.memberName], @"第一个加入家庭的成员信息与实际不一致");
      CCIAssert([expecteFirstMember.memberInfo.userId isEqualToString:self.firstMember.memberInfo.userId], @"第一个加入家庭的成员信息与实际不一致");
      CCIAssert([expecteFirstMember.memberInfo.name isEqualToString:self.firstMember.memberInfo.name], @"第一个加入家庭的成员信息与实际不一致");
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的虚拟用户加入家庭接口,成员ID为\"([^\"]*)\",成员名称为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSString *memberId = args[1];
      NSString *memberName = args[2];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family addVirtualMember:memberId
          memberName:memberName
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.addVirtualMemberResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.addVirtualMemberResult = result.success;
          }];
    });
    Then(@"^使用者收到虚拟用户加入家庭接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.addVirtualMemberResult == [result isEqualToString:@"成功"], @"家庭对象的虚拟用户加入家庭接口接口的回调结果为与实际不一致");
    });
    Then(@"^家庭数据源的虚拟用户加入家庭接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource addVirtualMember:[OCMArg any] memberId:[OCMArg any] memberName:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Given(@"^家庭数据源的虚拟用户加入家庭接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource addVirtualMember:[OCMArg any] memberId:[OCMArg any] memberName:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:5 retCode:nil retData:nil]);
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的虚拟用户加入家庭接口,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = args[0];
      NSString *memberId = array.firstObject[0];
      NSString *memberName = array.firstObject[1];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family addVirtualMember:memberId
          memberName:memberName
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.addVirtualMemberResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.addVirtualMemberResult = result.success;
          }];
    });
    Then(@"^家庭数据源的虚拟用户加入家庭接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      NSString *memberId = array.firstObject[1];
      NSString *memberName = array.firstObject[2];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource addVirtualMember:familyId memberId:memberId memberName:memberName success:[OCMArg any] failure:[OCMArg any]]);
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的编辑虚拟用户接口,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = args[0];
      VirtualMemberArgs *virtualMember = [[VirtualMemberArgs alloc] init];
      virtualMember.memberId = array.firstObject[0];
      virtualMember.memberName = array.firstObject[1];
      virtualMember.avatarUrl = array.firstObject[2];
      virtualMember.birthday = array.firstObject[4];
      virtualMember.isCreater = YES;
      if ([array.firstObject[3] isEqualToString:@"false"]) {
          virtualMember.isCreater = NO;
      }
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family modifyVirtualMember:virtualMember
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.modifyVirtualMemberResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.modifyVirtualMemberResult = result.success;
          }];
    });

    Given(@"^家庭数据源的编辑家庭成员身份接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource modifyMemberRoleOfFamily:[OCMArg any] memberId:[OCMArg any] memberRole:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:5 retCode:nil retData:nil]);
    });
    Given(@"^家庭数据源的编辑家庭虚拟成员身份接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([FamilyDataSource modifyVirtualMemberRoleOfFamily:[OCMArg any] memberId:[OCMArg any] memberRole:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:5 retCode:nil retData:nil]);
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的编辑家庭成员身份接口,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = args[0];
      NSString *memberId = array.firstObject[0];
      NSString *memberRole = array.firstObject[1];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family modifyMemberRole:memberId
          memberRole:memberRole
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.modifyMemberRoleResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.modifyMemberRoleResult = result.success;
          }];
    });
    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的编辑家庭虚拟成员身份接口,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = args[0];
      NSString *memberId = array.firstObject[0];
      NSString *memberRole = array.firstObject[1];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family modifyVirtualMemberRole:memberId
          memberRole:memberRole
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.modifyVirtualMemberRoleResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.modifyVirtualMemberRoleResult = result.success;
          }];
    });
    Then(@"^家庭数据源的编辑家庭成员身份接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource modifyMemberRoleOfFamily:[OCMArg any] memberId:[OCMArg any] memberRole:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^家庭数据源的编辑家庭虚拟成员身份接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource modifyVirtualMemberRoleOfFamily:[OCMArg any] memberId:[OCMArg any] memberRole:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });

    Then(@"^使用者收到编辑家庭成员身份接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.modifyMemberRoleResult == [result isEqualToString:@"成功"], @"家庭对象的编辑虚拟用户接口接口的回调结果为与实际不一致");
    });
    Then(@"^使用者收到编辑家庭虚拟成员身份接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.modifyVirtualMemberRoleResult == [result isEqualToString:@"成功"], @"家庭对象的编辑虚拟用户接口接口的回调结果为与实际不一致");
    });

    Then(@"^家庭数据源的编辑家庭成员身份接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      NSString *memberId = array.firstObject[1];
      NSString *memberRole = array.firstObject[2];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource modifyMemberRoleOfFamily:familyId memberId:memberId memberRole:memberRole success:[OCMArg any] failure:[OCMArg any]]);
    });

    Then(@"^家庭数据源的编辑家庭虚拟成员身份接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      NSString *memberId = array.firstObject[1];
      NSString *memberRole = array.firstObject[2];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource modifyVirtualMemberRoleOfFamily:familyId memberId:memberId memberRole:memberRole success:[OCMArg any] failure:[OCMArg any]]);
    });


    Then(@"^家庭数据源的编辑虚拟用户接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource modifyVirtualMember:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^使用者收到编辑虚拟用户接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.modifyVirtualMemberResult == [result isEqualToString:@"成功"], @"家庭对象的编辑虚拟用户接口接口的回调结果为与实际不一致");
    });
    Given(@"^家庭数据源的编辑虚拟用户接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([FamilyDataSource modifyVirtualMember:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        [invocation getArgument:&finishBlockPointer atIndex:2];
        self.actualAirtualMemberArgs = (__bridge VirtualMemberArgs *)finishBlockPointer;
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:3];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:4];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });

    Then(@"^家庭数据源的编辑虚拟用户接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource modifyVirtualMember:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *memberId = array.firstObject[0];
      NSString *memberName = array.firstObject[1];
      NSString *avatarUrl = array.firstObject[2];
      NSString *birthday = array.firstObject[4];
      BOOL isCreater = YES;
      if ([array.firstObject[3] isEqualToString:@"false"]) {
          isCreater = NO;
      }
      CCIAssert([avatarUrl isEqualToString:self.actualAirtualMemberArgs.avatarUrl], @"家庭成员avatarUrl与实际不一致");
      CCIAssert([memberId isEqualToString:self.actualAirtualMemberArgs.memberId], @"家庭成员memberID与实际不一致");
      CCIAssert([memberName isEqualToString:self.actualAirtualMemberArgs.memberName], @"家庭成员memberName与实际不一致");
      CCIAssert(isCreater == self.actualAirtualMemberArgs.isCreater, @"家庭isCreater与实际不一致");
      CCIAssert([birthday isEqualToString:self.actualAirtualMemberArgs.birthday], @"家庭成员birthday与实际不一致");
    });

    Then(@"^家庭ID为\"([^\"]*)\"的家庭成员列表数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSArray<FamilyMember *> *familyMember = getFamilyMemberListFromExpectUserInfo(userInfo);
      FamilyMember *expetMember = familyMember.firstObject;
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      FamilyMember *actualMember = (FamilyMember *)family.members.firstObject;
      CCIAssert([expetMember.memberInfo.avatarUrl isEqualToString:actualMember.memberInfo.avatarUrl], @"家庭成员avatarUrl与实际不一致");
      CCIAssert([expetMember.memberInfo.name isEqualToString:actualMember.memberInfo.name], @"家庭成员name与实际不一致");
      CCIAssert([expetMember.memberInfo.birthday isEqualToString:actualMember.memberInfo.birthday], @"家庭成员birthday与实际不一致");
      CCIAssert([expetMember.memberRole isEqualToString:actualMember.memberRole], @"家庭成员身份与实际不一致");
    });
    Then(@"^家庭ID为\"([^\"]*)\"的家庭虚拟成员列表数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      NSArray<FamilyMember *> *familyMember = getFamilyMemberListFromExpectUserInfo(userInfo);
      FamilyMember *expetMember = familyMember.firstObject;
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      FamilyMember *actualMember = (FamilyMember *)family.members.firstObject;
      CCIAssert([expetMember.memberInfo.avatarUrl isEqualToString:actualMember.memberInfo.avatarUrl], @"家庭成员avatarUrl与实际不一致");
      CCIAssert([expetMember.memberInfo.name isEqualToString:actualMember.memberInfo.name], @"家庭成员name与实际不一致");
      CCIAssert([expetMember.memberInfo.birthday isEqualToString:actualMember.memberInfo.birthday], @"家庭成员birthday与实际不一致");
      CCIAssert([expetMember.memberRole isEqualToString:actualMember.memberRole], @"家庭成员身份与实际不一致");
    });

    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的保存房间顺序接口,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = args[0];
      NSArray *rooms = convertParaFrom(array.firstObject[0]);
      NSString *floorId = array.firstObject[1];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family saveRoomsOrder:rooms
          floorId:floorId
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.saveRoomOrderResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.saveRoomOrderResult = result.success;
          }];
    });

    Then(@"^家庭数据源的保存房间顺序接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource saveRoomsOrderOfFamily:[OCMArg any] rooms:[OCMArg any] floorId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });

    Then(@"^使用者收到保存房间顺序接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.saveRoomOrderResult == [result isEqualToString:@"成功"], @"家庭对象的保存房间顺序接口的回调结果为与实际不一致");
    });

    Given(@"^家庭数据源的保存房间顺序接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      [OCMStub([FamilyDataSource saveRoomsOrderOfFamily:[OCMArg any] rooms:[OCMArg any] floorId:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        [invocation getArgument:&finishBlockPointer atIndex:3];
        self.actualRoomList = (__bridge NSArray *)finishBlockPointer;
        if ([resultString isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:5];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:6];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      }];
    });

    Then(@"^家庭数据源的保存房间顺序接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      NSArray *rooms = convertParaFrom(array.firstObject[1]);
      NSString *floorId = array.firstObject[2];
      OCMVerify(times(expectedInvocationTimes), [FamilyDataSource saveRoomsOrderOfFamily:familyId rooms:[OCMArg any] floorId:floorId success:[OCMArg any] failure:[OCMArg any]]);
      NSString *rooms1Str = [NSArray mj_keyValuesArrayWithObjectArray:rooms].mj_JSONString;
      NSString *rooms2Str = [NSArray mj_keyValuesArrayWithObjectArray:self.actualRoomList].mj_JSONString;
      CCIAssert([rooms1Str isEqualToString:rooms2Str], @"rooms与实际不一致");
    });
}

#pragma mark - private
- (void (^)(NSInvocation *))actionSuccessHandler:(NSInteger)location retData:(id)retData waitTime:(NSTimeInterval)waitTime
{
    return ^(NSInvocation *invocation) {
      if (waitTime) {
          [NSThread sleepForTimeInterval:waitTime];
      }
      [invocation retainArguments];
      void *finishBlockPointer;
      void (^userDomainCallback)(UserDomainSampleResult *result);
      [invocation getArgument:&finishBlockPointer atIndex:location];
      userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
      UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
      result.retData = retData;
      userDomainCallback(result);
    };
}


@end
