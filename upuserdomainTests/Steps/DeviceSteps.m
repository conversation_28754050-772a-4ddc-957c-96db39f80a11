//
//  DeviceSteps.m
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DeviceSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "UserdomainHolderTest.h"
#import "StepsUtils.h"
#import "User.h"
#import <OCMock/OCMock.h>
#import "UpDeviceListDataSource.h"

#define DeviceDataSource [UserdomainHolderTest getInstance].userdomain.provideDeviceDataSource

@interface DeviceSteps ()
@property (nonatomic, strong) NSMutableDictionary *mutiCallBackDic;
@property (nonatomic, assign) BOOL refreshDeviceListResult;
@property (nonatomic, strong) NSArray<Device *> *deviceList;
@property (nonatomic, assign) BOOL updateDeviceNameResult;
@property (nonatomic, assign) BOOL updateDeviceNameNewResult;
@property (nonatomic, assign) BOOL getGroupDeviceListResult;
@property (nonatomic, strong) NSArray<Device *> *groupDeviceList;
@property (nonatomic, strong) NSMutableDictionary *conditionVariableMap;

@end
@implementation DeviceSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.mutiCallBackDic = [NSMutableDictionary dictionary];
      self.deviceList = nil;
      self.refreshDeviceListResult = NO;
      self.updateDeviceNameResult = NO;
      self.updateDeviceNameNewResult = NO;
      self.getGroupDeviceListResult = NO;
      self.groupDeviceList = [NSArray array];
      self.conditionVariableMap = [NSMutableDictionary dictionary];

    });
    Given(@"^设备数据源的设备列表\"([^\"]*)\"数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      NSArray<Device *> *deviceList = getDeviceListFromExpectUserInfo(userInfo);
      [self.conditionVariableMap setObject:deviceList forKey:result];
    });

    Given(@"^缓存数据代理的设备列表信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<Device *> *deviceList = getDeviceListFromExpectUserInfo(userInfo);
      OCMStub([[UserdomainHolderTest getInstance].cache getObject:@"deviceList"]).andReturn(deviceList);
    });

    Given(@"^设备数据源刷新设备列表接口返回\"([^\"]*)\",返回的设备列表为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *keyStr = args[1];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        if ([args[0] isEqualToString:@"成功"]) {
            [invocation getArgument:&finishBlockPointer atIndex:2];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
            result.retData = self.conditionVariableMap[keyStr];
            userDomainCallback(result);
        }
        else {
            [invocation getArgument:&finishBlockPointer atIndex:3];
            userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
            UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
            userDomainCallback(result);
        }
      };
      [[[(OCMockObject *)DeviceDataSource stub] andDo:proxyBlock] queryDeviceList:[OCMArg any] failure:[OCMArg any]];
    });
    Given(@"^设备数据源刷新设备列表接口返回\"([^\"]*)\",错误码为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *errorCode = args[1];
      OCMStub([DeviceDataSource queryDeviceList:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:2 retCode:errorCode retData:nil]);
    });
    When(@"^执行刷新设备列表方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[UserdomainHolderTest getInstance]
              .userdomain.user refreshDeviceList:^(UserDomainSampleResult *_Nonnull result) {
        self.refreshDeviceListResult = result.success;
        self.deviceList = result.retData;
      }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.refreshDeviceListResult = result.success;
          }];

    });
    Then(@"^刷新设备列表回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.refreshDeviceListResult == [result isEqualToString:@"成功"], @"刷新设备列表回调结果与实际不一致");
    });
    Then(@"^设备数据源刷新设备列表接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [DeviceDataSource queryDeviceList:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^刷新设备列表回调结果为\"([^\"]*)\",设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.refreshDeviceListResult == [result isEqualToString:@"成功"], @"刷新设备列表回调结果与实际不一致");
      NSArray<Device *> *deviceList = getDeviceListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserDeviceList(self.deviceList, deviceList), @"设备列表应该相同，实际不一致");
    });
    Given(@"^设备数据源刷新设备列表接口等待\"([^\"]*)\"秒,返回的设备列表为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSTimeInterval waitTime = [args[0] floatValue];
      NSString *keyStr = args[1];
      [OCMStub([DeviceDataSource queryDeviceList:[OCMArg any] failure:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        if (waitTime) {
            [NSThread sleepForTimeInterval:waitTime];
        }
        [invocation retainArguments];
        void *finishBlockPointer;
        void (^userDomainCallback)(UserDomainSampleResult *result);
        [invocation getArgument:&finishBlockPointer atIndex:2];
        userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
        UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
        result.retData = self.conditionVariableMap[keyStr];
        userDomainCallback(result);
      }];
    });
    When(@"^线程\"([^\"]*)\"中\"([^\"]*)\"执行刷新设备列表方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[1];
      dispatch_async(dispatch_get_global_queue(0, 0), ^{
        [[UserdomainHolderTest getInstance]
                .userdomain.user refreshDeviceList:^(UserDomainSampleResult *_Nonnull result) {
          self.mutiCallBackDic[user] = result;
        }
            failure:^(UserDomainSampleResult *_Nonnull result) {
              self.mutiCallBackDic[user] = result;
            }];
      });
    });
    When(@"^\"([^\"]*)\"秒后线程\"([^\"]*)\"中\"([^\"]*)\"执行\"([^\"]*)\"次刷新设备列表方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[2];
      CGFloat afterTime = [args[0] floatValue];
      NSInteger count = [args[3] integerValue];
      dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(afterTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        dispatch_async(dispatch_get_global_queue(0, 0), ^{
          for (NSInteger i = 0; i < count; i++) {
              [[UserdomainHolderTest getInstance]
                      .userdomain.user refreshDeviceList:^(UserDomainSampleResult *_Nonnull result) {
                self.mutiCallBackDic[user] = result;
              }
                  failure:^(UserDomainSampleResult *_Nonnull result) {
                    self.mutiCallBackDic[user] = result;
                  }];
          }
        });
      });

    });
    Then(@"^\"([^\"]*)\"收到刷新设备列表回调结果为\"([^\"]*)\",设备列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      NSString *resultStr = args[1];
      UserDomainSampleResult *result = self.mutiCallBackDic[user];
      CCIAssert(result.success == [resultStr isEqualToString:@"成功"], @"刷新设备列表回调结果与实际不一致");
      NSArray<Device *> *deviceList = getDeviceListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserDeviceList(result.retData, deviceList), @"设备列表应该相同，实际不一致");
    });
    When(@"^执行修改设备名称方法,修改\"([^\"]*)\"设备,参数设备名称为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *deviceName = args[1];
      if ([deviceName isEqualToString:@"空对象"]) {
          deviceName = nil;
      }
      else if ([deviceName isEqualToString:@"空字符串"]) {
          deviceName = @"";
      }
      Device *device = [[UserdomainHolderTest getInstance]
                            .userdomain.user getDeviceById:deviceId];
      [device updateDeviceName:deviceName
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.updateDeviceNameResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.updateDeviceNameResult = result.success;
          }];

    });
    Then(@"^设备数据源修改设备名称接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [DeviceDataSource updateDeviceNameWithDeviceId:[OCMArg any] oldName:[OCMArg any] newName:[OCMArg any] familyId:[OCMArg any] deviceNetType:[OCMArg any] prodNo:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^修改设备名称回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.updateDeviceNameResult == [result isEqualToString:@"成功"], @"修改设备名称回调结果与实际不一致");
    });
    Then(@"^设备数据源修改设备名称接口被调用\"([^\"]*)\"次,参数设备id为\"([^\"]*)\",旧名称为\"([^\"]*)\",新名称为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *deviceId = args[1];
      NSString *oldName = args[2];
      NSString *newName = args[3];
      OCMVerify(times(expectedInvocationTimes), [DeviceDataSource updateDeviceNameWithDeviceId:deviceId oldName:oldName newName:newName familyId:[OCMArg any] deviceNetType:[OCMArg any] prodNo:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });
    Then(@"^\"([^\"]*)\"设备的名称为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *deviceName = args[1];
      Device *device = [[UserdomainHolderTest getInstance]
                            .userdomain.user getDeviceById:deviceId];
      CCIAssert([device.deviceName isEqualToString:deviceName], @"设备名称与实际不一致，实际为%@，期望为%@", device.deviceName, deviceName);
    });
    Given(@"^设备数据源修改设备名称接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([DeviceDataSource updateDeviceNameWithDeviceId:[OCMArg any] oldName:[OCMArg any] newName:[OCMArg any] familyId:[OCMArg any] deviceNetType:[OCMArg any] prodNo:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:8 retCode:nil retData:nil]);
    });

    Given(@"^设备数据源的获取组设备列表接口执行结果为\"([^\"]*)\",返回组设备列表数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<Device *> *groupDeviceList = getDeviceListFromExpectUserInfo(userInfo);
      OCMStub([DeviceDataSource getGroupDeviceList:[OCMArg any] filterFlag:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:4 retCode:nil retData:groupDeviceList]).ignoringNonObjectArgs();
    });

    When(@"^使用者调用ID为\"([^\"]*)\"的家庭对象的获取组设备列表接口,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *familyId = args[0];
      id<UDFamilyDelegate> family = [[UserdomainHolderTest getInstance].userdomain.user getFamilyById:familyId];
      [family getGroupDeviceListSuccess:^(UserDomainSampleResult *_Nonnull result) {
        self.getGroupDeviceListResult = result.success;
        self.groupDeviceList = result.retData;
      }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.getGroupDeviceListResult = result.success;
          }];
    });

    Then(@"^设备数据源的获取组设备列表接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<NSArray *> *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *familyId = array.firstObject[0];
      NSString *filterFlag = array.firstObject[1];
      OCMVerify(times(expectedInvocationTimes), [DeviceDataSource getGroupDeviceList:familyId filterFlag:[filterFlag isEqualToString:@"true"] success:[OCMArg any] failure:[OCMArg any]]);
    });

    Then(@"^使用者收到获取组设备列表接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.getGroupDeviceListResult == [result isEqualToString:@"成功"], @"家庭对象的获取组设备列表接口的回调结果为与实际不一致");
    });

    Then(@"^家庭ID为\"([^\"]*)\"的家庭组设备列表数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<Device *> *groupDeviceList = getDeviceListFromExpectUserInfo(userInfo);
      CCIAssert(isEqualUserDeviceList(self.groupDeviceList, groupDeviceList), @"组设备列表应该相同，实际不一致");
    });

    [self defineUpdateNameNewStepsAndHocks];
}

///设备改名并校验名称有效性接口
- (void)defineUpdateNameNewStepsAndHocks
{
    When(@"^执行修改并检查设备名称方法,修改\"([^\"]*)\"设备,参数设备名称为\"([^\"]*)\",修改类型为\"([^\"]*)\",检查级别为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *deviceId = args[0];
      NSString *deviceName = args[1];
      NSString *type = args[2];
      BOOL checkLevel = [args[3] boolValue];
      if ([deviceName isEqualToString:@"空对象"]) {
          deviceName = nil;
      }
      else if ([deviceName isEqualToString:@"空字符串"]) {
          deviceName = @"";
      }
      if ([type isEqualToString:@"空对象"]) {
          type = nil;
      }
      else if ([type isEqualToString:@"空字符串"]) {
          type = @"";
      }
      Device *device = [[UserdomainHolderTest getInstance]
                            .userdomain.user getDeviceById:deviceId];

      [device updateDeviceName:deviceName
          checkLevel:checkLevel
          type:type
          success:^(UserDomainSampleResult *_Nonnull result) {
            self.updateDeviceNameNewResult = result.success;
          }
          failure:^(UserDomainSampleResult *_Nonnull result) {
            self.updateDeviceNameNewResult = result.success;
          }];
    });

    Then(@"^修改并检查设备名称回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      CCIAssert(self.updateDeviceNameNewResult == [result isEqualToString:@"成功"], @"修改设备名称回调结果与实际不一致");
    });


    Then(@"^设备数据源修改并检查设备名称接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [DeviceDataSource updateDeviceNameWithDeviceId:[OCMArg any] oldName:[OCMArg any] newName:[OCMArg any] familyId:[OCMArg any] type:[OCMArg any] checkLevel:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]);
    });

    Given(@"^设备数据源修改并检查设备名称接口\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([DeviceDataSource updateDeviceNameWithDeviceId:[OCMArg any] oldName:[OCMArg any] newName:[OCMArg any] familyId:[OCMArg any] type:[OCMArg any] checkLevel:[OCMArg any] success:[OCMArg any] failure:[OCMArg any]]).andDo([StepsUtils actionHandler:args location:8 retCode:nil retData:nil]);
    });

    Then(@"^设备数据源修改并检查设备名称接口被调用\"([^\"]*)\"次,参数设备id为\"([^\"]*)\",旧名称为\"([^\"]*)\",新名称为\"([^\"]*)\",修改类型为\"([^\"]*)\",检查级别为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *deviceId = args[1];
      NSString *oldName = args[2];
      NSString *newName = args[3];
      NSString *type = args[4];
      BOOL checkLevel = [args[5] boolValue];
      OCMVerify(times(expectedInvocationTimes), [DeviceDataSource updateDeviceNameWithDeviceId:deviceId oldName:oldName newName:newName familyId:[OCMArg any] type:type checkLevel:checkLevel success:[OCMArg any] failure:[OCMArg any]]);
    });
}


@end
