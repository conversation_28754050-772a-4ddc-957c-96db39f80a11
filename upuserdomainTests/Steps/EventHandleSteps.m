//
//  EventHandleSteps.m
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "EventHandleSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "UpUserDomainListenerDelegate.h"
#import "UserdomainHolderTest.h"
#import "UpEventHandlerManager.h"
#import "StepsUtils.h"
#import <OCMock/OCMock.h>
#import "UDOperatorManager.h"
#import "UpEventHandlerDelegate.h"
#import "UDOperator.h"
#import "UpEventHandlerDelegate.h"
#import "UpEvent.h"
@interface ObserverDelegate : NSObject <UpUserDomainListenerDelegate>
@property (nonatomic, copy) NSString *identifier;
@property (nonatomic, strong) NSMutableArray *eventList;


@end

@implementation ObserverDelegate
- (instancetype)init
{
    if (self = [super init]) {
        _eventList = [NSMutableArray array];
    }
    return self;
}
- (void)onReceivedAction:(NSString *)action upuserdomain:(id<UpUserDomainDelegate>)domain
{
    [self.eventList addObject:action];
}
@end

@interface EventHandleSteps ()
@property (nonatomic, strong) NSMutableDictionary<NSString *, ObserverDelegate *> *obDic;
@property (nonatomic, strong) NSMutableDictionary *creatorMap;
@property (nonatomic, strong) NSMutableDictionary *operatorsMap;
@property (nonatomic, strong) NSMutableArray *eventNameList;
@property (nonatomic, strong) NSHashTable *hashTable;
@property (nonatomic, strong) NSMutableDictionary<UpEvent *, id<UpEventHandlerDelegate>> *handleMap;
@end

@implementation EventHandleSteps
- (instancetype)init
{
    if (self = [super init]) {
        _hashTable = [NSHashTable weakObjectsHashTable];
        _handleMap = [NSMutableDictionary dictionary];
    }
    return self;
}

- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.obDic = [NSMutableDictionary dictionary];
      self.operatorsMap = [NSMutableDictionary dictionary];
      self.eventNameList = [NSMutableArray array];
    });
    Then(@"^事件处理器收到\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *event = args[0];
      //      CCIAssert([self.eventNameList containsObject:event], @"事件处理器未收到此事件%@", event);
      OCMVerify([[UserdomainHolderTest getInstance].userdomain.provideEventManager sendEvent:event]);

    });

    Given(@"事件处理器发送事件$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [OCMStub([[UserdomainHolderTest getInstance].userdomain.provideEventManager sendEvent:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *finishBlockPointer;
        [invocation getArgument:&finishBlockPointer atIndex:2];
        NSString *event = (__bridge NSString *)finishBlockPointer;
        if (event) {
            [self.eventNameList addObject:event];
        }
        id<UpEventHandlerDelegate> eventHandler = [self.handleMap valueForKey:event];
        if (eventHandler == nil) {
            return;
        }
        [eventHandler handler:event userDomainPrvider:[UserdomainHolderTest getInstance].userdomain];

      }];
    });

    Given(@"^创建事件处理器$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      UDOperatorManager *operatorManager = OCMPartialMock([UserdomainHolderTest getInstance].userdomain.provideOperatorManager);
      [[UserdomainHolderTest getInstance].userdomain setValue:operatorManager forKey:@"operatorManager"];
      NSArray *classArray = @[ @"UDRefreshTerminalListOp", @"UDRefreshFamilyListOp", @"UDRefreshDeviceListOp", @"UDRefreshAddressListOp", @"UDPlannedRefreshAuthDataOp", @"UDSingleClientCheckOp", @"UDRefreshAllFamilyDetailOp" ];
      self.creatorMap = [NSMutableDictionary dictionary];
      for (NSString *clazz in classArray) {
          CreateOpBlock deleteDevicesOp = ^(void) {
            id op = OCMClassMock(NSClassFromString(clazz));
            [self.operatorsMap setValue:op forKey:clazz];
            return op;
          };
          [self.creatorMap setValue:deleteDevicesOp forKey:clazz];
      }
      [operatorManager setValue:self.creatorMap forKey:@"creatorMap"];
    });
    Given(@"^监听者\"([^\"]*)\"向事件处理器添加动作监听器$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *ID = args[0];
      ObserverDelegate *ob = [[ObserverDelegate alloc] init];
      ob.identifier = ID;
      [[UserdomainHolderTest getInstance].userdomain.provideEventManager registerListener:ob];
      [self.obDic setValue:ob forKey:ID];
    });
    When(@"^使用者向事件处理器发送事件\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *event = args[0];
      if ([event isEqualToString:@"空字符串"]) {
          event = @"";
      }
      else if ([event isEqualToString:@"空对象"]) {
          event = nil;
      }
      [[UserdomainHolderTest getInstance].userdomain.provideEventManager sendEvent:event];

    });
    Then(@"^操作管理器的创建操作接口被调用\"([^\"]*)\"次,操作器列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<NSArray<NSString *> *> *operatorNameList = getTableDataListFromExpectUserInfo(userInfo);
      if (operatorNameList.count == 0) {
          return;
      }
      NSString *opName = [self convertOperatorName:operatorNameList[0].firstObject];
      OCMVerify([[[UserdomainHolderTest getInstance].userdomain provideOperatorManager] searchByOperatorKey:opName]);
    });
    Then(@"操作器对象\"([^\"]*)\"的操作接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *opName = [self convertOperatorName:args[0]];
      NSString *invocationTimes = args[1];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      UDOperator *op = self.operatorsMap[opName];
      OCMVerify(times(expectedInvocationTimes), [op operate]);
    });
    Then(@"^监听者\"([^\"]*)\"收到动作通知为\"([^\"]*)\"次,动作列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *ID = args[0];
      NSString *invocationTimes = args[1];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray<NSArray<NSString *> *> *actionNameList = getTableDataListFromExpectUserInfo(userInfo);
      ObserverDelegate *ob = self.obDic[ID];
      CCIAssert(ob.eventList.count == expectedInvocationTimes, @"收到动作次数与实际不一致");
      if (actionNameList.count) {
          CCIAssert([actionNameList.firstObject.firstObject isEqualToString:ob.eventList.firstObject], @"动作列表与实际不一致");
      }
    });
    Given(@"^操作管理器创建\"([^\"]*)\"对象失败,返回空对象$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *opName = args[0];
      opName = [self convertOperatorName:opName];
      OCMStub([[UserdomainHolderTest getInstance].userdomain.provideOperatorManager searchByOperatorKey:opName]).andReturn(NULL);
    });
    Given(@"^事件处理器使用\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[UserdomainHolderTest getInstance].userdomain setValue:OCMProtocolMock(@protocol(UpEventHandlerManagerDelegate)) forKey:@"eventManager"];
    });
    Given(@"^监听者\"([^\"]*)\"向事件处理器取消动作监听器", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *ID = args[0];
      [[UserdomainHolderTest getInstance].userdomain.provideEventManager unregisterListener:[self.obDic valueForKey:ID]];
    });
    Then(@"^事件处理器收到\"([^\"]*)\"次\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *event = args[1];
      NSInteger i = 0;
      for (NSString *eventName in self.eventNameList) {
          if ([eventName isEqualToString:event]) {
              i++;
          }
      }

      CCIAssert([self.eventNameList containsObject:event] && expectedInvocationTimes == i, @"事件处理器未收到此事件%@次数应该为%ld,实际为%ld", event, expectedInvocationTimes, i);
    });
}
- (NSString *)convertOperatorName:(NSString *)originOperatorName
{
    NSString *opName = @"";
    if ([originOperatorName isEqualToString:@"PlannedRefreshAuthDataOp"]) {
        opName = @"UDPlannedRefreshAuthDataOp";
    }
    else if ([originOperatorName isEqualToString:@"SingleClientCheckOp"]) {
        opName = @"UDSingleClientCheckOp";
    }
    else if ([originOperatorName isEqualToString:@"RefreshAddressListOp"]) {
        opName = @"UDRefreshAddressListOp";
    }
    else if ([originOperatorName isEqualToString:@"RefreshTerminalListOp"]) {
        opName = @"UDRefreshTerminalListOp";
    }
    else if ([originOperatorName isEqualToString:@"RefreshFamilyListOp"]) {
        opName = @"UDRefreshFamilyListOp";
    }
    else if ([originOperatorName isEqualToString:@"RefreshAllFamilyDetailOp"]) {
        opName = @"UDRefreshAllFamilyDetailOp";
    }
    else if ([originOperatorName isEqualToString:@"RefreshDeviceListOp"]) {
        opName = @"UDRefreshDeviceListOp";
    }
    return opName;
}
@end
