//
//  CacheSteps.m
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "CacheSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "StepsUtils.h"
#import "UserAddressInfo.h"
#import "UserdomainHolderTest.h"
#import "UDUserDomainCache.h"
#import <OCMock/OCMock.h>
#import "UDCacheIMP.h"

extern NSString *const UDOAUTHDATAKEY;
extern NSString *const OLDOAUTHDATAKEY;

@interface CacheSteps ()

@property (nonatomic, strong) id<UpUserDomainCacheDelegate> userdomainCache;
@property (nonatomic, strong) ApplicationOauthData *authDataCache;
@property (nonatomic, strong) id<UDCache> cache;
@property (nonatomic, strong) NSMutableDictionary *actuallCacheList;
@property (nonatomic, strong) NSMutableArray *o<PERSON>hDataList;
@property (nonatomic, strong) ApplicationOauthData *mockOauthData;
@end

@implementation CacheSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.userdomainCache = nil;
      self.authDataCache = nil;
      self.cache = nil;
      self.actuallCacheList = [NSMutableDictionary dictionary];
      self.oauthDataList = [NSMutableArray array];
      self.mockOauthData = nil;
    });

    Given(@"用户缓存代理使用\"([^\"]*)\"", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *state = args[0];
      if ([state isEqualToString:@"真实的"]) {
          self.userdomainCache = [[UDUserDomainCache alloc] initUserDomainCache:[UserdomainHolderTest getInstance].cache];
      }
    });

    Given(@"^缓存数据的用户鉴权信息数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.mockOauthData = getOauthDataFromExpectUserInfo(userInfo);
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        ApplicationOauthData *result = self.mockOauthData;
        [invocation setReturnValue:&result];
      };
      [[[(OCMockObject *)[UserdomainHolderTest getInstance].cache stub] andDo:proxyBlock] getObject:@"authData"];
    });

    Given(@"缓存数据代理存储接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [OCMStub([[UserdomainHolderTest getInstance].cache putObject:[OCMArg any] key:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *valueBlockPointer;
        [invocation getArgument:&valueBlockPointer atIndex:2];
        NSArray *value = (__bridge NSArray *)valueBlockPointer;
        void *keyBlockPointer;
        [invocation getArgument:&keyBlockPointer atIndex:3];
        NSString *paramKey = (__bridge NSString *)keyBlockPointer;
        self.actuallCacheList[paramKey] = value;
      }];

    });

    Given(@"^缓存数据代理的地址列表数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UserAddressInfo *> *addressList = getAddressListFromExpectUserInfo(userInfo);
      OCMStub([[UserdomainHolderTest getInstance].cache getObject:@"addressList"]).andReturn(addressList);
    });

    Given(@"^缓存数据代理的用户信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      UserInfo *info = getUserInfoFromExpectUserInfo(userInfo);
      OCMStub([[UserdomainHolderTest getInstance].cache getObject:@"userInfo"]).andReturn(info);
    });

    Then(@"^缓存数据代理存储接口被调用\"([^\"]*)\"次,键为\"([^\"]*)\",值如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *key = args[1];
      OCMVerify(times(expectedInvocationTimes), [[UserdomainHolderTest getInstance].cache putObject:[OCMArg any] key:key]);
      id actualCach = self.actuallCacheList[key];
      NSString *actualKey = self.actuallCacheList.allKeys.firstObject;
      if ([key isEqualToString:@"addressList"]) {
          NSArray<UserAddressInfo *> *addressList = getAddressListFromExpectUserInfo(userInfo);
          CCIAssert(isEqualUserAddressList(addressList, actualCach), @"实际的key为%@，期望的key为%@", actualKey, key);
      }
      else if ([key isEqualToString:@"userInfo"]) {
          UserInfo *info = getUserInfoFromExpectUserInfo(userInfo);
          CCIAssert(isEqualUserInfo(info, actualCach), @"实际的key为%@，期望的key为%@", actualKey, key);
      }
      else if ([key isEqualToString:@"deviceList"]) {
          NSArray<Device *> *deviceList = getDeviceListFromExpectUserInfo(userInfo);
          CCIAssert(isEqualUserDeviceList(deviceList, actualCach), @"实际的key为%@，期望的key为%@", actualKey, key);
      }
      else if ([key isEqualToString:@"familyList"]) {
          NSArray<Family *> *familyList = getFamilyListFromExpectUserInfo(userInfo);
          CCIAssert(isEqualUserFamilyList(familyList, actualCach), @"实际的key为%@，期望的key为%@", self.actuallCacheList.allKeys.firstObject, key);
      }
      else if ([key isEqualToString:@"terminalList"]) {
          NSArray<UserTerminal *> *terminalList = getTerminalListFromExpectUserInfo(userInfo);
          CCIAssert(isEqualUserTerminalList(terminalList, actualCach), @"实际的key为%@，期望的key为%@", actualKey, key);
      }
      else if ([key isEqualToString:@"authData"]) {
          ApplicationOauthData *authData = getOauthDataFromExpectUserInfo(userInfo);
          CCIAssert(isEqualAuthDataInfo(authData, actualCach), @"实际的key为%@，期望的key为%@", actualKey, key);
      }
    });

    Then(@"^缓存数据代理清空缓存接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[UserdomainHolderTest getInstance].cache clear]);
    });
    Then(@"^缓存数据代理存储接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[UserdomainHolderTest getInstance].cache putObject:[OCMArg any] key:[OCMArg any]]);
    });
    Given(@"^键为\"([^\"]*)\"缓存数据的用户鉴权信息数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *cacheKey = args[0];
      if ([cacheKey isEqualToString:@"user-auth-data"]) {
          cacheKey = OLDOAUTHDATAKEY;
      }
      ApplicationOauthData *oauthData = getOauthDataFromExpectUserInfo(userInfo);
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        id result = oauthData;
        [invocation setReturnValue:&result];
      };
      [[[(OCMockObject *)[UserdomainHolderTest getInstance].cache stub] andDo:proxyBlock] getObject:cacheKey];
    });

    When(@"^使用者调用用户缓存代理的获取用户鉴权信息方法", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.authDataCache = (ApplicationOauthData *)[self.userdomainCache getAuthData];
    });
    Then(@"^缓存数据代理获取接口被调用\"([^\"]*)\"次，键为\"([^\"]*)\"", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger invocationTimes = [args[0] intValue];
      NSString *key = args[1];
      if ([key isEqualToString:@"user-auth-data"]) {
          key = OLDOAUTHDATAKEY;
      }
      OCMVerify(times(invocationTimes), [[UserdomainHolderTest getInstance].cache getObject:key]);
    });
    Then(@"^使用者获取的鉴权信息值如下：$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      ApplicationOauthData *expectAuthData = getOauthDataFromExpectUserInfo(userInfo);
      ApplicationOauthData *actualAuthData = self.authDataCache;
      if (expectAuthData && actualAuthData) {
          CCIAssert(isEqualAuthDataInfo(actualAuthData, expectAuthData), @"检验用户权鉴信息的期望值与实际值是否相同");
      }
      else {
          CCIAssert(actualAuthData == expectAuthData, @"检验用户权鉴信息的期望值与实际值是否相同");
      }
    });

    //iOS空实现
    Given(@"^缓存数据代理清除缓存数据$", ^(NSArray<NSString *> *args, NSDictionary *userInfo){

          });
}
@end
