//
//  RefreshTokenSteps.m
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "RefreshTokenSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "UserdomainHolderTest.h"
@interface RefreshTokenSteps ()
@property (nonatomic, strong) NSMutableDictionary *mutiCallBackDic;
@end
@implementation RefreshTokenSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.mutiCallBackDic = [NSMutableDictionary dictionary];
    });
    When(@"^调用自动刷新用户Token方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[UserdomainHolderTest getInstance].userdomain autoRefreshToken];
    });
    When(@"^使用者\"([^\"]*)\"调用自动刷新用户Token方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      [[UserdomainHolderTest getInstance]
              .userdomain autoRefreshToken:^(UserDomainSampleResult *_Nonnull result) {
        self.mutiCallBackDic[user] = result;
      }];
    });
    Then(@"^查询登录状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *loginStateStr = args[0];
      UpUserDomainState state = UpUserDomainStateLogingOut;
      if ([loginStateStr isEqualToString:@"未登录"]) {
          state = UpUserDomainStateUnLogin;
      }
      else if ([loginStateStr isEqualToString:@"已登出"]) {
          state = UpUserDomainStateUnLogin;
      }
      else if ([loginStateStr isEqualToString:@"已登录"]) {
          state = UpUserDomainStateDidLogin;
      }
      else if ([loginStateStr isEqualToString:@"登录中"]) {
          state = UpUserDomainStateLogining;
      }
      CCIAssert([UserdomainHolderTest getInstance].userdomain.state == state, @"当前登录状态应该相同，实际不相同");
    });
    Then(@"^使用者\"([^\"]*)\"收到刷新用户鉴权信息接口的回调结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      NSString *resultStr = args[1];
      UserDomainSampleResult *result = self.mutiCallBackDic[user];
      CCIAssert(result.success == [resultStr isEqualToString:@"成功"], @"刷新用户鉴权信息接口的回调结果与实际不一致");
    });
    When(@"^线程\"([^\"]*)\",调用自动刷新用户Token方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *user = args[0];
      dispatch_async(dispatch_get_global_queue(0, 0), ^{
        [[UserdomainHolderTest getInstance]
                .userdomain autoRefreshToken:^(UserDomainSampleResult *_Nonnull result) {
          self.mutiCallBackDic[user] = result;
        }];
      });
    });
    When(@"^等待\"([^\"]*)\"秒$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSTimeInterval time = [args[0] floatValue];
      XCTestExpectation *expectation = [[XCTestExpectation alloc] initWithDescription:@"等待秒的接口"];
      [XCTWaiter waitForExpectations:@[ expectation ] timeout:time enforceOrder:YES];
    });
    Given(@"^距离过期小于一小时触发刷新鉴权信息时间为\"([^\"]*)\"秒$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSTimeInterval time = [args[0] floatValue];
      [[[UserdomainHolderTest getInstance]
              .userdomain getSettings] setPlanRefreshTokenTime:time];
    });

    When(@"^执行定时刷新token方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[UserdomainHolderTest getInstance]
              .userdomain planRefreshTokenTask:nil
                                       failure:nil];
    });
    When(@"^执行进入前台检查刷新token方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[UserdomainHolderTest getInstance]
              .userdomain applicationWillEnterForegroundCheckRefreshToken];
    });
}
@end
