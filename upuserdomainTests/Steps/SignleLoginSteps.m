//
//  SignleLoginSteps.m
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SignleLoginSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "UserdomainHolderTest.h"
@implementation SignleLoginSteps
- (void)defineStepsAndHocks
{

    Given(@"^设置当前设备Id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *clientID = args[0];
      [[[UserdomainHolderTest getInstance].userdomain getSettings] setClientId:clientID];
    });
    Then(@"^登录状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *loginStateStr = args[0];
      UpUserDomainState state = UpUserDomainStateLogingOut;
      if ([loginStateStr isEqualToString:@"未登录"]) {
          state = UpUserDomainStateUnLogin;
      }
      else if ([loginStateStr isEqualToString:@"已登录"]) {
          state = UpUserDomainStateDidLogin;
      }
      else if ([loginStateStr isEqualToString:@"登录中"]) {
          state = UpUserDomainStateLogining;
      }
      CCIAssert([UserdomainHolderTest getInstance].userdomain.state == state, @"当前登录状态应该相同，实际不相同");
    });
}
@end
