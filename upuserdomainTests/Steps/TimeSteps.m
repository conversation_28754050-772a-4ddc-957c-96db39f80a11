//
//  TimeSteps.m
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "TimeSteps.h"
#import <Cucumberish/Cucumberish.h>
#import <OCMock/OCMock.h>
#import "UserdomainHolderTest.h"

@interface TimeSteps ()
@property (nonatomic, strong) NSString *timeStr;
@end
@implementation TimeSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.timeStr = nil;
    });

    Given(@"^设置当前系统时间为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.timeStr = args[0];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        [formatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
        NSDate *date = [formatter dateFromString:self.timeStr];
        NSInteger result = [date timeIntervalSince1970];
        [invocation setReturnValue:&result];
      };
      [[[(OCMockObject *)[UserdomainHolderTest getInstance].timeDelegate stub] andDo:proxyBlock] currentTimeMillis];
    });
}
@end
