//
//  CucumberRunner.c
//  upuserdomainTests
//
//  Created by 振兴郑 on 2019/4/15.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "Cucumberish.h"
#import <XCTest/XCTest.h>
#import "UserSteps.h"
#import "FamilySteps.h"
#import "DeviceSteps.h"
#import "CacheSteps.h"
#import "TimeSteps.h"
#import "EventHandleSteps.h"
#import "RefreshTokenSteps.h"
#import "SignleLoginSteps.h"
#import "InitializationSteps.h"
#import "UserdomainSteps.h"
@interface CucumberRunner : NSObject

@end

@implementation CucumberRunner

__attribute__((constructor)) void CucumberInit()
{
    [[Cucumberish instance] setPrettyNamesAllowed:NO];
    [Cucumberish instance].fixMissingLastScenario = YES;
    [[[UserSteps alloc] init] defineStepsAndHocks];
    [[[FamilySteps alloc] init] defineStepsAndHocks];
    [[[DeviceSteps alloc] init] defineStepsAndHocks];
    [[[CacheSteps alloc] init] defineStepsAndHocks];
    [[[TimeSteps alloc] init] defineStepsAndHocks];
    [[[EventHandleSteps alloc] init] defineStepsAndHocks];
    [[[RefreshTokenSteps alloc] init] defineStepsAndHocks];
    [[[SignleLoginSteps alloc] init] defineStepsAndHocks];
    [[[InitializationSteps alloc] init] defineStepsAndHocks];
    [[[UserdomainSteps alloc] init] defineStepsAndHocks];
    NSBundle *bundle = [NSBundle bundleForClass:[CucumberRunner class]];
    Cucumberish *cucumber = [[Cucumberish instance] parserFeaturesInDirectory:@"features" fromBundle:bundle includeTags:nil excludeTags:@[ @"ios_ignore", @"ignore" ]];
    [cucumber beginExecution];
}
@end
