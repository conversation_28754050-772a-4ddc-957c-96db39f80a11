//
//  StepsUtils.h
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UserAddressInfo.h"
#import "UserTerminal.h"
#import "UserLoginLogInfo.h"
#import "UserInfo.h"
#import "Room.h"
#import "FamilyMember.h"
#import "Family.h"
#import "Device.h"
#import "ApplicationOauthData.h"
#import "FamilyArgs.h"
#import "UDFloorInfo.h"
#import "FloorArg.h"
NS_ASSUME_NONNULL_BEGIN

@interface StepsUtils : NSObject
NSArray<NSArray<NSString *> *> *getTableDataListFromExpectUserInfo(NSDictionary *userInfo);
NSArray<UserAddressInfo *> *getAddressListFromExpectUserInfo(NSDictionary *userInfo);
NSArray<UserTerminal *> *getTerminalListFromExpectUserInfo(NSDictionary *userInfo);
NSArray<UserLoginLogInfo *> *getUserLoginLogInfoListFromExpectUserInfo(NSDictionary *userInfo);
UserInfo *getUserInfoFromExpectUserInfo(NSDictionary *userInfo);
NSDictionary<NSString *, NSArray<Room *> *> *getRoomListFromExpectUserInfo(NSDictionary *userInfo);
NSArray<FamilyMember *> *getFamilyMemberListFromExpectUserInfo(NSDictionary *userInfo);
NSArray<FamilyMember *> *getFamilyMemberInfoFromExpectUserInfo(NSDictionary *userInfo);
NSArray<Family *> *getFamilyListFromExpectUserInfo(NSDictionary *userInfo);
NSArray<UserInfo *> *getUserInfoListFromExpectUserInfo(NSDictionary *userInfo);
NSArray<UDFloorInfo *> *getFloorInfoListFromExpectUserInfo(NSDictionary *userInfo);
UserAddressInfo *createUserAddressInfo(NSDictionary *userInfo);
UserInfo *createUserInfo(NSDictionary *userInfo);
NSArray<Device *> *getDeviceListFromExpectUserInfo(NSDictionary *userInfo);
ApplicationOauthData *getOauthDataFromExpectUserInfo(NSDictionary *userInfo);
FamilyMember *getFamilyFirstMemberFromExpectUserInfo(NSDictionary *userInfo);
id convertParaFrom(NSString *jsonStr);
BOOL isEqualUserInfo(UserInfo *userInfo1, UserInfo *userInfo2);
BOOL isEqualUserTerminalList(NSArray<UserTerminal *> *ter1, NSArray<UserTerminal *> *ter2);
BOOL isEqualUserFamilyList(NSArray<Family *> *familyList1, NSArray<Family *> *familyList2);
BOOL isEqualUserDeviceList(NSArray<Device *> *deviceList1, NSArray<Device *> *deviceList2);
BOOL isEqualUserAddressList(NSArray<UserAddressInfo *> *addressList1, NSArray<UserAddressInfo *> *addressList2);
BOOL isEqualUserLoginLogInfoList(NSArray<UserLoginLogInfo *> *loginInfoList1, NSArray<UserLoginLogInfo *> *loginInfoList2);
BOOL isEqualRoomArgsInfo(RoomArgs *roomArgs1, RoomArgs *roomArgs2);
BOOL isEqualFloorArgInfo(FloorArg *floorArg1, FloorArg *floorArg2);
BOOL isEqualRoomList(NSArray<Room *> *roomList1, NSArray<Room *> *roomList2);
BOOL isEqualFloorList(NSArray<UDFloorInfo *> *floorList1, NSArray<UDFloorInfo *> *floorList2);
BOOL isEqualDictionary(NSDictionary *dict1, NSDictionary *dict2);
BOOL isEqualFamilyArgsInfo(FamilyArgs *args1, FamilyArgs *args2);
BOOL isEqualAuthDataInfo(ApplicationOauthData *data1, ApplicationOauthData *data2);
+ (void (^)(NSInvocation *))actionHandler:(NSArray *)args location:(NSInteger)location retCode:(id)retCode retData:(id)retData;
@end

NS_ASSUME_NONNULL_END
