//
//  StepsUtils.m
//  upuserdomainTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "StepsUtils.h"
#import <MJExtension/MJExtension.h>
#import "UserAddress.h"
#import "UserAddress+PrivateExtension.h"
#import "UserTerminal+PrivateExtension.h"
#import "UserLoginLogInfo+PrivateExtension.h"
#import "UserInfo+PrivateExtension.h"
#import "UserAddressInfo+PrivateExtension.h"
#import "Room+PrivateExtension.h"
#import "FamilyMember+PrivateExtension.h"
#import "MemberInfo.h"
#import "MemberInfo+PrivateExtension.h"
#import "Family+PrivateExtension.h"
#import "FamilyInfo.h"
#import "FamilyInfo+PrivateExtension.h"
#import "FamilyLocation.h"
#import "FamilyLocation+PrivateExtension.h"
#import "UDDeviceInfo.h"
#import "UDDeviceInfo+PrivateExtension.h"
#import "DevicePermission.h"
#import "DevicePermission+PrivateExtension.h"
#import "DeviceAuth.h"
#import "DeviceAuth+PrivateExtension.h"
#import "DeviceOwnerInfo.h"
#import "DeviceOwnerInfo+PrivateExtension.h"
#import "ApplicationOauthData+PrivateExtension.h"
#import "RoomArgs.h"
#import "NSObject+UD.h"
#import "UDFloorInfo+PrivateExtension.h"
@implementation StepsUtils
NSArray<NSArray<NSString *> *> *getTableDataListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *expect = userInfo[@"DataTable"];
    NSMutableArray *result = expect.mutableCopy;
    [result removeObjectAtIndex:0];
    return result;
}
NSArray<UserAddressInfo *> *getAddressListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *addressLiss = [NSMutableArray array];
    for (NSArray *data in list) {
        UserAddressInfo *info = [[UserAddressInfo alloc] init];
        info.realAddressId = data[0];
        info.realEmail = data[1];
        info.realIs_default = [data[2] integerValue];
        info.realIs_service = [data[3] integerValue];
        info.realReceiver_name = data[4];
        info.realReceiver_mobile = data[5];
        info.realSource = data[6];
        info.realTag = data[7];
        info.realUser_id = data[8];
        NSDictionary *userAddDic = [data[9] mj_JSONObject];
        UserAddress *userAddressInfo = [[UserAddress alloc] init];
        userAddressInfo.realProvince_id = userAddDic[@"provinceId"];
        userAddressInfo.realProvince = userAddDic[@"province"];
        userAddressInfo.realDistrict_id = userAddDic[@"districtId"];
        userAddressInfo.realDistrict = userAddDic[@"district"];
        userAddressInfo.realCity = userAddDic[@"city"];
        userAddressInfo.realCity_id = userAddDic[@"cityId"];
        userAddressInfo.realCountry_code = userAddDic[@"countryCode"];
        userAddressInfo.realPostcode = userAddDic[@"postcode"];
        userAddressInfo.realTown_id = userAddDic[@"townId"];
        userAddressInfo.realTown = userAddDic[@"town"];
        info.realAddress = userAddressInfo;
        [addressLiss addObject:info];
    }
    return addressLiss;
}
NSArray<UserTerminal *> *getTerminalListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *terminalList = [NSMutableArray array];
    for (NSArray *data in list) {
        UserTerminal *info = [[UserTerminal alloc] init];
        info.realUser_id = data[0];
        info.realUser_name = data[1];
        info.realClient_id = data[2];
        info.realAction_at = data[3];
        [terminalList addObject:info];
    }
    return terminalList;
}
NSArray<UserLoginLogInfo *> *getUserLoginLogInfoListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *loginLogInfoList = [NSMutableArray array];
    for (NSArray *data in list) {
        UserLoginLogInfo *info = [[UserLoginLogInfo alloc] init];
        info.realUser_id = data[0];
        info.realLogId = data[1];
        info.realUser_name = data[2];
        info.realClient_id = data[3];
        info.realProvince = data[4];
        info.realCity = data[5];
        info.realLongitude = data[6];
        info.realLatitude = data[7];
        info.realAction_at = data[8];
        [loginLogInfoList addObject:info];
    }
    return loginLogInfoList;
}
UserInfo *getUserInfoFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSArray *data = list.firstObject;
    UserInfo *info = [[UserInfo alloc] init];
    info.realUserId = data[0];
    info.realUsername = data[1];
    info.realEmail = data[2];
    info.realGivenName = data[3];
    info.realMobile = data[4];
    info.realNickname = data[5];
    info.realGender = data[6];
    info.realMarriage = data[7];
    info.realBirthday = data[8];
    info.realAvatarUrl = data[9];
    info.realFamilyNum = data[10];
    info.realEducation = data[11];
    info.realExtraPhone = data[12];
    info.realIncome = data[13];
    info.realHeight = data[14];
    info.realWeight = data[15];
    info.realCountryCode = data[16];
    return info;
}
NSDictionary<NSString *, NSArray<Room *> *> *getRoomListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableDictionary *rooms = [NSMutableDictionary dictionary];
    for (NSArray *data in list) {
        Room *info = [[Room alloc] init];
        NSString *floorId = data[0];
        info.realRoomId = data[1];
        info.realRoomName = data[2];
        info.realRoomClass = data[3];
        info.realRoomLabel = data[4];
        info.realRoomLogo = data[5];
        info.realRoomPicture = data[6];
        NSMutableArray *array = rooms[floorId];
        if (array == nil) {
            array = [NSMutableArray array];
        }
        [array addObject:info];
        rooms[floorId] = array;
    }
    return rooms;
}
NSArray<FamilyMember *> *getFamilyMemberListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *familyMemberList = [NSMutableArray array];
    for (NSArray *data in list) {
        FamilyMember *info = [[FamilyMember alloc] init];
        info.realFamilyId = data[0];
        info.realJoinTime = data[1];
        info.realMemberName = data[2];
        MemberInfo *mebInfo = [[MemberInfo alloc] init];
        NSDictionary *dic = [data[3] mj_JSONObject];
        mebInfo.realUserId = dic[@"userId"];
        mebInfo.realUcUserId = dic[@"userId"];
        mebInfo.realName = dic[@"name"];
        mebInfo.realAvatarUrl = dic[@"avatar"];
        mebInfo.realMobile = dic[@"mobile"];
        mebInfo.realBirthday = dic[@"birthday"];
        info.realMemberInfo = mebInfo;
        info.realShareDeviceCount = [data[4] integerValue];
        info.realMemberRole = data[5];
        [familyMemberList addObject:info];
    }
    return familyMemberList;
}
NSArray<FamilyMember *> *getFamilyMemberInfoFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *familyMemberList = [NSMutableArray array];
    for (NSArray *data in list) {
        FamilyMember *familyMember = [[FamilyMember alloc] init];
        familyMember.realFamilyId = data[0];
        familyMember.realJoinTime = data[1];
        familyMember.realMemberName = data[2];
        familyMember.realShareDeviceCount = [data[4] integerValue];
        familyMember.realMemberRole = data[5];
        NSDictionary *dic = [data[3] mj_JSONObject];
        MemberInfo *mebInfo = [[MemberInfo alloc] init];
        mebInfo.realUserId = dic[@"userId"];
        mebInfo.realUcUserId = dic[@"userId"];
        mebInfo.realName = dic[@"name"];
        mebInfo.realAvatarUrl = dic[@"avatar"];
        mebInfo.realMobile = dic[@"mobile"];
        mebInfo.realBirthday = dic[@"birthday"];
        familyMember.realMemberInfo = mebInfo;
        [familyMemberList addObject:familyMember];
    }
    return familyMemberList;
}
FamilyMember *getFamilyFirstMemberFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSArray *data = list[0];
    FamilyMember *info = [[FamilyMember alloc] init];
    info.realMemberName = data[2];
    MemberInfo *mebInfo = [[MemberInfo alloc] init];
    mebInfo.realUserId = data[0];
    mebInfo.realName = data[1];
    info.realMemberInfo = mebInfo;
    return info;
}

NSArray<Family *> *getFamilyListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *familyList = [NSMutableArray array];
    for (NSArray *data in list) {
        Family *info = [[Family alloc] init];
        info.realFamilyId = data[0];
        MemberInfo *mebInfo = [[MemberInfo alloc] init];
        NSDictionary *dic = [data[2] mj_JSONObject];
        mebInfo.realUserId = dic[@"userId"];
        mebInfo.realUcUserId = dic[@"userId"];
        mebInfo.realName = dic[@"name"];
        mebInfo.realAvatarUrl = dic[@"avatar"];
        mebInfo.realMobile = dic[@"mobile"];
        mebInfo.realBirthday = dic[@"birthday"];
        NSString *floodIdsStr = data[3];
        NSArray *floodIds = [floodIdsStr componentsSeparatedByString:@","];
        NSMutableArray<UDFloorInfo *> *floorInfos = [NSMutableArray array];
        for (NSString *floorId in floodIds) {
            UDFloorInfo *floorInfo = [[UDFloorInfo alloc] init];
            floorInfo.realFloorId = floorId;
            [floorInfos addObject:floorInfo];
        }
        info.realFloorInfos = floorInfos;
        info.realOwner = mebInfo;
        info.realDefaultFamily = [data[4] boolValue];
        info.realAppId = data[5];
        info.realCreateTime = data[6];
        FamilyInfo *fInfo = [[FamilyInfo alloc] init];
        fInfo.realFamilyName = data[1];
        FamilyLocation *fLocation = [[FamilyLocation alloc] init];
        NSDictionary *fLocationDic = [data[7] mj_JSONObject];
        fLocation.realCityCode = fLocationDic[@"cityCode"];
        fLocation.realLatitude = fLocationDic[@"lat"];
        fLocation.realLongitude = fLocationDic[@"lon"];
        fInfo.realFamilyLocation = fLocation;
        fInfo.realFamilyPosition = data[9];
        info.realInfo = fInfo;
        info.realLocationChangeFlag = [data[10] boolValue];
        NSString *familyMemberIdsStr = data[8];
        NSArray *familyMemberIds = [familyMemberIdsStr componentsSeparatedByString:@","];
        NSMutableArray<FamilyMember *> *mbList = [NSMutableArray array];
        for (NSString *familyMemberId in familyMemberIds) {
            FamilyMember *mb = [[FamilyMember alloc] init];
            MemberInfo *me = [[MemberInfo alloc] init];
            me.realUserId = familyMemberId;
            mb.realMemberInfo = me;
            [mbList addObject:mb];
        }
        info.realMembers = mbList;
        [familyList addObject:info];
    }
    return familyList;
}
NSArray<UserInfo *> *getUserInfoListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *infoList = [NSMutableArray array];
    for (NSArray *data in list) {
        UserInfo *info = [[UserInfo alloc] init];
        info.realUserId = data[0];
        info.realUsername = data[1];
        info.realEmail = data[2];
        info.realGivenName = data[3];
        info.realMobile = data[4];
        info.realNickname = data[5];
        info.realGender = data[6];
        info.realMarriage = data[7];
        info.realBirthday = data[8];
        info.realAvatarUrl = data[9];
        info.realFamilyNum = data[10];
        info.realEducation = data[11];
        info.realExtraPhone = data[12];
        info.realIncome = data[13];
        info.realHeight = data[14];
        info.realWeight = data[15];
        info.realCountryCode = data[16];
        [infoList addObject:info];
    }
    return infoList;
}
UserAddressInfo *createUserAddressInfo(NSDictionary *userInfo)
{
    UserAddressInfo *info = [[UserAddressInfo alloc] init];
    info.realAddressId = userInfo[@"addressId"];
    info.realSource = userInfo[@"source"];
    info.realReceiver_name = userInfo[@"receiverName"];
    info.realReceiver_mobile = userInfo[@"receiverMobile"];
    UserAddress *userAddressInfo = [[UserAddress alloc] init];
    userAddressInfo.realCity = userInfo[@"city"];
    userAddressInfo.realCity_id = userInfo[@"cityId"];
    userAddressInfo.realProvince = userInfo[@"province"];
    userAddressInfo.realProvince_id = userInfo[@"provinceId"];
    userAddressInfo.realDistrict = userInfo[@"district"];
    userAddressInfo.realDistrict_id = userInfo[@"districtId"];
    info.realAddress = userAddressInfo;
    return info;
}
UserInfo *createUserInfo(NSDictionary *userInfo)
{
    UserInfo *info = [[UserInfo alloc] init];
    info.realUserId = userInfo[@"userId"];
    info.realGender = userInfo[@"gender"];
    info.realMarriage = userInfo[@"marriage"];
    info.realBirthday = userInfo[@"birthday"];
    info.realFamilyNum = userInfo[@"familyNum"];
    info.realEducation = userInfo[@"education"];
    info.realIncome = userInfo[@"income"];
    return info;
}
NSArray<Device *> *getDeviceListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *deviceList = [NSMutableArray array];
    for (NSArray *data in list) {
        Device *device = [[Device alloc] init];
        UDDeviceInfo *deviceInfo = [[UDDeviceInfo alloc] init];
        deviceInfo.realDeviceId = data[0];
        deviceInfo.realDeviceName = data[1];
        deviceInfo.realDevName = data[2];
        deviceInfo.realDeviceType = data[3];
        deviceInfo.realFamilyId = data[4];
        deviceInfo.realOwnerId = data[5];
        DevicePermission *permission = [[DevicePermission alloc] init];
        DeviceAuth *auth = [[DeviceAuth alloc] init];
        NSDictionary *permDict = [data[6] mj_JSONObject];
        permission.realAuthType = permDict[@"authType"];
        auth.realControl = [permDict[@"auth"][@"control"] boolValue];
        auth.realSet = [permDict[@"auth"][@"set"] boolValue];
        auth.realView = [permDict[@"auth"][@"view"] boolValue];
        permission.realAuth = auth;
        deviceInfo.realPermission = permission;
        deviceInfo.realWifiType = data[7];
        deviceInfo.realBindTime = data[8];
        deviceInfo.realIsOnline = data[9];
        DeviceOwnerInfo *ownerInfo = [[DeviceOwnerInfo alloc] init];
        NSDictionary *ownerDict = [data[10] mj_JSONObject];
        ownerInfo.realMobile = ownerDict[@"mobile"];
        ownerInfo.realUserId = ownerDict[@"userId"];
        ownerInfo.realUserNickName = ownerDict[@"userNickName"];
        deviceInfo.realOwnerInfo = ownerInfo;
        deviceInfo.realSubDevIds = data[11];
        deviceInfo.realParentId = data[12];
        deviceInfo.realDeviceRole = data[13];
        deviceInfo.realDeviceRoleType = data[14];
        deviceInfo.realApptypeName = data[15];
        deviceInfo.realApptypeCode = data[16];
        deviceInfo.realCategoryGrouping = data[17];
        deviceInfo.realBarcode = data[18];
        deviceInfo.realBindType = data[19];
        deviceInfo.realBrand = data[20];
        deviceInfo.realImageAddr1 = data[21];
        deviceInfo.realImageAddr2 = data[22];
        deviceInfo.realModel = data[23];
        deviceInfo.realProdNo = data[24];
        deviceInfo.realRoomName = data[25];
        deviceInfo.realRoomId = data[26];
        NSDictionary *roomDic = [data[27] mj_JSONObject];
        Room *room = [[Room alloc] init];
        room.realRoomId = roomDic[@"roomId"];
        room.realRoomName = roomDic[@"roomName"];
        room.realRoomClass = roomDic[@"roomClass"];
        room.realRoomLabel = roomDic[@"roomLabel"];
        room.realRoomLogo = roomDic[@"roomLogo"];
        room.realRoomPicture = roomDic[@"roomPicture"];
        deviceInfo.realRoom = room;
        deviceInfo.realIsOwned = [data[28] boolValue];
        deviceInfo.realAccessType = data[29];
        deviceInfo.realConfigType = data[30];
        deviceInfo.realDevFloorId = data[31];
        deviceInfo.realDevFloorOrderId = data[32];
        deviceInfo.realDevFloorName = data[33];
        deviceInfo.realDeviceNetType = data[34];
        [device setDeviceInfo:deviceInfo];
        [deviceList addObject:device];
    }
    return deviceList;
}
NSArray<UDFloorInfo *> *getFloorInfoListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *floorInfos = [NSMutableArray array];
    for (NSArray *data in list) {
        UDFloorInfo *floorInfo = [[UDFloorInfo alloc] init];
        floorInfo.realFloorId = data[0];
        floorInfo.realFloorOrderId = data[1];
        floorInfo.realFloorName = data[2];
        floorInfo.realFloorClass = data[3];
        floorInfo.realFloorLabel = data[4];
        floorInfo.realFloorLogo = data[5];
        floorInfo.realFloorPicture = data[6];
        floorInfo.realFloorCreateTime = data[7];
        NSString *roomsJson = data[8];
        NSArray *array = [roomsJson componentsSeparatedByString:@"#"];
        NSMutableArray<Room *> *roomList = [NSMutableArray array];
        for (NSString *str in array) {
            NSDictionary *parDict = [str mj_JSONObject];
            Room *info = [[Room alloc] init];
            info.realRoomId = parDict[@"roomId"];
            info.realRoomName = parDict[@"roomName"];
            info.realRoomClass = parDict[@"roomClass"];
            info.realRoomLabel = parDict[@"roomLabel"];
            info.realRoomLogo = parDict[@"roomLogo"];
            info.realRoomPicture = parDict[@"roomPicture"];
            [roomList addObject:info];
        }
        floorInfo.realRooms = roomList;
        [floorInfos addObject:floorInfo];
    }
    return floorInfos;
}
ApplicationOauthData *getOauthDataFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    if (list.count == 0) {
        return nil;
    }
    NSArray *data = list.firstObject;
    ApplicationOauthData *oauthData = [[ApplicationOauthData alloc] init];
    oauthData.realAccess_token = data[0];
    oauthData.realRefresh_token = data[1];
    oauthData.realUhome_access_token = data[2];
    oauthData.realExpires_in = data[3];
    oauthData.realScope = data[4];
    oauthData.realToken_type = data[5];
    oauthData.realUhome_user_id = data[6];
    oauthData.realUc_user_id = data[8];
    NSDateFormatter *format = [[NSDateFormatter alloc] init];
    format.dateFormat = @"yyyy-MM-dd HH:mm:ss";
    oauthData.realCreateTime = [format dateFromString:data[7]];
    return oauthData;
}

BOOL isEqualUserInfo(UserInfo *userInfo1, UserInfo *userInfo2)
{
    NSString *userInfo1Str = [userInfo1 mj_keyValues].mj_JSONString;
    NSString *userInfo2Str = [userInfo2 mj_keyValues].mj_JSONString;
    return [userInfo1Str isEqualToString:userInfo2Str];
}
BOOL isEqualUserTerminalList(NSArray<UserTerminal *> *ter1, NSArray<UserTerminal *> *ter2)
{
    if (ter1.count != ter2.count) {
        return NO;
    }
    NSString *ter1Str = [NSArray mj_keyValuesArrayWithObjectArray:ter1].mj_JSONString;
    NSString *ter2Str = [NSArray mj_keyValuesArrayWithObjectArray:ter2].mj_JSONString;
    return [ter1Str isEqualToString:ter2Str];
}
BOOL isEqualUserFamilyList(NSArray<Family *> *familyList1, NSArray<Family *> *familyList2)
{
    if (familyList1.count != familyList2.count) {
        return NO;
    }
    NSMutableArray *array = [Family mj_totalIgnoredPropertyNames];
    [array addObject:@"members"];
    [array addObject:@"floorInfos"];
    [array addObject:@"firstMember"];
    NSString *family1Str = [NSArray mj_keyValuesArrayWithObjectArray:familyList1].mj_JSONString;
    NSString *family2Str = [NSArray mj_keyValuesArrayWithObjectArray:familyList2].mj_JSONString;
    return [family1Str isEqualToString:family2Str];
}
BOOL isEqualUserDeviceList(NSArray<Device *> *deviceList1, NSArray<Device *> *deviceList2)
{
    if (deviceList1.count != deviceList2.count) {
        return NO;
    }
    NSMutableArray *array = [Device mj_totalIgnoredPropertyNames];
    [array addObject:@"room"];
    NSString *device1Str = [NSArray mj_keyValuesArrayWithObjectArray:deviceList1].mj_JSONString;
    NSString *device2Str = [NSArray mj_keyValuesArrayWithObjectArray:deviceList2].mj_JSONString;
    return [device1Str isEqualToString:device2Str];
}
BOOL isEqualUserAddressList(NSArray<UserAddressInfo *> *addressList1, NSArray<UserAddressInfo *> *addressList2)
{
    if (addressList1.count != addressList2.count) {
        return NO;
    }
    NSString *address1Str = [NSArray mj_keyValuesArrayWithObjectArray:addressList1].mj_JSONString;
    NSString *address2Str = [NSArray mj_keyValuesArrayWithObjectArray:addressList2].mj_JSONString;
    return [address1Str isEqualToString:address2Str];
}
BOOL isEqualUserLoginLogInfoList(NSArray<UserLoginLogInfo *> *loginInfoList1, NSArray<UserLoginLogInfo *> *loginInfoList2)
{
    if (loginInfoList1.count != loginInfoList2.count) {
        return NO;
    }
    NSString *loginInfoList1Str = [NSArray mj_keyValuesArrayWithObjectArray:loginInfoList1].mj_JSONString;
    NSString *loginInfoList2Str = [NSArray mj_keyValuesArrayWithObjectArray:loginInfoList2].mj_JSONString;
    return [loginInfoList1Str isEqualToString:loginInfoList2Str];
}
BOOL isEqualRoomArgsInfo(RoomArgs *roomArgs1, RoomArgs *roomArgs2)
{
    NSString *args1Str = [roomArgs1 mj_keyValues].mj_JSONString;
    NSString *args2Str = [roomArgs2 mj_keyValues].mj_JSONString;
    return [args1Str isEqualToString:args2Str];
}
BOOL isEqualFloorArgInfo(FloorArg *floorArg1, FloorArg *floorArg2)
{
    NSString *args1Str = [floorArg1 mj_keyValues].mj_JSONString;
    NSString *args2Str = [floorArg2 mj_keyValues].mj_JSONString;
    return [args1Str isEqualToString:args2Str];
}
BOOL isEqualRoomList(NSArray<Room *> *roomList1, NSArray<Room *> *roomList2)
{
    if (roomList1.count != roomList2.count) {
        return NO;
    }
    NSString *roomList1Str = [NSArray mj_keyValuesArrayWithObjectArray:roomList1].mj_JSONString;
    NSString *roomList2Str = [NSArray mj_keyValuesArrayWithObjectArray:roomList2].mj_JSONString;
    return [roomList1Str isEqualToString:roomList2Str];
}
BOOL isEqualFloorList(NSArray<UDFloorInfo *> *floorList1, NSArray<UDFloorInfo *> *floorList2)
{
    if (floorList1.count != floorList2.count) {
        return NO;
    }
    NSString *floorList1Str = [NSArray mj_keyValuesArrayWithObjectArray:floorList1].mj_JSONString;
    NSString *floorList2Str = [NSArray mj_keyValuesArrayWithObjectArray:floorList2].mj_JSONString;
    return [floorList1Str isEqualToString:floorList2Str];
}
BOOL isEqualDictionary(NSDictionary *dict1, NSDictionary *dict2)
{
    return [dict1 ud_isEqualTo:dict2];
}
BOOL isEqualFamilyArgsInfo(FamilyArgs *args1, FamilyArgs *args2)
{
    NSString *args1Str = [args1 mj_keyValues].mj_JSONString;
    NSString *args2Str = [args2 mj_keyValues].mj_JSONString;
    return [args1Str isEqualToString:args2Str];
}
BOOL isEqualAuthDataInfo(ApplicationOauthData *data1, ApplicationOauthData *data2)
{
    NSString *data1Str = [data1 mj_keyValues].mj_JSONString;
    NSString *data2Str = [data2 mj_keyValues].mj_JSONString;
    return [data1Str isEqualToString:data2Str];
}

id convertParaFrom(NSString *jsonStr)
{
    if (!jsonStr) {
        return nil;
    }
    NSData *jsonData = [jsonStr dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    id result = [NSJSONSerialization JSONObjectWithData:jsonData ?: [NSData new]
                                                options:NSJSONReadingMutableContainers
                                                  error:&err];
    if (err) {
        NSLog(@"json解析失败：%@", err);
        return nil;
    }
    return result;
}

+ (void (^)(NSInvocation *))actionHandler:(NSArray *)args location:(NSInteger)location retCode:(id)retCode retData:(id)retData
{
    return ^(NSInvocation *invocation) {
      [invocation retainArguments];
      void *finishBlockPointer;
      void (^userDomainCallback)(UserDomainSampleResult *result);
      if ([args[0] isEqualToString:@"成功"]) {
          [invocation getArgument:&finishBlockPointer atIndex:location];
          userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
          UserDomainSampleResult *result = [UserDomainSampleResult defaultSuccessResult];
          if (retData) {
              result.retData = retData;
          }
          userDomainCallback(result);
      }
      else {
          [invocation getArgument:&finishBlockPointer atIndex:location + 1];
          userDomainCallback = (__bridge void (^)(UserDomainSampleResult *))finishBlockPointer;
          UserDomainSampleResult *result = [UserDomainSampleResult defaultFailureResult];
          if (retCode) {
              result.retCode = retCode;
          }
          userDomainCallback(result);
      }
    };
}
@end
