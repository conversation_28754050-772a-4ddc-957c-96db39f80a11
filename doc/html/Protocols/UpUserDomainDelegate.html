<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>UpUserDomainDelegate Protocol Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	

	

	
	<optgroup label="Instance Methods">
		
		<option value="//api/name/addObserver:">- addObserver:</option>
		
		<option value="//api/name/autoRefreshToken">- autoRefreshToken</option>
		
		<option value="//api/name/cancleLogin">- cancleLogin</option>
		
		<option value="//api/name/getSettings">- getSettings</option>
		
		<option value="//api/name/isLogin">- isLogin</option>
		
		<option value="//api/name/isRefreshCompleted">- isRefreshCompleted</option>
		
		<option value="//api/name/isRefreshDeviceListCompleted">- isRefreshDeviceListCompleted</option>
		
		<option value="//api/name/isRefreshFamilyListCompleted">- isRefreshFamilyListCompleted</option>
		
		<option value="//api/name/isRefreshUserCompleted">- isRefreshUserCompleted</option>
		
		<option value="//api/name/lastLoginRecord">- lastLoginRecord</option>
		
		<option value="//api/name/logOut:">- logOut:</option>
		
		<option value="//api/name/oauthData">- oauthData</option>
		
		<option value="//api/name/queryServiceOrder:success:failure:">- queryServiceOrder:success:failure:</option>
		
		<option value="//api/name/refreshUser:">- refreshUser:</option>
		
		<option value="//api/name/refreshUser:callback:">- refreshUser:callback:</option>
		
		<option value="//api/name/removeObserver:">- removeObserver:</option>
		
		<option value="//api/name/state">- state</option>
		
		<option value="//api/name/updateOauthData:refreshToken:uhome_access_token:expires_in:scope:token_type:uhome_user_id:">- updateOauthData:refreshToken:uhome_access_token:expires_in:scope:token_type:uhome_user_id:</option>
		
		<option value="//api/name/user">- user</option>
		
	</optgroup>
	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">UpUserDomainDelegate Protocol Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Conforms to</th>
	<td>NSObject<br /><a href="../Protocols/UDPlanRefreshTokenTaskDelegate.html">UDPlanRefreshTokenTaskDelegate</a></td>
</tr><tr>
	<th>Declared in</th>
	<td>UpUserDomainDelegate.h</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/autoRefreshToken" title="autoRefreshToken"></a>
	<h3 class="method-title"><code><a href="#//api/name/autoRefreshToken">&ndash;&nbsp;autoRefreshToken</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>自动刷新用户令牌，通过回调接口返回刷新结果 此接口支持多次调用</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)autoRefreshToken</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>自动刷新用户令牌，通过回调接口返回刷新结果 此接口支持多次调用</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/oauthData" title="oauthData"></a>
	<h3 class="method-title"><code><a href="#//api/name/oauthData">&ndash;&nbsp;oauthData</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取用户鉴权信息 当内存引用的鉴权信息为null时，会尝试从本地缓存读取</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (id&lt;UDAuthDataDelegate&gt;)oauthData</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取用户鉴权信息 当内存引用的鉴权信息为null时，会尝试从本地缓存读取</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/lastLoginRecord" title="lastLoginRecord"></a>
	<h3 class="method-title"><code><a href="#//api/name/lastLoginRecord">&ndash;&nbsp;lastLoginRecord</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取最后一次的登录信息</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (id&lt;UDLastLoginInfoDelegate&gt;)lastLoginRecord</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取最后一次的登录信息</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/state" title="state"></a>
	<h3 class="method-title"><code><a href="#//api/name/state">&ndash;&nbsp;state</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取用户的登录状态</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (UpUserDomainState)state</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取用户的登录状态</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/getSettings" title="getSettings"></a>
	<h3 class="method-title"><code><a href="#//api/name/getSettings">&ndash;&nbsp;getSettings</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取用户基础信息的设置对象</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (id&lt;UPUserDomainSettingsDelegate&gt;)getSettings</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取用户基础信息的设置对象</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/user" title="user"></a>
	<h3 class="method-title"><code><a href="#//api/name/user">&ndash;&nbsp;user</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取用户对象 此接口不会返回null，但在未登录状态，User对象中的数据不可用</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (id&lt;UDUserDelegate&gt;)user</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取用户对象 此接口不会返回null，但在未登录状态，User对象中的数据不可用</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/isLogin" title="isLogin"></a>
	<h3 class="method-title"><code><a href="#//api/name/isLogin">&ndash;&nbsp;isLogin</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>是否登录</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (BOOL)isLogin</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>是否登录</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/isRefreshCompleted" title="isRefreshCompleted"></a>
	<h3 class="method-title"><code><a href="#//api/name/isRefreshCompleted">&ndash;&nbsp;isRefreshCompleted</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取数据刷新完成标识 当用户的详细信息，地址和登录终端列表，家庭列表，设备列表全部刷新完时返回true</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (BOOL)isRefreshCompleted</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取数据刷新完成标识 当用户的详细信息，地址和登录终端列表，家庭列表，设备列表全部刷新完时返回true</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/isRefreshDeviceListCompleted" title="isRefreshDeviceListCompleted"></a>
	<h3 class="method-title"><code><a href="#//api/name/isRefreshDeviceListCompleted">&ndash;&nbsp;isRefreshDeviceListCompleted</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取设备列表数据刷新完成标识 当用户的设备列表全部刷新完时返回true</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (BOOL)isRefreshDeviceListCompleted</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取设备列表数据刷新完成标识 当用户的设备列表全部刷新完时返回true</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/isRefreshFamilyListCompleted" title="isRefreshFamilyListCompleted"></a>
	<h3 class="method-title"><code><a href="#//api/name/isRefreshFamilyListCompleted">&ndash;&nbsp;isRefreshFamilyListCompleted</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取家庭列表数据刷新完成标识 当用户的家庭列表刷新完时返回true</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (BOOL)isRefreshFamilyListCompleted</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取家庭列表数据刷新完成标识 当用户的家庭列表刷新完时返回true</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/isRefreshUserCompleted" title="isRefreshUserCompleted"></a>
	<h3 class="method-title"><code><a href="#//api/name/isRefreshUserCompleted">&ndash;&nbsp;isRefreshUserCompleted</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取用户详细数据刷新完成标识 当用户的详细信息，地址和登录终端列表刷新完时返回true</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (BOOL)isRefreshUserCompleted</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取用户详细数据刷新完成标识 当用户的详细信息，地址和登录终端列表刷新完时返回true</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/logOut:" title="logOut:"></a>
	<h3 class="method-title"><code><a href="#//api/name/logOut:">&ndash;&nbsp;logOut:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户登出 调用服务端登出接口，无论接口返回成功还是失败，均清除内存和本地缓存</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)logOut:(userDomainCallback)<em>callback</em></code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户登出 调用服务端登出接口，无论接口返回成功还是失败，均清除内存和本地缓存</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/refreshUser:" title="refreshUser:"></a>
	<h3 class="method-title"><code><a href="#//api/name/refreshUser:">&ndash;&nbsp;refreshUser:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新用户信息，包括用户的详细信息，家庭列表和设备信息</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)refreshUser:(userDomainCallback)<em>callback</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>callback</code></th>
						<td><p>回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新用户信息，包括用户的详细信息，家庭列表和设备信息</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/refreshUser:callback:" title="refreshUser:callback:"></a>
	<h3 class="method-title"><code><a href="#//api/name/refreshUser:callback:">&ndash;&nbsp;refreshUser:callback:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新用户：信息、家庭、设备列表，传YES时从服务器请求数据，false时取本地数据，当为false时会根据缓存数据是否已经过期来判断
若已经过期则从服务器请求数据</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)refreshUser:(BOOL)<em>immediate</em> callback:(userDomainCallback)<em>callback</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>callback</code></th>
						<td><p>回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>immediate</code></th>
						<td><p>是否立即</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新用户：信息、家庭、设备列表，传YES时从服务器请求数据，false时取本地数据，当为false时会根据缓存数据是否已经过期来判断
若已经过期则从服务器请求数据</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/addObserver:" title="addObserver:"></a>
	<h3 class="method-title"><code><a href="#//api/name/addObserver:">&ndash;&nbsp;addObserver:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>添加组件订阅者，订阅基础组件的各种事件</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)addObserver:(id&lt;UpUserDomainObserver&gt;)<em>observer</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>observer</code></th>
						<td><p>(<a href="../Protocols/UpUserDomainObserver.html">UpUserDomainObserver</a>)，必填</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>添加组件订阅者，订阅基础组件的各种事件</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/removeObserver:" title="removeObserver:"></a>
	<h3 class="method-title"><code><a href="#//api/name/removeObserver:">&ndash;&nbsp;removeObserver:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>移除订阅者</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)removeObserver:(id&lt;UpUserDomainObserver&gt;)<em>observer</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>observer</code></th>
						<td><p>(<a href="../Protocols/UpUserDomainObserver.html">UpUserDomainObserver</a>)，必填</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>移除订阅者</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/updateOauthData:refreshToken:uhome_access_token:expires_in:scope:token_type:uhome_user_id:" title="updateOauthData:refreshToken:uhome_access_token:expires_in:scope:token_type:uhome_user_id:"></a>
	<h3 class="method-title"><code><a href="#//api/name/updateOauthData:refreshToken:uhome_access_token:expires_in:scope:token_type:uhome_user_id:">&ndash;&nbsp;updateOauthData:refreshToken:uhome_access_token:expires_in:scope:token_type:uhome_user_id:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>更新用户鉴权信息,更新成功以后组件会缓存用户鉴权信息，在下次应用启动时调用autoRefreshToken可实现自动登录</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (BOOL)updateOauthData:(NSString *)<em>access_token</em> refreshToken:(NSString *)<em>refreshToken</em> uhome_access_token:(NSString *)<em>uhome_access_token</em> expires_in:(NSString *)<em>expires_in</em> scope:(NSString *)<em>scope</em> token_type:(NSString *)<em>token_type</em> uhome_user_id:(NSString *)<em>uhome_user_id</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>access_token</code></th>
						<td><p>用户中心个人级别token，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>refreshToken</code></th>
						<td><p>用户中心刷新token，用于刷新个人token，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>uhome_access_token</code></th>
						<td><p>云平台个人token，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>expires_in</code></th>
						<td><p>用户中心个人token有效期，单位秒，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>scope</code></th>
						<td><p>用户中心个人token作用域，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>token_type</code></th>
						<td><p>用户中心个人token类型，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>uhome_user_id</code></th>
						<td><p>云平台用户id，必填</p></td>
					</tr>
				
				</table>
			</div>
			

			
			<div class="method-subsection return">
				<h4 class="method-subtitle parameter-title">Return Value</h4>
				<p>是否更新鉴权信息成功</p>
			</div>
			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>更新用户鉴权信息,更新成功以后组件会缓存用户鉴权信息，在下次应用启动时调用autoRefreshToken可实现自动登录</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/queryServiceOrder:success:failure:" title="queryServiceOrder:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/queryServiceOrder:success:failure:">&ndash;&nbsp;queryServiceOrder:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>查询用户电子工单（未登录可用）,内部自动获取应用级Token</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)queryServiceOrder:(NSString *)<em>phoneNumber</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>phoneNumber</code></th>
						<td><p>手机号码，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>查询用户电子工单（未登录可用）,内部自动获取应用级Token</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/cancleLogin" title="cancleLogin"></a>
	<h3 class="method-title"><code><a href="#//api/name/cancleLogin">&ndash;&nbsp;cancleLogin</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>取消登录</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)cancleLogin</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>取消登录</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>