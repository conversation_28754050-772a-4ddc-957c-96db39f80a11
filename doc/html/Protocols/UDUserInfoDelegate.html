<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>UDUserInfoDelegate Protocol Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	
	<optgroup label="Properties">
		
		<option value="//api/name/addresses">addresses</option>
		
		<option value="//api/name/avatarUrl">avatarUrl</option>
		
		<option value="//api/name/birthday">birthday</option>
		
		<option value="//api/name/countryCode">countryCode</option>
		
		<option value="//api/name/defaultAddress">defaultAddress</option>
		
		<option value="//api/name/education">education</option>
		
		<option value="//api/name/email">email</option>
		
		<option value="//api/name/extraPhone">extraPhone</option>
		
		<option value="//api/name/familyNum">familyNum</option>
		
		<option value="//api/name/gender">gender</option>
		
		<option value="//api/name/givenName">givenName</option>
		
		<option value="//api/name/height">height</option>
		
		<option value="//api/name/income">income</option>
		
		<option value="//api/name/marriage">marriage</option>
		
		<option value="//api/name/mobile">mobile</option>
		
		<option value="//api/name/nickname">nickname</option>
		
		<option value="//api/name/privacyCountryCode">privacyCountryCode</option>
		
		<option value="//api/name/regClientId">regClientId</option>
		
		<option value="//api/name/signature">signature</option>
		
		<option value="//api/name/userId">userId</option>
		
		<option value="//api/name/username">username</option>
		
		<option value="//api/name/weight">weight</option>
		
	</optgroup>
	

	

	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">UDUserInfoDelegate Protocol Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Conforms to</th>
	<td>NSObject</td>
</tr><tr>
	<th>Declared in</th>
	<td>UDUserInfoDelegate.h</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/userId" title="userId"></a>
	<h3 class="method-title"><code><a href="#//api/name/userId">&nbsp;&nbsp;userId</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户iD</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *userId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户iD</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/mobile" title="mobile"></a>
	<h3 class="method-title"><code><a href="#//api/name/mobile">&nbsp;&nbsp;mobile</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>手机号</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *mobile</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>手机号</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/email" title="email"></a>
	<h3 class="method-title"><code><a href="#//api/name/email">&nbsp;&nbsp;email</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>邮箱</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *email</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>邮箱</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/username" title="username"></a>
	<h3 class="method-title"><code><a href="#//api/name/username">&nbsp;&nbsp;username</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *username</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/givenName" title="givenName"></a>
	<h3 class="method-title"><code><a href="#//api/name/givenName">&nbsp;&nbsp;givenName</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>真实姓名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *givenName</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>真实姓名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/nickname" title="nickname"></a>
	<h3 class="method-title"><code><a href="#//api/name/nickname">&nbsp;&nbsp;nickname</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>昵称</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *nickname</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>昵称</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/familyNum" title="familyNum"></a>
	<h3 class="method-title"><code><a href="#//api/name/familyNum">&nbsp;&nbsp;familyNum</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭数量</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *familyNum</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭数量</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/gender" title="gender"></a>
	<h3 class="method-title"><code><a href="#//api/name/gender">&nbsp;&nbsp;gender</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>性别</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *gender</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>性别</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/marriage" title="marriage"></a>
	<h3 class="method-title"><code><a href="#//api/name/marriage">&nbsp;&nbsp;marriage</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>婚姻状况</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *marriage</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>婚姻状况</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/birthday" title="birthday"></a>
	<h3 class="method-title"><code><a href="#//api/name/birthday">&nbsp;&nbsp;birthday</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>生日</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *birthday</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>生日</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/education" title="education"></a>
	<h3 class="method-title"><code><a href="#//api/name/education">&nbsp;&nbsp;education</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>学历</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *education</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>学历</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/avatarUrl" title="avatarUrl"></a>
	<h3 class="method-title"><code><a href="#//api/name/avatarUrl">&nbsp;&nbsp;avatarUrl</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>头像</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *avatarUrl</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>头像</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/extraPhone" title="extraPhone"></a>
	<h3 class="method-title"><code><a href="#//api/name/extraPhone">&nbsp;&nbsp;extraPhone</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>备用手机号</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *extraPhone</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>备用手机号</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/income" title="income"></a>
	<h3 class="method-title"><code><a href="#//api/name/income">&nbsp;&nbsp;income</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>收入状况</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *income</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>收入状况</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/height" title="height"></a>
	<h3 class="method-title"><code><a href="#//api/name/height">&nbsp;&nbsp;height</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>身高</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *height</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>身高</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/weight" title="weight"></a>
	<h3 class="method-title"><code><a href="#//api/name/weight">&nbsp;&nbsp;weight</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>体重</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *weight</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>体重</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/countryCode" title="countryCode"></a>
	<h3 class="method-title"><code><a href="#//api/name/countryCode">&nbsp;&nbsp;countryCode</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>国家代码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *countryCode</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>国家代码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/privacyCountryCode" title="privacyCountryCode"></a>
	<h3 class="method-title"><code><a href="#//api/name/privacyCountryCode">&nbsp;&nbsp;privacyCountryCode</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>国家隐私代码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *privacyCountryCode</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>国家隐私代码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/addresses" title="addresses"></a>
	<h3 class="method-title"><code><a href="#//api/name/addresses">&nbsp;&nbsp;addresses</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>地址列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (strong, nonatomic, readonly) NSArray&lt;id&lt;UDAddressDelegate&gt; &gt; *addresses</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>地址列表</p>
			</div>
			

			

			
			<div class="method-subsection see-also-section">
				<h4 class="method-subtitle">See Also</h4>
				<ul>
					
					<li><code><p><a href="../Classes/UserAddressInfo.html">UserAddressInfo</a></p></code></li>
					
				</ul>
			</div>
			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/defaultAddress" title="defaultAddress"></a>
	<h3 class="method-title"><code><a href="#//api/name/defaultAddress">&nbsp;&nbsp;defaultAddress</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>默认地址</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (strong, nonatomic, readonly) id&lt;UDAddressDelegate&gt; defaultAddress</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>默认地址</p>
			</div>
			

			

			
			<div class="method-subsection see-also-section">
				<h4 class="method-subtitle">See Also</h4>
				<ul>
					
					<li><code><p><a href="../Classes/UserAddressInfo.html">UserAddressInfo</a></p></code></li>
					
				</ul>
			</div>
			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/signature" title="signature"></a>
	<h3 class="method-title"><code><a href="#//api/name/signature">&nbsp;&nbsp;signature</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>个性签名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic, readonly) NSString *signature</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>个性签名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/regClientId" title="regClientId"></a>
	<h3 class="method-title"><code><a href="#//api/name/regClientId">&nbsp;&nbsp;regClientId</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>注册来源</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic, readonly) NSString *regClientId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>注册来源</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>