<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>UDDeviceInfoDelegate Protocol Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	
	<optgroup label="Properties">
		
		<option value="//api/name/accessType">accessType</option>
		
		<option value="//api/name/apptypeCode">apptypeCode</option>
		
		<option value="//api/name/apptypeIcon">apptypeIcon</option>
		
		<option value="//api/name/apptypeName">apptypeName</option>
		
		<option value="//api/name/barcode">barcode</option>
		
		<option value="//api/name/bindTime">bindTime</option>
		
		<option value="//api/name/bindType">bindType</option>
		
		<option value="//api/name/brand">brand</option>
		
		<option value="//api/name/categoryGrouping">categoryGrouping</option>
		
		<option value="//api/name/comunicationMode">comunicationMode</option>
		
		<option value="//api/name/configType">configType</option>
		
		<option value="//api/name/devFloorId">devFloorId</option>
		
		<option value="//api/name/devFloorName">devFloorName</option>
		
		<option value="//api/name/devFloorOrderId">devFloorOrderId</option>
		
		<option value="//api/name/devName">devName</option>
		
		<option value="//api/name/deviceGroupId">deviceGroupId</option>
		
		<option value="//api/name/deviceGroupType">deviceGroupType</option>
		
		<option value="//api/name/deviceId">deviceId</option>
		
		<option value="//api/name/deviceName">deviceName</option>
		
		<option value="//api/name/deviceNetType">deviceNetType</option>
		
		<option value="//api/name/deviceRole">deviceRole</option>
		
		<option value="//api/name/deviceRoleType">deviceRoleType</option>
		
		<option value="//api/name/deviceType">deviceType</option>
		
		<option value="//api/name/familyId">familyId</option>
		
		<option value="//api/name/imageAddr1">imageAddr1</option>
		
		<option value="//api/name/imageAddr2">imageAddr2</option>
		
		<option value="//api/name/isOnline">isOnline</option>
		
		<option value="//api/name/isOwned">isOwned</option>
		
		<option value="//api/name/model">model</option>
		
		<option value="//api/name/ownerId">ownerId</option>
		
		<option value="//api/name/ownerInfo">ownerInfo</option>
		
		<option value="//api/name/parentId">parentId</option>
		
		<option value="//api/name/permission">permission</option>
		
		<option value="//api/name/prodNo">prodNo</option>
		
		<option value="//api/name/room">room</option>
		
		<option value="//api/name/roomId">roomId</option>
		
		<option value="//api/name/roomName">roomName</option>
		
		<option value="//api/name/subDevIds">subDevIds</option>
		
		<option value="//api/name/wifiType">wifiType</option>
		
	</optgroup>
	

	

	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">UDDeviceInfoDelegate Protocol Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Conforms to</th>
	<td>NSObject</td>
</tr><tr>
	<th>Declared in</th>
	<td>UDDeviceInfoDelegate.h</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/deviceId" title="deviceId"></a>
	<h3 class="method-title"><code><a href="#//api/name/deviceId">&nbsp;&nbsp;deviceId</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>设备mac</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *deviceId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>设备mac</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/deviceName" title="deviceName"></a>
	<h3 class="method-title"><code><a href="#//api/name/deviceName">&nbsp;&nbsp;deviceName</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>设备名称</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *deviceName</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>设备名称</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/devName" title="devName"></a>
	<h3 class="method-title"><code><a href="#//api/name/devName">&nbsp;&nbsp;devName</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭下设备名称</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *devName</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭下设备名称</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/deviceType" title="deviceType"></a>
	<h3 class="method-title"><code><a href="#//api/name/deviceType">&nbsp;&nbsp;deviceType</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>设备类型</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *deviceType</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>设备类型</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/familyId" title="familyId"></a>
	<h3 class="method-title"><code><a href="#//api/name/familyId">&nbsp;&nbsp;familyId</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>设备家庭id</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *familyId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>设备家庭id</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/ownerId" title="ownerId"></a>
	<h3 class="method-title"><code><a href="#//api/name/ownerId">&nbsp;&nbsp;ownerId</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>设备拥有者ID</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *ownerId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>设备拥有者ID</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/permission" title="permission"></a>
	<h3 class="method-title"><code><a href="#//api/name/permission">&nbsp;&nbsp;permission</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>权限信息(<a href="../Classes/DevicePermission.html">DevicePermission</a>)</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) id&lt;UDDevicePermissionDelegate&gt; permission</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>权限信息(<a href="../Classes/DevicePermission.html">DevicePermission</a>)</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/wifiType" title="wifiType"></a>
	<h3 class="method-title"><code><a href="#//api/name/wifiType">&nbsp;&nbsp;wifiType</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>typeId</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *wifiType</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>typeId</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/deviceNetType" title="deviceNetType"></a>
	<h3 class="method-title"><code><a href="#//api/name/deviceNetType">&nbsp;&nbsp;deviceNetType</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>网器：netDevice，非网器 ：nonNetDevice，</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *deviceNetType</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>网器：netDevice，非网器 ：nonNetDevice，</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/bindTime" title="bindTime"></a>
	<h3 class="method-title"><code><a href="#//api/name/bindTime">&nbsp;&nbsp;bindTime</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>设备绑定时间</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *bindTime</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>设备绑定时间</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/isOnline" title="isOnline"></a>
	<h3 class="method-title"><code><a href="#//api/name/isOnline">&nbsp;&nbsp;isOnline</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>是否在线</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, assign, readonly) BOOL isOnline</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>是否在线</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/ownerInfo" title="ownerInfo"></a>
	<h3 class="method-title"><code><a href="#//api/name/ownerInfo">&nbsp;&nbsp;ownerInfo</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>拥有者信息(<a href="../Classes/DeviceOwnerInfo.html">DeviceOwnerInfo</a>)</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) id&lt;UDDeviceOwnerInfoDelegate&gt; ownerInfo</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>拥有者信息(<a href="../Classes/DeviceOwnerInfo.html">DeviceOwnerInfo</a>)</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/subDevIds" title="subDevIds"></a>
	<h3 class="method-title"><code><a href="#//api/name/subDevIds">&nbsp;&nbsp;subDevIds</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>子设备ID列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) NSArray *subDevIds</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>子设备ID列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/parentId" title="parentId"></a>
	<h3 class="method-title"><code><a href="#//api/name/parentId">&nbsp;&nbsp;parentId</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>父设备ID</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *parentId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>父设备ID</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/deviceRole" title="deviceRole"></a>
	<h3 class="method-title"><code><a href="#//api/name/deviceRole">&nbsp;&nbsp;deviceRole</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>1 普通设备; 2 网关设备; 3 附件设备; 4 子设备;</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *deviceRole</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>1 普通设备; 2 网关设备; 3 附件设备; 4 子设备;</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/deviceRoleType" title="deviceRoleType"></a>
	<h3 class="method-title"><code><a href="#//api/name/deviceRoleType">&nbsp;&nbsp;deviceRoleType</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>主设备/子设备/无主从关系，0 无主设备，1 主设备，2 子设备</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *deviceRoleType</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>主设备/子设备/无主从关系，0 无主设备，1 主设备，2 子设备</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/apptypeName" title="apptypeName"></a>
	<h3 class="method-title"><code><a href="#//api/name/apptypeName">&nbsp;&nbsp;apptypeName</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>应用分类</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *apptypeName</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>应用分类</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/apptypeCode" title="apptypeCode"></a>
	<h3 class="method-title"><code><a href="#//api/name/apptypeCode">&nbsp;&nbsp;apptypeCode</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>应用分类</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *apptypeCode</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>应用分类</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/categoryGrouping" title="categoryGrouping"></a>
	<h3 class="method-title"><code><a href="#//api/name/categoryGrouping">&nbsp;&nbsp;categoryGrouping</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>应用分组</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *categoryGrouping</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>应用分组</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/barcode" title="barcode"></a>
	<h3 class="method-title"><code><a href="#//api/name/barcode">&nbsp;&nbsp;barcode</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>机编码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *barcode</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>机编码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/bindType" title="bindType"></a>
	<h3 class="method-title"><code><a href="#//api/name/bindType">&nbsp;&nbsp;bindType</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>绑定方式（SmartLink SmartAP SoftAP Scancode bt DirectLink Nolink BLE&amp;SoftAP）</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *bindType</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>绑定方式（SmartLink SmartAP SoftAP Scancode bt DirectLink Nolink BLE&amp;SoftAP）</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/brand" title="brand"></a>
	<h3 class="method-title"><code><a href="#//api/name/brand">&nbsp;&nbsp;brand</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>品牌</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *brand</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>品牌</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/imageAddr1" title="imageAddr1"></a>
	<h3 class="method-title"><code><a href="#//api/name/imageAddr1">&nbsp;&nbsp;imageAddr1</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>实物图1</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *imageAddr1</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>实物图1</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/imageAddr2" title="imageAddr2"></a>
	<h3 class="method-title"><code><a href="#//api/name/imageAddr2">&nbsp;&nbsp;imageAddr2</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>实物图2，（备用字段，暂时无用）</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *imageAddr2</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>实物图2，（备用字段，暂时无用）</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/model" title="model"></a>
	<h3 class="method-title"><code><a href="#//api/name/model">&nbsp;&nbsp;model</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>型号</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *model</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>型号</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/prodNo" title="prodNo"></a>
	<h3 class="method-title"><code><a href="#//api/name/prodNo">&nbsp;&nbsp;prodNo</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>产品编码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *prodNo</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>产品编码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/roomName" title="roomName"></a>
	<h3 class="method-title"><code><a href="#//api/name/roomName">&nbsp;&nbsp;roomName</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>房间名称</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *roomName</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>房间名称</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/roomId" title="roomId"></a>
	<h3 class="method-title"><code><a href="#//api/name/roomId">&nbsp;&nbsp;roomId</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>房间ID</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *roomId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>房间ID</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/room" title="room"></a>
	<h3 class="method-title"><code><a href="#//api/name/room">&nbsp;&nbsp;room</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>房间(<a href="../Classes/Room.html">Room</a>)</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) id&lt;UDRoomDelegate&gt; room</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>房间(<a href="../Classes/Room.html">Room</a>)</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/isOwned" title="isOwned"></a>
	<h3 class="method-title"><code><a href="#//api/name/isOwned">&nbsp;&nbsp;isOwned</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>是否归当前用户所有</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, assign, readonly) BOOL isOwned</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>是否归当前用户所有</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/accessType" title="accessType"></a>
	<h3 class="method-title"><code><a href="#//api/name/accessType">&nbsp;&nbsp;accessType</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>联网类型
13:WIFI, 14:SDK_LINUX, 15:SDK_RTOS,31:SDK_ANDROID,90:NB-IoT, 93:Wi-Fi&amp;BLE, 98:DEVICE_CLOUD</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *accessType</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>联网类型
13:WIFI, 14:SDK_LINUX, 15:SDK_RTOS,31:SDK_ANDROID,90:NB-IoT, 93:Wi-Fi&amp;BLE, 98:DEVICE_CLOUD</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/configType" title="configType"></a>
	<h3 class="method-title"><code><a href="#//api/name/configType">&nbsp;&nbsp;configType</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>app端是否支持绑定
0 app端支持配网绑定；1 app端不支持配网绑定</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *configType</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>app端是否支持绑定
0 app端支持配网绑定；1 app端不支持配网绑定</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/comunicationMode" title="comunicationMode"></a>
	<h3 class="method-title"><code><a href="#//api/name/comunicationMode">&nbsp;&nbsp;comunicationMode</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>通讯协议</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *comunicationMode</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>通讯协议</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/devFloorId" title="devFloorId"></a>
	<h3 class="method-title"><code><a href="#//api/name/devFloorId">&nbsp;&nbsp;devFloorId</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层ID</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *devFloorId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层ID</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/devFloorOrderId" title="devFloorOrderId"></a>
	<h3 class="method-title"><code><a href="#//api/name/devFloorOrderId">&nbsp;&nbsp;devFloorOrderId</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>在家庭中的楼层序列（-3到5，没有0）</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *devFloorOrderId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>在家庭中的楼层序列（-3到5，没有0）</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/devFloorName" title="devFloorName"></a>
	<h3 class="method-title"><code><a href="#//api/name/devFloorName">&nbsp;&nbsp;devFloorName</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *devFloorName</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/apptypeIcon" title="apptypeIcon"></a>
	<h3 class="method-title"><code><a href="#//api/name/apptypeIcon">&nbsp;&nbsp;apptypeIcon</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>应用分类图片</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *apptypeIcon</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>应用分类图片</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/deviceGroupId" title="deviceGroupId"></a>
	<h3 class="method-title"><code><a href="#//api/name/deviceGroupId">&nbsp;&nbsp;deviceGroupId</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>组设备ID</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *deviceGroupId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>组设备ID</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/deviceGroupType" title="deviceGroupType"></a>
	<h3 class="method-title"><code><a href="#//api/name/deviceGroupType">&nbsp;&nbsp;deviceGroupType</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>组设备类型</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *deviceGroupType</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>组设备类型</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDDeviceInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>