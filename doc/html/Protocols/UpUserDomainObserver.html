<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>UpUserDomainObserver Protocol Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	

	

	
	<optgroup label="Instance Methods">
		
		<option value="//api/name/onCancleLogin">- onCancleLogin</option>
		
		<option value="//api/name/onCancleQrScanLoginFailure:">- onCancleQrScanLoginFailure:</option>
		
		<option value="//api/name/onCancleQrScanLoginSuccess:">- onCancleQrScanLoginSuccess:</option>
		
		<option value="//api/name/onCurrentFamilyDidChanged:">- onCurrentFamilyDidChanged:</option>
		
		<option value="//api/name/onGetCacheTokenSuccess:">- onGetCacheTokenSuccess:</option>
		
		<option value="//api/name/onInvalidRefreshToken">- onInvalidRefreshToken</option>
		
		<option value="//api/name/onLogInElsewhere">- onLogInElsewhere</option>
		
		<option value="//api/name/onLogOut:">- onLogOut:</option>
		
		<option value="//api/name/onPlannedRefreshTokenFailure">- onPlannedRefreshTokenFailure</option>
		
		<option value="//api/name/onPlannedRefreshTokenSuccess:">- onPlannedRefreshTokenSuccess:</option>
		
		<option value="//api/name/onQrScanLoginFailure:">- onQrScanLoginFailure:</option>
		
		<option value="//api/name/onQrScanLoginSuccess:">- onQrScanLoginSuccess:</option>
		
		<option value="//api/name/onRefeshUserFaild:">- onRefeshUserFaild:</option>
		
		<option value="//api/name/onRefeshUserSuccess:">- onRefeshUserSuccess:</option>
		
		<option value="//api/name/onRefreshAddressListFaild:">- onRefreshAddressListFaild:</option>
		
		<option value="//api/name/onRefreshAddressListSuccess:">- onRefreshAddressListSuccess:</option>
		
		<option value="//api/name/onRefreshComplete:">- onRefreshComplete:</option>
		
		<option value="//api/name/onRefreshDeviceListFaild:">- onRefreshDeviceListFaild:</option>
		
		<option value="//api/name/onRefreshDeviceListSuccess:">- onRefreshDeviceListSuccess:</option>
		
		<option value="//api/name/onRefreshFamilyDetailFailure:">- onRefreshFamilyDetailFailure:</option>
		
		<option value="//api/name/onRefreshFamilyDetailSuccess:">- onRefreshFamilyDetailSuccess:</option>
		
		<option value="//api/name/onRefreshFamilyListFaild:">- onRefreshFamilyListFaild:</option>
		
		<option value="//api/name/onRefreshFamilyListSuccess:">- onRefreshFamilyListSuccess:</option>
		
		<option value="//api/name/onRefreshLoginTerminalFaild:">- onRefreshLoginTerminalFaild:</option>
		
		<option value="//api/name/onRefreshLoginTerminalSuccess:">- onRefreshLoginTerminalSuccess:</option>
		
		<option value="//api/name/onRefreshTokenSuccess:">- onRefreshTokenSuccess:</option>
		
		<option value="//api/name/onTokenMismatchDevice">- onTokenMismatchDevice</option>
		
		<option value="//api/name/onWillLogOut:">- onWillLogOut:</option>
		
	</optgroup>
	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">UpUserDomainObserver Protocol Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Conforms to</th>
	<td>NSObject</td>
</tr><tr>
	<th>Declared in</th>
	<td>UpUserDomainObserver.h</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/onGetCacheTokenSuccess:" title="onGetCacheTokenSuccess:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onGetCacheTokenSuccess:">&ndash;&nbsp;onGetCacheTokenSuccess:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取缓存鉴权信息（鉴权信息未过期）oauthdata</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onGetCacheTokenSuccess:(id&lt;UDAuthDataDelegate&gt;)<em>oauthData</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>oauthData</code></th>
						<td><p>鉴权信息对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取缓存鉴权信息（鉴权信息未过期）oauthdata</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onRefreshTokenSuccess:" title="onRefreshTokenSuccess:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onRefreshTokenSuccess:">&ndash;&nbsp;onRefreshTokenSuccess:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新Token成功 oauthdata</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onRefreshTokenSuccess:(id&lt;UDAuthDataDelegate&gt;)<em>oauthData</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>oauthData</code></th>
						<td><p>鉴权信息对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新Token成功 oauthdata</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onPlannedRefreshTokenSuccess:" title="onPlannedRefreshTokenSuccess:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onPlannedRefreshTokenSuccess:">&ndash;&nbsp;onPlannedRefreshTokenSuccess:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>通过定时任务刷新Token成功</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onPlannedRefreshTokenSuccess:(id&lt;UDAuthDataDelegate&gt;)<em>oauthData</em></code></div>

		    
			

			

			
			<div class="method-subsection availability">
				<h4 class="method-subtitle parameter-title">Availability</h4>
				<p>1.0.0</p>
			</div>
			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>通过定时任务刷新Token成功</p>
			</div>
			

			

			
			<div class="method-subsection see-also-section">
				<h4 class="method-subtitle">See Also</h4>
				<ul>
					
					<li><code><p><a href="../Classes/ApplicationOauthData.html">ApplicationOauthData</a></p></code></li>
					
				</ul>
			</div>
			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onPlannedRefreshTokenFailure" title="onPlannedRefreshTokenFailure"></a>
	<h3 class="method-title"><code><a href="#//api/name/onPlannedRefreshTokenFailure">&ndash;&nbsp;onPlannedRefreshTokenFailure</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>通过定时任务刷新Token失败</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onPlannedRefreshTokenFailure</code></div>

		    
			

			

			
			<div class="method-subsection availability">
				<h4 class="method-subtitle parameter-title">Availability</h4>
				<p>1.0.0</p>
			</div>
			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>通过定时任务刷新Token失败</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onInvalidRefreshToken" title="onInvalidRefreshToken"></a>
	<h3 class="method-title"><code><a href="#//api/name/onInvalidRefreshToken">&ndash;&nbsp;onInvalidRefreshToken</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>RefreshToken 过期</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onInvalidRefreshToken</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>RefreshToken 过期</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onRefeshUserSuccess:" title="onRefeshUserSuccess:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onRefeshUserSuccess:">&ndash;&nbsp;onRefeshUserSuccess:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新用户信息成功</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onRefeshUserSuccess:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>user对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新用户信息成功</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onRefeshUserFaild:" title="onRefeshUserFaild:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onRefeshUserFaild:">&ndash;&nbsp;onRefeshUserFaild:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新用户信息失败</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onRefeshUserFaild:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>user对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新用户信息失败</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onRefreshFamilyListSuccess:" title="onRefreshFamilyListSuccess:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onRefreshFamilyListSuccess:">&ndash;&nbsp;onRefreshFamilyListSuccess:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新家庭列表成功</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onRefreshFamilyListSuccess:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>user对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新家庭列表成功</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onRefreshFamilyListFaild:" title="onRefreshFamilyListFaild:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onRefreshFamilyListFaild:">&ndash;&nbsp;onRefreshFamilyListFaild:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新家庭列表失败</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onRefreshFamilyListFaild:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>user对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新家庭列表失败</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onRefreshAddressListSuccess:" title="onRefreshAddressListSuccess:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onRefreshAddressListSuccess:">&ndash;&nbsp;onRefreshAddressListSuccess:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新地址列表成功</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onRefreshAddressListSuccess:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>user对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新地址列表成功</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onRefreshAddressListFaild:" title="onRefreshAddressListFaild:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onRefreshAddressListFaild:">&ndash;&nbsp;onRefreshAddressListFaild:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新地址列表失败</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onRefreshAddressListFaild:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>user对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新地址列表失败</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onRefreshFamilyDetailSuccess:" title="onRefreshFamilyDetailSuccess:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onRefreshFamilyDetailSuccess:">&ndash;&nbsp;onRefreshFamilyDetailSuccess:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户所有家庭列表的详细信息刷新完成。</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onRefreshFamilyDetailSuccess:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>user对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onRefreshFamilyDetailFailure:" title="onRefreshFamilyDetailFailure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onRefreshFamilyDetailFailure:">&ndash;&nbsp;onRefreshFamilyDetailFailure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户所有家庭列表的详细信息刷新失败。</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onRefreshFamilyDetailFailure:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>user对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onRefreshDeviceListSuccess:" title="onRefreshDeviceListSuccess:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onRefreshDeviceListSuccess:">&ndash;&nbsp;onRefreshDeviceListSuccess:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新设备列表成功</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onRefreshDeviceListSuccess:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>user对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新设备列表成功</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onRefreshDeviceListFaild:" title="onRefreshDeviceListFaild:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onRefreshDeviceListFaild:">&ndash;&nbsp;onRefreshDeviceListFaild:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新设备列表失败</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onRefreshDeviceListFaild:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>user对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新设备列表失败</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onRefreshLoginTerminalSuccess:" title="onRefreshLoginTerminalSuccess:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onRefreshLoginTerminalSuccess:">&ndash;&nbsp;onRefreshLoginTerminalSuccess:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新登录终端成功</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onRefreshLoginTerminalSuccess:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>user对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新登录终端成功</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onRefreshLoginTerminalFaild:" title="onRefreshLoginTerminalFaild:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onRefreshLoginTerminalFaild:">&ndash;&nbsp;onRefreshLoginTerminalFaild:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新设备登录终端失败</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onRefreshLoginTerminalFaild:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>user对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新设备登录终端失败</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onRefreshComplete:" title="onRefreshComplete:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onRefreshComplete:">&ndash;&nbsp;onRefreshComplete:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户刷新完成</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onRefreshComplete:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>user对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户刷新完成</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onLogOut:" title="onLogOut:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onLogOut:">&ndash;&nbsp;onLogOut:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户注销</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onLogOut:(id&lt;UpUserDomainDelegate&gt;)<em>userDomain</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>userDomain</code></th>
						<td><p>userdomain对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户注销</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onWillLogOut:" title="onWillLogOut:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onWillLogOut:">&ndash;&nbsp;onWillLogOut:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户将要注销</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onWillLogOut:(id&lt;UpUserDomainDelegate&gt;)<em>userDomain</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>userDomain</code></th>
						<td><p>userdomain对象</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户将要注销</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onCurrentFamilyDidChanged:" title="onCurrentFamilyDidChanged:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onCurrentFamilyDidChanged:">&ndash;&nbsp;onCurrentFamilyDidChanged:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>当前家庭发生变化</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onCurrentFamilyDidChanged:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>用户</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>当前家庭发生变化</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onQrScanLoginSuccess:" title="onQrScanLoginSuccess:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onQrScanLoginSuccess:">&ndash;&nbsp;onQrScanLoginSuccess:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>扫码授权登录成功</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onQrScanLoginSuccess:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>用户</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>扫码授权登录成功</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onQrScanLoginFailure:" title="onQrScanLoginFailure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onQrScanLoginFailure:">&ndash;&nbsp;onQrScanLoginFailure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>扫码授权登录失败</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onQrScanLoginFailure:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>用户</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>扫码授权登录失败</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onCancleQrScanLoginSuccess:" title="onCancleQrScanLoginSuccess:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onCancleQrScanLoginSuccess:">&ndash;&nbsp;onCancleQrScanLoginSuccess:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>取消扫码授权登录成功</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onCancleQrScanLoginSuccess:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>用户</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>取消扫码授权登录成功</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onCancleQrScanLoginFailure:" title="onCancleQrScanLoginFailure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/onCancleQrScanLoginFailure:">&ndash;&nbsp;onCancleQrScanLoginFailure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>取消扫码授权登录失败</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onCancleQrScanLoginFailure:(id&lt;UDUserDelegate&gt;)<em>user</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>user</code></th>
						<td><p>用户</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>取消扫码授权登录失败</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onCancleLogin" title="onCancleLogin"></a>
	<h3 class="method-title"><code><a href="#//api/name/onCancleLogin">&ndash;&nbsp;onCancleLogin</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>取消登录&ndash;打开登录页面 未登录 点击关闭按钮时触发-</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onCancleLogin</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>取消登录&ndash;打开登录页面 未登录 点击关闭按钮时触发-</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onLogInElsewhere" title="onLogInElsewhere"></a>
	<h3 class="method-title"><code><a href="#//api/name/onLogInElsewhere">&ndash;&nbsp;onLogInElsewhere</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户在其他地方登录事件</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onLogInElsewhere</code></div>

		    
			

			

			

			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/onTokenMismatchDevice" title="onTokenMismatchDevice"></a>
	<h3 class="method-title"><code><a href="#//api/name/onTokenMismatchDevice">&ndash;&nbsp;onTokenMismatchDevice</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>token与设备不匹配</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)onTokenMismatchDevice</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>token与设备不匹配</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainObserver.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>