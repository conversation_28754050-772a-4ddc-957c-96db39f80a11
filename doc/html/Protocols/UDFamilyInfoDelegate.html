<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>UDFamilyInfoDelegate Protocol Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	
	<optgroup label="Properties">
		
		<option value="//api/name/familyLocation">familyLocation</option>
		
		<option value="//api/name/familyName">familyName</option>
		
		<option value="//api/name/familyPosition">familyPosition</option>
		
	</optgroup>
	

	

	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">UDFamilyInfoDelegate Protocol Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Conforms to</th>
	<td>NSObject</td>
</tr><tr>
	<th>Declared in</th>
	<td>UDFamilyInfoDelegate.h</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/familyName" title="familyName"></a>
	<h3 class="method-title"><code><a href="#//api/name/familyName">&nbsp;&nbsp;familyName</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭名称</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *familyName</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭名称</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/familyPosition" title="familyPosition"></a>
	<h3 class="method-title"><code><a href="#//api/name/familyPosition">&nbsp;&nbsp;familyPosition</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭位置</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *familyPosition</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭位置</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/familyLocation" title="familyLocation"></a>
	<h3 class="method-title"><code><a href="#//api/name/familyLocation">&nbsp;&nbsp;familyLocation</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭详细位置信息</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) id&lt;UDFamilyLocationDelegate&gt; familyLocation</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭详细位置信息</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>