<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>UDFamilyDelegate Protocol Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	
	<optgroup label="Properties">
		
		<option value="//api/name/appId">appId</option>
		
		<option value="//api/name/createTime">createTime</option>
		
		<option value="//api/name/defaultFamily">defaultFamily</option>
		
		<option value="//api/name/devices">devices</option>
		
		<option value="//api/name/familyDeviceCount">familyDeviceCount</option>
		
		<option value="//api/name/familyId">familyId</option>
		
		<option value="//api/name/firstMember">firstMember</option>
		
		<option value="//api/name/floorInfos">floorInfos</option>
		
		<option value="//api/name/getDeviceList">getDeviceList</option>
		
		<option value="//api/name/info">info</option>
		
		<option value="//api/name/locationChangeFlag">locationChangeFlag</option>
		
		<option value="//api/name/members">members</option>
		
		<option value="//api/name/owner">owner</option>
		
		<option value="//api/name/ownerId">ownerId</option>
		
		<option value="//api/name/sharedDevices">sharedDevices</option>
		
	</optgroup>
	

	

	
	<optgroup label="Instance Methods">
		
		<option value="//api/name/addRoom:success:failure:">- addRoom:success:failure:</option>
		
		<option value="//api/name/addVirtualMember:memberName:success:failure:">- addVirtualMember:memberName:success:failure:</option>
		
		<option value="//api/name/adminDeleteMember:success:failure:">- adminDeleteMember:success:failure:</option>
		
		<option value="//api/name/adminInvitateMember:success:failure:">- adminInvitateMember:success:failure:</option>
		
		<option value="//api/name/changeFamilyAdminUserId:success:failure:">- changeFamilyAdminUserId:success:failure:</option>
		
		<option value="//api/name/createFloor:success:failure:">- createFloor:success:failure:</option>
		
		<option value="//api/name/deleteFloor:success:failure:">- deleteFloor:success:failure:</option>
		
		<option value="//api/name/destoryFamilyAsAdminSuccess:failure:">- destoryFamilyAsAdminSuccess:failure:</option>
		
		<option value="//api/name/editFloor:success:failure:">- editFloor:success:failure:</option>
		
		<option value="//api/name/exitFamilyAsAdmin:success:failure:">- exitFamilyAsAdmin:success:failure:</option>
		
		<option value="//api/name/exitFamilyAsMemberSuccess:failure:">- exitFamilyAsMemberSuccess:failure:</option>
		
		<option value="//api/name/modifyVirtualMember:memberName:avatarUrl:isCreater:success:failure:">- modifyVirtualMember:memberName:avatarUrl:isCreater:success:failure:</option>
		
		<option value="//api/name/moveDevicesToOtherFamily:devices:success:failure:">- moveDevicesToOtherFamily:devices:success:failure:</option>
		
		<option value="//api/name/moveDevicesToOtherRoom:devices:success:failure:">- moveDevicesToOtherRoom:devices:success:failure:</option>
		
		<option value="//api/name/queryFirstMemeberSuccess:failure:">- queryFirstMemeberSuccess:failure:</option>
		
		<option value="//api/name/removeDevices:success:failure:">- removeDevices:success:failure:</option>
		
		<option value="//api/name/removeRoom:success:failure:">- removeRoom:success:failure:</option>
		
		<option value="//api/name/setDefaultFamily:">- setDefaultFamily:</option>
		
		<option value="//api/name/unBindDevices:success:failure:">- unBindDevices:success:failure:</option>
		
		<option value="//api/name/updateFamilyInfo:success:failure:">- updateFamilyInfo:success:failure:</option>
		
		<option value="//api/name/updateRoomName:roomId:success:failure:">- updateRoomName:roomId:success:failure:</option>
		
	</optgroup>
	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">UDFamilyDelegate Protocol Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Conforms to</th>
	<td>NSObject</td>
</tr><tr>
	<th>Declared in</th>
	<td>UDFamilyDelegate.h</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/adminDeleteMember:success:failure:" title="adminDeleteMember:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/adminDeleteMember:success:failure:">&ndash;&nbsp;adminDeleteMember:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭管理员删除家庭成员，成功后组件订阅者会收到家庭列表更新回调</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)adminDeleteMember:(NSString *)<em>userId</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>userId</code></th>
						<td><p>userId，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭管理员删除家庭成员，成功后组件订阅者会收到家庭列表更新回调</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/adminInvitateMember:success:failure:" title="adminInvitateMember:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/adminInvitateMember:success:failure:">&ndash;&nbsp;adminInvitateMember:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭管理员邀请新成员</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)adminInvitateMember:(NSString *)<em>phone</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>phone</code></th>
						<td><p>手机号，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭管理员邀请新成员</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/changeFamilyAdminUserId:success:failure:" title="changeFamilyAdminUserId:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/changeFamilyAdminUserId:success:failure:">&ndash;&nbsp;changeFamilyAdminUserId:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>更换家庭管理员，成功以后订阅者会收到家庭列表更新的回调</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)changeFamilyAdminUserId:(NSString *)<em>userId</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>userId</code></th>
						<td><p>新管理员ID,必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>更换家庭管理员，成功以后订阅者会收到家庭列表更新的回调</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/addRoom:success:failure:" title="addRoom:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/addRoom:success:failure:">&ndash;&nbsp;addRoom:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>为家庭添加房间，成功以后组件订阅者会收到家庭列表更新的回调</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)addRoom:(RoomArgs *)<em>roomArgs</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>roomArgs</code></th>
						<td><p>房间对象，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调，非必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调，非必填</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>为家庭添加房间，成功以后组件订阅者会收到家庭列表更新的回调</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/destoryFamilyAsAdminSuccess:failure:" title="destoryFamilyAsAdminSuccess:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/destoryFamilyAsAdminSuccess:failure:">&ndash;&nbsp;destoryFamilyAsAdminSuccess:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>解散家庭，成功以后订阅者会收到家庭列表更新的回调</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)destoryFamilyAsAdminSuccess:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调，非必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调，非必填</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>解散家庭，成功以后订阅者会收到家庭列表更新的回调</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/removeRoom:success:failure:" title="removeRoom:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/removeRoom:success:failure:">&ndash;&nbsp;removeRoom:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>删除家庭房间，成功以后组件订阅者会收到家庭列表更新的回调</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)removeRoom:(NSString *)<em>roomId</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>roomId</code></th>
						<td><p>房间ID必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>删除家庭房间，成功以后组件订阅者会收到家庭列表更新的回调</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/updateFamilyInfo:success:failure:" title="updateFamilyInfo:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/updateFamilyInfo:success:failure:">&ndash;&nbsp;updateFamilyInfo:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>更新家庭信息，成功以后订阅者会收到家庭列表更新的回调</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)updateFamilyInfo:(FamilyArgs *)<em>familyArgs</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>familyArgs</code></th>
						<td><p>家庭参数</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>更新家庭信息，成功以后订阅者会收到家庭列表更新的回调</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/updateRoomName:roomId:success:failure:" title="updateRoomName:roomId:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/updateRoomName:roomId:success:failure:">&ndash;&nbsp;updateRoomName:roomId:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>更新房间名称</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)updateRoomName:(NSString *)<em>roomName</em> roomId:(NSString *)<em>roomId</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>roomName</code></th>
						<td><p>新的房间名称，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>roomId</code></th>
						<td><p>房间ID，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>更新房间名称</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/exitFamilyAsAdmin:success:failure:" title="exitFamilyAsAdmin:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/exitFamilyAsAdmin:success:failure:">&ndash;&nbsp;exitFamilyAsAdmin:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>管理员退出家庭，成功以后订阅者会收到家庭列表和设备列表更新的回调</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)exitFamilyAsAdmin:(NSString *)<em>userId</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>userId</code></th>
						<td><p>新管理员ID，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>管理员退出家庭，成功以后订阅者会收到家庭列表和设备列表更新的回调</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/exitFamilyAsMemberSuccess:failure:" title="exitFamilyAsMemberSuccess:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/exitFamilyAsMemberSuccess:failure:">&ndash;&nbsp;exitFamilyAsMemberSuccess:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭成员退出家庭，成功以后订阅者会收到家庭列表更新的回调</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)exitFamilyAsMemberSuccess:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭成员退出家庭，成功以后订阅者会收到家庭列表更新的回调</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/familyId" title="familyId"></a>
	<h3 class="method-title"><code><a href="#//api/name/familyId">&nbsp;&nbsp;familyId</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭id</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *familyId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭id</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/createTime" title="createTime"></a>
	<h3 class="method-title"><code><a href="#//api/name/createTime">&nbsp;&nbsp;createTime</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>创建时间</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *createTime</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>创建时间</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/appId" title="appId"></a>
	<h3 class="method-title"><code><a href="#//api/name/appId">&nbsp;&nbsp;appId</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>appId</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *appId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>appId</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/devices" title="devices"></a>
	<h3 class="method-title"><code><a href="#//api/name/devices">&nbsp;&nbsp;devices</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭设备列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) NSDictionary&lt;NSString*id&lt;UDDeviceDelegate&gt; &gt; *devices</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭设备列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/getDeviceList" title="getDeviceList"></a>
	<h3 class="method-title"><code><a href="#//api/name/getDeviceList">&nbsp;&nbsp;getDeviceList</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取此家庭的设备</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) NSArray&lt;id&lt;UDDeviceDelegate&gt; &gt; *getDeviceList</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取此家庭的设备</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/info" title="info"></a>
	<h3 class="method-title"><code><a href="#//api/name/info">&nbsp;&nbsp;info</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭信息</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) id&lt;UDFamilyInfoDelegate&gt; info</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭信息</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/members" title="members"></a>
	<h3 class="method-title"><code><a href="#//api/name/members">&nbsp;&nbsp;members</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取成员列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) NSArray&lt;id&lt;UDFamilyMemberDelegate&gt; &gt; *members</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取成员列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/firstMember" title="firstMember"></a>
	<h3 class="method-title"><code><a href="#//api/name/firstMember">&nbsp;&nbsp;firstMember</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>第一个成员</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) id&lt;UDFamilyMemberDelegate&gt; firstMember</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>第一个成员</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/owner" title="owner"></a>
	<h3 class="method-title"><code><a href="#//api/name/owner">&nbsp;&nbsp;owner</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭管理员信息</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) id&lt;UDMemberInfoDelegate&gt; owner</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭管理员信息</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/ownerId" title="ownerId"></a>
	<h3 class="method-title"><code><a href="#//api/name/ownerId">&nbsp;&nbsp;ownerId</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭管理员信息ID</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) NSString *ownerId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭管理员信息ID</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/floorInfos" title="floorInfos"></a>
	<h3 class="method-title"><code><a href="#//api/name/floorInfos">&nbsp;&nbsp;floorInfos</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭楼层列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) NSArray&lt;id&lt;UDFloorInfoDelegate&gt; &gt; *floorInfos</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭楼层列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/sharedDevices" title="sharedDevices"></a>
	<h3 class="method-title"><code><a href="#//api/name/sharedDevices">&nbsp;&nbsp;sharedDevices</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>当前用户分享的设备</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) NSDictionary&lt;NSString*id&lt;UDDeviceDelegate&gt; &gt; *sharedDevices</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>当前用户分享的设备</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/defaultFamily" title="defaultFamily"></a>
	<h3 class="method-title"><code><a href="#//api/name/defaultFamily">&nbsp;&nbsp;defaultFamily</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>是否默认家庭</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, assign, readonly) BOOL defaultFamily</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>是否默认家庭</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/locationChangeFlag" title="locationChangeFlag"></a>
	<h3 class="method-title"><code><a href="#//api/name/locationChangeFlag">&nbsp;&nbsp;locationChangeFlag</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭位置更改标志位</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (assign, nonatomic, readonly) BOOL locationChangeFlag</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭位置更改标志位</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/familyDeviceCount" title="familyDeviceCount"></a>
	<h3 class="method-title"><code><a href="#//api/name/familyDeviceCount">&nbsp;&nbsp;familyDeviceCount</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭设备数量，包括网器+非网器设备的数量</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy, readonly) NSString *familyDeviceCount</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭设备数量，包括网器+非网器设备的数量</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/moveDevicesToOtherRoom:devices:success:failure:" title="moveDevicesToOtherRoom:devices:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/moveDevicesToOtherRoom:devices:success:failure:">&ndash;&nbsp;moveDevicesToOtherRoom:devices:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>批量移动设备到新的房间，成功以后订阅者会收到家庭列表和设备列表更新的回调</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)moveDevicesToOtherRoom:(id&lt;UDRoomDelegate&gt;)<em>newRoom</em> devices:(NSArray&lt;id&lt;UDDeviceDelegate&gt; &gt; *)<em>devices</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>newRoom</code></th>
						<td><p>新的房间</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>devices</code></th>
						<td><p>要移动的设备</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>批量移动设备到新的房间，成功以后订阅者会收到家庭列表和设备列表更新的回调</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/removeDevices:success:failure:" title="removeDevices:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/removeDevices:success:failure:">&ndash;&nbsp;removeDevices:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭批量移除设备，成功以后订阅者会收到家庭列表和设备列表更新的回调</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)removeDevices:(NSArray&lt;id&lt;UDDeviceDelegate&gt; &gt; *)<em>devices</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>devices</code></th>
						<td><p>设备，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭批量移除设备，成功以后订阅者会收到家庭列表和设备列表更新的回调</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setDefaultFamily:" title="setDefaultFamily:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setDefaultFamily:">&ndash;&nbsp;setDefaultFamily:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>设置默认家庭</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setDefaultFamily:(BOOL)<em>defaultFamily</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>defaultFamily</code></th>
						<td><p>YES是 NO否</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>设置默认家庭</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/unBindDevices:success:failure:" title="unBindDevices:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/unBindDevices:success:failure:">&ndash;&nbsp;unBindDevices:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭管理员家庭批量解绑设备，解绑成功后组件订阅者会收到家庭列表和设备列表的更新回调</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)unBindDevices:(NSArray&lt;id&lt;UDDeviceDelegate&gt; &gt; *)<em>devices</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>devices</code></th>
						<td><p>设备，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调，非必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调，非必填</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭管理员家庭批量解绑设备，解绑成功后组件订阅者会收到家庭列表和设备列表的更新回调</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/moveDevicesToOtherFamily:devices:success:failure:" title="moveDevicesToOtherFamily:devices:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/moveDevicesToOtherFamily:devices:success:failure:">&ndash;&nbsp;moveDevicesToOtherFamily:devices:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>批量移动设备到新的家庭，成功以后订阅者会收到家庭列表和设备列表更新的回调</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)moveDevicesToOtherFamily:(NSString *)<em>newFamilyId</em> devices:(NSArray&lt;id&lt;UDDeviceDelegate&gt; &gt; *)<em>devices</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>newFamilyId</code></th>
						<td><p>新的家庭ID，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>devices</code></th>
						<td><p><a href="../Classes/Device.html">Device</a>,必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调，非必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调，非必填</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>批量移动设备到新的家庭，成功以后订阅者会收到家庭列表和设备列表更新的回调</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/createFloor:success:failure:" title="createFloor:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/createFloor:success:failure:">&ndash;&nbsp;createFloor:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>添加楼层</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)createFloor:(FloorArg *)<em>floorArg</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>floorArg</code></th>
						<td><p>楼层参数</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>添加楼层</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/deleteFloor:success:failure:" title="deleteFloor:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/deleteFloor:success:failure:">&ndash;&nbsp;deleteFloor:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>删除楼层</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)deleteFloor:(NSString *)<em>floorId</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>floorId</code></th>
						<td><p>楼层id</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>删除楼层</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/editFloor:success:failure:" title="editFloor:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/editFloor:success:failure:">&ndash;&nbsp;editFloor:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>编辑楼层</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)editFloor:(FloorArg *)<em>floorArg</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>floorArg</code></th>
						<td><p>楼层参数</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>编辑楼层</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/queryFirstMemeberSuccess:failure:" title="queryFirstMemeberSuccess:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/queryFirstMemeberSuccess:failure:">&ndash;&nbsp;queryFirstMemeberSuccess:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>  查询第一个加入家庭的家庭成员</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)queryFirstMemeberSuccess:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>  查询第一个加入家庭的家庭成员</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/addVirtualMember:memberName:success:failure:" title="addVirtualMember:memberName:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/addVirtualMember:memberName:success:failure:">&ndash;&nbsp;addVirtualMember:memberName:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>   虚拟用户加入家庭</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)addVirtualMember:(NSString *)<em>memberId</em> memberName:(NSString *)<em>memberName</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>memberId</code></th>
						<td><p>虚拟用户用户中心ID</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>memberName</code></th>
						<td><p>用户加入家庭附属参数,为用户在家庭中昵称</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>   虚拟用户加入家庭</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/modifyVirtualMember:memberName:avatarUrl:isCreater:success:failure:" title="modifyVirtualMember:memberName:avatarUrl:isCreater:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/modifyVirtualMember:memberName:avatarUrl:isCreater:success:failure:">&ndash;&nbsp;modifyVirtualMember:memberName:avatarUrl:isCreater:success:failure:</a></code>
<span class="task-item-suffix">required method</span></h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>  编辑虚拟用户</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)modifyVirtualMember:(NSString *)<em>memberId</em> memberName:(NSString *)<em>memberName</em> avatarUrl:(NSString *)<em>avatarUrl</em> isCreater:(BOOL)<em>isCreater</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>memberId</code></th>
						<td><p>虚拟成员用户中心id</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>memberName</code></th>
						<td><p>成员名称</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>avatarUrl</code></th>
						<td><p>成员头像地址</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>isCreater</code></th>
						<td><p>true:创建者；false：非创建者</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>  编辑虚拟用户</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>