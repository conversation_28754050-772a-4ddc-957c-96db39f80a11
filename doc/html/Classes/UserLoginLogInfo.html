<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>UserLoginLogInfo Class Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	
	<optgroup label="Properties">
		
		<option value="//api/name/realAction_at">realAction_at</option>
		
		<option value="//api/name/realCity">realCity</option>
		
		<option value="//api/name/realClient_id">realClient_id</option>
		
		<option value="//api/name/realLatitude">realLatitude</option>
		
		<option value="//api/name/realLogId">realLogId</option>
		
		<option value="//api/name/realLongitude">realLongitude</option>
		
		<option value="//api/name/realProvince">realProvince</option>
		
		<option value="//api/name/realUser_id">realUser_id</option>
		
		<option value="//api/name/realUser_name">realUser_name</option>
		
	</optgroup>
	

	

	
	<optgroup label="Instance Methods">
		
		<option value="//api/name/action_at">- action_at</option>
		
		<option value="//api/name/city">- city</option>
		
		<option value="//api/name/client_id">- client_id</option>
		
		<option value="//api/name/latitude">- latitude</option>
		
		<option value="//api/name/logId">- logId</option>
		
		<option value="//api/name/longitude">- longitude</option>
		
		<option value="//api/name/province">- province</option>
		
		<option value="//api/name/user_id">- user_id</option>
		
		<option value="//api/name/user_name">- user_name</option>
		
	</optgroup>
	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">UserLoginLogInfo Class Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Inherits from</th>
	<td>NSObject</td>
</tr><tr>
	<th>Conforms to</th>
	<td>NSCoding<br /><a href="../Protocols/UDUserLoginLogInfoDelegate.html">UDUserLoginLogInfoDelegate</a></td>
</tr><tr>
	<th>Declared in</th>
	<td>UserLoginLogInfo.h<br />UserLoginLogInfo.m</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						<h2 class="task-title">Other Methods</h2>

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/action_at" title="action_at"></a>
	<h3 class="method-title"><code><a href="#//api/name/action_at">&ndash;&nbsp;action_at</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>登录时间</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)action_at</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>登录时间</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserLoginLogInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/city" title="city"></a>
	<h3 class="method-title"><code><a href="#//api/name/city">&ndash;&nbsp;city</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>城市</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)city</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>城市</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserLoginLogInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/client_id" title="client_id"></a>
	<h3 class="method-title"><code><a href="#//api/name/client_id">&ndash;&nbsp;client_id</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>登录终端应用Id</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)client_id</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>登录终端应用Id</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserLoginLogInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/latitude" title="latitude"></a>
	<h3 class="method-title"><code><a href="#//api/name/latitude">&ndash;&nbsp;latitude</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>纬度</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)latitude</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>纬度</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserLoginLogInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/logId" title="logId"></a>
	<h3 class="method-title"><code><a href="#//api/name/logId">&ndash;&nbsp;logId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>登录记录id</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)logId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>登录记录id</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserLoginLogInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/longitude" title="longitude"></a>
	<h3 class="method-title"><code><a href="#//api/name/longitude">&ndash;&nbsp;longitude</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>经度</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)longitude</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>经度</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserLoginLogInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/province" title="province"></a>
	<h3 class="method-title"><code><a href="#//api/name/province">&ndash;&nbsp;province</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>省份</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)province</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>省份</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserLoginLogInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/user_id" title="user_id"></a>
	<h3 class="method-title"><code><a href="#//api/name/user_id">&ndash;&nbsp;user_id</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户id</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)user_id</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户id</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserLoginLogInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/user_name" title="user_name"></a>
	<h3 class="method-title"><code><a href="#//api/name/user_name">&ndash;&nbsp;user_name</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)user_name</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserLoginLogInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						

						<a title="Extension Methods" name="task_Extension Methods"></a>
						<h2 class="task-title">Extension Methods</h2>

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/realLogId" title="realLogId"></a>
	<h3 class="method-title"><code><a href="#//api/name/realLogId">&nbsp;&nbsp;realLogId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>登录记录id</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realLogId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>登录记录id</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserLoginLogInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realUser_id" title="realUser_id"></a>
	<h3 class="method-title"><code><a href="#//api/name/realUser_id">&nbsp;&nbsp;realUser_id</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户id</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realUser_id</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户id</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserLoginLogInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realUser_name" title="realUser_name"></a>
	<h3 class="method-title"><code><a href="#//api/name/realUser_name">&nbsp;&nbsp;realUser_name</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realUser_name</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserLoginLogInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realClient_id" title="realClient_id"></a>
	<h3 class="method-title"><code><a href="#//api/name/realClient_id">&nbsp;&nbsp;realClient_id</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>登录终端应用Id</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realClient_id</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>登录终端应用Id</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserLoginLogInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realProvince" title="realProvince"></a>
	<h3 class="method-title"><code><a href="#//api/name/realProvince">&nbsp;&nbsp;realProvince</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>省份</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realProvince</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>省份</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserLoginLogInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realCity" title="realCity"></a>
	<h3 class="method-title"><code><a href="#//api/name/realCity">&nbsp;&nbsp;realCity</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>城市</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realCity</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>城市</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserLoginLogInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realLongitude" title="realLongitude"></a>
	<h3 class="method-title"><code><a href="#//api/name/realLongitude">&nbsp;&nbsp;realLongitude</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>经度</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realLongitude</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>经度</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserLoginLogInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realLatitude" title="realLatitude"></a>
	<h3 class="method-title"><code><a href="#//api/name/realLatitude">&nbsp;&nbsp;realLatitude</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>纬度</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realLatitude</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>纬度</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserLoginLogInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realAction_at" title="realAction_at"></a>
	<h3 class="method-title"><code><a href="#//api/name/realAction_at">&nbsp;&nbsp;realAction_at</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>登录时间</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realAction_at</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>登录时间</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserLoginLogInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>