<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>UserInfo Class Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	
	<optgroup label="Properties">
		
		<option value="//api/name/realAddresses">realAddresses</option>
		
		<option value="//api/name/realAvatarUrl">realAvatarUrl</option>
		
		<option value="//api/name/realBirthday">realBirthday</option>
		
		<option value="//api/name/realCountryCode">realCountryCode</option>
		
		<option value="//api/name/realDefaultAddress">realDefaultAddress</option>
		
		<option value="//api/name/realEducation">realEducation</option>
		
		<option value="//api/name/realEmail">realEmail</option>
		
		<option value="//api/name/realExtraPhone">realExtraPhone</option>
		
		<option value="//api/name/realFamilyNum">realFamilyNum</option>
		
		<option value="//api/name/realGender">realGender</option>
		
		<option value="//api/name/realGivenName">realGivenName</option>
		
		<option value="//api/name/realHeight">realHeight</option>
		
		<option value="//api/name/realIncome">realIncome</option>
		
		<option value="//api/name/realMarriage">realMarriage</option>
		
		<option value="//api/name/realMobile">realMobile</option>
		
		<option value="//api/name/realNickname">realNickname</option>
		
		<option value="//api/name/realPrivacyCountryCode">realPrivacyCountryCode</option>
		
		<option value="//api/name/realRegClientId">realRegClientId</option>
		
		<option value="//api/name/realSignature">realSignature</option>
		
		<option value="//api/name/realUserId">realUserId</option>
		
		<option value="//api/name/realUsername">realUsername</option>
		
		<option value="//api/name/realWeight">realWeight</option>
		
	</optgroup>
	

	

	
	<optgroup label="Instance Methods">
		
		<option value="//api/name/addresses">- addresses</option>
		
		<option value="//api/name/avatarUrl">- avatarUrl</option>
		
		<option value="//api/name/birthday">- birthday</option>
		
		<option value="//api/name/countryCode">- countryCode</option>
		
		<option value="//api/name/defaultAddress">- defaultAddress</option>
		
		<option value="//api/name/education">- education</option>
		
		<option value="//api/name/email">- email</option>
		
		<option value="//api/name/extraPhone">- extraPhone</option>
		
		<option value="//api/name/familyNum">- familyNum</option>
		
		<option value="//api/name/gender">- gender</option>
		
		<option value="//api/name/givenName">- givenName</option>
		
		<option value="//api/name/height">- height</option>
		
		<option value="//api/name/income">- income</option>
		
		<option value="//api/name/marriage">- marriage</option>
		
		<option value="//api/name/mobile">- mobile</option>
		
		<option value="//api/name/nickname">- nickname</option>
		
		<option value="//api/name/privacyCountryCode">- privacyCountryCode</option>
		
		<option value="//api/name/regClientId">- regClientId</option>
		
		<option value="//api/name/signature">- signature</option>
		
		<option value="//api/name/userId">- userId</option>
		
		<option value="//api/name/username">- username</option>
		
		<option value="//api/name/weight">- weight</option>
		
	</optgroup>
	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">UserInfo Class Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Inherits from</th>
	<td>NSObject</td>
</tr><tr>
	<th>Conforms to</th>
	<td>NSCoding<br /><a href="../Protocols/UDUserInfoDelegate.html">UDUserInfoDelegate</a></td>
</tr><tr>
	<th>Declared in</th>
	<td>UserInfo.h<br />UserInfo.m</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						<h2 class="task-title">Other Methods</h2>

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/countryCode" title="countryCode"></a>
	<h3 class="method-title"><code><a href="#//api/name/countryCode">&ndash;&nbsp;countryCode</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>国家代码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)countryCode</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>国家代码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/privacyCountryCode" title="privacyCountryCode"></a>
	<h3 class="method-title"><code><a href="#//api/name/privacyCountryCode">&ndash;&nbsp;privacyCountryCode</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>国家隐私代码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)privacyCountryCode</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>国家隐私代码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/avatarUrl" title="avatarUrl"></a>
	<h3 class="method-title"><code><a href="#//api/name/avatarUrl">&ndash;&nbsp;avatarUrl</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>头像</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)avatarUrl</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>头像</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/birthday" title="birthday"></a>
	<h3 class="method-title"><code><a href="#//api/name/birthday">&ndash;&nbsp;birthday</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>生日</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)birthday</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>生日</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/education" title="education"></a>
	<h3 class="method-title"><code><a href="#//api/name/education">&ndash;&nbsp;education</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>学历</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)education</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>学历</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/email" title="email"></a>
	<h3 class="method-title"><code><a href="#//api/name/email">&ndash;&nbsp;email</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>邮箱</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)email</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>邮箱</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/extraPhone" title="extraPhone"></a>
	<h3 class="method-title"><code><a href="#//api/name/extraPhone">&ndash;&nbsp;extraPhone</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>备用手机号</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)extraPhone</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>备用手机号</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/familyNum" title="familyNum"></a>
	<h3 class="method-title"><code><a href="#//api/name/familyNum">&ndash;&nbsp;familyNum</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭数量</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)familyNum</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭数量</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/gender" title="gender"></a>
	<h3 class="method-title"><code><a href="#//api/name/gender">&ndash;&nbsp;gender</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>性别</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)gender</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>性别</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/givenName" title="givenName"></a>
	<h3 class="method-title"><code><a href="#//api/name/givenName">&ndash;&nbsp;givenName</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>真实姓名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)givenName</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>真实姓名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/height" title="height"></a>
	<h3 class="method-title"><code><a href="#//api/name/height">&ndash;&nbsp;height</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>身高</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)height</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>身高</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/income" title="income"></a>
	<h3 class="method-title"><code><a href="#//api/name/income">&ndash;&nbsp;income</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>收入状况</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)income</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>收入状况</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/marriage" title="marriage"></a>
	<h3 class="method-title"><code><a href="#//api/name/marriage">&ndash;&nbsp;marriage</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>婚姻状况</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)marriage</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>婚姻状况</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/mobile" title="mobile"></a>
	<h3 class="method-title"><code><a href="#//api/name/mobile">&ndash;&nbsp;mobile</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>手机号</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)mobile</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>手机号</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/nickname" title="nickname"></a>
	<h3 class="method-title"><code><a href="#//api/name/nickname">&ndash;&nbsp;nickname</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>昵称</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)nickname</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>昵称</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/userId" title="userId"></a>
	<h3 class="method-title"><code><a href="#//api/name/userId">&ndash;&nbsp;userId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户iD</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)userId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户iD</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/username" title="username"></a>
	<h3 class="method-title"><code><a href="#//api/name/username">&ndash;&nbsp;username</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)username</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/weight" title="weight"></a>
	<h3 class="method-title"><code><a href="#//api/name/weight">&ndash;&nbsp;weight</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>体重</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)weight</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>体重</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/addresses" title="addresses"></a>
	<h3 class="method-title"><code><a href="#//api/name/addresses">&ndash;&nbsp;addresses</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>地址列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSArray&lt;id&lt;UDAddressDelegate&gt; &gt; *)addresses</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>地址列表</p>
			</div>
			

			

			
			<div class="method-subsection see-also-section">
				<h4 class="method-subtitle">See Also</h4>
				<ul>
					
					<li><code><p><a href="../Classes/UserAddressInfo.html">UserAddressInfo</a></p></code></li>
					
				</ul>
			</div>
			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/defaultAddress" title="defaultAddress"></a>
	<h3 class="method-title"><code><a href="#//api/name/defaultAddress">&ndash;&nbsp;defaultAddress</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>默认地址</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (id&lt;UDAddressDelegate&gt;)defaultAddress</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>默认地址</p>
			</div>
			

			

			
			<div class="method-subsection see-also-section">
				<h4 class="method-subtitle">See Also</h4>
				<ul>
					
					<li><code><p><a href="../Classes/UserAddressInfo.html">UserAddressInfo</a></p></code></li>
					
				</ul>
			</div>
			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/regClientId" title="regClientId"></a>
	<h3 class="method-title"><code><a href="#//api/name/regClientId">&ndash;&nbsp;regClientId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>注册来源</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)regClientId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>注册来源</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/signature" title="signature"></a>
	<h3 class="method-title"><code><a href="#//api/name/signature">&ndash;&nbsp;signature</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>个性签名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)signature</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>个性签名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						

						<a title="Extension Methods" name="task_Extension Methods"></a>
						<h2 class="task-title">Extension Methods</h2>

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/realUserId" title="realUserId"></a>
	<h3 class="method-title"><code><a href="#//api/name/realUserId">&nbsp;&nbsp;realUserId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户iD</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realUserId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户iD</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realMobile" title="realMobile"></a>
	<h3 class="method-title"><code><a href="#//api/name/realMobile">&nbsp;&nbsp;realMobile</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>手机号</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realMobile</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>手机号</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realEmail" title="realEmail"></a>
	<h3 class="method-title"><code><a href="#//api/name/realEmail">&nbsp;&nbsp;realEmail</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>邮箱</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realEmail</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>邮箱</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realUsername" title="realUsername"></a>
	<h3 class="method-title"><code><a href="#//api/name/realUsername">&nbsp;&nbsp;realUsername</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realUsername</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realGivenName" title="realGivenName"></a>
	<h3 class="method-title"><code><a href="#//api/name/realGivenName">&nbsp;&nbsp;realGivenName</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>真实姓名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realGivenName</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>真实姓名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realNickname" title="realNickname"></a>
	<h3 class="method-title"><code><a href="#//api/name/realNickname">&nbsp;&nbsp;realNickname</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>昵称</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realNickname</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>昵称</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realFamilyNum" title="realFamilyNum"></a>
	<h3 class="method-title"><code><a href="#//api/name/realFamilyNum">&nbsp;&nbsp;realFamilyNum</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>家庭数量</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realFamilyNum</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>家庭数量</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realGender" title="realGender"></a>
	<h3 class="method-title"><code><a href="#//api/name/realGender">&nbsp;&nbsp;realGender</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>性别</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realGender</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>性别</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realMarriage" title="realMarriage"></a>
	<h3 class="method-title"><code><a href="#//api/name/realMarriage">&nbsp;&nbsp;realMarriage</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>婚姻状况</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realMarriage</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>婚姻状况</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realBirthday" title="realBirthday"></a>
	<h3 class="method-title"><code><a href="#//api/name/realBirthday">&nbsp;&nbsp;realBirthday</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>生日</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realBirthday</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>生日</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realEducation" title="realEducation"></a>
	<h3 class="method-title"><code><a href="#//api/name/realEducation">&nbsp;&nbsp;realEducation</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>学历</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realEducation</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>学历</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realAvatarUrl" title="realAvatarUrl"></a>
	<h3 class="method-title"><code><a href="#//api/name/realAvatarUrl">&nbsp;&nbsp;realAvatarUrl</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>头像</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realAvatarUrl</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>头像</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realExtraPhone" title="realExtraPhone"></a>
	<h3 class="method-title"><code><a href="#//api/name/realExtraPhone">&nbsp;&nbsp;realExtraPhone</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>备用手机号</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realExtraPhone</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>备用手机号</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realIncome" title="realIncome"></a>
	<h3 class="method-title"><code><a href="#//api/name/realIncome">&nbsp;&nbsp;realIncome</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>收入状况</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realIncome</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>收入状况</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realHeight" title="realHeight"></a>
	<h3 class="method-title"><code><a href="#//api/name/realHeight">&nbsp;&nbsp;realHeight</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>身高</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realHeight</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>身高</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realWeight" title="realWeight"></a>
	<h3 class="method-title"><code><a href="#//api/name/realWeight">&nbsp;&nbsp;realWeight</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>体重</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realWeight</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>体重</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realCountryCode" title="realCountryCode"></a>
	<h3 class="method-title"><code><a href="#//api/name/realCountryCode">&nbsp;&nbsp;realCountryCode</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>国家代码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realCountryCode</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>国家代码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realPrivacyCountryCode" title="realPrivacyCountryCode"></a>
	<h3 class="method-title"><code><a href="#//api/name/realPrivacyCountryCode">&nbsp;&nbsp;realPrivacyCountryCode</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>国家隐私代码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realPrivacyCountryCode</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>国家隐私代码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realAddresses" title="realAddresses"></a>
	<h3 class="method-title"><code><a href="#//api/name/realAddresses">&nbsp;&nbsp;realAddresses</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>地址列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (strong, nonatomic) NSArray&lt;id&lt;UDAddressDelegate&gt; &gt; *realAddresses</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>地址列表</p>
			</div>
			

			

			
			<div class="method-subsection see-also-section">
				<h4 class="method-subtitle">See Also</h4>
				<ul>
					
					<li><code><p><a href="../Classes/UserAddressInfo.html">UserAddressInfo</a></p></code></li>
					
				</ul>
			</div>
			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realDefaultAddress" title="realDefaultAddress"></a>
	<h3 class="method-title"><code><a href="#//api/name/realDefaultAddress">&nbsp;&nbsp;realDefaultAddress</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>默认地址</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (strong, nonatomic) id&lt;UDAddressDelegate&gt; realDefaultAddress</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>默认地址</p>
			</div>
			

			

			
			<div class="method-subsection see-also-section">
				<h4 class="method-subtitle">See Also</h4>
				<ul>
					
					<li><code><p><a href="../Classes/UserAddressInfo.html">UserAddressInfo</a></p></code></li>
					
				</ul>
			</div>
			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realSignature" title="realSignature"></a>
	<h3 class="method-title"><code><a href="#//api/name/realSignature">&nbsp;&nbsp;realSignature</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>个性签名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *realSignature</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>个性签名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realRegClientId" title="realRegClientId"></a>
	<h3 class="method-title"><code><a href="#//api/name/realRegClientId">&nbsp;&nbsp;realRegClientId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>注册来源</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *realRegClientId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>注册来源</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>