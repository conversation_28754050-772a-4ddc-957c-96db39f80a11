<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>UPUserDomainSettings Class Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	

	

	
	<optgroup label="Instance Methods">
		
		<option value="//api/name/getClientId">- getClientId</option>
		
		<option value="//api/name/getHttpRequestRetryDelay">- getHttpRequestRetryDelay</option>
		
		<option value="//api/name/getUserDomainPlatform">- getUserDomainPlatform</option>
		
		<option value="//api/name/isRefreshDeviceListEnable">- isRefreshDeviceListEnable</option>
		
		<option value="//api/name/isRefreshFamilyListEnable">- isRefreshFamilyListEnable</option>
		
		<option value="//api/name/isSingleClientCheckEnabled">- isSingleClientCheckEnabled</option>
		
		<option value="//api/name/setClientId:">- setClientId:</option>
		
		<option value="//api/name/setHttpRequestRetryDelay:">- setHttpRequestRetryDelay:</option>
		
		<option value="//api/name/setRefreshDeviceListEnable:">- setRefreshDeviceListEnable:</option>
		
		<option value="//api/name/setRefreshFamilyListEnable:">- setRefreshFamilyListEnable:</option>
		
		<option value="//api/name/setSingleClientCheckEnabled:">- setSingleClientCheckEnabled:</option>
		
		<option value="//api/name/setUserDomainPlatform:">- setUserDomainPlatform:</option>
		
	</optgroup>
	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">UPUserDomainSettings Class Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Inherits from</th>
	<td>NSObject</td>
</tr><tr>
	<th>Conforms to</th>
	<td><a href="../Protocols/UPUserDomainSettingsDelegate.html">UPUserDomainSettingsDelegate</a></td>
</tr><tr>
	<th>Declared in</th>
	<td>UPUserDomainSettings.h<br />UPUserDomainSettings.m</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/getClientId" title="getClientId"></a>
	<h3 class="method-title"><code><a href="#//api/name/getClientId">&ndash;&nbsp;getClientId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取客户端id</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)getClientId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取客户端id</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/getHttpRequestRetryDelay" title="getHttpRequestRetryDelay"></a>
	<h3 class="method-title"><code><a href="#//api/name/getHttpRequestRetryDelay">&ndash;&nbsp;getHttpRequestRetryDelay</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取请求HTTP接口失败时重试的延时，返回当前延时（秒）</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSInteger)getHttpRequestRetryDelay</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取请求HTTP接口失败时重试的延时，返回当前延时（秒）</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/isSingleClientCheckEnabled" title="isSingleClientCheckEnabled"></a>
	<h3 class="method-title"><code><a href="#//api/name/isSingleClientCheckEnabled">&ndash;&nbsp;isSingleClientCheckEnabled</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取是否开启单点登录检测, true - 开启，false - 关闭</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (BOOL)isSingleClientCheckEnabled</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取是否开启单点登录检测, true - 开启，false - 关闭</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setClientId:" title="setClientId:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setClientId:">&ndash;&nbsp;setClientId:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>客户端ID，不能为null</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setClientId:(NSString *)<em>clientId</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>clientId</code></th>
						<td><p>设置客户端ID</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>客户端ID，不能为null</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setHttpRequestRetryDelay:" title="setHttpRequestRetryDelay:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setHttpRequestRetryDelay:">&ndash;&nbsp;setHttpRequestRetryDelay:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>设置请求HTTP接口失败时重试的延时</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setHttpRequestRetryDelay:(NSInteger)<em>delay</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>delay</code></th>
						<td><p>延时（秒），需大于等于0，否则无效</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>设置请求HTTP接口失败时重试的延时</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setSingleClientCheckEnabled:" title="setSingleClientCheckEnabled:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setSingleClientCheckEnabled:">&ndash;&nbsp;setSingleClientCheckEnabled:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>设置是否开启单点登录检测 注：当开启单点登录时，必须通过setClientId(String)设置客户端ID</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setSingleClientCheckEnabled:(BOOL)<em>enabled</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>enabled</code></th>
						<td><p>true - 开启，false - 关闭</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>设置是否开启单点登录检测 注：当开启单点登录时，必须通过setClientId(String)设置客户端ID</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setUserDomainPlatform:" title="setUserDomainPlatform:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setUserDomainPlatform:">&ndash;&nbsp;setUserDomainPlatform:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>设置平台</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setUserDomainPlatform:(UPUserDomainPlatform)<em>platform</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>platform</code></th>
						<td><p>平台值</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>设置平台</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/getUserDomainPlatform" title="getUserDomainPlatform"></a>
	<h3 class="method-title"><code><a href="#//api/name/getUserDomainPlatform">&ndash;&nbsp;getUserDomainPlatform</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取当前平台</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (UPUserDomainPlatform)getUserDomainPlatform</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取当前平台</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setRefreshFamilyListEnable:" title="setRefreshFamilyListEnable:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setRefreshFamilyListEnable:">&ndash;&nbsp;setRefreshFamilyListEnable:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>是否开启家庭列表刷新</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setRefreshFamilyListEnable:(BOOL)<em>enabled</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>enabled</code></th>
						<td><p>true - 开启，false - 关闭</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>是否开启家庭列表刷新</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/isRefreshFamilyListEnable" title="isRefreshFamilyListEnable"></a>
	<h3 class="method-title"><code><a href="#//api/name/isRefreshFamilyListEnable">&ndash;&nbsp;isRefreshFamilyListEnable</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取是否开启家庭列表刷新</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (BOOL)isRefreshFamilyListEnable</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取是否开启家庭列表刷新</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setRefreshDeviceListEnable:" title="setRefreshDeviceListEnable:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setRefreshDeviceListEnable:">&ndash;&nbsp;setRefreshDeviceListEnable:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>是否开启设备列表刷新</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setRefreshDeviceListEnable:(BOOL)<em>enabled</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>enabled</code></th>
						<td><p>true - 开启，false - 关闭</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>是否开启设备列表刷新</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/isRefreshDeviceListEnable" title="isRefreshDeviceListEnable"></a>
	<h3 class="method-title"><code><a href="#//api/name/isRefreshDeviceListEnable">&ndash;&nbsp;isRefreshDeviceListEnable</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取是否开启设备列表刷新</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (BOOL)isRefreshDeviceListEnable</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取是否开启设备列表刷新</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>