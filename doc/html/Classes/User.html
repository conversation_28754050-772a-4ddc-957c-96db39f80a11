<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>User Class Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	

	

	
	<optgroup label="Instance Methods">
		
		<option value="//api/name/changePassword:newPassword:callback:">- changePassword:newPassword:callback:</option>
		
		<option value="//api/name/createFamily:success:failure:">- createFamily:success:failure:</option>
		
		<option value="//api/name/createNewAddress:success:failure:">- createNewAddress:success:failure:</option>
		
		<option value="//api/name/currentFamily">- currentFamily</option>
		
		<option value="//api/name/deleteAddress:success:failure:">- deleteAddress:success:failure:</option>
		
		<option value="//api/name/devices">- devices</option>
		
		<option value="//api/name/editAddress:success:failure:">- editAddress:success:failure:</option>
		
		<option value="//api/name/extraInfo">- extraInfo</option>
		
		<option value="//api/name/families">- families</option>
		
		<option value="//api/name/getDeviceById:">- getDeviceById:</option>
		
		<option value="//api/name/getDeviceList:">- getDeviceList:</option>
		
		<option value="//api/name/getFamilyById:">- getFamilyById:</option>
		
		<option value="//api/name/getFamilyList">- getFamilyList</option>
		
		<option value="//api/name/modifyUserInfo:success:failure:">- modifyUserInfo:success:failure:</option>
		
		<option value="//api/name/pollqrCodeState:success:failure:">- pollqrCodeState:success:failure:</option>
		
		<option value="//api/name/qrCancleLogin:success:failure:">- qrCancleLogin:success:failure:</option>
		
		<option value="//api/name/qrConfirmLogin:success:failure:">- qrConfirmLogin:success:failure:</option>
		
		<option value="//api/name/qrConfirmScan:success:failure:">- qrConfirmScan:success:failure:</option>
		
		<option value="//api/name/qrWorkOrderInfo:success:failure:">- qrWorkOrderInfo:success:failure:</option>
		
		<option value="//api/name/queryLoginLogs:pageSize:success:failure:">- queryLoginLogs:pageSize:success:failure:</option>
		
		<option value="//api/name/refreshAddressList:failure:">- refreshAddressList:failure:</option>
		
		<option value="//api/name/refreshDeviceList:failure:">- refreshDeviceList:failure:</option>
		
		<option value="//api/name/refreshFamilyList:failure:">- refreshFamilyList:failure:</option>
		
		<option value="//api/name/refreshTerminalList:failure:">- refreshTerminalList:failure:</option>
		
		<option value="//api/name/refreshUser:">- refreshUser:</option>
		
		<option value="//api/name/refreshUser:callback:">- refreshUser:callback:</option>
		
		<option value="//api/name/refreshUserInfo:">- refreshUserInfo:</option>
		
		<option value="//api/name/replyFamilyInvite:familyId:agree:success:failure:">- replyFamilyInvite:familyId:agree:success:failure:</option>
		
		<option value="//api/name/replyFamilyInvite:familyId:memberName:agree:success:failure:">- replyFamilyInvite:familyId:memberName:agree:success:failure:</option>
		
		<option value="//api/name/replyJoinFamily:agree:success:failure:">- replyJoinFamily:agree:success:failure:</option>
		
		<option value="//api/name/setCurrentFamily:">- setCurrentFamily:</option>
		
		<option value="//api/name/terminals">- terminals</option>
		
		<option value="//api/name/uHomeUserId">- uHomeUserId</option>
		
		<option value="//api/name/updateAvatar:success:failure:">- updateAvatar:success:failure:</option>
		
		<option value="//api/name/user_center_userId">- user_center_userId</option>
		
	</optgroup>
	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">User Class Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Inherits from</th>
	<td>NSObject</td>
</tr><tr>
	<th>Conforms to</th>
	<td>NSCoding<br /><a href="../Protocols/UDUserDelegate.html">UDUserDelegate</a><br />UDUserModelTransformer</td>
</tr><tr>
	<th>Declared in</th>
	<td>User.h<br />User.m</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/changePassword:newPassword:callback:" title="changePassword:newPassword:callback:"></a>
	<h3 class="method-title"><code><a href="#//api/name/changePassword:newPassword:callback:">&ndash;&nbsp;changePassword:newPassword:callback:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>修改密码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)changePassword:(NSString *)<em>oldPassword</em> newPassword:(NSString *)<em>newPassword</em> callback:(userDomainCallback)<em>callback</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>oldPassword</code></th>
						<td><p>旧密码</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>newPassword</code></th>
						<td><p>新密码</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>callback</code></th>
						<td><p>回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>修改密码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/createFamily:success:failure:" title="createFamily:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/createFamily:success:failure:">&ndash;&nbsp;createFamily:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>创建家庭</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)createFamily:(CreateFamilyArgs *)<em>createFamilyArgs</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>createFamilyArgs</code></th>
						<td><p>创建家庭参数对象</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>创建家庭</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/createNewAddress:success:failure:" title="createNewAddress:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/createNewAddress:success:failure:">&ndash;&nbsp;createNewAddress:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>创建新的收货地址</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)createNewAddress:(UserAddressArgs *)<em>userAddressArgs</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>userAddressArgs</code></th>
						<td><p>地址信息(userAddressArgs)，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>创建新的收货地址</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/currentFamily" title="currentFamily"></a>
	<h3 class="method-title"><code><a href="#//api/name/currentFamily">&ndash;&nbsp;currentFamily</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取当前家庭</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (id&lt;UDFamilyDelegate&gt;)currentFamily</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取当前家庭</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/deleteAddress:success:failure:" title="deleteAddress:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/deleteAddress:success:failure:">&ndash;&nbsp;deleteAddress:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>删除用户地址</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)deleteAddress:(NSString *)<em>addressId</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>addressId</code></th>
						<td><p>地址ID，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>删除用户地址</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/devices" title="devices"></a>
	<h3 class="method-title"><code><a href="#//api/name/devices">&ndash;&nbsp;devices</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>  设备列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSDictionary&lt;NSString*,id&lt;UDDeviceDelegate&gt; &gt; *)devices</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>  设备列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/editAddress:success:failure:" title="editAddress:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/editAddress:success:failure:">&ndash;&nbsp;editAddress:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>编辑用户地址</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)editAddress:(UserAddressArgs *)<em>userAddressArgs</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>userAddressArgs</code></th>
						<td><p>地址信息(userAddressArgs)</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>编辑用户地址</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/extraInfo" title="extraInfo"></a>
	<h3 class="method-title"><code><a href="#//api/name/extraInfo">&ndash;&nbsp;extraInfo</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取用户详细信息</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (id&lt;UDUserInfoDelegate&gt;)extraInfo</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取用户详细信息</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/families" title="families"></a>
	<h3 class="method-title"><code><a href="#//api/name/families">&ndash;&nbsp;families</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>  家庭列表，返回值字典，key是家庭id,value是实现家庭协议的家庭对象</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSDictionary&lt;NSString*,id&lt;UDFamilyDelegate&gt; &gt; *)families</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>  家庭列表，返回值字典，key是家庭id,value是实现家庭协议的家庭对象</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/getDeviceById:" title="getDeviceById:"></a>
	<h3 class="method-title"><code><a href="#//api/name/getDeviceById:">&ndash;&nbsp;getDeviceById:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>通过设备ID获取指定设备对象</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (id&lt;UDDeviceDelegate&gt;)getDeviceById:(NSString *)<em>deviceId</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>deviceId</code></th>
						<td><p>设备id</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>通过设备ID获取指定设备对象</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/getDeviceList:" title="getDeviceList:"></a>
	<h3 class="method-title"><code><a href="#//api/name/getDeviceList:">&ndash;&nbsp;getDeviceList:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>通过过滤器获取指定的设备列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSArray&lt;id&lt;UDDeviceDelegate&gt; &gt; *)getDeviceList:(BOOL ( ^ ) ( id&lt;UDDeviceDelegate&gt; ))<em>deviceFilter</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>deviceFilter</code></th>
						<td><p>block中过滤</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>通过过滤器获取指定的设备列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/getFamilyById:" title="getFamilyById:"></a>
	<h3 class="method-title"><code><a href="#//api/name/getFamilyById:">&ndash;&nbsp;getFamilyById:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>通过家庭ID获取指定家庭对象</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (id&lt;UDFamilyDelegate&gt;)getFamilyById:(NSString *)<em>familyId</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>familyId</code></th>
						<td><p>家庭id</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>通过家庭ID获取指定家庭对象</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/getFamilyList" title="getFamilyList"></a>
	<h3 class="method-title"><code><a href="#//api/name/getFamilyList">&ndash;&nbsp;getFamilyList</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取家庭列表，返回数组</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSArray&lt;id&lt;UDFamilyDelegate&gt; &gt; *)getFamilyList</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取家庭列表，返回数组</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/modifyUserInfo:success:failure:" title="modifyUserInfo:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/modifyUserInfo:success:failure:">&ndash;&nbsp;modifyUserInfo:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>修改用户信息</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)modifyUserInfo:(UserInfoArgs *)<em>userInfo</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>userInfo</code></th>
						<td><p>用户信息</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>修改用户信息</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/pollqrCodeState:success:failure:" title="pollqrCodeState:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/pollqrCodeState:success:failure:">&ndash;&nbsp;pollqrCodeState:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>扫码登录状态</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)pollqrCodeState:(NSString *)<em>uuid</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>uuid</code></th>
						<td><p>扫码得到的uuid，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>扫码登录状态</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/qrCancleLogin:success:failure:" title="qrCancleLogin:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/qrCancleLogin:success:failure:">&ndash;&nbsp;qrCancleLogin:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>取消扫码登陆</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)qrCancleLogin:(NSString *)<em>uuid</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>uuid</code></th>
						<td><p>扫码得到的uuid，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>取消扫码登陆</p>
			</div>
			

			

			
			<div class="method-subsection see-also-section">
				<h4 class="method-subtitle">See Also</h4>
				<ul>
					
					<li><code><p><a href="../Blocks/userDomainCallback.html">userDomainCallback</a></p></code></li>
					
				</ul>
			</div>
			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/qrConfirmLogin:success:failure:" title="qrConfirmLogin:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/qrConfirmLogin:success:failure:">&ndash;&nbsp;qrConfirmLogin:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>确认登陆，扫码成功后</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)qrConfirmLogin:(NSString *)<em>uuid</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>uuid</code></th>
						<td><p>扫码得到的uuid，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>确认登陆，扫码成功后</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/qrConfirmScan:success:failure:" title="qrConfirmScan:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/qrConfirmScan:success:failure:">&ndash;&nbsp;qrConfirmScan:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>确认扫码登录</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)qrConfirmScan:(NSString *)<em>uuid</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>uuid</code></th>
						<td><p>扫码得到的uuid，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>确认扫码登录</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/qrWorkOrderInfo:success:failure:" title="qrWorkOrderInfo:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/qrWorkOrderInfo:success:failure:">&ndash;&nbsp;qrWorkOrderInfo:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>查询用户电子工单（未登录可用）,内部自动获取应用级Token</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)qrWorkOrderInfo:(NSString *)<em>phoneNumber</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>phoneNumber</code></th>
						<td><p>手机号码，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>查询用户电子工单（未登录可用）,内部自动获取应用级Token</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/queryLoginLogs:pageSize:success:failure:" title="queryLoginLogs:pageSize:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/queryLoginLogs:pageSize:success:failure:">&ndash;&nbsp;queryLoginLogs:pageSize:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>分页查询用户登录日志</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)queryLoginLogs:(NSInteger)<em>pageNo</em> pageSize:(NSInteger)<em>pageSize</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>pageNo</code></th>
						<td><p>起始页，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>pageSize</code></th>
						<td><p>单页数量，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>分页查询用户登录日志</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/refreshAddressList:failure:" title="refreshAddressList:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/refreshAddressList:failure:">&ndash;&nbsp;refreshAddressList:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新用户地址列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)refreshAddressList:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新用户地址列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/refreshDeviceList:failure:" title="refreshDeviceList:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/refreshDeviceList:failure:">&ndash;&nbsp;refreshDeviceList:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新设备列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)refreshDeviceList:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新设备列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/refreshFamilyList:failure:" title="refreshFamilyList:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/refreshFamilyList:failure:">&ndash;&nbsp;refreshFamilyList:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新家庭列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)refreshFamilyList:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新家庭列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/refreshTerminalList:failure:" title="refreshTerminalList:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/refreshTerminalList:failure:">&ndash;&nbsp;refreshTerminalList:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新终端列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)refreshTerminalList:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新终端列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/refreshUser:callback:" title="refreshUser:callback:"></a>
	<h3 class="method-title"><code><a href="#//api/name/refreshUser:callback:">&ndash;&nbsp;refreshUser:callback:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新用户：信息、家庭、设备列表，传YES时从服务器请求数据，false时取本地数据，当为false时会根据缓存数据是否已经过期来判断
若已经过期则从服务器请求数据</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)refreshUser:(BOOL)<em>immediate</em> callback:(userDomainCallback)<em>callback</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>callback</code></th>
						<td><p>回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>immediate</code></th>
						<td><p>是否立即</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新用户：信息、家庭、设备列表，传YES时从服务器请求数据，false时取本地数据，当为false时会根据缓存数据是否已经过期来判断
若已经过期则从服务器请求数据</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/refreshUser:" title="refreshUser:"></a>
	<h3 class="method-title"><code><a href="#//api/name/refreshUser:">&ndash;&nbsp;refreshUser:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新用户：信息、家庭、设备列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)refreshUser:(userDomainCallback)<em>callback</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>callback</code></th>
						<td><p>回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新用户：信息、家庭、设备列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/refreshUserInfo:" title="refreshUserInfo:"></a>
	<h3 class="method-title"><code><a href="#//api/name/refreshUserInfo:">&ndash;&nbsp;refreshUserInfo:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>刷新用户基本信息，地址，登录终端信息</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)refreshUserInfo:(userDomainCallback)<em>callback</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>callback</code></th>
						<td><p>刷新回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>刷新用户基本信息，地址，登录终端信息</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/replyFamilyInvite:familyId:agree:success:failure:" title="replyFamilyInvite:familyId:agree:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/replyFamilyInvite:familyId:agree:success:failure:">&ndash;&nbsp;replyFamilyInvite:familyId:agree:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>回复家庭邀请，同意家庭邀请操作成功以后，订阅者会收到家庭列表更新通知</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)replyFamilyInvite:(NSString *)<em>code</em> familyId:(NSString *)<em>familyId</em> agree:(BOOL)<em>agree</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>code</code></th>
						<td><p>邀请码，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>familyId</code></th>
						<td><p>家庭ID，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>agree</code></th>
						<td><p>是否同意，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>回复家庭邀请，同意家庭邀请操作成功以后，订阅者会收到家庭列表更新通知</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/replyFamilyInvite:familyId:memberName:agree:success:failure:" title="replyFamilyInvite:familyId:memberName:agree:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/replyFamilyInvite:familyId:memberName:agree:success:failure:">&ndash;&nbsp;replyFamilyInvite:familyId:memberName:agree:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>回复家庭邀请，同意家庭邀请操作成功以后，订阅者会收到家庭列表更新通知</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)replyFamilyInvite:(NSString *)<em>code</em> familyId:(NSString *)<em>familyId</em> memberName:(NSString *)<em>memberName</em> agree:(BOOL)<em>agree</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>code</code></th>
						<td><p>邀请码，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>familyId</code></th>
						<td><p>家庭ID，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>memberName</code></th>
						<td><p>成员名称</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>agree</code></th>
						<td><p>是否同意，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>回复家庭邀请，同意家庭邀请操作成功以后，订阅者会收到家庭列表更新通知</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/replyJoinFamily:agree:success:failure:" title="replyJoinFamily:agree:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/replyJoinFamily:agree:success:failure:">&ndash;&nbsp;replyJoinFamily:agree:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>管理员同意/拒绝用户加入家庭</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)replyJoinFamily:(NSString *)<em>applicationId</em> agree:(BOOL)<em>agree</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>applicationId</code></th>
						<td><p>申请ID，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>agree</code></th>
						<td><p>BOOL，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>管理员同意/拒绝用户加入家庭</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setCurrentFamily:" title="setCurrentFamily:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setCurrentFamily:">&ndash;&nbsp;setCurrentFamily:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取当前家庭</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setCurrentFamily:(id&lt;UDFamilyDelegate&gt;)<em>currentFamily</em></code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取当前家庭</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/terminals" title="terminals"></a>
	<h3 class="method-title"><code><a href="#//api/name/terminals">&ndash;&nbsp;terminals</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>登录终端列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSArray&lt;id&lt;UDUserTermDelegate&gt; &gt; *)terminals</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>登录终端列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/uHomeUserId" title="uHomeUserId"></a>
	<h3 class="method-title"><code><a href="#//api/name/uHomeUserId">&ndash;&nbsp;uHomeUserId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取优家的UserID</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)uHomeUserId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取优家的UserID</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/updateAvatar:success:failure:" title="updateAvatar:success:failure:"></a>
	<h3 class="method-title"><code><a href="#//api/name/updateAvatar:success:failure:">&ndash;&nbsp;updateAvatar:success:failure:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>更新用户头像</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)updateAvatar:(UIImage *)<em>image</em> success:(userDomainCallback)<em>success</em> failure:(userDomainCallback)<em>failure</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>image</code></th>
						<td><p>头像图片，必填</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>success</code></th>
						<td><p>成功回调</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>failure</code></th>
						<td><p>失败回调</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>更新用户头像</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/user_center_userId" title="user_center_userId"></a>
	<h3 class="method-title"><code><a href="#//api/name/user_center_userId">&ndash;&nbsp;user_center_userId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取用户中心的UserID</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)user_center_userId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取用户中心的UserID</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDUserDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>