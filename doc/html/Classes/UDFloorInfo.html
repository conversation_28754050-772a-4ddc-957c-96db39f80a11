<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>UDFloorInfo Class Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	
	<optgroup label="Properties">
		
		<option value="//api/name/realFloorClass">realFloorClass</option>
		
		<option value="//api/name/realFloorCreateTime">realFloorCreateTime</option>
		
		<option value="//api/name/realFloorId">realFloorId</option>
		
		<option value="//api/name/realFloorLabel">realFloorLabel</option>
		
		<option value="//api/name/realFloorLogo">realFloorLogo</option>
		
		<option value="//api/name/realFloorName">realFloorName</option>
		
		<option value="//api/name/realFloorOrderId">realFloorOrderId</option>
		
		<option value="//api/name/realFloorPicture">realFloorPicture</option>
		
		<option value="//api/name/realRooms">realRooms</option>
		
	</optgroup>
	

	

	
	<optgroup label="Instance Methods">
		
		<option value="//api/name/floorClass">- floorClass</option>
		
		<option value="//api/name/floorCreateTime">- floorCreateTime</option>
		
		<option value="//api/name/floorId">- floorId</option>
		
		<option value="//api/name/floorLabel">- floorLabel</option>
		
		<option value="//api/name/floorLogo">- floorLogo</option>
		
		<option value="//api/name/floorName">- floorName</option>
		
		<option value="//api/name/floorOrderId">- floorOrderId</option>
		
		<option value="//api/name/floorPicture">- floorPicture</option>
		
		<option value="//api/name/rooms">- rooms</option>
		
	</optgroup>
	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">UDFloorInfo Class Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Inherits from</th>
	<td>NSObject</td>
</tr><tr>
	<th>Conforms to</th>
	<td><a href="../Protocols/UDFloorInfoDelegate.html">UDFloorInfoDelegate</a></td>
</tr><tr>
	<th>Declared in</th>
	<td>UDFloorInfo.h<br />UDFloorInfo.m</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						<h2 class="task-title">Other Methods</h2>

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/floorId" title="floorId"></a>
	<h3 class="method-title"><code><a href="#//api/name/floorId">&ndash;&nbsp;floorId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层Id</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)floorId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层Id</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/floorLogo" title="floorLogo"></a>
	<h3 class="method-title"><code><a href="#//api/name/floorLogo">&ndash;&nbsp;floorLogo</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层logo url</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)floorLogo</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层logo url</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/floorName" title="floorName"></a>
	<h3 class="method-title"><code><a href="#//api/name/floorName">&ndash;&nbsp;floorName</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层名称</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)floorName</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层名称</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/floorClass" title="floorClass"></a>
	<h3 class="method-title"><code><a href="#//api/name/floorClass">&ndash;&nbsp;floorClass</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层类型</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)floorClass</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层类型</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/floorLabel" title="floorLabel"></a>
	<h3 class="method-title"><code><a href="#//api/name/floorLabel">&ndash;&nbsp;floorLabel</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层标签</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)floorLabel</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层标签</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/floorPicture" title="floorPicture"></a>
	<h3 class="method-title"><code><a href="#//api/name/floorPicture">&ndash;&nbsp;floorPicture</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层图片 url</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)floorPicture</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层图片 url</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/floorOrderId" title="floorOrderId"></a>
	<h3 class="method-title"><code><a href="#//api/name/floorOrderId">&ndash;&nbsp;floorOrderId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层次序（-3到5，没有0）</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)floorOrderId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层次序（-3到5，没有0）</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/rooms" title="rooms"></a>
	<h3 class="method-title"><code><a href="#//api/name/rooms">&ndash;&nbsp;rooms</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>房间列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSArray&lt;id&lt;UDRoomDelegate&gt; &gt; *)rooms</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>房间列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/floorCreateTime" title="floorCreateTime"></a>
	<h3 class="method-title"><code><a href="#//api/name/floorCreateTime">&ndash;&nbsp;floorCreateTime</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层创建时间</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)floorCreateTime</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层创建时间</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfoDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						

						<a title="Extension Methods" name="task_Extension Methods"></a>
						<h2 class="task-title">Extension Methods</h2>

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/realFloorName" title="realFloorName"></a>
	<h3 class="method-title"><code><a href="#//api/name/realFloorName">&nbsp;&nbsp;realFloorName</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层名称</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realFloorName</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层名称</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realFloorId" title="realFloorId"></a>
	<h3 class="method-title"><code><a href="#//api/name/realFloorId">&nbsp;&nbsp;realFloorId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层Id</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realFloorId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层Id</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realFloorOrderId" title="realFloorOrderId"></a>
	<h3 class="method-title"><code><a href="#//api/name/realFloorOrderId">&nbsp;&nbsp;realFloorOrderId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层次序（-3到5，没有0）</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realFloorOrderId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层次序（-3到5，没有0）</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realFloorClass" title="realFloorClass"></a>
	<h3 class="method-title"><code><a href="#//api/name/realFloorClass">&nbsp;&nbsp;realFloorClass</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层类型</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realFloorClass</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层类型</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realFloorLabel" title="realFloorLabel"></a>
	<h3 class="method-title"><code><a href="#//api/name/realFloorLabel">&nbsp;&nbsp;realFloorLabel</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层标签</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realFloorLabel</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层标签</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realFloorLogo" title="realFloorLogo"></a>
	<h3 class="method-title"><code><a href="#//api/name/realFloorLogo">&nbsp;&nbsp;realFloorLogo</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层logo url</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realFloorLogo</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层logo url</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realFloorPicture" title="realFloorPicture"></a>
	<h3 class="method-title"><code><a href="#//api/name/realFloorPicture">&nbsp;&nbsp;realFloorPicture</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层图片 url</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realFloorPicture</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层图片 url</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realFloorCreateTime" title="realFloorCreateTime"></a>
	<h3 class="method-title"><code><a href="#//api/name/realFloorCreateTime">&nbsp;&nbsp;realFloorCreateTime</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>楼层创建时间</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realFloorCreateTime</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>楼层创建时间</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realRooms" title="realRooms"></a>
	<h3 class="method-title"><code><a href="#//api/name/realRooms">&nbsp;&nbsp;realRooms</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>房间列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong) NSArray&lt;id&lt;UDRoomDelegate&gt; &gt; *realRooms</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>房间列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFloorInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>