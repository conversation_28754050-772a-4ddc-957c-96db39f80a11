<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>UserAddressArgs Class Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	
	<optgroup label="Properties">
		
		<option value="//api/name/addressId">addressId</option>
		
		<option value="//api/name/city">city</option>
		
		<option value="//api/name/city_id">city_id</option>
		
		<option value="//api/name/country_code">country_code</option>
		
		<option value="//api/name/district">district</option>
		
		<option value="//api/name/district_id">district_id</option>
		
		<option value="//api/name/email">email</option>
		
		<option value="//api/name/is_default">is_default</option>
		
		<option value="//api/name/is_service">is_service</option>
		
		<option value="//api/name/postcode">postcode</option>
		
		<option value="//api/name/province">province</option>
		
		<option value="//api/name/province_id">province_id</option>
		
		<option value="//api/name/receiver_mobile">receiver_mobile</option>
		
		<option value="//api/name/receiver_name">receiver_name</option>
		
		<option value="//api/name/tag">tag</option>
		
		<option value="//api/name/town">town</option>
		
		<option value="//api/name/town_id">town_id</option>
		
		<option value="//api/name/user_id">user_id</option>
		
	</optgroup>
	

	

	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">UserAddressArgs Class Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Inherits from</th>
	<td>NSObject</td>
</tr><tr>
	<th>Declared in</th>
	<td>UserAddressArgs.h<br />UserAddressArgs.m</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/email" title="email"></a>
	<h3 class="method-title"><code><a href="#//api/name/email">&nbsp;&nbsp;email</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>邮箱</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *email</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>邮箱</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/addressId" title="addressId"></a>
	<h3 class="method-title"><code><a href="#//api/name/addressId">&nbsp;&nbsp;addressId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>地址编号</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic, readonly) NSString *addressId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>地址编号</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/is_default" title="is_default"></a>
	<h3 class="method-title"><code><a href="#//api/name/is_default">&nbsp;&nbsp;is_default</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>是否是默认地址</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (assign, nonatomic) NSInteger is_default</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>是否是默认地址</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/is_service" title="is_service"></a>
	<h3 class="method-title"><code><a href="#//api/name/is_service">&nbsp;&nbsp;is_service</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>是否有效</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (assign, nonatomic) NSInteger is_service</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>是否有效</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/receiver_mobile" title="receiver_mobile"></a>
	<h3 class="method-title"><code><a href="#//api/name/receiver_mobile">&nbsp;&nbsp;receiver_mobile</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>手机号码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *receiver_mobile</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>手机号码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/receiver_name" title="receiver_name"></a>
	<h3 class="method-title"><code><a href="#//api/name/receiver_name">&nbsp;&nbsp;receiver_name</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>收货人姓名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *receiver_name</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>收货人姓名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/tag" title="tag"></a>
	<h3 class="method-title"><code><a href="#//api/name/tag">&nbsp;&nbsp;tag</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>标签</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *tag</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>标签</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/user_id" title="user_id"></a>
	<h3 class="method-title"><code><a href="#//api/name/user_id">&nbsp;&nbsp;user_id</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户id</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *user_id</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户id</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/city" title="city"></a>
	<h3 class="method-title"><code><a href="#//api/name/city">&nbsp;&nbsp;city</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>城市</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *city</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>城市</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/city_id" title="city_id"></a>
	<h3 class="method-title"><code><a href="#//api/name/city_id">&nbsp;&nbsp;city_id</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>城市编码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *city_id</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>城市编码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/country_code" title="country_code"></a>
	<h3 class="method-title"><code><a href="#//api/name/country_code">&nbsp;&nbsp;country_code</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>国家编码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *country_code</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>国家编码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/district" title="district"></a>
	<h3 class="method-title"><code><a href="#//api/name/district">&nbsp;&nbsp;district</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>区域</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *district</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>区域</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/district_id" title="district_id"></a>
	<h3 class="method-title"><code><a href="#//api/name/district_id">&nbsp;&nbsp;district_id</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>区域编码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *district_id</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>区域编码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/postcode" title="postcode"></a>
	<h3 class="method-title"><code><a href="#//api/name/postcode">&nbsp;&nbsp;postcode</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>邮编</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *postcode</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>邮编</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/province" title="province"></a>
	<h3 class="method-title"><code><a href="#//api/name/province">&nbsp;&nbsp;province</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>省</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *province</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>省</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/province_id" title="province_id"></a>
	<h3 class="method-title"><code><a href="#//api/name/province_id">&nbsp;&nbsp;province_id</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>省编码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *province_id</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>省编码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/town" title="town"></a>
	<h3 class="method-title"><code><a href="#//api/name/town">&nbsp;&nbsp;town</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>乡镇、街道</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *town</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>乡镇、街道</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/town_id" title="town_id"></a>
	<h3 class="method-title"><code><a href="#//api/name/town_id">&nbsp;&nbsp;town_id</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>乡镇街道编码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *town_id</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>乡镇街道编码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressArgs.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>