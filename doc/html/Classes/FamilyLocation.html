<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>FamilyLocation Class Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	
	<optgroup label="Properties">
		
		<option value="//api/name/realCityCode">realCityCode</option>
		
		<option value="//api/name/realLatitude">realLatitude</option>
		
		<option value="//api/name/realLongitude">realLongitude</option>
		
	</optgroup>
	

	

	
	<optgroup label="Instance Methods">
		
		<option value="//api/name/cityCode">- cityCode</option>
		
		<option value="//api/name/latitude">- latitude</option>
		
		<option value="//api/name/longitude">- longitude</option>
		
	</optgroup>
	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">FamilyLocation Class Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Inherits from</th>
	<td>NSObject</td>
</tr><tr>
	<th>Conforms to</th>
	<td>NSCoding<br /><a href="../Protocols/UDFamilyLocationDelegate.html">UDFamilyLocationDelegate</a></td>
</tr><tr>
	<th>Declared in</th>
	<td>FamilyLocation.h<br />FamilyLocation.m</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						<h2 class="task-title">Other Methods</h2>

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/cityCode" title="cityCode"></a>
	<h3 class="method-title"><code><a href="#//api/name/cityCode">&ndash;&nbsp;cityCode</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>城市编码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)cityCode</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>城市编码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyLocationDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/latitude" title="latitude"></a>
	<h3 class="method-title"><code><a href="#//api/name/latitude">&ndash;&nbsp;latitude</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>纬度</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)latitude</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>纬度</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyLocationDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/longitude" title="longitude"></a>
	<h3 class="method-title"><code><a href="#//api/name/longitude">&ndash;&nbsp;longitude</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>经度</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)longitude</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>经度</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDFamilyLocationDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						

						<a title="Extension Methods" name="task_Extension Methods"></a>
						<h2 class="task-title">Extension Methods</h2>

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/realLongitude" title="realLongitude"></a>
	<h3 class="method-title"><code><a href="#//api/name/realLongitude">&nbsp;&nbsp;realLongitude</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>经度</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realLongitude</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>经度</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">FamilyLocation+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realLatitude" title="realLatitude"></a>
	<h3 class="method-title"><code><a href="#//api/name/realLatitude">&nbsp;&nbsp;realLatitude</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>纬度</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realLatitude</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>纬度</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">FamilyLocation+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realCityCode" title="realCityCode"></a>
	<h3 class="method-title"><code><a href="#//api/name/realCityCode">&nbsp;&nbsp;realCityCode</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>城市编码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realCityCode</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>城市编码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">FamilyLocation+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>