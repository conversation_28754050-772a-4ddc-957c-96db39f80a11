<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>UserAddressInfo Class Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	
	<optgroup label="Properties">
		
		<option value="//api/name/realAddress">realAddress</option>
		
		<option value="//api/name/realAddressId">realAddressId</option>
		
		<option value="//api/name/realEmail">realEmail</option>
		
		<option value="//api/name/realIs_default">realIs_default</option>
		
		<option value="//api/name/realIs_service">realIs_service</option>
		
		<option value="//api/name/realReceiver_mobile">realReceiver_mobile</option>
		
		<option value="//api/name/realReceiver_name">realReceiver_name</option>
		
		<option value="//api/name/realSource">realSource</option>
		
		<option value="//api/name/realTag">realTag</option>
		
		<option value="//api/name/realUser_id">realUser_id</option>
		
	</optgroup>
	

	

	
	<optgroup label="Instance Methods">
		
		<option value="//api/name/address">- address</option>
		
		<option value="//api/name/addressId">- addressId</option>
		
		<option value="//api/name/email">- email</option>
		
		<option value="//api/name/is_default">- is_default</option>
		
		<option value="//api/name/is_service">- is_service</option>
		
		<option value="//api/name/receiver_mobile">- receiver_mobile</option>
		
		<option value="//api/name/receiver_name">- receiver_name</option>
		
		<option value="//api/name/source">- source</option>
		
		<option value="//api/name/tag">- tag</option>
		
		<option value="//api/name/user_id">- user_id</option>
		
	</optgroup>
	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">UserAddressInfo Class Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Inherits from</th>
	<td>NSObject</td>
</tr><tr>
	<th>Conforms to</th>
	<td>NSCoding<br /><a href="../Protocols/UDAddressDelegate.html">UDAddressDelegate</a></td>
</tr><tr>
	<th>Declared in</th>
	<td>UserAddressInfo.h<br />UserAddressInfo.m</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						<h2 class="task-title">Other Methods</h2>

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/address" title="address"></a>
	<h3 class="method-title"><code><a href="#//api/name/address">&ndash;&nbsp;address</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>地址信息</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (id&lt;UDUserAddressDelegate&gt;)address</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>地址信息</p>
			</div>
			

			

			
			<div class="method-subsection see-also-section">
				<h4 class="method-subtitle">See Also</h4>
				<ul>
					
					<li><code><p><a href="../Classes/UserAddress.html">UserAddress</a></p></code></li>
					
				</ul>
			</div>
			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAddressDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/email" title="email"></a>
	<h3 class="method-title"><code><a href="#//api/name/email">&ndash;&nbsp;email</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>邮箱</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)email</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>邮箱</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAddressDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/receiver_name" title="receiver_name"></a>
	<h3 class="method-title"><code><a href="#//api/name/receiver_name">&ndash;&nbsp;receiver_name</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>收货人姓名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)receiver_name</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>收货人姓名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAddressDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/receiver_mobile" title="receiver_mobile"></a>
	<h3 class="method-title"><code><a href="#//api/name/receiver_mobile">&ndash;&nbsp;receiver_mobile</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>手机号码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)receiver_mobile</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>手机号码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAddressDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/addressId" title="addressId"></a>
	<h3 class="method-title"><code><a href="#//api/name/addressId">&ndash;&nbsp;addressId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>地址编号</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)addressId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>地址编号</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAddressDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/is_default" title="is_default"></a>
	<h3 class="method-title"><code><a href="#//api/name/is_default">&ndash;&nbsp;is_default</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>是否是默认地址</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSInteger)is_default</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>是否是默认地址</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAddressDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/is_service" title="is_service"></a>
	<h3 class="method-title"><code><a href="#//api/name/is_service">&ndash;&nbsp;is_service</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>是否有效</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSInteger)is_service</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>是否有效</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAddressDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/tag" title="tag"></a>
	<h3 class="method-title"><code><a href="#//api/name/tag">&ndash;&nbsp;tag</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>标签</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)tag</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>标签</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAddressDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/user_id" title="user_id"></a>
	<h3 class="method-title"><code><a href="#//api/name/user_id">&ndash;&nbsp;user_id</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户id</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)user_id</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户id</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAddressDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/source" title="source"></a>
	<h3 class="method-title"><code><a href="#//api/name/source">&ndash;&nbsp;source</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>来源</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)source</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>来源</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAddressDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						

						<a title="Extension Methods" name="task_Extension Methods"></a>
						<h2 class="task-title">Extension Methods</h2>

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/realAddress" title="realAddress"></a>
	<h3 class="method-title"><code><a href="#//api/name/realAddress">&nbsp;&nbsp;realAddress</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>地址信息</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (strong, nonatomic) id&lt;UDUserAddressDelegate&gt; realAddress</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>地址信息</p>
			</div>
			

			

			
			<div class="method-subsection see-also-section">
				<h4 class="method-subtitle">See Also</h4>
				<ul>
					
					<li><code><p><a href="../Classes/UserAddress.html">UserAddress</a></p></code></li>
					
				</ul>
			</div>
			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realEmail" title="realEmail"></a>
	<h3 class="method-title"><code><a href="#//api/name/realEmail">&nbsp;&nbsp;realEmail</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>邮箱</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *realEmail</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>邮箱</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realAddressId" title="realAddressId"></a>
	<h3 class="method-title"><code><a href="#//api/name/realAddressId">&nbsp;&nbsp;realAddressId</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>地址编号</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *realAddressId</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>地址编号</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realIs_default" title="realIs_default"></a>
	<h3 class="method-title"><code><a href="#//api/name/realIs_default">&nbsp;&nbsp;realIs_default</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>是否是默认地址</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (assign, nonatomic) NSInteger realIs_default</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>是否是默认地址</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realIs_service" title="realIs_service"></a>
	<h3 class="method-title"><code><a href="#//api/name/realIs_service">&nbsp;&nbsp;realIs_service</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>是否有效</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (assign, nonatomic) NSInteger realIs_service</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>是否有效</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realReceiver_mobile" title="realReceiver_mobile"></a>
	<h3 class="method-title"><code><a href="#//api/name/realReceiver_mobile">&nbsp;&nbsp;realReceiver_mobile</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>手机号码</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *realReceiver_mobile</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>手机号码</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realReceiver_name" title="realReceiver_name"></a>
	<h3 class="method-title"><code><a href="#//api/name/realReceiver_name">&nbsp;&nbsp;realReceiver_name</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>收货人姓名</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *realReceiver_name</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>收货人姓名</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realSource" title="realSource"></a>
	<h3 class="method-title"><code><a href="#//api/name/realSource">&nbsp;&nbsp;realSource</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>来源</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *realSource</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>来源</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realTag" title="realTag"></a>
	<h3 class="method-title"><code><a href="#//api/name/realTag">&nbsp;&nbsp;realTag</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>标签</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *realTag</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>标签</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realUser_id" title="realUser_id"></a>
	<h3 class="method-title"><code><a href="#//api/name/realUser_id">&nbsp;&nbsp;realUser_id</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户id</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (copy, nonatomic) NSString *realUser_id</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户id</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UserAddressInfo+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>