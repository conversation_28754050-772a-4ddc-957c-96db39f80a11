<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>ApplicationOauthData Class Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	
	<optgroup label="Properties">
		
		<option value="//api/name/realAccess_token">realAccess_token</option>
		
		<option value="//api/name/realCreateTime">realCreateTime</option>
		
		<option value="//api/name/realExpires_in">realExpires_in</option>
		
		<option value="//api/name/realRefresh_token">realRefresh_token</option>
		
		<option value="//api/name/realScope">realScope</option>
		
		<option value="//api/name/realToken_type">realToken_type</option>
		
		<option value="//api/name/realUhome_access_token">realUhome_access_token</option>
		
		<option value="//api/name/realUhome_user_id">realUhome_user_id</option>
		
	</optgroup>
	

	

	
	<optgroup label="Instance Methods">
		
		<option value="//api/name/access_token">- access_token</option>
		
		<option value="//api/name/createTime">- createTime</option>
		
		<option value="//api/name/expires_in">- expires_in</option>
		
		<option value="//api/name/refresh_token">- refresh_token</option>
		
		<option value="//api/name/scope">- scope</option>
		
		<option value="//api/name/setAccess_token:">- setAccess_token:</option>
		
		<option value="//api/name/setCreateTime:">- setCreateTime:</option>
		
		<option value="//api/name/setExpires_in:">- setExpires_in:</option>
		
		<option value="//api/name/setRefresh_token:">- setRefresh_token:</option>
		
		<option value="//api/name/setScope:">- setScope:</option>
		
		<option value="//api/name/setToken_type:">- setToken_type:</option>
		
		<option value="//api/name/setUhome_access_token:">- setUhome_access_token:</option>
		
		<option value="//api/name/setUhome_user_id:">- setUhome_user_id:</option>
		
		<option value="//api/name/token_type">- token_type</option>
		
		<option value="//api/name/uhome_access_token">- uhome_access_token</option>
		
		<option value="//api/name/uhome_user_id">- uhome_user_id</option>
		
	</optgroup>
	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">ApplicationOauthData Class Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Inherits from</th>
	<td>NSObject</td>
</tr><tr>
	<th>Conforms to</th>
	<td>NSCoding<br /><a href="../Protocols/UDAuthDataDelegate.html">UDAuthDataDelegate</a></td>
</tr><tr>
	<th>Declared in</th>
	<td>ApplicationOauthData.h<br />ApplicationOauthData.m</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						<h2 class="task-title">Other Methods</h2>

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/access_token" title="access_token"></a>
	<h3 class="method-title"><code><a href="#//api/name/access_token">&ndash;&nbsp;access_token</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心个人token</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)access_token</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心个人token</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setAccess_token:" title="setAccess_token:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setAccess_token:">&ndash;&nbsp;setAccess_token:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心个人token</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setAccess_token:(NSString *)<em>access_token</em></code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心个人token</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/expires_in" title="expires_in"></a>
	<h3 class="method-title"><code><a href="#//api/name/expires_in">&ndash;&nbsp;expires_in</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心<a href="../Protocols/UDAuthDataDelegate.html#//api/name/access_token">access_token</a> 过期剩余时间：秒</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)expires_in</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心<a href="../Protocols/UDAuthDataDelegate.html#//api/name/access_token">access_token</a> 过期剩余时间：秒</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setExpires_in:" title="setExpires_in:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setExpires_in:">&ndash;&nbsp;setExpires_in:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心<a href="../Protocols/UDAuthDataDelegate.html#//api/name/access_token">access_token</a> 过期剩余时间：秒</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setExpires_in:(NSString *)<em>expires_in</em></code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心<a href="../Protocols/UDAuthDataDelegate.html#//api/name/access_token">access_token</a> 过期剩余时间：秒</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/refresh_token" title="refresh_token"></a>
	<h3 class="method-title"><code><a href="#//api/name/refresh_token">&ndash;&nbsp;refresh_token</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心刷新token</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)refresh_token</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心刷新token</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setRefresh_token:" title="setRefresh_token:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setRefresh_token:">&ndash;&nbsp;setRefresh_token:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心刷新token</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setRefresh_token:(NSString *)<em>refresh_token</em></code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心刷新token</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/scope" title="scope"></a>
	<h3 class="method-title"><code><a href="#//api/name/scope">&ndash;&nbsp;scope</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心access_token作用域</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)scope</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心access_token作用域</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setScope:" title="setScope:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setScope:">&ndash;&nbsp;setScope:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心access_token作用域</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setScope:(NSString *)<em>scope</em></code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心access_token作用域</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/token_type" title="token_type"></a>
	<h3 class="method-title"><code><a href="#//api/name/token_type">&ndash;&nbsp;token_type</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心<a href="../Protocols/UDAuthDataDelegate.html#//api/name/access_token">access_token</a> 类型</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)token_type</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心<a href="../Protocols/UDAuthDataDelegate.html#//api/name/access_token">access_token</a> 类型</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setToken_type:" title="setToken_type:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setToken_type:">&ndash;&nbsp;setToken_type:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心<a href="../Protocols/UDAuthDataDelegate.html#//api/name/access_token">access_token</a> 类型</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setToken_type:(NSString *)<em>token_type</em></code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心<a href="../Protocols/UDAuthDataDelegate.html#//api/name/access_token">access_token</a> 类型</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/uhome_access_token" title="uhome_access_token"></a>
	<h3 class="method-title"><code><a href="#//api/name/uhome_access_token">&ndash;&nbsp;uhome_access_token</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>云平台用户token</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)uhome_access_token</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>云平台用户token</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setUhome_access_token:" title="setUhome_access_token:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setUhome_access_token:">&ndash;&nbsp;setUhome_access_token:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>云平台用户token</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setUhome_access_token:(NSString *)<em>uhome_access_token</em></code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>云平台用户token</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/uhome_user_id" title="uhome_user_id"></a>
	<h3 class="method-title"><code><a href="#//api/name/uhome_user_id">&ndash;&nbsp;uhome_user_id</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>云平台用户ID</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSString *)uhome_user_id</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>云平台用户ID</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setUhome_user_id:" title="setUhome_user_id:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setUhome_user_id:">&ndash;&nbsp;setUhome_user_id:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>云平台用户ID</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setUhome_user_id:(NSString *)<em>uhome_user_id</em></code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>云平台用户ID</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/createTime" title="createTime"></a>
	<h3 class="method-title"><code><a href="#//api/name/createTime">&ndash;&nbsp;createTime</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>创建时间</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSDate *)createTime</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>创建时间</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setCreateTime:" title="setCreateTime:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setCreateTime:">&ndash;&nbsp;setCreateTime:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>创建时间</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setCreateTime:(NSDate *)<em>createTime</em></code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>创建时间</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UDAuthDataDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						

						<a title="Extension Methods" name="task_Extension Methods"></a>
						<h2 class="task-title">Extension Methods</h2>

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/realAccess_token" title="realAccess_token"></a>
	<h3 class="method-title"><code><a href="#//api/name/realAccess_token">&nbsp;&nbsp;realAccess_token</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心个人token</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realAccess_token</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心个人token</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">ApplicationOauthData+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realExpires_in" title="realExpires_in"></a>
	<h3 class="method-title"><code><a href="#//api/name/realExpires_in">&nbsp;&nbsp;realExpires_in</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心<a href="#//api/name/access_token">access_token</a> 过期剩余时间：秒</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realExpires_in</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心<a href="#//api/name/access_token">access_token</a> 过期剩余时间：秒</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">ApplicationOauthData+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realRefresh_token" title="realRefresh_token"></a>
	<h3 class="method-title"><code><a href="#//api/name/realRefresh_token">&nbsp;&nbsp;realRefresh_token</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心刷新token</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realRefresh_token</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心刷新token</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">ApplicationOauthData+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realScope" title="realScope"></a>
	<h3 class="method-title"><code><a href="#//api/name/realScope">&nbsp;&nbsp;realScope</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心access_token作用域</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realScope</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心access_token作用域</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">ApplicationOauthData+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realToken_type" title="realToken_type"></a>
	<h3 class="method-title"><code><a href="#//api/name/realToken_type">&nbsp;&nbsp;realToken_type</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户中心<a href="#//api/name/access_token">access_token</a> 类型</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realToken_type</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户中心<a href="#//api/name/access_token">access_token</a> 类型</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">ApplicationOauthData+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realUhome_access_token" title="realUhome_access_token"></a>
	<h3 class="method-title"><code><a href="#//api/name/realUhome_access_token">&nbsp;&nbsp;realUhome_access_token</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>云平台用户token</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realUhome_access_token</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>云平台用户token</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">ApplicationOauthData+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realUhome_user_id" title="realUhome_user_id"></a>
	<h3 class="method-title"><code><a href="#//api/name/realUhome_user_id">&nbsp;&nbsp;realUhome_user_id</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>云平台用户ID</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, copy) NSString *realUhome_user_id</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>云平台用户ID</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">ApplicationOauthData+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/realCreateTime" title="realCreateTime"></a>
	<h3 class="method-title"><code><a href="#//api/name/realCreateTime">&nbsp;&nbsp;realCreateTime</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>创建时间</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong) NSDate *realCreateTime</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>创建时间</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">ApplicationOauthData+PrivateExtension.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>