<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>UDUserDomainCache Class Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	

	

	
	<optgroup label="Instance Methods">
		
		<option value="//api/name/clearAllData">- clearAllData</option>
		
		<option value="//api/name/getAddressList">- getAddressList</option>
		
		<option value="//api/name/getAuthData">- getAuthData</option>
		
		<option value="//api/name/getDeviceList">- getDeviceList</option>
		
		<option value="//api/name/getFamilyList">- getFamilyList</option>
		
		<option value="//api/name/getTerminalList">- getTerminalList</option>
		
		<option value="//api/name/getUserInfo">- getUserInfo</option>
		
		<option value="//api/name/setAddressList:">- setAddressList:</option>
		
		<option value="//api/name/setAuthData:">- setAuthData:</option>
		
		<option value="//api/name/setDeviceList:">- setDeviceList:</option>
		
		<option value="//api/name/setFamilyList:">- setFamilyList:</option>
		
		<option value="//api/name/setTerminalList:">- setTerminalList:</option>
		
		<option value="//api/name/setUserInfo:">- setUserInfo:</option>
		
	</optgroup>
	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">UDUserDomainCache Class Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Inherits from</th>
	<td>NSObject</td>
</tr><tr>
	<th>Conforms to</th>
	<td><a href="../Protocols/UpUserDomainCacheDelegate.html">UpUserDomainCacheDelegate</a></td>
</tr><tr>
	<th>Declared in</th>
	<td>UDUserDomainCache.h<br />UDUserDomainCache.m</td>
</tr>
						</tbody></table></div>
					

                    

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/clearAllData" title="clearAllData"></a>
	<h3 class="method-title"><code><a href="#//api/name/clearAllData">&ndash;&nbsp;clearAllData</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>清除全部缓存</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)clearAllData</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>清除全部缓存</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainCacheDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/getAuthData" title="getAuthData"></a>
	<h3 class="method-title"><code><a href="#//api/name/getAuthData">&ndash;&nbsp;getAuthData</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>读取鉴权信息</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (id&lt;UDAuthDataDelegate&gt;)getAuthData</code></div>

		    
			

			
			<div class="method-subsection return">
				<h4 class="method-subtitle parameter-title">Return Value</h4>
				<p>鉴权信息，没有时返回null</p>
			</div>
			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>读取鉴权信息</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainCacheDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/getDeviceList" title="getDeviceList"></a>
	<h3 class="method-title"><code><a href="#//api/name/getDeviceList">&ndash;&nbsp;getDeviceList</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>读取设备信息列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSArray&lt;id&lt;UDDeviceDelegate&gt; &gt; *)getDeviceList</code></div>

		    
			

			
			<div class="method-subsection return">
				<h4 class="method-subtitle parameter-title">Return Value</h4>
				<p>设备信息列表，没有时返回null</p>
			</div>
			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>读取设备信息列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainCacheDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/getFamilyList" title="getFamilyList"></a>
	<h3 class="method-title"><code><a href="#//api/name/getFamilyList">&ndash;&nbsp;getFamilyList</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>读取家庭信息列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSArray&lt;id&lt;UDFamilyDelegate&gt; &gt; *)getFamilyList</code></div>

		    
			

			
			<div class="method-subsection return">
				<h4 class="method-subtitle parameter-title">Return Value</h4>
				<p>家庭信息列表，没有时返回null</p>
			</div>
			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>读取家庭信息列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainCacheDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/getTerminalList" title="getTerminalList"></a>
	<h3 class="method-title"><code><a href="#//api/name/getTerminalList">&ndash;&nbsp;getTerminalList</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>读取终端列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSArray&lt;id&lt;UDUserTermDelegate&gt; &gt; *)getTerminalList</code></div>

		    
			

			
			<div class="method-subsection return">
				<h4 class="method-subtitle parameter-title">Return Value</h4>
				<p>终端列表，没有时返回null</p>
			</div>
			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>读取终端列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainCacheDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/getAddressList" title="getAddressList"></a>
	<h3 class="method-title"><code><a href="#//api/name/getAddressList">&ndash;&nbsp;getAddressList</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>获取地址列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (NSArray&lt;id&lt;UDAddressDelegate&gt; &gt; *)getAddressList</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>获取地址列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainCacheDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/getUserInfo" title="getUserInfo"></a>
	<h3 class="method-title"><code><a href="#//api/name/getUserInfo">&ndash;&nbsp;getUserInfo</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>读取用户信息</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (id&lt;UDUserInfoDelegate&gt;)getUserInfo</code></div>

		    
			

			
			<div class="method-subsection return">
				<h4 class="method-subtitle parameter-title">Return Value</h4>
				<p>用户信息，没有时返回null</p>
			</div>
			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>读取用户信息</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainCacheDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setAuthData:" title="setAuthData:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setAuthData:">&ndash;&nbsp;setAuthData:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>存储鉴权信息</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setAuthData:(id&lt;UDAuthDataDelegate&gt;)<em>authData</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>authData</code></th>
						<td><p>鉴权信息，传null时为删除</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>存储鉴权信息</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainCacheDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setDeviceList:" title="setDeviceList:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setDeviceList:">&ndash;&nbsp;setDeviceList:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>存储设备信息列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setDeviceList:(NSArray&lt;id&lt;UDDeviceDelegate&gt; &gt; *)<em>infoList</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>infoList</code></th>
						<td><p>设备信息列表，传null时为删除</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>存储设备信息列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainCacheDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setFamilyList:" title="setFamilyList:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setFamilyList:">&ndash;&nbsp;setFamilyList:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>存储家庭信息列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setFamilyList:(NSArray&lt;id&lt;UDFamilyDelegate&gt; &gt; *)<em>infoList</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>infoList</code></th>
						<td><p>家庭信息列表，传null时为删除</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>存储家庭信息列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainCacheDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setTerminalList:" title="setTerminalList:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setTerminalList:">&ndash;&nbsp;setTerminalList:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>存储终端列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setTerminalList:(NSArray&lt;id&lt;UDUserTermDelegate&gt; &gt; *)<em>infoList</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>infoList</code></th>
						<td><p>终端列表，传null时为删除</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>存储终端列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainCacheDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setUserInfo:" title="setUserInfo:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setUserInfo:">&ndash;&nbsp;setUserInfo:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>存储用户信息</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setUserInfo:(id&lt;UDUserInfoDelegate&gt;)<em>userInfo</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>userInfo</code></th>
						<td><p>用户信息，传null时为删除</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>存储用户信息</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainCacheDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/setAddressList:" title="setAddressList:"></a>
	<h3 class="method-title"><code><a href="#//api/name/setAddressList:">&ndash;&nbsp;setAddressList:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>设置地址列表</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)setAddressList:(NSArray&lt;id&lt;UDAddressDelegate&gt; &gt; *)<em>addressList</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>addressList</code></th>
						<td><p>地址列表</p></td>
					</tr>
				
				</table>
			</div>
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>设置地址列表</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainCacheDelegate.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>