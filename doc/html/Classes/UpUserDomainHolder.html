<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>UpUserDomainHolder Class Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	
	<option value="overview">Overview</option>
	

	
	
	<option value="tasks">Tasks</option>
	
	

	
	
	<optgroup label="Properties">
		
		<option value="//api/name/userDomain">userDomain</option>
		
	</optgroup>
	

	
	<optgroup label="Class Methods">
		
		<option value="//api/name/instance">+ instance</option>
		
	</optgroup>
	

	
	<optgroup label="Instance Methods">
		
		<option value="//api/name/initializeUserDomainForHomeland">- initializeUserDomainForHomeland</option>
		
		<option value="//api/name/initializeUserDomainForHomelandSYN">- initializeUserDomainForHomelandSYN</option>
		
		<option value="//api/name/initializeUserDomainForSouthEastAsia">- initializeUserDomainForSouthEastAsia</option>
		
		<option value="//api/name/initializeUserDomainWithMyOwnUserDataSource:deviceDataSource:familyDataSource:platform:">- initializeUserDomainWithMyOwnUserDataSource:deviceDataSource:familyDataSource:platform:</option>
		
	</optgroup>
	
	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">UpUserDomainHolder Class Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Inherits from</th>
	<td>NSObject</td>
</tr><tr>
	<th>Declared in</th>
	<td>UpUserDomainHolder.h<br />UpUserDomainHolder.m</td>
</tr>
						</tbody></table></div>
					

                    
					
					<div class="section section-overview">
						<a title="Overview" name="overview"></a>
						<h2 class="subtitle subtitle-overview">Overview</h2>
						<p>用户基础信息组件实例持有者（单例子），有默认的userdomain，如果需要定制userdomain，调用initializeUserDomainWithUserDataSource接口进行初始化</p>
					</div>
					
					

					
					
					<div class="section section-tasks">
						<a title="Tasks" name="tasks"></a>
						

						
						

						<div class="task-list">
							<div class="section-method">
	<a name="//api/name/userDomain" title="userDomain"></a>
	<h3 class="method-title"><code><a href="#//api/name/userDomain">&nbsp;&nbsp;userDomain</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>用户基础信息组件实例(<a href="../Classes/UpUserDomain.html">UpUserDomain</a>)</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>@property (nonatomic, strong, readonly) UpUserDomain *userDomain</code></div>

		    
			

			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>用户基础信息组件实例(<a href="../Classes/UpUserDomain.html">UpUserDomain</a>)</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainHolder.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/instance" title="instance"></a>
	<h3 class="method-title"><code><a href="#//api/name/instance">+&nbsp;instance</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>单例</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>+ (instancetype)instance</code></div>

		    
			

			
			<div class="method-subsection return">
				<h4 class="method-subtitle parameter-title">Return Value</h4>
				<p>（UpUserDomainHolder）</p>
			</div>
			

			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<p>单例</p>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainHolder.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/initializeUserDomainForHomeland" title="initializeUserDomainForHomeland"></a>
	<h3 class="method-title"><code><a href="#//api/name/initializeUserDomainForHomeland">&ndash;&nbsp;initializeUserDomainForHomeland</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>初始化国内版本的UserDomain。</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)initializeUserDomainForHomeland</code></div>

		    
			

			

			
			<div class="method-subsection availability">
				<h4 class="method-subtitle parameter-title">Availability</h4>
				<p>3.0.0</p>
			</div>
			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<div class="warning"><p><strong>Warning:</strong> 该方法会初始化国内版本的UserDomain相关数据源及服务器请求源。请勿他用。</p></div>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainHolder.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/initializeUserDomainForHomelandSYN" title="initializeUserDomainForHomelandSYN"></a>
	<h3 class="method-title"><code><a href="#//api/name/initializeUserDomainForHomelandSYN">&ndash;&nbsp;initializeUserDomainForHomelandSYN</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>初始化国内三翼鸟版本的UserDomain。</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)initializeUserDomainForHomelandSYN</code></div>

		    
			

			

			
			<div class="method-subsection availability">
				<h4 class="method-subtitle parameter-title">Availability</h4>
				<p>3.0.0</p>
			</div>
			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<div class="warning"><p><strong>Warning:</strong> 该方法会初始化国内三翼鸟版本的UserDomain相关数据源及服务器请求源。请勿他用。</p></div>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainHolder.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/initializeUserDomainForSouthEastAsia" title="initializeUserDomainForSouthEastAsia"></a>
	<h3 class="method-title"><code><a href="#//api/name/initializeUserDomainForSouthEastAsia">&ndash;&nbsp;initializeUserDomainForSouthEastAsia</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>初始化东南亚版本的UserDomain。</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)initializeUserDomainForSouthEastAsia</code></div>

		    
			

			

			
			<div class="method-subsection availability">
				<h4 class="method-subtitle parameter-title">Availability</h4>
				<p>3.0.0</p>
			</div>
			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<div class="warning"><p><strong>Warning:</strong> 该方法会初始化东南亚版本的UserDomain相关数据源及服务器请求源。请勿他用。</p></div>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainHolder.h</code></p>
			</div>
			
			
		</div>
	</div>
</div><div class="section-method">
	<a name="//api/name/initializeUserDomainWithMyOwnUserDataSource:deviceDataSource:familyDataSource:platform:" title="initializeUserDomainWithMyOwnUserDataSource:deviceDataSource:familyDataSource:platform:"></a>
	<h3 class="method-title"><code><a href="#//api/name/initializeUserDomainWithMyOwnUserDataSource:deviceDataSource:familyDataSource:platform:">&ndash;&nbsp;initializeUserDomainWithMyOwnUserDataSource:deviceDataSource:familyDataSource:platform:</a></code>
</h3>

	<div class="method-info">
		<div class="pointy-thing"></div>

		<div class="method-info-container">
			
			
			<div class="method-subsection brief-description">
				<p>初始化自有数据版本的UserDomain。</p>
			</div>
			
		    

			<div class="method-subsection method-declaration"><code>- (void)initializeUserDomainWithMyOwnUserDataSource:(id&lt;UpUserDataSource&gt;)<em>userDataSource</em> deviceDataSource:(id&lt;UpDeviceListDataSource&gt;)<em>deviceDataSource</em> familyDataSource:(id&lt;UpFamilyDataSource&gt;)<em>familyDataSource</em> platform:(UPUserDomainPlatform)<em>platform</em></code></div>

		    
			
			<div class="method-subsection arguments-section parameters">
				<h4 class="method-subtitle parameter-title">Parameters</h4>
				<table class="argument-def parameter-def">
				
					<tr>
						<th scope="row" class="argument-name"><code>userDataSource</code></th>
						<td><p>自有的用户数据源接口协议实现对象，不能为nil。</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>deviceDataSource</code></th>
						<td><p>自有的设备数据源接口协议实现对象，不能为nil。</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>familyDataSource</code></th>
						<td><p>自有的家庭数据源接口协议实现对象，不能为nil。</p></td>
					</tr>
				
					<tr>
						<th scope="row" class="argument-name"><code>platform</code></th>
						<td><p>运行平台</p></td>
					</tr>
				
				</table>
			</div>
			

			

			
			<div class="method-subsection availability">
				<h4 class="method-subtitle parameter-title">Availability</h4>
				<p>3.0.0</p>
			</div>
			

			
			<div class="method-subsection discussion-section">
				<h4 class="method-subtitle">Discussion</h4>
				<div class="warning"><p><strong>Warning:</strong> 该方法会根据传入的自有数据源相关对象。请在清楚并了解的基础下使用，切勿乱用。若入参不符合接口注释所示要求，则UserDomain初始化失败，相关功能无法保证。</p></div>
			</div>
			

			

			

			
			<div class="method-subsection declared-in-section">
				<h4 class="method-subtitle">Declared In</h4>
				<p><code class="declared-in-ref">UpUserDomainHolder.h</code></p>
			</div>
			
			
		</div>
	</div>
</div>
						</div>
						
					</div>
					
					

                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>