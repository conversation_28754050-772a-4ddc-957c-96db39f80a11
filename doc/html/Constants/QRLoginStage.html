<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>QRLoginStage Constants Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	

	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">QRLoginStage Constants Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Declared in</th>
	<td>UserQRLoginStage.h</td>
</tr>
						</tbody></table></div>
					

                    

					

                    
                    <h3 class="subsubtitle method-title">QRLoginStage</h3>
                    
                    <div class="section">
                        <!-- display enum values -->
                        
                        <h4 class="method-subtitle">Definition</h4>
                        <code>typedef NS_ENUM(NSInteger, QRLoginStage ) {<br>
                            
                            &nbsp;&nbsp; <a href="">UserQRLoginStageCreate</a> = 0,<br>
                            
                            &nbsp;&nbsp; <a href="">UserQRLoginStageDidScan</a> = 1,<br>
                            
                            &nbsp;&nbsp; <a href="">UserQRLoginStageConfirmLogin</a> = 2,<br>
                            
                            &nbsp;&nbsp; <a href="">UserQRLoginStageCancleLoginOrOverdue</a> = 3,<br>
                            
                            };</code>
                    
                    </div>
                    
                    <div class="section section-methods">
                        <h4 class="method-subtitle">Constants</h4>
                        <dl class="termdef">
                            
                            <dt><a name="" title="UserQRLoginStageCreate"></a><code>UserQRLoginStageCreate</code></dt>
<dd>

    
	<p>
		Declared In <code class="declared-in-ref">UserQRLoginStage.h</code>.
	</p>
	
</dd>
                            
                            <dt><a name="" title="UserQRLoginStageDidScan"></a><code>UserQRLoginStageDidScan</code></dt>
<dd>

    
	<p>
		Declared In <code class="declared-in-ref">UserQRLoginStage.h</code>.
	</p>
	
</dd>
                            
                            <dt><a name="" title="UserQRLoginStageConfirmLogin"></a><code>UserQRLoginStageConfirmLogin</code></dt>
<dd>

    
	<p>
		Declared In <code class="declared-in-ref">UserQRLoginStage.h</code>.
	</p>
	
</dd>
                            
                            <dt><a name="" title="UserQRLoginStageCancleLoginOrOverdue"></a><code>UserQRLoginStageCancleLoginOrOverdue</code></dt>
<dd>

    
	<p>
		Declared In <code class="declared-in-ref">UserQRLoginStage.h</code>.
	</p>
	
</dd>
                            
                        </dl>
                    </div>
                    

                    
                    
                    
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>