<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">

	<title>userDomainCallback Block Reference</title>

	<link rel="stylesheet" href="../css/style.css">
	<meta name="viewport" content="initial-scale=1, maximum-scale=1.4">
	<meta name="generator" content="appledoc 2.2.1 (build 1334)">
</head>
<body class="appledoc">
	<header>
		<div class="container" class="hide-in-xcode">
			
			<h1 id="library-title">
				<a href="../index.html">upuserdomain </a>
			</h1>

			<p id="developer-home">
				<a href="../index.html">com.uhome.haier.uplus</a>
			</p>
			
		</div>
	</header>

	<aside>
		<div class="container">
			<nav>
				<ul id="header-buttons" role="toolbar">
					<li><a href="../index.html">Index</a></li>
<li><a href="../hierarchy.html">Hierarchy</a></li>

					<li id="on-this-page" role="navigation">
						<label>
							On This Page

							<div class="chevron">
								<div class="chevy chevron-left"></div>
								<div class="chevy chevron-right"></div>
							</div>

							<select id="jump-to">
	<option value="top">Jump To&#133;</option>
	

	

	
</select>
						</label>
					</li>
				</ul>
			</nav>
		</div>
	</aside>

	<article>
		<div id="overview_contents" class="container">
			<div id="content">
				<main role="main">
					<h1 class="title">userDomainCallback Block Reference</h1>

					
					<div class="section section-specification"><table cellspacing="0"><tbody>
						<tr>
	<th>Declared in</th>
	<td>UserDomainSampleResult.h</td>
</tr><tr>
	<th>References</th>
	<td><a href="Classes/UserDomainSampleResult.html">UserDomainSampleResult</a></td>
</tr>
						</tbody></table></div>
					

                    

					

                    
                    
          
          <a title="Block Definition" name="instance_methods"></a>
          <h4 class="method-subtitle parameter-title">Block Definition</h4>
          <h3 class="subsubtitle method-title">userDomainCallback</h3>


<div class="method-subsection brief-description">
  <ul>
<li>用户基础信息组件回调Block</li>
</ul>

</div>



<code>typedef void (^userDomainCallback) (UserDomainSampleResult *result)</code>
  
  
  
  <div class="method-subsection discussion-section">
  <h4 class="method-subtitle">Discussion</h4>
  <ul>
<li>用户基础信息组件回调Block</li>
</ul>

  </div>
  
  
  
  
  
  
  
  <div class="method-subsection see-also-section">
  <h4 class="method-subtitle">See Also</h4>
  <ul>
  
  <li><code><p><a href="../Classes/UserDomainSampleResult.html">UserDomainSampleResult</a></p></code></li>
  
  </ul>
  </div>
  
  
  
  <div class="method-subsection declared-in-section">
  <h4 class="method-subtitle">Declared In</h4>
  <code class="declared-in-ref">UserDomainSampleResult.h</code><br />
  </div>
  
  
          
				</main>

				<footer>
					<div class="footer-copyright">
						
						<p class="copyright">Copyright &copy; 2022 com.uhome.haier.uplus. All rights reserved. Updated: 2022-03-25</p>
						
						
						<p class="generator">Generated by <a href="http://appledoc.gentlebytes.com">appledoc 2.2.1 (build 1334)</a>.</p>
						
					</div>
				</footer>
			</div>
		</div>
	</article>

	<script src="../js/script.js"></script>
</body>
</html>