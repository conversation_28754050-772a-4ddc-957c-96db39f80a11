#  版本发布记录
## 当前版本: 3.8.0

### 3.8.0
1.升级更新设备名称接口

### 3.7.12
1.修改podspec中的源

### 3.7.11
1.配合CI上库gitlab

### 3.7.10

1.优化家庭列表接口

### 3.7.9
1.支持验收环境
### 3.7.8
1.过滤设备敏感名称接口适配
### 3.7.7
1.切换server域名为zj.haier.net
### 3.7.5
1.修复取消登录事件未响应问题
### 3.7.3
1.修改东南亚Token刷新时，失败问题

### 3.6.2
1.合入master,出tag
### 3.6.0
1.切换为静态库,源码依赖
### 3.4.6
1.针对用户中心返回错误的特殊性，对返回错误进行同步处理,若返回特殊错误，则返回特别错误码
### 3.7.0
1.支持三翼鸟单点登录查询功能
### 3.5.1
1.合并东南亚多用户中心功能到master分支
### 3.5.0
1.新增东南亚基础信息配置单例类 SEBaseInfoConfig，适配多用户登录
### 3.4.5
1.修复Token过期时登录状态为已登录问题
### 3.4.4
1.修改设备名称接口适配非网器设备
### 3.4.3
1.修改非网器批量解绑设备接口
### 3.4.2
1.合并feature_3.4.x到master分支
### 3.4.1
1.用户信息升级设备相关接口及适配单元测试
### 3.4.0
1.用户信息新增是否同意加入家庭接口,并新增测试代码用例
### 3.3.4
1.设备修改名称成功后增加刷新设备列表成功事件
### 3.3.3
1.修复多线程访问用户附加信息时，引起数据解析时空指针异常
### 3.3.2
1.修改退出登录结果回调解析出错问题
### 3.3.1
1.将feature_3.0.x分支合并到master
### 3.3.0
1.退出登录接口升级
## 当前版本: 3.2.18
### 3.2.18
1.修复删除默认地址失败问题
### 3.2.17
1.修复删除默认地址失败问题
### 3.2.14
1.修复apptypeIcon取值不到问题
### 3.2.13
1.用户信息支持配置是否需要刷新设备列表和家庭列表
2.apptypeIcon
### 3.2.12
1.修复无firstMember问题
### 3.2.10
1.修复tag失败问题
### 3.2.8
1.修改家庭移动设备到新房间Path信息
### 3.2.6
1.修改定时刷新token对外事件、在14.2系统上定时任务提前触发问题
### 3.2.5
1.修改楼层id错误问题
### 3.2.4
1.修改Framework下，刷新家庭列表签名可能失败问题
### 3.2.3
1.修复验收
### 3.2.2
1.支持楼层业务代码实现，单元测试代码实现
### 3.2.1
1.支持楼层业务代码实现
### 3.2.0
1.支持楼层业务
### 3.1.7
1.修复多线程闪退问题
### 3.1.6
1.修复更新用户信息生日的校验
### 3.1.5
1.清除缓存后将刷新状态置成NO
### 3.1.4
1.修改刷新token取值问题
### 3.1.3
1.修改token即将过期时无法刷新token问题
### 3.1.2
1.补充东南亚数据源缺失的代理的接口

### 3.1.1
1.修复设备列表为空时，断网下无法加载用户信息问题
### 3.1.0
1.东南亚分支合并后测试并修改问题 
2.user添加隐私国家编码字段
3.退出登录增加即将退出登录的监听事件

### 3.0.17
1.修复删除地址后还有默认地址的问题
### 3.0.16
1.修复bugZHIJIAAPP-15146
### 3.0.15
1.修复覆盖安装后无法获取鉴权信息问题
### 3.0.14
1.设备缺失bindType问题修复 
2.退出前增加block回调

### 3.0.12
1.修复监听收不到的问题
### 3.0.11
1.修复设备取不到room对象问题
### 3.0.9
1.修复批量操作设备成功后的缺失逻辑
### 3.0.7
1.修复创建地址和编辑地址失败问题
### 3.0.4
1.修复更新设备名称问题,修复更新家庭详情的时候默认家庭被错误覆盖的问题
### 3.0.3
1.修改重复定义的类
### 3.0.1
1.头文件整体添加到userdomainHolder中 
### 3.0.0
1.完成userdomain重构
### 2.5.13
1.设备添加apptypeCode字段
### 2.5.12
1. 优化User类的监听属性及其事件发送逻辑，以解决可能导致的崩溃问题。

### 2.5.11
1. 解决UpUserDomain在framework发版后，UpUserDomain.h变成小写的upuserdomain.h的问题。

### 2.5.10
1. 修复将upuserdomain至其他工程导致编译错误的问题。

### 2.5.9
1. 修正集成至其他工程报non-module相关错误的问题。

### 2.5.8
1. 调整代码文件目录结构，以解决CI无法生成AppleDoc的问题。

### 2.5.7
1. 修正podspec文件中关于resource配置错误的问题。

### 2.5.6
1. 修复生成的framework库里的readme和release文档错误的问题。

### 2.5.5
1. 调整api库的封装逻辑，将其也封装成framework随主库一起发版。

### 2.5.3
1. 修正podsepc配置错误的问题。

### 2.5.2
1. 将组件库封装成framework形式发布。

### 2.5.1
1.合并刷新token接口升级为server接口、token刷新机制逻辑修改到master分支

### 2.5.0
1.  userDomain用户基础信息添加注册来源属性。
2. 上传头像异常交互优化。

### 2.4.8
1. 当前家庭变化通知逻辑优化，以解决可能导致的崩溃问题。
2. 优化列表接口缓存逻辑，以解决可能导致的崩溃问题。

### 2.4.6
1. 修复断网启动时，智慧家页面因当前家庭下午设备列表导致的问题。

### 2.4.5
1. 查询接口回退为用户中心接口，修改信息后使用智家接口查询一次
2. 修复断网情况下，未使用缓存的鉴权信息初始化upnetwork组件导致的问题。

### 2.4.4
1.查询用户信息使用智家api
### 2.4.3
1. 退出登录后，token失效，不做token刷新的重试

### 2.4.2
1.修改用户中心secret不同环境为同一个key
### 2.4.1
1.修改获取oauthData逻辑 2.修复东南亚请求失败问题及部分代理方法未实现导致闪退问题 
### 2.4.0
1. 将U+App东南亚版本中使用的UserDomain组件Server接口API子类、数据源及其相关代码逻辑整合适配至主线版本中。
2. UserDomain组件分别增加针对国内版、东南亚版及其他版本的数据源初始化接口，以便针对不同情况进行有差别的组件初始化。
3. 自此版本之后，UserDomain组件双线合一，开启单线版本同时支持双线、乃至多线数据源的一对多的风骚时代。

### 2.3.4
1.修复获取缓存后，未过滤默认家庭问题
### 2.3.3
1. 修复迁移的uplusApi业务接口中,更新用户信息接口、上传登录记录接口和刷新token接口因参数配置错误导致的接口请求失败问题。
2. 用户基础信息数据从缓存加载的时机调整，由原初始化User时加载缓存调整为用户自动登录成功之后加载缓存。

### 2.3.2
1. 迁移过来的uplusApi并适配UPNetwork 3.2.1版本的相关代码逻辑优化、完善。

### 2.3.1
1. 新建UserDomainAPIs target，用来放置从uplusApi库中移过来的各UPRequest业务接口子类。
2. 适配UPNetwork 3.2.1版本。
3. 设置默认家庭时，未同步更新缓存家庭列表问题修复

### 2.3.0
1. 新增家庭详情后台刷新逻辑及相关通知事件。

### 2.2.9
1. 修复因2.2.8版本中ApplicationOauthData的MJExtension的忽略属性方法中去除了createTime导致的App发生的因JSON格式解析不了NSDate属性的崩溃的问题。

### 2.2.8
1. 优化UserDomain消息事件通知的发送逻辑。
2. 优化用户鉴权信息刷新完成后，接口请求所需的accessToken设置逻辑。
3. 优化日志逻辑，添加因无效token导致的刷新用户鉴权信息相关的日志，将Debug级别的日志改为Info级别。

### 2.2.7
1. 优化家庭中的设备转移房间的逻辑，以解决设备移动房间后，User对象中的对应设备房间信息未变的问题。

### 2.2.6
1. 优化UserDomain消息事件的通知发送逻辑，以解决因在非主线程发事件消息，消息监听方收到消息在非主线程操作UI可能导致的崩溃问题。

### 2.2.5
1. 调整refreshUser方法的内部逻辑，将立即刷新的参数由NO，修改为YES。以此解决APP中已有通过refreshUser刷新无法达到目的的问题。

### 2.2.4
1. 修复在刷新完用户信息、设备列表、家庭列表后，断网条件下，再加载了缓存之后，刷新状态依然为未刷新完成的问题。

### 2.2.3
1. 优化refreshUser方法的内部逻辑。
2. 优化用户相关信息缓存时机的逻辑。
3. 更新依赖的upnetwork版本至3.0.10。

### 2.2.2
1. 适配并更新无缓存功能的upnetwork 3.0.3版本，uplusApi 1.0.7版本。
2. 添加用户数据、家庭列表和设备列表的本地缓存功能。
3. 接口重试次数调整，验证登录信息接口取消3次重试逻辑。
4. 完善用户登录、登出等基础事件通知的日志打印逻辑，去掉事件通知逻辑中的僵尸代码和方法。
5. 添加关键逻辑点的日志打印代码。
