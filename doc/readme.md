# UPUserDomain

#### 介绍
U+App 用户信息组件,其代码仓库地址为:http://10.138.228.89:8092/upuserdomain/ios 。
先将其clone下来，命令如下：
```
git clone "http://yourUserName@10.138.228.89:8092/upuserdomain/ios"
```
克隆完成后，进入工程所在目录的跟目录，会发现有一个名称为Podfile的文件。
然后，打开Terminal，进入工程所在目录，执行pod install。
pod install完成之后，会生成一个UPUserDomain.xcworkspace的文件，双击打开它。
在执行单元测试之前，还需要clone该库的单元测试用例库，如下：
```
git clone "http://yourUserName@10.138.228.89:8092/uplus/features/userdomain"
```
克隆下来后，将该库放置与UPUserDomain库文件夹同级目录，
然后，将单元测试用例的仓库以文件形式引入进UPUserDomain的单元测试target中即可运行单元测试用例。
