PODS:
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AFNetworking/UIKit (4.0.1):
    - AFNetworking/NSURLSession
  - Cucumberish (1.4.0)
  - Masonry (1.0.0)
  - MJExtension (3.2.1)
  - OCMock (3.8.1)
  - Protobuf (3.17.0)
  - SVProgressHUD (2.2.5)
  - uplog (1.5.1):
    - AFNetworking (>= 4.0.1)
    - Protobuf (= 3.17.0)
    - ZipArchive (>= 1.4.0)
  - upnetwork (********25061801):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign (= ********25061801)
    - upnetwork/Headers (= ********25061801)
    - upnetwork/HTTPDns (= ********25061801)
    - upnetwork/Manager (= ********25061801)
    - upnetwork/Request (= ********25061801)
    - upnetwork/Settings (= ********25061801)
    - upnetwork/Utils (= ********25061801)
  - upnetwork/DynamicSign (********25061801):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign/Private (= ********25061801)
  - upnetwork/DynamicSign/Private (********25061801):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Headers (********25061801):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Headers/Private (= ********25061801)
  - upnetwork/Headers/Private (********25061801):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/HTTPDns (********25061801):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Manager (********25061801):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Manager/Private (= ********25061801)
  - upnetwork/Manager/Private (********25061801):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Request (********25061801):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Request/Private (= ********25061801)
  - upnetwork/Request/Private (********25061801):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Settings (********25061801):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Settings/Private (= ********25061801)
  - upnetwork/Settings/Private (********25061801):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Utils (********25061801):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Utils/Private (= ********25061801)
  - upnetwork/Utils/Private (********25061801):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - ZipArchive (1.4.0)

DEPENDENCIES:
  - AFNetworking (= 4.0.1)
  - Cucumberish (= 1.4.0)
  - Masonry (= 1.0.0)
  - MJExtension (= 3.2.1)
  - OCMock (= 3.8.1)
  - SVProgressHUD (= 2.2.5)
  - uplog (= 1.5.1)
  - upnetwork (= ********25061801)

SPEC REPOS:
  https://git.haier.net/uplus/shell/cocoapods/Specs.git:
    - uplog
    - upnetwork
  trunk:
    - AFNetworking
    - Cucumberish
    - Masonry
    - MJExtension
    - OCMock
    - Protobuf
    - SVProgressHUD
    - ZipArchive

SPEC CHECKSUMS:
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  Cucumberish: 6cbd0c1f50306b369acebfe7d9f514c9c287d26c
  Masonry: b529bb169217897b6354d4b56b1fada6d475b13d
  MJExtension: 635f2c663dcb1bf76fa4b715b2570a5710aec545
  OCMock: 29f6e52085b4e7d9b075cbf03ed7c3112f82f934
  Protobuf: 7327d4444215b5f18e560a97f879ff5503c4581c
  SVProgressHUD: 1428aafac632c1f86f62aa4243ec12008d7a51d6
  uplog: 7774a64dab5e5178282819e1e63ad57ae415e805
  upnetwork: 3dda230231a2bfe8a2cf2872922c258cc16dfef4
  ZipArchive: e25a4373192673e3229ac8d6e9f64a3e5713c966

PODFILE CHECKSUM: 7cc18328397f525c2e4190c5be05eaf70087e1d9

COCOAPODS: 1.12.1
