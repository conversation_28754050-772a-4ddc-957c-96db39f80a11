//
//  SERequestConfig.h
//  UpCloud
//
//  Created by <PERSON> on 2019/7/5.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
typedef enum : NSUInteger {
    /* 验收 */
    SERequestEnvironmentAcceptance,

    /* 生产 */
    SERequestEnvironmentProduction,

} SERequestEnvironment;

@interface SERequestConfig : NSObject
/// 用户ID
@property (nonatomic, copy) NSString *userId;
/// 国家编码
@property (nonatomic, copy) NSString *sea_country_code;
/// 语言标识
@property (nonatomic, copy) NSString * (^langCode)(void);
/// 环境
@property (nonatomic, assign) SERequestEnvironment environment;
/// 单例
+ (SERequestConfig *)sharedConfig;

@end

NS_ASSUME_NONNULL_END
