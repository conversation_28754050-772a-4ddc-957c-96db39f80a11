//
//  SERequestConfig.m
//  UpCloud
//
//  Created by <PERSON> on 2019/7/5.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SERequestConfig.h"

@implementation SERequestConfig
+ (SERequestConfig *)sharedConfig
{
    static SERequestConfig *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[self alloc] init];
    });
    return instance;
}

@end
