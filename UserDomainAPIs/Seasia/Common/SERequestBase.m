//
//  SERequestBase.m
//  UserDomainAPIs
//
//  Created by <PERSON> on 2019/7/2.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SERequestBase.h"
#import "SEEncryption.h"
#import "SERequestConfig.h"

NSString *const kSERequestHeadersAppID = @"appId";
NSString *const kSERequestHeadersAppVersion = @"appVersion";
NSString *const kSERequestHeadersClientId = @"clientId";
NSString *const kSERequestHeadersTimestamp = @"timestamp";
NSString *const kSERequestHeadersAccessToken = @"accessToken";
NSString *const kSERequestHeadersSign = @"sign";
NSString *const kSERequestHeadersContentType = @"Content-Type";
NSString *const kSERequestHeadersAccountToken = @"accountToken";
NSString *const kSERequestHeadersServiceModel = @"serviceModel";
NSString *const kSERequestHeadersTraceId = @"uTraceId";
NSString *const kSERequestHeadersSpanId = @"uSpanId";
NSString *const kSERequestHeadersBid = @"bid";
NSString *const kSERequestHeadersCountryCode = @"zoneInfo";
NSString *const kSERequestHeadersLangCode = @"langCode";

@interface SERequestBase ()
@property (nonatomic, copy, readonly) NSString *appId;
@property (nonatomic, copy, readonly) NSString *appKey;
@property (nonatomic, copy, readonly) NSString *clientId;
@property (nonatomic, copy, readonly) NSString *timestamp;
@property (nonatomic, copy, readonly) NSString *accountToken;
@property (nonatomic, copy, readonly) NSString *uTraceId;
@property (nonatomic, copy, readonly) NSString *bid;

@end

@implementation SERequestBase
#pragma mark - Overrides
- (NSString *)baseURL
{
    SERequestEnvironment env = [SERequestConfig sharedConfig].environment;
    if (env == SERequestEnvironmentAcceptance) {
        return @"https://uhome-sea-yanshou.haieriot.net";
    }
    else {
        return @"https://uhome-sea.haieriot.net";
    }
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSTimeInterval)timeoutInterval
{
    return 15.0;
}

- (NSUInteger)retryTimes
{
    return 0;
}

- (NSTimeInterval)retryDelay
{
    return 0.0;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSString *appID = [UPNetworkSettings sharedSettings].appID;
    NSString *appVersion = [UPNetworkSettings sharedSettings].appVersion;
    NSString *clientId = [UPNetworkSettings sharedSettings].clientID;
    NSString *accessToken = [UPNetworkSettings sharedSettings].accessToken;
    NSString *uhomeAccessToken = [UPNetworkSettings sharedSettings].uhomeAccessToken;
    NSString *timestamp = self.timestamp;
    NSString *countryCode = [SERequestConfig sharedConfig].sea_country_code;
    NSString *langCode = [SERequestConfig sharedConfig].langCode ? [SERequestConfig sharedConfig].langCode() : @"";

    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    dict[kSERequestHeadersAppID] = appID;
    dict[kSERequestHeadersAppVersion] = appVersion;
    dict[kSERequestHeadersClientId] = clientId;
    dict[kSERequestHeadersAccessToken] = uhomeAccessToken.length ? uhomeAccessToken : nil;
    dict[kSERequestHeadersAccountToken] = accessToken.length ? accessToken : nil;
    dict[kSERequestHeadersTimestamp] = timestamp;
    dict[kSERequestHeadersSign] = [self createSignByTimestamp:timestamp];
    dict[kSERequestHeadersContentType] = @"application/json;charset=utf-8";
    dict[kSERequestHeadersCountryCode] = countryCode;
    if ([langCode isKindOfClass:NSString.class] && langCode.length > 0) {
        dict[kSERequestHeadersLangCode] = langCode;
    }

    return dict;
}

#pragma mark - Non-Public Methods
- (NSString *)uTraceId
{
    NSString *UUIDString = [NSUUID UUID].UUIDString;
    return [UUIDString stringByReplacingOccurrencesOfString:@"-" withString:@""];
}

- (NSString *)bid
{
    NSString *path = [self path];
    NSString *defaultBidString = @"common";
    NSArray *pathComponents = [path componentsSeparatedByString:@"/"];
    if (![pathComponents isKindOfClass:[NSArray class]]) {
        return defaultBidString;
    }
    NSMutableArray *mutablePathComponents = [pathComponents mutableCopy];
    NSMutableArray *reversedPathComponents = [NSMutableArray array];
    while (mutablePathComponents.count > 0) {
        NSString *lastString = [mutablePathComponents lastObject];
        if (lastString.length > 0) {
            [reversedPathComponents addObject:lastString];
        }
        [mutablePathComponents removeLastObject];
    }
    [reversedPathComponents removeLastObject];
    NSString *bidString = [reversedPathComponents lastObject];
    if (![bidString isKindOfClass:[NSString class]] || bidString.length == 0) {
        return defaultBidString;
    }
    return bidString;
}

- (NSString *)createSignByTimestamp:(NSString *)timestamp
{
    NSString *bodyStr = @"";
    NSDictionary *arguments = (NSDictionary *)self.requestBody;
    if ([arguments isKindOfClass:[NSDictionary class]]) {
        NSData *bodyData = [NSJSONSerialization dataWithJSONObject:arguments options:0 error:nil];
        bodyStr = [[NSString alloc] initWithData:bodyData encoding:NSUTF8StringEncoding];
        bodyStr = [bodyStr stringByReplacingOccurrencesOfString:@"\n" withString:@""];
        bodyStr = [bodyStr stringByReplacingOccurrencesOfString:@"\r" withString:@""];
        bodyStr = [bodyStr stringByReplacingOccurrencesOfString:@" " withString:@""];
        bodyStr = [bodyStr stringByReplacingOccurrencesOfString:@"\b" withString:@""];
        bodyStr = [bodyStr stringByReplacingOccurrencesOfString:@"\t" withString:@""];
    }
    NSString *appID = [UPNetworkSettings sharedSettings].appID;
    NSString *appKey = [UPNetworkSettings sharedSettings].appKey;
    NSString *urlStr = self.path;
    if (![urlStr isKindOfClass:[NSString class]]) {
        urlStr = @"";
    }
    if (![urlStr hasPrefix:@"/"]) {
        urlStr = [NSString stringWithFormat:@"/%@", urlStr];
    }
    NSString *originString = [NSString stringWithFormat:@"%@%@%@%@%@", urlStr, bodyStr, appID, appKey, timestamp];
    NSString *signString = [[SEEncryption getSHA256:originString] lowercaseString];
    return signString;
}

#pragma mark - Property Methods
- (NSString *)uniqueID
{
    return [NSString stringWithFormat:@"%@-%@", self.name, self.timestamp];
}

- (NSString *)timestamp
{
    NSTimeInterval millisecondTimeInterval = [[NSDate date] timeIntervalSince1970] * 1000;
    NSString *millisecondString = [NSString stringWithFormat:@"%lld", (long long)millisecondTimeInterval];
    return millisecondString;
}

@end
