//
//  SEUpdateFamilyRoomNameApi.m
//  UserDomainAPIs
//
//  Created by 吴子航 on 2022/5/10.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEUpdateFamilyRoomNameApi.h"

@interface SEUpdateFamilyRoomNameApi ()
@property (strong, nonatomic) NSString *roomName;
@property (strong, nonatomic) NSString *familyId;
@property (nonatomic, strong) NSString *roomId;
@property (nonatomic, copy) NSString *floorId;
@end

@implementation SEUpdateFamilyRoomNameApi

- (instancetype)initWithRoomName:(NSString *)roomName familyId:(NSString *)familyId roomId:(NSString *)roomId floorId:(NSString *)floorId
{
    if (self = [super init]) {
        self.roomName = roomName;
        self.familyId = familyId;
        self.roomId = roomId;
        self.floorId = floorId;
    }
    return self;
}

#pragma mark - Overrides
- (NSString *)name
{
    return @"修改房间名称";
}

- (NSString *)path
{
    return @"/wisdomfamily/family/v2/room/modify";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"roomId" : self.roomId ?: @"",
        @"roomName" : self.roomName ?: @"",
        @"floorId" : self.floorId ?: @""
    };
}

@end
