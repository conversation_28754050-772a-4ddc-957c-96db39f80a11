//
//  SEFamilyMoveDevicesToOtherFamilyApi.m
//  UserDomainAPIs
//
//  Created by 吴子航 on 2022/5/17.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEFamilyMoveDevicesToOtherFamilyApi.h"

static NSString *const kSEdeviceIds = @"deviceIds";
static NSString *const kSEoldFamilyId = @"oldFamilyId";
static NSString *const kSEnewFamilyId = @"newFamilyId";
@interface SEFamilyMoveDevicesToOtherFamilyApi ()
@property (strong, nonatomic) NSArray<NSDictionary<NSString *, NSString *> *> *deviceIds;
@property (strong, nonatomic) NSString *oldFamilyId;
@property (strong, nonatomic) NSString *familyId_new;
@end

@implementation SEFamilyMoveDevicesToOtherFamilyApi
- (instancetype)initWithDeviceIds:(NSArray<NSDictionary<NSString *, NSString *> *> *)deviceIds oldFamilyId:(NSString *)oldFamilyId newFamilyId:(NSString *)newFamilyId
{
    if (self = [super init]) {
        self.deviceIds = deviceIds;
        self.oldFamilyId = oldFamilyId;
        self.familyId_new = newFamilyId;
    }
    return self;
}
- (NSString *)name
{
    return @"移动设备到新的家庭";
}
- (NSString *)path
{
    return @"/uplussea/devices/v2/family/transfer";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSObject *)requestBody
{
    return @{
        kSEdeviceIds : self.deviceIds ?: [NSNull null],
        kSEoldFamilyId : self.oldFamilyId ?: @"",
        kSEnewFamilyId : self.familyId_new ?: @""
    };
}
@end
