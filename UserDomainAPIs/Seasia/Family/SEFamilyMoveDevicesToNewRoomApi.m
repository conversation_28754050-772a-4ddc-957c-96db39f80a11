//
//  SEFamilyMoveDevicesToNewRoomApi.m
//  UserDomainAPIs
//
//  Created by 吴子航 on 2022/5/10.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEFamilyMoveDevicesToNewRoomApi.h"

static NSString *const kSEfamilyId = @"familyId";
static NSString *const kSEdeviceIds = @"deviceIds";
static NSString *const kSEnewRoom = @"newRoom";
static NSString *const kSEnewRoomId = @"newRoomId";
@interface SEFamilyMoveDevicesToNewRoomApi ()
@property (strong, nonatomic) NSString *familyId;
@property (strong, nonatomic) NSArray<NSDictionary<NSString *, NSString *> *> *deviceIds;
@property (strong, nonatomic) NSString *roomName_new;
@property (strong, nonatomic) NSString *roomId_new;
@end

@implementation SEFamilyMoveDevicesToNewRoomApi

- (instancetype)initWithFamilyId:(NSString *)familyId
                       deviceIds:(NSArray<NSDictionary<NSString *, NSString *> *> *)deviceIds
                     newRoomName:(NSString *)newRoomName
                       newRoomId:(NSString *)newRoomId
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.deviceIds = deviceIds;
        self.roomName_new = newRoomName;
        self.roomId_new = newRoomId;
    }
    return self;
}

#pragma mark - Overrides
- (NSString *)name
{
    return @"设备批量移动位置";
}

- (NSString *)path
{
    return @"/uplussea/devices/v2/family/moveplace";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSObject *)requestBody
{
    return @{
        kSEfamilyId : self.familyId ?: @"",
        kSEdeviceIds : self.deviceIds ?: @[],
        kSEnewRoom : self.roomName_new ?: @"",
        kSEnewRoomId : self.roomId_new ?: @""
    };
}

@end
