//
//  SEFamilyInfoApi.m
//  UserDomainAPIs
//
//  Created by 吴子航 on 2022/5/10.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEFamilyInfoApi.h"

@interface SEFamilyInfoApi ()
@property (strong, nonatomic) NSString *familyId;
@end

@implementation SEFamilyInfoApi

- (instancetype)initWithFamilyId:(NSString *)familyId
{
    if (self = [super init]) {
        self.familyId = familyId;
    }
    return self;
}

#pragma mark - Overrides
- (NSString *)name
{
    return @"获取家庭信息";
}

- (NSString *)path
{
    return @"/wisdomfamily/family/v2/family/familyInfo";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSUInteger)retryTimes
{
    return 2;
}

- (NSTimeInterval)retryDelay
{
    return 3.0;
}

- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @""
    };
}

@end
