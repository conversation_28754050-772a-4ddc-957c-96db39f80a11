//
//  SEFamilyRoomListApi.m
//  UserDomainAPIs
//
//  Created by 吴子航 on 2022/5/10.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEFamilyRoomListApi.h"

@interface SEFamilyRoomListApi ()
@property (copy, nonatomic) NSString *familyId;
@property (copy, nonatomic) NSString *floorId;
@end

@implementation SEFamilyRoomListApi

- (instancetype)initWithFamilyId:(NSString *)familyId floorId:(NSString *)floorId
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.floorId = floorId;
    }
    return self;
}

#pragma mark - Overrides
- (NSString *)name
{
    return @"获取家庭房间列表";
}

- (NSString *)path
{
    return @"/wisdomfamily/family/v2/family/room/list";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"floorId" : self.floorId ?: @""
    };
}

@end
