//
//  SECreateFamilyApi.m
//  UserDomainAPIs
//
//  Created by 吴子航 on 2022/5/10.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SECreateFamilyApi.h"

@interface SECreateFamilyApi ()
@property (nonatomic, strong) NSString *familyName;
@property (nonatomic, strong) NSString *familyPosition;
@property (nonatomic, strong) NSString *longitude;
@property (nonatomic, strong) NSString *latitude;
@property (nonatomic, strong) NSArray<NSDictionary<NSString *, NSString *> *> *rooms;
@end

@implementation SECreateFamilyApi
- (instancetype)initWithFamilyName:(NSString *)familyName familyPosition:(NSString *)familyPosition longitude:(NSString *)longitude latitude:(NSString *)latitude rooms:(NSArray<NSDictionary<NSString *, NSString *> *> *)rooms
{
    if (self = [super init]) {
        self.familyName = familyName;
        self.familyPosition = familyPosition;
        self.latitude = latitude;
        self.longitude = longitude;
        self.rooms = [rooms isKindOfClass:[NSArray class]] ? rooms : @[];
    }
    return self;
}
#pragma mark - Overrides
- (NSString *)name
{
    return @"家庭管理员创建家庭";
}

- (NSString *)path
{
    return @"/wisdomfamily/family/v1/family/create";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSObject *)requestBody
{
    return @{
        @"familyName" : self.familyName ?: @"",
        @"familyPosition" : self.familyPosition,
        @"familyLocation" : @{
            @"longitude" : self.longitude ?: @"",
            @"latitude" : self.latitude ?: @"",
        },
        @"rooms" : self.rooms ?: @[]
    };
}

@end
