//
//  SERemoveRoomFromFamilyApi.m
//  UserDomainAPIs
//
//  Created by 吴子航 on 2022/5/10.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SERemoveRoomFromFamilyApi.h"

@interface SERemoveRoomFromFamilyApi ()
@property (nonatomic, strong) NSString *familyId;
@property (nonatomic, strong) NSString *roomId;
@end

@implementation SERemoveRoomFromFamilyApi

- (instancetype)initWithFamilyId:(NSString *)familyId roomId:(NSString *)roomId
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.roomId = roomId;
    }
    return self;
}

#pragma mark - Overrides
- (NSString *)name
{
    return @"删除房间";
}

- (NSString *)path
{
    return @"/wisdomfamily/family/v1/family/room/destroy";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"roomId" : self.roomId ?: @""
    };
}

@end
