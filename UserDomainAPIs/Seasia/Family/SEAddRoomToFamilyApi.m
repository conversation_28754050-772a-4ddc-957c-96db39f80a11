//
//  SEAddRoomToFamilyApi.m
//  UserDomainAPIs
//
//  Created by 吴子航 on 2022/5/10.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEAddRoomToFamilyApi.h"

@interface SEAddRoomToFamilyApi ()

@property (nonatomic, strong) NSString *familyId;
@property (nonatomic, strong) NSString *roomName;
@property (nonatomic, strong) NSString *roomClass;
@property (nonatomic, copy) NSString *floorId;

@end

@implementation SEAddRoomToFamilyApi

- (instancetype)initWithFamilyId:(NSString *)familyId roomName:(NSString *)roomName roomClass:(NSString *)roomClass floorId:(NSString *)floorId
{
    if (self = [super init]) {

        self.familyId = familyId;
        self.roomName = roomName;
        self.roomClass = roomClass;
        self.floorId = floorId;
    }
    return self;
}

#pragma mark - Overrides
- (NSString *)name
{
    return @"添加房间";
}

- (NSString *)path
{
    return @"/wisdomfamily/family/v2/room/create";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSObject *)requestBody
{
    return @{
        @"roomName" : self.roomName ?: @"",
        @"familyId" : self.familyId ?: @"",
        @"roomClass" : self.roomClass ?: @"",
        @"floorId" : self.floorId ?: @""
    };
}

@end
