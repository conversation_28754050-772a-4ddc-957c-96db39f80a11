//
//  SEUpdateFamilyInfoApi.m
//  UserDomainAPIs
//
//  Created by 吴子航 on 2022/5/10.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEUpdateFamilyInfoApi.h"

@interface SEUpdateFamilyInfoApi ()
@property (nonatomic, strong) NSString *familyId;
@property (nonatomic, strong) NSString *familyName;

@end

@implementation SEUpdateFamilyInfoApi

- (instancetype)initWithFamilyId:(NSString *)familyId familyName:(NSString *)familyName
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.familyName = familyName;
    }
    return self;
}

#pragma mark - Overrides
- (NSString *)name
{
    return @"家庭信息编辑";
}

- (NSString *)path
{
    return @"/wisdomfamily/family/v1/family/edit";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"familyName" : self.familyName ?: @""
    };
}

@end
