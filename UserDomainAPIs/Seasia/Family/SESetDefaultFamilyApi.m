//
//  SESetDefaultFamilyApi.m
//  UserDomainAPIs
//
//  Created by 吴子航 on 2022/5/10.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SESetDefaultFamilyApi.h"

@interface SESetDefaultFamilyApi ()
@property (strong, nonatomic) NSString *familyId;
@end

@implementation SESetDefaultFamilyApi

- (instancetype)initWithFamilyId:(NSString *)familyId
{
    if (self = [super init]) {
        self.familyId = familyId;
    }
    return self;
}

#pragma mark - Overrides
- (NSString *)name
{
    return @"设置默认家庭";
}

- (NSString *)path
{
    return @"/wisdomfamily/family/v1/defaultFamily/set";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @""
    };
}

@end
