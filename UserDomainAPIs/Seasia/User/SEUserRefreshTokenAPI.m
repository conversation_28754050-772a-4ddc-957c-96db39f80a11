//
//  SEUserRefreshTokenAPI.m
//  UserDomainAPIs
//
//  Created by <PERSON> on 2019/7/8.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEUserRefreshTokenAPI.h"

@interface SEUserRefreshTokenAPI ()
@property (nonatomic, copy) NSString *refreshToken;
@end

@implementation SEUserRefreshTokenAPI
#pragma mark - Overrides
- (NSString *)name
{
    return @"刷新普通登录";
}

- (NSString *)path
{
    return @"/uplussea/accounts/v1/user/refreshToken";
}

- (NSObject *)requestBody
{
    return @{ @"refreshToken" : self.refreshToken };
}

#pragma mark - Property Methods
- (NSString *)refreshToken
{
    return [NSString stringWithFormat:@"%@", _refreshToken];
}

#pragma mark - Public Methods
- (instancetype)initWithRefreshToken:(NSString *)refreshToken
{
    if (self = [super init]) {
        _refreshToken = refreshToken;
    }
    return self;
}

- (NSUInteger)retryTimes
{
    return 2;
}

- (NSTimeInterval)retryDelay
{
    return 3.0;
}

@end
