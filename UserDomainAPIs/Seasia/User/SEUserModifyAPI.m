//
//  SEUserModifyAPI.m
//  UserDomainAPIs
//
//  Created by <PERSON> on 2019/7/8.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEUserModifyAPI.h"

@interface SEUserModifyAPI ()
@property (nonatomic, strong) SECloudUserInfo *userInfo;
@end

@implementation SEUserModifyAPI
#pragma mark - Overrides
- (NSString *)name
{
    return @"修改用户信息";
}

- (NSString *)path
{
    return @"/uplussea/users/v1/user/modify";
}

- (nullable NSObject *)requestBody
{
    NSMutableDictionary *arguments = [NSMutableDictionary dictionary];
    NSString *userId = self.userInfo.userId;
    if ([userId isKindOfClass:[NSString class]]) {
        [arguments setObject:userId forKey:@"userId"];
    }
    NSString *givenName = self.userInfo.givenName;
    if ([givenName isKindOfClass:[NSString class]]) {
        [arguments setObject:givenName forKey:@"givenName"];
    }
    NSString *nickname = self.userInfo.nickname;
    if ([nickname isKindOfClass:[NSString class]]) {
        [arguments setObject:nickname forKey:@"nickname"];
    }
    NSString *avatarUrl = self.userInfo.avatarUrl;
    if ([avatarUrl isKindOfClass:[NSString class]]) {
        [arguments setObject:avatarUrl forKey:@"avatarUrl"];
    }
    NSString *privacyCountryCode = self.userInfo.privacyCountryCode;
    if ([privacyCountryCode isKindOfClass:[NSString class]]) {
        [arguments setObject:privacyCountryCode forKey:@"privacyCountryCode"];
    }
    if (![arguments.allKeys containsObject:@"userId"] || arguments.count <= 1) {
        return nil;
    }
    return arguments;
}

#pragma mark - Public Methods
- (instancetype)initWithUserInfo:(SECloudUserInfo *)userInfo
{

    if (self = [super init]) {
        _userInfo = userInfo;
    }
    return self;
}

@end
