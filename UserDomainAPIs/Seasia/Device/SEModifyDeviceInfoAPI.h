//
//  SEModifyDeviceInfoAPI.h
//  UserDomainAPIs
//
//  Created by <PERSON> on 2019/7/11.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SERequestBase.h"

NS_ASSUME_NONNULL_BEGIN

extern NSString *const kSEModifyDeviceInfoDeviceID;
extern NSString *const kSEModifyDeviceInfoDeviceName;
extern NSString *const kSEModifyDeviceInfoRoom;
extern NSString *const kSEModifyDeviceInfoFamilyId;

@interface SEModifyDeviceInfoAPI : SERequestBase
- (instancetype)initAPIWithParameters:(NSDictionary<NSString *, NSString *> *)parameters;
@end

NS_ASSUME_NONNULL_END
