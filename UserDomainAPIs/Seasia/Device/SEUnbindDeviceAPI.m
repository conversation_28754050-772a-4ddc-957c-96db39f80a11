//
//  SEUnbindDeviceAPI.m
//  UserDomainAPIs
//
//  Created by <PERSON> on 2019/7/25.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEUnbindDeviceAPI.h"

@interface SEUnbindDeviceAPI ()
@property (nonatomic, strong) NSArray<NSString *> *deviceIDs;
@end

@implementation SEUnbindDeviceAPI
#pragma mark - Overrides
- (NSString *)name
{
    return @"设备解绑";
}

- (NSString *)path
{
    return @"/uplussea/devices/v1/device/unbind";
}

- (nullable NSObject *)requestBody
{
    if (![self.deviceIDs isKindOfClass:[NSArray class]]) {

        return nil;
    }
    return @{ @"deviceIds" : self.deviceIDs };
}

#pragma mark - Public Methods
- (instancetype)initAPIWithDeviceIDs:(NSArray<NSString *> *)deviceIDs
{
    if (self = [super init]) {
        _deviceIDs = deviceIDs;
    }
    return self;
}
@end
