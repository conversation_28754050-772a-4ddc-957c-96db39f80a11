//
//  SEQueryDeviceListAPI.m
//  UserDomainAPIs
//
//  Created by <PERSON> on 2019/7/9.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEQueryDeviceListAPI.h"

@implementation SEQueryDeviceListAPI
#pragma mark - Overrides
- (NSString *)name
{
    return @"查询用户的设备列表";
}

- (NSString *)path
{
    return @"/uplussea/devices/v2/user/devices";
}

- (UPRequestMethod)method
{
    return UPRequestMethodGET;
}

- (NSUInteger)retryTimes
{
    return 2;
}

- (NSTimeInterval)retryDelay
{
    return 3.0;
}

@end
