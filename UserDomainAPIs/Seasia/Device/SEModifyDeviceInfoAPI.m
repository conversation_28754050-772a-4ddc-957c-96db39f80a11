//
//  SEModifyDeviceInfoAPI.m
//  UserDomainAPIs
//
//  Created by <PERSON> on 2019/7/11.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEModifyDeviceInfoAPI.h"

NSString *const kSEModifyDeviceInfoDeviceID = @"deviceId";
NSString *const kSEModifyDeviceInfoDeviceName = @"deviceName";
NSString *const kSEModifyDeviceInfoRoom = @"room";
NSString *const kSEModifyDeviceInfoFamilyId = @"familyId";

@interface SEModifyDeviceInfoAPI ()
@property (nonatomic, copy) NSString *deviceId;
@property (nonatomic, copy) NSString *deviceName;
@property (nonatomic, copy) NSString *room;
@property (nonatomic, copy) NSString *familyId;
@end

@implementation SEModifyDeviceInfoAPI
#pragma mark - Overrides
- (NSString *)name
{
    return @"设备信息修改";
}

- (NSString *)path
{
    return @"/uplussea/devices/v1/device/info/update";
}

- (NSObject *)requestBody
{
    NSMutableDictionary *parameters = [NSMutableDictionary dictionary];
    if ([self.deviceId isKindOfClass:[NSString class]]) {
        [parameters setValue:self.deviceId forKey:kSEModifyDeviceInfoDeviceID];
    }
    if ([self.deviceName isKindOfClass:[NSString class]]) {
        [parameters setValue:self.deviceName forKey:kSEModifyDeviceInfoDeviceName];
    }
    if ([self.room isKindOfClass:[NSString class]]) {
        [parameters setValue:self.room forKey:kSEModifyDeviceInfoRoom];
    }
    if ([self.familyId isKindOfClass:[NSString class]]) {
        [parameters setValue:self.familyId forKey:kSEModifyDeviceInfoFamilyId];
    }
    return parameters;
}

#pragma mark - Public Methods
- (instancetype)initAPIWithParameters:(NSDictionary<NSString *, NSString *> *)parameters
{
    if (![parameters isKindOfClass:[NSDictionary class]]) {
        return nil;
    }
    if (self = [super init]) {
        _deviceId = parameters[kSEModifyDeviceInfoDeviceID];
        _deviceName = parameters[kSEModifyDeviceInfoDeviceName];
        _room = parameters[kSEModifyDeviceInfoRoom];
        _familyId = parameters[kSEModifyDeviceInfoFamilyId];
    }
    return self;
}

@end
