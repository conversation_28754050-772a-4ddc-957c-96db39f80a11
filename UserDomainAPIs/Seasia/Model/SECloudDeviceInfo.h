//
//  SECloudDeviceInfo.h
//  UserDomainAPIs
//
//  Created by <PERSON> on 2019/7/9.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "SECloudDevicePermission.h"
NS_ASSUME_NONNULL_BEGIN

@interface SECloudDeviceBaseInfo : NSObject
@property (nonatomic, copy) NSString *deviceId;
@property (nonatomic, copy) NSString *deviceName;
@property (nonatomic, copy) NSString *deviceType;
@property (nonatomic, copy) NSString *wifiType;
@property (nonatomic, copy) NSString *familyId;
@property (nonatomic, assign) BOOL isOnline;
@property (nonatomic, strong) SECloudDevicePermission *permission;
@property (nonatomic, copy) NSString *deviceRole;
@property (nonatomic, copy) NSString *deviceRoleType;
/**
设备绑定时间
*/
@property (copy, nonatomic) NSString *bindTime;

@end

@interface SECloudDeviceExtendedInfo : NSObject
@property (nonatomic, copy) NSString *brand;
@property (nonatomic, copy) NSString *appTypeName;
@property (nonatomic, copy) NSString *room;
@property (nonatomic, copy) NSString *roomId;
@property (nonatomic, copy) NSString *model;
@property (nonatomic, copy) NSString *prodNo;
@property (nonatomic, copy) NSString *bindType;
@property (nonatomic, copy) NSString *imageAddr1;
@end

@interface SECloudDeviceInfo : NSObject
@property (nonatomic, strong) SECloudDeviceBaseInfo *baseInfo;
@property (nonatomic, strong) SECloudDeviceExtendedInfo *extendedInfo;
@end

NS_ASSUME_NONNULL_END
