//
//  UPGetGroupDevicesApi.m
//  UserDomainAPIs
//
//  Created by 冉东军 on 2022/11/4.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPGetGroupDevicesApi.h"

@interface UPGetGroupDevicesApi ()
@property (nonatomic, copy) NSString *familyId;
@property (nonatomic, assign) BOOL filterFlag;
@end

@implementation UPGetGroupDevicesApi
- (instancetype)initWithfamilyId:(NSString *)familyId filterFlag:(BOOL)filterFlag
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.filterFlag = filterFlag;
    }
    return self;
}
- (NSString *)name
{
    return @"获取组设备列表";
}

- (UPRequestMethod)method
{
    return UPRequestMethodGET;
}

- (NSString *)path
{
    return [NSString stringWithFormat:@"/emuplus/device/v1/family/group/devices?familyId=%@&filterFlag=%@", self.familyId, self.filterFlag ? @"true" : @"false"];
}

- (NSUInteger)retryTimes
{
    return 1;
}

- (NSTimeInterval)retryDelay
{
    return 3.0;
}

- (NSTimeInterval)retryStep
{
    return 4.0;
}

- (nullable NSObject *)requestBody
{
    return nil;
}

@end
