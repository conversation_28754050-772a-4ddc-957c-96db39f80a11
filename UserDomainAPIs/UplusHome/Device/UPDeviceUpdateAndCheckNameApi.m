//
//  UPDeviceUpdateAndCheckNameApi.m
//  UserDomainAPIs
//
//  Created by whenwe on 2023/9/20.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPDeviceUpdateAndCheckNameApi.h"

@interface UPDeviceUpdateAndCheckNameApi ()

@property (nonatomic, copy) NSString *deviceId;
@property (nonatomic, copy) NSString *deviceName;
@property (nonatomic, copy) NSString *familyId;
@property (nonatomic, copy) NSString *oldName;
@property (nonatomic, copy) NSString *type;
@property (nonatomic, assign) BOOL checkLevel;

@end

@implementation UPDeviceUpdateAndCheckNameApi

- (instancetype)initWithDeviceId:(NSString *)deviceId oldName:(NSString *)oldName newName:(NSString *)newName familyId:(NSString *)familyId type:(NSString *)type checkLevel:(BOOL)checkLevel
{
    self = [super init];
    if (self) {
        self.deviceId = deviceId;
        self.oldName = oldName;
        self.deviceName = newName;
        self.familyId = familyId;
        self.type = type;
        self.checkLevel = checkLevel;
    }
    return self;
}

- (NSString *)name
{
    return @"修改设备名称v4";
}

- (NSString *)path
{
    return @"/api-gw/wisdomdevice/device/v4/modify/name";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSObject *)requestBody
{
    return @{ @"deviceId" : self.deviceId,
              @"deviceName" : self.deviceName,
              @"checkLevel2" : @(self.checkLevel),
              @"familyId" : self.familyId,
              @"oldDeviceName" : self.oldName,
              @"type" : self.type };
}

@end
