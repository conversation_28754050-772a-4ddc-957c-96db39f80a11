//
//  UpDeviceUpdateNameApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/2/25.
//

#import "UPDeviceUpdateNameApi.h"
@interface UPDeviceUpdateNameApi ()
@property (strong, nonatomic) NSString *deviceId;
@property (strong, nonatomic) NSString *oldDeviceName;
@property (strong, nonatomic) NSString *deviceName;
@property (copy, nonatomic) NSString *familyId;
@property (copy, nonatomic) NSString *deviceNetType;
@property (copy, nonatomic) NSString *prodNo;
@end

@implementation UPDeviceUpdateNameApi
- (instancetype)initWithDeviceId:(NSString *)deviceId oldName:(NSString *)oldName newName:(NSString *)newName familyId:(NSString *)familyId deviceNetType:(NSString *)deviceNetType prodNo:(NSString *)prodNo
{
    if (self = [super init]) {
        self.deviceId = deviceId;
        self.oldDeviceName = oldName;
        self.deviceName = newName;
        self.familyId = familyId;
        self.deviceNetType = deviceNetType;
        self.prodNo = prodNo;
    }
    return self;
}
- (NSString *)name
{
    return @"更新设备名称";
}
- (NSString *)path
{
    return @"/api-gw/wisdomdevice/device/v3/modify/name";
}
- (NSObject *)requestBody
{
    return @{
        @"deviceId" : self.deviceId,
        @"newName" : self.deviceName,
        @"oldName" : self.oldDeviceName,
        @"familyId" : self.familyId,
        @"deviceNetType" : self.deviceNetType,
        @"prodNo" : self.prodNo
    };
}
@end
