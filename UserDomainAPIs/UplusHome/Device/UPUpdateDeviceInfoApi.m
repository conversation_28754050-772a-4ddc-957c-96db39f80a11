//
//  UpUpdateDeviceInfoApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/4.
//

#import "UPUpdateDeviceInfoApi.h"
@interface UPUpdateDeviceInfoApi ()

@property (nonatomic, strong) NSDictionary<NSString *, NSString *> *deviceInfo;
@end

@implementation UPUpdateDeviceInfoApi
- (instancetype)initWithDeviceInfo:(NSDictionary<NSString *, NSString *> *)deviceInfo
{
    if (self = [super init]) {
        self.deviceInfo = deviceInfo;
    }
    return self;
}

- (NSString *)name
{
    return @"查询设备详细信息";
}

- (NSString *)path
{
    return @"/emuplus/device/v5/update";
}

- (NSUInteger)retryTimes
{
    return 2;
}

- (NSTimeInterval)retryDelay
{
    return 3.0;
}

- (NSObject *)requestBody
{
    return self.deviceInfo;
}
@end
