//
//  UpDeviceUpdateNameApi.h
//  AFNetworking
//
//  Created by 振兴郑 on 2019/2/25.
//

#import "ZJServerAPIBase.h"
NS_ASSUME_NONNULL_BEGIN

@interface UPDeviceUpdateNameApi : ZJServerAPIBase
- (instancetype)initWithDeviceId:(NSString *)deviceId oldName:(NSString *)oldName newName:(NSString *)newName familyId:(NSString *)familyId deviceNetType:(NSString *)deviceNetType prodNo:(NSString *)prodNo;
@end

NS_ASSUME_NONNULL_END
