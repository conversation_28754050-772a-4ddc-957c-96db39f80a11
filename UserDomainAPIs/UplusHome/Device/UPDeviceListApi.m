//
//  UpDeviceListApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/2/20.
//

#import "UPDeviceListApi.h"

static NSString *const kfamilyId = @"familyId";
static NSString *const kQueryAll = @"ALL";

@interface UPDeviceListApi ()
@property (strong, nonatomic) NSString *familyId;
@end

@implementation UPDeviceListApi
- (instancetype)initWithfamilyId:(NSString *_Nullable)familyId
{
    if (self = [super init]) {
        self.familyId = familyId ?: kQueryAll;
    }
    return self;
}
- (NSString *)name
{
    return @"查询设备列表";
}
- (UPRequestMethod)method
{
    return UPRequestMethodGET;
}

- (NSString *)path
{
    return [NSString stringWithFormat:@"/api-gw/wisdomdevice/device/v10/family/devices?familyId=%@", self.familyId];
}

- (NSUInteger)retryTimes
{
    return 1;
}

- (NSTimeInterval)retryDelay
{
    return 3.0;
}

- (NSTimeInterval)retryStep
{
    return 4.0;
}

- (nullable NSObject *)requestBody
{
    return nil;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"grayMode"] = [self getGrayMode] ? @"true" : @"false";
    return headersDict;
}
@end
