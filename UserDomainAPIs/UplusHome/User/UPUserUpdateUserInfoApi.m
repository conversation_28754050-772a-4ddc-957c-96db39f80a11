//
//  UPUserUpdateUserInfoApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2018/11/29.
//

#import "UPUserUpdateUserInfoApi.h"

@interface UPUserUpdateUserInfoApi ()
@property (nonatomic, strong) NSDictionary *infoDic;
@end

@implementation UPUserUpdateUserInfoApi
- (instancetype)initWithInfo:(NSDictionary *)info
{
    if (self = [super init]) {
        _infoDic = info;
    }
    return self;
}

- (NSString *)name
{
    return @"更新用户信息";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSString *)path
{
    return @"/v2/haier/md/userinfo";
}

- (NSObject *)requestBody
{
    return self.infoDic;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Content-Type" : @"application/json;charset=UTF-8",
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken]
    };
}

@end
