//
//  UPUserUpdatePwdApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2018/11/29.
//

#import "UPUserUpdatePwdApi.h"

@interface UPUserUpdatePwdApi ()
@property (nonatomic, copy) NSString *oldPassword;
@property (nonatomic, copy) NSString *nPassword;

@end

@implementation UPUserUpdatePwdApi
- (instancetype)initWithold_password:(NSString *)old_password nw_password:(NSString *)nw_password
{
    if (self = [super init]) {
        _oldPassword = old_password;
        _nPassword = nw_password;
    }
    return self;
}

- (NSString *)name
{
    return @"修改密码";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPUT;
}

- (NSString *)path
{
    return @"/v1/users/change-password";
}

- (NSObject *)requestBody
{
    return @{
        @"old_password" : self.oldPassword,
        @"new_password" : self.nPassword
    };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Content-Type" : @"application/json;charset=UTF-8",
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken]
    };
}
@end
