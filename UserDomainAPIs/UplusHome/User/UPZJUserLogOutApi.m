//
//  UPZJUserLogOutApi.m
//  UserDomainAPIs
//
//  Created by 冉东军 on 2021/2/20.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPZJUserLogOutApi.h"

@interface UPZJUserLogOutApi ()
@property (nonatomic, copy) NSString *uhome_token;
@end

@implementation UPZJUserLogOutApi
- (instancetype)initWithuhome_token:(NSString *)uhome_token
{
    if (self = [super init]) {
        self.uhome_token = uhome_token;
    }
    return self;
}

- (NSString *)name
{
    return @"用户退出登录";
}

- (NSString *)path
{
    return @"/api-gw/oauthserver/account/v1/logout";
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"accountToken"] = [UPNetworkSettings sharedSettings].accessToken;
    headersDict[@"accessToken"] = self.uhome_token;
    headersDict[@"uniqueDeviceId"] = [UPNetworkSettings sharedSettings].clientID ?: @"";
    return headersDict;
}

- (NSObject *)requestBody
{
    return @{};
}

@end
