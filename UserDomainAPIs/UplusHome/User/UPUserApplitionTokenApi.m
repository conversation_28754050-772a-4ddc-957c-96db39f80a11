//
//  UPUserApplitionTokenApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2018/11/19.
//

#import "UPUserApplitionTokenApi.h"

@implementation UPUserApplitionTokenApi

- (NSString *)name
{
    return @"获取应用级Token";
}

- (NSString *)path
{
    return [NSString stringWithFormat:@"/oauth/token?client_id=%@&client_secret=%@&grant_type=%@", [UserCenterAPIBase clientID], [UserCenterAPIBase clientSecret], [UserCenterAPIBase clientCredentials]];
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{ @"Content-Type" : @"application/x-www-form-urlencoded" };
}
@end
