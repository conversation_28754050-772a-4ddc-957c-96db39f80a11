//
//  UPUserLoginLogsApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/8/8.
//

#import "UPUserLoginLogsApi.h"

@interface UPUserLoginLogsApi ()
@property (nonatomic, assign) NSInteger pageNo;
@property (nonatomic, assign) NSInteger pageSize;
@end

@implementation UPUserLoginLogsApi
- (instancetype)initWithPageNo:(NSInteger)pageNo pageSize:(NSInteger)pageSize
{
    if (self = [super init]) {
        self.pageNo = pageNo;
        self.pageSize = pageSize;
    }
    return self;
}

- (NSString *)name
{
    return @"查询用户登录日志（分页查询）";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSString *)path
{
    return @"/haier/v1/statistic/list-user-login-logs";
}

- (NSObject *)requestBody
{
    return @{
        @"pageNo" : @(self.pageNo),
        @"pageSize" : @(self.pageSize)
    };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken]
    };
}
@end
