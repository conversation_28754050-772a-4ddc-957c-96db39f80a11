//
//  UPConfirmDeviceSharingRelationApi.m
//  UserDomainAPIs
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/3/10.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPConfirmDeviceSharingRelationApi.h"
@interface UPConfirmDeviceSharingRelationApi ()
@property (nonatomic, copy) NSString *shareUuid;
@end
@implementation UPConfirmDeviceSharingRelationApi
- (instancetype)initWithShareUuid:(NSString *)shareUuid
{
    if (self = [super init]) {
        self.shareUuid = shareUuid;
    }
    return self;
}
- (NSString *)name
{
    return @"接收方点击确认";
}

- (NSString *)path
{
    return @"/api-gw/wisdomdevice/device/share/relation/confirm";
}
- (nullable NSObject *)requestBody
{
    return @{ @"shareUuid" : self.shareUuid,
              @"shareType" : @"1" };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"grayMode"] = [self getGrayMode] ? @"true" : @"false";
    return headersDict;
}
@end
