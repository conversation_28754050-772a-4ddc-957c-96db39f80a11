//
//  UPUserAddressApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/8.
//

#import "UPUserAddressApi.h"

@implementation UPUserAddressApi

- (NSString *)name
{
    return @"查询用户地址";
}

- (UPRequestMethod)method
{
    return UPRequestMethodGET;
}

- (NSString *)path
{
    return @"/haier/v2/userAddresses";
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken]
    };
}
- (NSUInteger)retryTimes
{
    return 2;
}
- (NSTimeInterval)retryDelay
{
    return 3.0;
}

@end
