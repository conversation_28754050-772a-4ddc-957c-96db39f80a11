//
//  UPUserDeleteAddressApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/17.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPUserDeleteAddressApi.h"

@interface UPUserDeleteAddressApi ()
@property (nonatomic, strong) NSString *addressId;
@end

@implementation UPUserDeleteAddressApi
- (instancetype)initWithAddressId:(NSString *)addressId
{
    if (self = [super init]) {
        self.addressId = addressId;
    }
    return self;
}

- (NSString *)name
{
    return @"删除用户地址";
}

- (UPRequestMethod)method
{
    return UPRequestMethodDELETE;
}

- (NSString *)path
{
    return [NSString stringWithFormat:@"/haier/v1/userAddress/%@", self.addressId];
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken]
    };
}
@end
