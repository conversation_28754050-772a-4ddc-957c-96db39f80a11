//
//  UPUserRefreshTokenApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2018/11/29.
//

#import "UPUserRefreshTokenApi.h"

@interface UPUserRefreshTokenApi ()
@property (nonatomic, copy) NSString *refreshToken;
@end

@implementation UPUserRefreshTokenApi
- (instancetype)initWithrefresh_token:(NSString *)refresh_token
{
    if (self = [super init]) {
        _refreshToken = refresh_token;
    }
    return self;
}

- (NSString *)name
{
    return @"刷新token";
}

- (NSString *)path
{
    NSString *grantType = @"refresh_token";
    NSString *tokenTypeUhome = @"type_uhome_common_token";
    NSString *clientID = [UPNetworkSettings sharedSettings].clientID;
    NSString *appID = [UPNetworkSettings sharedSettings].appID;

    return [NSString stringWithFormat:@"/oauth/token?client_id=%@&client_secret=%@&grant_type=%@&refresh_token=%@&type_uhome=%@&uhome_client_id=%@&uhome_app_id=%@&uhome_sign=%@", self.userCenterClientID, self.userCenterClientSecret, grantType, self.refreshToken, tokenTypeUhome, clientID, appID, self.userCenterUhomeSign];
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{

    return @{

        @"Content-Type" : @"application/x-www-form-urlencoded"
    };
}
- (NSTimeInterval)retryDelay
{
    return 3.0;
}
- (NSUInteger)retryTimes
{
    return 2;
}
@end
