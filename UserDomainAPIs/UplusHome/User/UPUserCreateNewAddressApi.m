//
//  UPUserCreateNewAddressApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/16.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPUserCreateNewAddressApi.h"
#import <MJExtension/MJExtension.h>

@interface UPUserCreateNewAddressApi ()
@property (nonatomic, strong) NSDictionary *addressInfo;

@end

@implementation UPUserCreateNewAddressApi
- (instancetype)initWithAddressInfo:(NSDictionary *)addressInfo
{
    if (self = [super init]) {
        self.addressInfo = addressInfo;
    }
    return self;
}

- (NSString *)name
{
    return @"用户创建新地址";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSString *)path
{
    return @"/haier/v2/userAddress";
}

- (NSObject *)requestBody
{
    [self.addressInfo setValue:self.userCenterClientID forKey:@"source"];
    return self.addressInfo;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken],
        @"Content-Type" : @"application/json;charset=UTF-8"
    };
}
@end
