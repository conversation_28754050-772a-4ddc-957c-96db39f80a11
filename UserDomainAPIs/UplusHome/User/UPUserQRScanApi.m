//
//  UPUserQRScanApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/29.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPUserQRScanApi.h"

@interface UPUserQRScanApi ()
@property (nonatomic, strong) NSString *uuid;
@end
@implementation UPUserQRScanApi
- (instancetype)initWithUUID:(NSString *)uuid
{
    if (self = [super init]) {
        self.uuid = uuid;
    }
    return self;
}

- (NSString *)name
{
    return @"确认扫码登录";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSString *)path
{
    return @"/v1/qr-login/scan";
}

- (NSObject *)requestBody
{
    return @{ @"token" : self.uuid ?: @"" };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Content-Type" : @"application/json;charset=UTF-8",
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken]
    };
}
@end
