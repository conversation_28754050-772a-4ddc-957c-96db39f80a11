//
//  UPUserQRConfirmLoginApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/17.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPUserQRConfirmLoginApi.h"

@interface UPUserQRConfirmLoginApi ()
@property (nonatomic, strong) NSString *uuid;
@end

@implementation UPUserQRConfirmLoginApi
- (instancetype)initWithUUID:(NSString *)uuid
{
    if (self = [super init]) {
        self.uuid = uuid;
    }
    return self;
}

- (NSString *)name
{
    return @"确认登陆接口";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSString *)path
{
    return @"/v1/qr-login/confirm";
}

- (NSObject *)requestBody
{
    return @{ @"token" : self.uuid ?: @"" };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Content-Type" : @"application/json;charset=UTF-8",
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken]
    };
}
@end
