//
//  UPUserQRloginPollApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/17.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPUserQRloginPollApi.h"

@interface UPUserQRloginPollApi ()
@property (nonatomic, strong) NSString *uuid;
@end

@implementation UPUserQRloginPollApi
- (instancetype)initWithUUID:(NSString *)uuid
{
    if (self = [super init]) {
        self.uuid = uuid;
    }
    return self;
}

- (NSString *)name
{
    return @"轮询用户中心此UUID是否已经变为登录状态";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSString *)path
{
    return @"/v1/qr-login/poll";
}

- (NSObject *)requestBody
{
    return @{ @"token" : self.uuid ?: @"" };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Content-Type" : @"application/json;charset=UTF-8",
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken]
    };
}

@end
