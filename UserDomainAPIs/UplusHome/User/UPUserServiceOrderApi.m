//
//  UPUserServiceOrderApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/8/26.
//

#import "UPUserServiceOrderApi.h"

@interface UPUserServiceOrderApi ()
@property (nonatomic, strong) NSString *phoneNumber;
@end

@implementation UPUserServiceOrderApi
- (instancetype)initWithPhoneNumber:(NSString *)phoneNumber
{
    if (self = [super init]) {
        self.phoneNumber = phoneNumber;
    }
    return self;
}

- (NSString *)name
{
    return @"获取用户服务单";
}

- (NSString *)path
{
    return [NSString stringWithFormat:@"/haier/v1/statistic/serviceOrderByphoneNum/%@", self.phoneNumber];
}

- (UPRequestMethod)method
{
    return UPRequestMethodGET;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Content-Type" : @"application/x-www-form-urlencoded",
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken]

    };
}
@end
