//
//  UPZJRefreshTokenApi.m
//  upuserdomain
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/6/17.
//

#import "UPZJRefreshTokenApi.h"

@interface UPZJRefreshTokenApi ()
@property (nonatomic, copy) NSString *refreshToken;
@end

@implementation UPZJRefreshTokenApi
- (instancetype)initWithrefresh_token:(NSString *)refresh_token
{
    if (self = [super init]) {
        _refreshToken = refresh_token;
    }
    return self;
}

- (NSString *)name
{
    return @"刷新token";
}
- (NSString *)path
{
    return @"/api-gw/oauthserver/account/v1/refreshToken";
}
- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *dic = [UPCommonServerHeader uwsHeaderWithUrlString:self.path body:self.requestBody].mutableCopy;
    [dic removeObjectForKey:@"accessToken"];
    dic[@"uniqueDeviceId"] = [UPNetworkSettings sharedSettings].clientID ?: @"";
    return dic;
}
- (NSObject *)requestBody
{
    return @{
        @"refreshToken" : _refreshToken ?: @"",
        @"phoneBrand" : @"",
        @"phoneType" : [UPNetworkSettings sharedSettings].phoneType ?: @""
    };
}
- (NSTimeInterval)retryDelay
{
    return 3.0;
}
- (NSUInteger)retryTimes
{
    return 2;
}
@end
