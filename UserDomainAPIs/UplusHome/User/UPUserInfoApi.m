//
//  UPUserInfoApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2018/11/29.
//

#import "UPUserInfoApi.h"

@interface UPUserInfoApi ()
@property (nonatomic, strong) NSString *uhome_user_id;
@end

@implementation UPUserInfoApi
- (instancetype)initWithUhome_user_id:(NSString *)uhome_user_id
{
    if (self = [super init]) {
        self.uhome_user_id = uhome_user_id;
    }
    return self;
}

- (NSString *)name
{
    return @"查询用户信息";
}

- (UPRequestMethod)method
{
    return UPRequestMethodGET;
}

- (NSString *)path
{
    return @"/v2/haier/userinfo";
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken]
    };
}
- (NSUInteger)retryTimes
{
    return 1;
}
- (NSTimeInterval)retryDelay
{
    return 3.0;
}

- (NSTimeInterval)retryStep
{
    return 4.0;
}
@end
