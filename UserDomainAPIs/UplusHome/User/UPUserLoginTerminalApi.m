//
//  UPUserLoginTerminalApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/17.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPUserLoginTerminalApi.h"

@interface UPUserLoginTerminalApi ()
@property (nonatomic, strong) NSString *uhome_user_id;
@end

@implementation UPUserLoginTerminalApi
- (instancetype)initWithUhome_user_id:(NSString *)uhome_user_id
{
    if (self = [super init]) {
        self.uhome_user_id = uhome_user_id;
    }
    return self;
}

- (NSString *)name
{
    return @"查询用户登录终端";
}

- (UPRequestMethod)method
{
    return UPRequestMethodGET;
}

- (NSString *)path
{
    return @"/haier/v1/statistic/newest-user-logs";
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken]
    };
}
- (NSUInteger)retryTimes
{
    return 2;
}
- (NSTimeInterval)retryDelay
{
    return 3.0;
}
@end
