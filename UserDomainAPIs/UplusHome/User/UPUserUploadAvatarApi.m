//
//  UPUserUploadAvatarApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/9/26.
//

#import "UPUserUploadAvatarApi.h"
#import "AFURLRequestSerialization.h"

@interface UPUserUploadAvatarApi ()
@property (nonatomic, strong) UIImage *image;
@end

@implementation UPUserUploadAvatarApi
- (instancetype)initWithImage:(UIImage *)image
{
    if (self = [super init]) {
        self.image = image ?: [UIImage imageNamed:@""];
    }
    return self;
}

- (NSString *)name
{
    return @"用户中心头像上传Api";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPUT;
}

- (NSString *)path
{
    return @"/v2/user/avatar/upload";
}

- (UPRequestConstructingBodyBlock)constructingBodyBlock
{
    return ^(id<AFMultipartFormData> _Nonnull formData) {

      NSData *data = [NSData data];
      if (self.image) {
          data = UIImageJPEGRepresentation(self.image, 0.8);
      }
      NSString *name = @"file";
      NSString *formKey = @"file";
      NSString *type = @"image/jpeg";
      [formData appendPartWithFileData:data name:formKey fileName:name mimeType:type];
    };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Content-Type" : @"multipart/form-data",
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken]

    };
}
@end
