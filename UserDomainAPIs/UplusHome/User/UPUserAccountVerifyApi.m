//
//  UPUserAccountVerifyApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2018/11/29.
//

#import "UPUserAccountVerifyApi.h"

@interface UPUserAccountVerifyApi ()
@property (nonatomic, strong) NSString *identifier;
@property (nonatomic, strong) NSString *applicationToken;
@end


@implementation UPUserAccountVerifyApi
- (instancetype)initWithidentifier:(NSString *)identifier applicationToken:(NSString *)applicationToken
{
    if (self = [super init]) {
        _identifier = identifier;
        _applicationToken = applicationToken;
    }
    return self;
}

- (NSString *)name
{
    return @"验证号码是否可用";
}

- (NSString *)path
{
    return [NSString stringWithFormat:@"/v1/users/identifier-available?identifier=%@", self.identifier];
}

- (UPRequestMethod)method
{
    return UPRequestMethodGET;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Content-Type" : @"application/x-www-form-urlencoded",
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken]

    };
}

@end
