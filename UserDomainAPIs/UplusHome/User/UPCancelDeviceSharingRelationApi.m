//
//  UPCancelDeviceSharingRelationApi.m
//  UserDomainAPIs
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/3/10.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPCancelDeviceSharingRelationApi.h"

@interface UPCancelDeviceSharingRelationApi ()
@property (nonatomic, strong) NSArray<NSString *> *shareUuids;
@end
@implementation UPCancelDeviceSharingRelationApi
- (instancetype)initWithShareUuids:(NSArray<NSString *> *)shareUuids
{
    if (self = [super init]) {
        self.shareUuids = shareUuids;
    }
    return self;
}
- (NSString *)name
{
    return @"设备管理者取消分享/接收方删除分享关系接口";
}

- (NSString *)path
{
    return @"/api-gw/wisdomdevice/device/share/relation/cancel";
}
- (nullable NSObject *)requestBody
{
    return @{ @"shareUuids" : self.shareUuids };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"grayMode"] = [self getGrayMode] ? @"true" : @"false";
    return headersDict;
}

@end
