//
//  UpEditAggregationSwitchApi.m
//  UserDomainAPIs
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/25.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEditAggregationSwitchApi.h"

@interface UpEditAggregationSwitchApi ()

@property (nonatomic, copy) NSString *source;

@property (nonatomic, strong) NSArray<NSDictionary *> *familyAgg;

@end

@implementation UpEditAggregationSwitchApi

- (instancetype)initWithSource:(NSString *)source
                     familyAgg:(NSArray<NSDictionary *> *)familyAgg
{
    if (self = [super init]) {
        self.source = source;
        self.familyAgg = familyAgg;
    }
    return self;
}

- (NSString *)name
{
    return @"打开或关闭聚合开关";
}

- (NSString *)path
{
    return @"/api-gw/wisdomdevice/device/v4/aggregation/switch/operate";
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"grayMode"] = [self getGrayMode] ? @"true" : @"false";
    return headersDict;
}

- (NSObject *)requestBody
{
    return @{
        @"source" : self.source,
        @"familyAgg" : self.familyAgg
    };
}

@end
