//
//  UPUserEditAddressApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/17.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPUserEditAddressApi.h"

@interface UPUserEditAddressApi ()
@property (nonatomic, strong) NSDictionary *addressInfo;

@end

@implementation UPUserEditAddressApi
- (instancetype)initWithAddressInfo:(NSDictionary *)addressInfo
{
    if (self = [super init]) {
        self.addressInfo = addressInfo;
    }
    return self;
}

- (NSString *)name
{
    return @"编辑用户地址";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPUT;
}

- (NSString *)path
{
    return [NSString stringWithFormat:@"/haier/v2/userAddress/%@", self.addressInfo[@"id"]];
}

- (NSObject *)requestBody
{
    return self.addressInfo;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{

    return @{
        @"Authorization" : [NSString stringWithFormat:@"Bearer %@", [UPNetworkSettings sharedSettings].accessToken],
        @"Content-Type" : @"application/json;charset=UTF-8"
    };
}
@end
