//
//  ZJServerAPIBase.m
//  UserDomainAPIs
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/5.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ZJServerAPIBase.h"
#import "UserCenterAPIBase.h"
@implementation ZJServerAPIBase
- (NSString *)baseURL
{
    if ([UserCenterAPIBase getEnv] == UserCenterAPIEnvProduction) {
        return @"https://zj.haier.net";
    }
    else if ([UserCenterAPIBase getEnv] == UserCenterAPIEnvAcceptance) {
        return @"https://zj-yanshou.haier.net";
    }
    return @"https://zj.haier.net";
}
- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSTimeInterval)timeoutInterval
{
    return 15.0;
}

- (NSUInteger)retryTimes
{
    return 0;
}

- (NSTimeInterval)retryDelay
{
    return 0.0;
}
- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return [UPCommonServerHeader uwsHeaderWithUrlString:self.path body:self.requestBody];
}

- (BOOL)getGrayMode
{
    return [UserCenterAPIBase getGrayMode];
}

@end
