//
//  APPServerAPIBase.m
//  UserDomainAPIs
//
//  Created by <PERSON> on 2020/4/13.
//  Copyright © 2020 振兴郑. All rights reserved.
//

#import "APPServerAPIBase.h"
#import "UserCenterAPIBase.h"
#import <upnetwork/UPCommonServerHeader.h>
@implementation APPServerAPIBase

- (NSString *)baseURL
{
    if ([UserCenterAPIBase getEnv] == UserCenterAPIEnvProduction) {
        return @"https://zj.haier.net";
    }
    else if ([UserCenterAPIBase getEnv] == UserCenterAPIEnvAcceptance) {
        return @"https://zj-yanshou.haier.net";
    }
    return @"https://zj.haier.net";
}
- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSTimeInterval)timeoutInterval
{
    return 15.0;
}

- (NSUInteger)retryTimes
{
    return 0;
}

- (NSTimeInterval)retryDelay
{
    return 0.0;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return [UPCommonServerHeader signHeaderWithBody:self.requestBody];
}
@end
