//
//  UserCenterAPIBase.m
//  UserDomainAPIs
//
//  Created by <PERSON> on 2020/4/13.
//  Copyright © 2020 振兴郑. All rights reserved.
//

#import "UserCenterAPIBase.h"
#import <upnetwork/NSString+XHash.h>

NSString *const UserCenterAPIURL_Development = @"https://kaccount.haier.com";
NSString *const UserCenterAPIURL_Acceptance = @"https://testaccount.haier.com";
NSString *const UserCenterAPIURL_Production = @"https://account-api.haier.net";
NSString *const UserCenterAPIClientSecret = @"f2idn67sujfjC1";

static UserCenterAPIEnv UPUserDomain_UserCenterEnvironment = UserCenterAPIEnvProduction;
static BOOL UPUserdomain_GrayMode = NO;

@implementation UserCenterAPIBase
#pragma mark - Property Methods
- (NSString *)userCenterClientID
{
    return [[self class] clientID];
}

- (NSString *)userCenterClientSecret
{
    return [[self class] clientSecret];
}

- (NSString *)userCenterUhomeSign
{
    NSString *clientID = [UPNetworkSettings sharedSettings].clientID;
    NSString *appID = [UPNetworkSettings sharedSettings].appID;
    NSString *appKey = [UPNetworkSettings sharedSettings].appKey;
    NSString *uhomeSign = [NSString stringWithFormat:@"%@%@%@", appID, appKey, clientID].sha256String;
    return uhomeSign;
}

#pragma mark - Overrides Methods
- (NSString *)baseURL
{
    if (UPUserDomain_UserCenterEnvironment == UserCenterAPIEnvDevelopment) {
        return UserCenterAPIURL_Development;
    }
    if (UPUserDomain_UserCenterEnvironment == UserCenterAPIEnvAcceptance) {
        return UserCenterAPIURL_Acceptance;
    }
    return UserCenterAPIURL_Production;
}

- (NSTimeInterval)timeoutInterval
{
    return 15.0;
}

- (NSUInteger)retryTimes
{
    return 0;
}

- (NSTimeInterval)retryDelay
{
    return 0.0;
}

- (UPRequestSerializerType)requestSerializerType
{
    return UPRequestSerializerTypeJSON;
}

- (UPResponseSerializerType)responseSerializerType
{
    return UPResponseSerializerTypeJSON;
}

#pragma mark - Public Methods
+ (void)setUserCenterEnvironment:(UserCenterAPIEnv)env
{
    UPUserDomain_UserCenterEnvironment = env;
}

+ (NSString *)clientID
{
    return @"uplusappios";
}

+ (NSString *)clientSecret
{
    return UserCenterAPIClientSecret;
}

+ (NSString *)clientCredentials
{
    return @"client_credentials";
}
+ (UserCenterAPIEnv)getEnv
{
    return UPUserDomain_UserCenterEnvironment;
}

+ (void)setGrayMode:(BOOL)isGray
{
    UPUserdomain_GrayMode = isGray;
}

+ (BOOL)getGrayMode
{
    return UPUserdomain_GrayMode;
}
@end
