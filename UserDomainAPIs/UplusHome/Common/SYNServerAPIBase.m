//
//  SYNServerAPIBase.m
//  UserDomainAPIs
//
//  Created by 韩波标 on 2021/5/21.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SYNServerAPIBase.h"
#import "UserCenterAPIBase.h"
#import <upnetwork/UPCommonServerHeader.h>

@implementation SYNServerAPIBase

- (NSString *)baseURL
{
    if ([UserCenterAPIBase getEnv] == UserCenterAPIEnvProduction) {
        return @"https://sybird.haier.net";
    }
    else if ([UserCenterAPIBase getEnv] == UserCenterAPIEnvAcceptance) {
        return @"https://syntest.haier.net";
    }
    return @"https://sybird.haier.net";
}
- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSTimeInterval)timeoutInterval
{
    return 15.0;
}

- (NSUInteger)retryTimes
{
    return 0;
}

- (NSTimeInterval)retryDelay
{
    return 0.0;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return [UPCommonServerHeader uwsHeaderWithUrlString:self.path body:self.requestBody];
}

@end
