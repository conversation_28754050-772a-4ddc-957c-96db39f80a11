//
//  UserCenterAPIBase.h
//  UserDomainAPIs
//
//  Created by <PERSON> on 2020/4/13.
//  Copyright © 2020 振兴郑. All rights reserved.
//

#import <upnetwork/UPNetwork.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, UserCenterAPIEnv) {
    UserCenterAPIEnvDevelopment,
    UserCenterAPIEnvAcceptance,
    UserCenterAPIEnvProduction
};

@interface UserCenterAPIBase : UPRequest
@property (nonatomic, copy) NSString *userCenterClientID;
@property (nonatomic, copy) NSString *userCenterClientSecret;
@property (nonatomic, copy) NSString *userCenterUhomeSign;

+ (void)setUserCenterEnvironment:(UserCenterAPIEnv)env;
+ (NSString *)clientID;
+ (NSString *)clientSecret;
+ (NSString *)clientCredentials;
+ (UserCenterAPIEnv)getEnv;

/// 设置灰度模式
/// @param isGray 是否开启灰度
+ (void)setGrayMode:(BOOL)isGray;
+ (BOOL)getGrayMode;
@end

NS_ASSUME_NONNULL_END
