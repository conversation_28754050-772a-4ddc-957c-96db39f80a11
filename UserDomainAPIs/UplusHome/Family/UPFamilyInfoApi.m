//
//  UpFamilyMemberApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/2/19.
//

#import "UPFamilyInfoApi.h"
@interface UPFamilyInfoApi ()
@property (strong, nonatomic) NSString *familyId;
@property (nonatomic, strong) NSString *uhome_user_id;
@end

@implementation UPFamilyInfoApi
- (instancetype)initWithFamilyId:(NSString *)familyId
{
    if (self = [super init]) {
        self.familyId = familyId;
    }
    return self;
}
- (NSString *)name
{
    return @"获取家庭详细信息";
}

- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/refactor/v1/family/familyInfo";
}

- (NSUInteger)retryTimes
{
    return 1;
}

- (NSTimeInterval)retryDelay
{
    return 3.0;
}

- (NSTimeInterval)retryStep
{
    return 4.0;
}

- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @""
    };
}

@end
