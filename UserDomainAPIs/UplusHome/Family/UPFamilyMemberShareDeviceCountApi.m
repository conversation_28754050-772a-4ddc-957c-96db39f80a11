//
//  UpFamilyMemberShareDeviceCountApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/5/14.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPFamilyMemberShareDeviceCountApi.h"
@interface UPFamilyMemberShareDeviceCountApi ()
@property (strong, nonatomic) NSString *familyId;
@end

@implementation UPFamilyMemberShareDeviceCountApi
- (instancetype)initWithFamilyId:(NSString *)familyId
{
    if (self = [super init]) {
        self.familyId = familyId;
    }
    return self;
}
- (NSString *)name
{
    return @"获取家庭相信信息";
}
- (NSString *)path
{
    return @"/emuplus/family/v1/devowner/share/count";
}
- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @""
    };
}
@end
