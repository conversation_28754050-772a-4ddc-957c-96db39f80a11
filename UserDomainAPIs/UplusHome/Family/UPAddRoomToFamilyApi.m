//
//  UpAddRoomToFamilyApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/3/15.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPAddRoomToFamilyApi.h"
@interface UPAddRoomToFamilyApi ()
@property (nonatomic, strong) NSString *familyId;
@property (nonatomic, strong) NSString *roomName;
@property (nonatomic, strong) NSString *roomClass;
@property (nonatomic, strong) NSString *roomLabel;
@property (nonatomic, strong) NSString *roomLogo;
@property (nonatomic, strong) NSString *roomPicture;
@property (nonatomic, copy) NSString *floorId;
@end

@implementation UPAddRoomToFamilyApi
- (instancetype)initWithfamilyId:(NSString *)familyId roomName:(NSString *)roomName roomClass:(NSString *)roomClass roomLabel:(NSString *)roomLabel roomLogo:(NSString *)roomLogo roomPicture:(NSString *)roomPicture floorId:(NSString *)floorId
{
    if (self = [super init]) {

        self.familyId = familyId;
        self.roomName = roomName;
        self.roomClass = roomClass;
        self.roomLabel = roomLabel;
        self.roomLogo = roomLogo;
        self.roomPicture = roomPicture;
        self.floorId = floorId;
    }
    return self;
}
- (NSString *)name
{
    return @"添加房间";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v2/room/create";
}

- (NSObject *)requestBody
{
    return @{
        @"roomName" : self.roomName ?: @"",
        @"familyId" : self.familyId ?: @"",
        @"roomClass" : self.roomClass ?: @"",
        @"roomLabel" : self.roomLabel ?: @"",
        @"roomLogo" : self.roomLogo ?: @"",
        @"roomPicture" : self.roomPicture ?: @"",
        @"floorId" : self.floorId ?: @""
    };
}
@end
