//
//  UpFamilyInviteMemberApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/2/19.
//

#import "UPFamilyInviteMemberApi.h"
@interface UPFamilyInviteMemberApi ()
@property (strong, nonatomic) NSString *userId;
@property (strong, nonatomic) NSString *nickname;
@property (strong, nonatomic) NSString *familyId;
@property (strong, nonatomic) NSString *memberRole;
@property (assign, nonatomic) NSInteger memberType;
@end

@implementation UPFamilyInviteMemberApi
- (instancetype)initWithUserId:(NSString *)userId nickname:(NSString *)nickname memberRole:(NSString *)memberRole memberType:(NSInteger)memberType familyId:(NSString *)familyId
{
    if (self = [super init]) {
        self.userId = userId;
        self.nickname = nickname;
        self.familyId = familyId;
        self.memberRole = memberRole;
        self.memberType = memberType;
    }
    return self;
}
- (NSString *)name
{
    return @"邀请家庭成员";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/refactor/v1/family/invitation";
}
- (NSObject *)requestBody
{
    NSMutableDictionary *requestBody = [NSMutableDictionary dictionary];
    requestBody[@"userId"] = self.userId ?: @"";
    requestBody[@"nickname"] = self.nickname ?: @"";
    requestBody[@"memberType"] = @(self.memberType);
    requestBody[@"familyId"] = self.familyId ?: @"";
    if (self.memberRole.length > 0) {
        requestBody[@"memberRole"] = self.memberRole ?: @"";
    }
    return requestBody;
}
@end
