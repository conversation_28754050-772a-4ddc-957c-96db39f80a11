//
//  UpRemoveRoomFromFamilyApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/3/15.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPRemoveRoomFromFamilyApi.h"

@interface UPRemoveRoomFromFamilyApi ()
@property (nonatomic, strong) NSString *familyId;
@property (nonatomic, strong) NSString *roomId;
@end

@implementation UPRemoveRoomFromFamilyApi
- (instancetype)initWithFamilyId:(NSString *)familyId roomId:(NSString *)roomId
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.roomId = roomId;
    }
    return self;
}
- (NSString *)name
{
    return @"移除房间";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v1/family/room/destroy";
}
- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"roomId" : self.roomId ?: @""
    };
}
@end
