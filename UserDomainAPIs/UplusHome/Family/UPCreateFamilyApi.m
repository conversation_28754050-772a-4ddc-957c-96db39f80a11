//
//  UpCreateFamilyApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/3/15.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPCreateFamilyApi.h"
@interface UPCreateFamilyApi ()
@property (nonatomic, strong) NSString *familyName;
@property (nonatomic, strong) NSString *familyPosition;
@property (nonatomic, strong) NSString *longitude;
@property (nonatomic, strong) NSString *latitude;
@property (nonatomic, strong) NSString *cityCode;
@property (nonatomic, strong) NSArray<NSDictionary *> *rooms;
@end


@implementation UPCreateFamilyApi
- (instancetype)initWithfamilyName:(NSString *)familyName familyPosition:(NSString *)familyPosition longitude:(NSString *)longitude latitude:(NSString *)latitude cityCode:(NSString *)cityCode rooms:(NSArray<NSDictionary<NSString *, NSString *> *> *)rooms
{
    if (self = [super init]) {
        self.familyName = familyName;
        self.familyPosition = familyPosition;
        self.latitude = latitude;
        self.longitude = longitude;
        self.cityCode = cityCode;
        self.rooms = [rooms isKindOfClass:[NSArray class]] ? rooms : @[];
    }
    return self;
}
- (NSString *)name
{
    return @"创建家庭";
}

- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/refactor/v1/family/create";
}
- (NSObject *)requestBody
{
    NSMutableDictionary *body = [NSMutableDictionary dictionary];
    body[@"familyName"] = self.familyName ?: @"";
    if (self.familyPosition.length && self.longitude.length && self.latitude.length) {
        body[@"familyPosition"] = self.familyPosition;
        body[@"familyLocation"] = @{
            @"longitude" : self.longitude,
            @"latitude" : self.latitude,
            @"cityCode" : self.cityCode ?: @""
        };
    }

    if (self.rooms.count) {
        body[@"rooms"] = self.rooms;
    }
    return body;
}
@end
