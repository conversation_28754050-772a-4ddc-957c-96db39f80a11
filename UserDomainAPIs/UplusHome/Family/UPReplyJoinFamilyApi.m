//
//  UPReplyJoinFamilyApi.m
//  UserDomainAPIs
//
//  Created by 冉东军 on 2021/3/4.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPReplyJoinFamilyApi.h"

static NSString *const kAgree = @"agree";
static NSString *const kApplicationId = @"applicationId";

@interface UPReplyJoinFamilyApi ()
@property (nonatomic, copy) NSString *applicationId;
@property (nonatomic, copy) NSString *agree;
@end

@implementation UPReplyJoinFamilyApi

- (instancetype)initWithApplicationId:(NSString *)applicationId agree:(BOOL)agree
{
    if (self = [super init]) {
        self.applicationId = applicationId;
        self.agree = agree ? @"true" : @"false";
    }
    return self;
}

- (NSString *)name
{
    return @"家庭管理员同意/拒绝用户加入家庭";
}

- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v1/family/join/agree";
}

- (NSObject *)requestBody
{
    return @{
        kApplicationId : self.applicationId ?: @"",
        kAgree : self.agree,
    };
}

@end
