//
//  UpFamilyRoomListApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/3/15.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPFamilyRoomListApi.h"
@interface UPFamilyRoomListApi ()
@property (strong, nonatomic) NSString *familyId;
@property (copy, nonatomic) NSString *floorId;
@end

@implementation UPFamilyRoomListApi
- (instancetype)initWithFamilyId:(NSString *)familyId floorId:(NSString *)floorId
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.floorId = floorId;
    }
    return self;
}
- (NSString *)name
{
    return @"获取家庭房间列表信息";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/refactor/v1/family/room/list";
}
- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"floorId" : self.floorId ?: @""
    };
}
@end
