//
//  UPModifyVirtualMemberNameApi.m
//  UserDomainAPIs
//
//  Created by 冉东军 on 2022/1/17.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPModifyVirtualMemberApi.h"

@interface UPModifyVirtualMemberApi ()
@property (copy, nonatomic) NSString *memberId;
@property (copy, nonatomic) NSString *memberName;
@property (copy, nonatomic) NSString *avatarUrl;
@property (assign, nonatomic) BOOL isCreater;
@property (copy, nonatomic) NSString *birthday;
@end

@implementation UPModifyVirtualMemberApi
- (instancetype)initWithVirtualMemberId:(NSString *)memberId memberName:(NSString *)memberName avatarUrl:(NSString *)avatarUrl isCreater:(BOOL)isCreater birthday:(NSString *)birthday
{
    if (self = [super init]) {
        self.memberId = memberId;
        self.memberName = memberName;
        self.avatarUrl = avatarUrl;
        self.isCreater = isCreater;
        self.birthday = birthday;
    }
    return self;
}

- (NSString *)name
{
    return @"编辑虚拟角色信息";
}

- (NSString *)path
{
    return @"/oauthserver/virtual/v1/info/modify";
}


- (NSObject *)requestBody
{
    return @{
        @"id" : self.memberId ?: @"",
        @"nickName" : self.memberName ?: @"",
        @"avatarUrl" : self.avatarUrl ?: @"",
        @"isCreater" : @(self.isCreater),
        @"birthday" : self.birthday ?: @""
    };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"accountToken"] = [UPNetworkSettings sharedSettings].accessToken;
    return headersDict;
}
@end
