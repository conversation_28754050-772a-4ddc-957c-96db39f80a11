//
//  UpFamilyRemoveDevicesApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "UPFamilyRemoveDevicesApi.h"
static NSString *kfamilyId = @"familyId";
static NSString *kdeviceIds = @"deviceIds";
static NSString *kdeviceId = @"deviceId";

@interface UPFamilyRemoveDevicesApi ()
@property (strong, nonatomic) NSString *familyId;
@property (strong, nonatomic) NSArray<NSDictionary<NSString *, NSString *> *> *deviceIds;
@property (strong, nonatomic) NSDictionary *body;
@end

@implementation UPFamilyRemoveDevicesApi
- (instancetype)initWithfamilyId:(NSString *)familyId deviceIds:(NSArray<NSDictionary<NSString *, NSString *> *> *)deviceIds
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.deviceIds = deviceIds;
    }
    return self;
}
- (NSString *)name
{
    return @"从家庭批量移除设备";
}
- (NSString *)path
{
    return @"/emuplus/device/v3/family/moveout";
}
- (NSObject *)requestBody
{
    return @{
        kfamilyId : self.familyId ?: @"",
        kdeviceIds : self.deviceIds ?: @[]
    };
    ;
}
@end
