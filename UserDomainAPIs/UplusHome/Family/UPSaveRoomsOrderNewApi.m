//
//  UPSaveRoomsOrderNewApi.m
//  UserDomainAPIs
//
//  Created by l<PERSON><PERSON> on 2025/4/21.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPSaveRoomsOrderNewApi.h"

@interface UPSaveRoomsOrderNewApi ()

@property (nonatomic, copy) NSString *familyId;

@property (nonatomic, strong) NSArray<NSDictionary *> *sortList;

@end

@implementation UPSaveRoomsOrderNewApi

- (instancetype)initWithFamilyId:(NSString *)familyId sortList:(NSArray<NSDictionary *> *)sortList
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.sortList = sortList;
    }
    return self;
}

- (NSString *)name
{
    return @"保存房间顺序";
}

- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/refactor/v2/room/order";
}

- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"sortList" : self.sortList ?: @[]
    };
}

@end
