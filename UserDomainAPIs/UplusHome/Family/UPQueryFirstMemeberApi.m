//
//  UPQueryFirstMemeberApi.m
//  UserDomainAPIs
//
//  Created by 冉东军 on 2022/1/17.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPQueryFirstMemeberApi.h"

@interface UPQueryFirstMemeberApi ()
@property (strong, nonatomic) NSString *familyId;

@end

@implementation UPQueryFirstMemeberApi
- (instancetype)initWithFamilyId:(NSString *)familyId
{
    if (self = [super init]) {
        self.familyId = familyId;
    }
    return self;
}

- (NSString *)name
{
    return @"获取第一个加入家庭的用户信息";
}

- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v1/member/first";
}

- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @""
    };
}

@end
