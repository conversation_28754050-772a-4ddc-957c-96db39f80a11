//
//  UpFamilyDeleteMemberApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/2/19.
//

#import "UPFamilyDeleteMemberApi.h"

@interface UPFamilyDeleteMemberApi ()
@property (strong, nonatomic) NSString *userId;
@property (strong, nonatomic) NSString *familyId;
@end

@implementation UPFamilyDeleteMemberApi
- (instancetype)initWithUserId:(NSString *)userId familyId:(NSString *)familyId
{
    if (self = [super init]) {

        self.userId = userId;
        self.familyId = familyId;
    }
    return self;
}
- (NSString *)name
{
    return @"删除家庭成员";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/refactor/v1/member/destroy";
}
- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"memberId" : self.userId ?: @""
    };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"supportAccountUserId"] = @"true";
    return headersDict;
}

@end
