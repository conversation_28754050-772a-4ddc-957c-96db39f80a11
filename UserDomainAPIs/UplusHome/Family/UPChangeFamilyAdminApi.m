//
//  UpChangeFamilyAdminApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/3/15.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPChangeFamilyAdminApi.h"
@interface UPChangeFamilyAdminApi ()
@property (strong, nonatomic) NSString *familyId;
@property (nonatomic, strong) NSString *userId;
@end

@implementation UPChangeFamilyAdminApi
- (instancetype)initWithfamilyId:(NSString *)familyId userId:(nonnull NSString *)userId
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.userId = userId;
    }
    return self;
}
- (NSString *)name
{
    return @"退出家庭";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v1/family/manager/change";
}
- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"userId" : self.userId ?: @""
    };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"supportAccountUserId"] = @"true";
    return headersDict;
}

@end
