//
//  UpFamilyMoveDevicesToOtherFamilyApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "UPFamilyMoveDevicesToOtherFamilyApi.h"

static NSString *const kdeviceIds = @"deviceIds";
static NSString *const kdeviceId = @"deviceId";
static NSString *const koldFamilyId = @"oldFamilyId";
static NSString *const knewFamilyId = @"newFamilyId";
@interface UPFamilyMoveDevicesToOtherFamilyApi ()
@property (strong, nonatomic) NSArray<NSDictionary<NSString *, NSString *> *> *deviceIds;
@property (strong, nonatomic) NSString *oldFamilyId;
@property (strong, nonatomic) NSString *familyId_new;
@end

@implementation UPFamilyMoveDevicesToOtherFamilyApi
- (instancetype)initWithDeviceIds:(NSArray<NSDictionary<NSString *, NSString *> *> *)deviceIds oldFamilyId:(NSString *)oldFamilyId newFamilyId:(NSString *)newFamilyId
{
    if (self = [super init]) {
        self.deviceIds = deviceIds;
        self.oldFamilyId = oldFamilyId;
        self.familyId_new = newFamilyId;
    }
    return self;
}
- (NSString *)name
{
    return @"移动设备到新的家庭";
}
- (NSString *)path
{
    return @"/api-gw/wisdomdevice/device/v3/family/transfer";
}
- (NSObject *)requestBody
{
    return @{
        kdeviceIds : self.deviceIds ?: [NSNull null],
        koldFamilyId : self.oldFamilyId ?: @"",
        knewFamilyId : self.familyId_new ?: @""
    };
}
@end
