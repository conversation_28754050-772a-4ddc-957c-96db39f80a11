//
//  UPDeleteFloorApi.m
//  UserDomainAPIs
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/5.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPDeleteFloorApi.h"

@interface UPDeleteFloorApi ()
@property (nonatomic, copy) NSString *floorId;
@property (nonatomic, copy) NSString *familyId;
@end

@implementation UPDeleteFloorApi
- (instancetype)initWithFloorId:(NSString *)floorId familyId:(NSString *)familyId
{
    if (self = [super init]) {
        _floorId = floorId;
        _familyId = familyId;
    }
    return self;
}
- (NSString *)name
{
    return @"删除楼层接口";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v2/floor/destroy";
}
- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"floorId" : self.floorId ?: @""
    };
}

@end
