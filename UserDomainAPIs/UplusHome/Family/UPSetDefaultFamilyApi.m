//
//  UpSetDefaultFamilyApi.m
//  uplusApi
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/11/16.
//

#import "UPSetDefaultFamilyApi.h"

@interface UPSetDefaultFamilyApi ()
@property (strong, nonatomic) NSString *familyId;
@end

@implementation UPSetDefaultFamilyApi
- (instancetype)initWithFamilyId:(NSString *)familyId
{
    if (self = [super init]) {

        self.familyId = familyId;
    }
    return self;
}
- (NSString *)name
{
    return @"设置默认家庭";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v1/defaultFamily/set";
}
- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @""
    };
}
@end
