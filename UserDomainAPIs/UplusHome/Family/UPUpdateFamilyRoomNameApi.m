//
//  UpUpdateFamilyNameApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/2/19.
//

#import "UPUpdateFamilyRoomNameApi.h"

@interface UPUpdateFamilyRoomNameApi ()
@property (strong, nonatomic) NSString *roomName;
@property (strong, nonatomic) NSString *familyId;
@property (nonatomic, strong) NSString *roomId;
@property (nonatomic, copy) NSString *floorId;
@end

@implementation UPUpdateFamilyRoomNameApi
- (instancetype)initWithRoomName:(NSString *)roomName familyId:(NSString *)familyId roomId:(NSString *)roomId floorId:(NSString *)floorId
{
    if (self = [super init]) {

        self.roomName = roomName;
        self.familyId = familyId;
        self.roomId = roomId;
        self.floorId = floorId;
    }
    return self;
}
- (NSString *)name
{
    return @"修改房间名称";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v2/room/modify";
}
- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"roomId" : self.roomId ?: @"",
        @"roomName" : self.roomName ?: @"",
        @"floorId" : self.floorId ?: @""
    };
}
@end
