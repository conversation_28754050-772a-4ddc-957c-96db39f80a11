//
//  UpAddRoomToFamilyNewApi.m
//  UserDomainAPIs
//
//  Created by <PERSON><PERSON><PERSON> on 2025/4/10.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpAddRoomToFamilyNewApi.h"

@interface UpAddRoomToFamilyNewApi ()

@property (nonatomic, copy) NSString *familyId;
@property (nonatomic, copy) NSString *roomName;
@property (nonatomic, copy) NSString *roomClass;
@property (nonatomic, copy) NSString *floorOrderId;

@end

@implementation UpAddRoomToFamilyNewApi

- (instancetype)initWithFamilyId:(NSString *)familyId
                        roomName:(NSString *)roomName
                       roomClass:(NSString *)roomClass
                    floorOrderId:(NSString *)floorOrderId
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.roomName = roomName;
        self.roomClass = roomClass;
        self.floorOrderId = floorOrderId;
    }
    return self;
}

- (NSString *)name
{
    return @"在楼层添加房间";
}

- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/refactor/v1/room/create";
}

- (NSObject *)requestBody
{
    NSMutableDictionary *body = [NSMutableDictionary dictionary];
    body[@"familyId"] = self.familyId;
    body[@"roomName"] = self.roomName;
    if (self.roomClass.length) {
        body[@"roomClass"] = self.roomClass;
    }
    if (self.floorOrderId.length) {
        body[@"floorOrderId"] = self.floorOrderId;
    }
    return body;
}

@end
