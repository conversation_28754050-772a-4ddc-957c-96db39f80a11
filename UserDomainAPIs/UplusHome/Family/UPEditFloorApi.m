//
//  UPEditFloorApi.m
//  UserDomainAPIs
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/5.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPEditFloorApi.h"

@interface UPEditFloorApi ()
@property (nonatomic, copy) NSString *floorOrderId;
@property (nonatomic, copy) NSString *familyId;
@property (nonatomic, copy) NSString *floorName;
@property (nonatomic, copy) NSString *floorLabel;
@property (nonatomic, copy) NSString *floorLogo;
@property (nonatomic, copy) NSString *floorPicture;
@property (nonatomic, copy) NSString *floorId;
@end

@implementation UPEditFloorApi
- (instancetype)initWithfamilyId:(NSString *)familyId floorOrderId:(NSString *)floorOrderId floorName:(NSString *)floorName floorLabel:(NSString *)floorLabel floorLogo:(NSString *)floorLogo floorPicture:(NSString *)floorPicture floorId:(NSString *)floorId
{
    if (self = [super init]) {
        _familyId = familyId;
        _floorOrderId = floorOrderId;
        _floorPicture = floorPicture;
        _floorName = floorName;
        _floorLabel = floorLabel;
        _floorLogo = floorLogo;
        _floorId = floorId;
    }
    return self;
}
- (NSString *)name
{
    return @"编辑楼层接口";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v2/floor/modify";
}
- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"floorName" : self.floorName ?: @"",
        @"floorLabel" : self.floorLabel ?: @"",
        @"floorLogo" : self.floorLogo ?: @"",
        @"floorPicture" : self.floorPicture ?: @"",
        @"floorId" : self.floorId ?: @""
    };
}

@end
