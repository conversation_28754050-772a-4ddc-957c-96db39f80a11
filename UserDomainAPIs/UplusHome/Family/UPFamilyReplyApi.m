//
//  UpFamilyReplyApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/2/19.
//

#import "UPFamilyReplyApi.h"

static NSString *const kinviteCode = @"inviteCode";
static NSString *const kmemberName = @"memberName";
static NSString *const kagree = @"agree";
static NSString *const kfamilyId = @"familyId";

@interface UPFamilyReplyApi ()
@property (strong, nonatomic) NSString *inviteCode;
@property (strong, nonatomic) NSString *familyId;
@property (strong, nonatomic) NSString *memberName;
@property (assign, nonatomic) BOOL agree;
@end

@implementation UPFamilyReplyApi
- (instancetype)initWithinviteCode:(NSString *)inviteCode familyId:(NSString *)familyId memberName:(NSString *)memberName agree:(BOOL)agree
{
    if (self = [super init]) {
        self.inviteCode = inviteCode;
        self.memberName = memberName;
        self.agree = agree;
        self.familyId = familyId;
    }
    return self;
}
- (NSString *)name
{
    return @"回复家庭邀请";
}

- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v1/member/invite/agree";
}
- (NSObject *)requestBody
{
    return @{
        kfamilyId : self.familyId,
        kinviteCode : self.inviteCode ?: @"",
        kmemberName : self.memberName ?: @"",
        kagree : @(self.agree),
    };
}

@end
