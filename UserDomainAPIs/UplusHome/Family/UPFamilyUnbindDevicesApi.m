//
//  UpFamilyUnbindDevicesApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "UPFamilyUnbindDevicesApi.h"

@interface UPFamilyUnbindDevicesApi ()
@property (strong, nonatomic) NSString *familyId;
@property (strong, nonatomic) NSArray *unbindDevices;
@property (strong, nonatomic) NSArray *shareDevices;
@end

@implementation UPFamilyUnbindDevicesApi
- (instancetype)initWithfamilyId:(NSString *)familyId unbindDevices:(nonnull NSArray<NSDictionary<NSString *, NSString *> *> *)unbindDevices shareDevices:(nonnull NSArray<NSDictionary<NSString *, NSString *> *> *)shareDevices
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.unbindDevices = unbindDevices;
        self.shareDevices = shareDevices;
    }
    return self;
}
- (NSString *)name
{
    return @"家庭批量解绑设备";
}
- (NSString *)path
{
    return @"/api-gw/wisdomdevice/device/share/v2/relation/delete";
}
- (NSObject *)requestBody
{
    return @{
        @"shareDeviceIds" : self.shareDevices ?: @[],
        @"unbindDevice" : @{@"familyId" : self.familyId ?: @"",
                            @"deviceids" : self.unbindDevices ?: @[]}
    };
}
- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"grayMode"] = [self getGrayMode] ? @"true" : @"false";
    return headersDict;
}

@end
