//
//  UPAddVirtualMember.m
//  UserDomainAPIs
//
//  Created by 冉东军 on 2022/1/17.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPAddVirtualMemberApi.h"

@interface UPAddVirtualMemberApi ()
@property (strong, nonatomic) NSString *familyId;
@property (strong, nonatomic) NSString *memberName;
@property (strong, nonatomic) NSString *memberId;
@end

@implementation UPAddVirtualMemberApi
- (instancetype)initWithFamilyId:(NSString *)familyId memberId:(NSString *)memberId memberName:(NSString *)memberName
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.memberId = memberId;
        self.memberName = memberName;
    }
    return self;
}

- (NSString *)name
{
    return @"虚拟用户加入家庭";
}

- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v1/virtual/member/join";
}

- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"virtualUCId" : self.memberId ?: @"",
        @"userFamilyName" : self.memberName ?: @""
    };
}

@end
