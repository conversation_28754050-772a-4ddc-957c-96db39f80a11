//
//  UPSaveRoomsOrderNewApi.h
//  UserDomainAPIs
//
//  Created by lubiao on 2025/4/21.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ZJServerAPIBase.h"

NS_ASSUME_NONNULL_BEGIN

@interface UPSaveRoomsOrderNewApi : ZJServerAPIBase

/// 保存房间顺序
/// @param familyId 家庭id
/// @param sortList 楼层+房间排序信息
///   [
///     {"floorId": "123", "rooms": ["1", "2", ...]},
///     {"floorId": "abc", "rooms": ["a", "b", ...]},
///   ]
- (instancetype)initWithFamilyId:(NSString *)familyId sortList:(NSArray<NSDictionary *> *)sortList;


@end

NS_ASSUME_NONNULL_END
