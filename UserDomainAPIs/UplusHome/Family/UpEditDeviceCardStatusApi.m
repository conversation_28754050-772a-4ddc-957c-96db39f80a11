//
//  UpEditDeviceCardListApi.m
//  UserDomainAPIs
//
//  Created by l<PERSON><PERSON> on 2025/3/25.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEditDeviceCardStatusApi.h"

@interface UpEditDeviceCardStatusApi ()

@property (nonatomic, copy) NSString *familyId;

@property (nonatomic, strong) NSArray<NSString *> *orderList;

@property (nonatomic, strong) NSArray<NSString *> *bigCardList;

@property (nonatomic, strong) NSArray<NSString *> *middleCardList;

@property (nonatomic, strong) NSArray<NSString *> *smallCardList;

@end

@implementation UpEditDeviceCardStatusApi

- (instancetype)initWithFamilyId:(NSString *)familyId
                       orderList:(NSArray<NSString *> *)orderList
                     bigCardList:(NSArray<NSString *> *)bigCardList
                  middleCardList:(NSArray<NSString *> *)middleCardList
                   smallCardList:(NSArray<NSString *> *)smallCardList
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.orderList = orderList;
        self.bigCardList = bigCardList;
        self.middleCardList = middleCardList;
        self.smallCardList = smallCardList;
    }
    return self;
}

- (NSString *)name
{
    return @"设备卡片顺序&状态操作";
}

- (NSString *)path
{
    return @"/api-gw/wisdomdevice/device/v5/card/sortAndStatus/operate";
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"grayMode"] = [self getGrayMode] ? @"true" : @"false";
    return headersDict;
}

- (NSObject *)requestBody
{
    NSMutableDictionary *body = [NSMutableDictionary dictionary];
    if (self.familyId) {
        body[@"familyId"] = self.familyId;
    }

    if (self.orderList.count) {
        body[@"cardOrderList"] = self.orderList;
    }

    if (self.bigCardList) {
        body[@"bigCardList"] = self.bigCardList;
    }

    if (self.middleCardList) {
        body[@"middleCardList"] = self.middleCardList;
    }

    if (self.smallCardList) {
        body[@"smallCardList"] = self.smallCardList;
    }

    return body;
}

@end
