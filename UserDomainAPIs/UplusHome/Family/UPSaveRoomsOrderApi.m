//
//  UPSaveRoomsOrderApi.m
//  UserDomainAPIs
//
//  Created by 冉东军 on 2022/11/14.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPSaveRoomsOrderApi.h"

@interface UPSaveRoomsOrderApi ()
@property (nonatomic, copy) NSString *floorId;
@property (nonatomic, copy) NSString *familyId;
@property (nonatomic, strong) NSArray<NSString *> *rooms;
@end

@implementation UPSaveRoomsOrderApi
- (instancetype)initWithFamilyId:(NSString *)familyId rooms:(NSArray<NSString *> *)rooms floorId:(NSString *)floorId
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.rooms = rooms;
        self.floorId = floorId;
    }
    return self;
}

- (NSString *)name
{
    return @"保存房间顺序";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v2/room/order";
}
- (NSObject *)requestBody
{
    return @{
        @"rooms" : self.rooms ?: @[],
        @"familyId" : self.familyId ?: @"",
        @"floorId" : self.floorId ?: @""
    };
}

@end
