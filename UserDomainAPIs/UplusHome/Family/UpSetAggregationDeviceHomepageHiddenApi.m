//
//  UpSetAggregationDeviceHomepageHiddenApi.m
//  UserDomainAPIs
//
//  Created by <PERSON><PERSON> on 2025/7/10.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpSetAggregationDeviceHomepageHiddenApi.h"

@interface UpSetAggregationDeviceHomepageHiddenApi ()

@property (nonatomic, copy) NSString *familyId;
@property (nonatomic, strong, nullable) NSArray<NSString *> *devicesDisplayed;
@property (nonatomic, strong, nullable) NSArray<NSString *> *devicesHidden;

@end

@implementation UpSetAggregationDeviceHomepageHiddenApi

- (instancetype)initWithFamilyId:(NSString *)familyId
                devicesDisplayed:(nullable NSArray<NSString *> *)devicesDisplayed
                   devicesHidden:(nullable NSArray<NSString *> *)devicesHidden
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.devicesDisplayed = devicesDisplayed ?: @[];
        self.devicesHidden = devicesHidden ?: @[];
    }
    return self;
}

- (NSString *)name
{
    return @"设置聚合设备在首页隐藏与否";
}

- (NSString *)path
{
    return @"/api-gw/wisdomdevice/device/v1/aggdevice/homepage/hidden/setting";
}

- (NSObject *)requestBody
{
    NSMutableDictionary *body = [NSMutableDictionary dictionary];
    if (self.familyId) {
        body[@"familyId"] = self.familyId;
    }

    if (self.devicesDisplayed) {
        body[@"devicesDisplayed"] = self.devicesDisplayed;
    }

    if (self.devicesHidden) {
        body[@"devicesHidden"] = self.devicesHidden;
    }

    return body;
}

@end
