//
//  UpFamilyListApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/2/18.
//

#import "UPFamilyListApi.h"
@implementation UPFamilyListApi

- (NSString *)name
{
    return @"查询用户家庭列表";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/refactor/v2/family/list";
}

- (NSUInteger)retryTimes
{
    return 1;
}

- (NSTimeInterval)retryDelay
{
    return 3.0;
}

- (NSTimeInterval)retryStep
{
    return 4.0;
}

- (NSObject *)requestBody
{
    return @{};
}

@end
