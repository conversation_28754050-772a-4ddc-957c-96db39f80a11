//
//  UpFamilyMoveDevicesToNewRoomApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/3/1.
//

#import "UPFamilyMoveDevicesToNewRoomApi.h"

static NSString *const kfamilyId = @"familyId";
static NSString *const kdeviceIds = @"deviceIds";
static NSString *const kdeviceId = @"deviceId";
static NSString *const kdeviceName = @"deviceName";
static NSString *const koldRoom = @"oldRoom";
static NSString *const knewRoom = @"newRoom";
static NSString *const knewRoomId = @"newRoomId";
@interface UPFamilyMoveDevicesToNewRoomApi ()
@property (strong, nonatomic) NSString *familyId;
@property (strong, nonatomic)
    NSArray<NSDictionary<NSString *, NSString *> *> *deviceIds;
@property (strong, nonatomic) NSString *roomName_new;
@property (strong, nonatomic) NSString *roomId_new;
@end

@implementation UPFamilyMoveDevicesToNewRoomApi
- (instancetype)
    initWithFamilyId:(NSString *)familyId
           deviceIds:(NSArray<NSDictionary<NSString *, NSString *> *> *)deviceIds
         newRoomName:(NSString *)newRoomName
           newRoomId:(NSString *)newRoomId
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.deviceIds = deviceIds;
        self.roomName_new = newRoomName;
        self.roomId_new = newRoomId;
    }
    return self;
}
- (NSString *)name
{
    return @"移动设备到新的房间";
}
- (NSString *)path
{
    return @"/emuplus/device/v3/family/moveplace";
}
- (NSObject *)requestBody
{
    return @{
        kfamilyId : self.familyId ?: @"",
        kdeviceIds : self.deviceIds ?: @[],
        knewRoom : self.roomName_new ?: @"",
        knewRoomId : self.roomId_new ?: @""
    };
}
@end
