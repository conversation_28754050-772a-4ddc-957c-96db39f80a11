//
//  UPModifyMemberRoleApi.m
//  UserDomainAPIs
//
//  Created by 冉东军 on 2022/8/9.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPModifyMemberRoleApi.h"

@interface UPModifyMemberRoleApi ()
@property (copy, nonatomic) NSString *memberId;
@property (copy, nonatomic) NSString *familyId;
@property (copy, nonatomic) NSString *memberRole;
@end

@implementation UPModifyMemberRoleApi
- (instancetype)initWithMemberId:(NSString *)memberId familyId:(NSString *)familyId memberRole:(NSString *)memberRole
{
    if (self = [super init]) {
        self.memberId = memberId;
        self.familyId = familyId;
        self.memberRole = memberRole;
    }
    return self;
}

- (NSString *)name
{
    return @"修改家庭成员在家庭里的身份";
}

- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v2/family/memberRole/modify";
}


- (NSObject *)requestBody
{
    return @{
        @"memberId" : self.memberId ?: @"",
        @"familyId" : self.familyId ?: @"",
        @"memberRole" : self.memberRole ?: @""
    };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"supportAccountUserId"] = @"true";
    return headersDict;
}
@end
