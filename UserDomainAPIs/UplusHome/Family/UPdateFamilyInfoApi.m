//
//  UpdateFamilyInfoApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/3/15.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPdateFamilyInfoApi.h"
@interface UPdateFamilyInfoApi ()
@property (nonatomic, strong) NSString *familyId;
@property (nonatomic, strong) NSString *familyName;
@property (nonatomic, strong) NSString *familyPosition;
@property (nonatomic, strong) NSString *longitude;
@property (nonatomic, strong) NSString *latitude;
@property (nonatomic, strong) NSString *cityCode;

@end


@implementation UPdateFamilyInfoApi
- (instancetype)initWithfamilyId:(NSString *)familyId familyName:(NSString *)familyName familyPosition:(NSString *)familyPosition longitude:(NSString *)longitude latitude:(NSString *)latitude cityCode:(NSString *)cityCode
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.familyName = familyName;
        self.familyPosition = familyPosition;
        self.latitude = latitude;
        self.longitude = longitude;
        self.cityCode = cityCode;
    }
    return self;
}

- (NSString *)name
{
    return @"更新家庭信息";
}

- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v1/family/edit";
}

- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"familyName" : self.familyName ?: @"",
        @"familyPosition" : self.familyPosition,
        @"familyLocation" : @{
            @"longitude" : self.longitude ?: @"",
            @"latitude" : self.latitude ?: @"",
            @"cityCode" : self.cityCode ?: @""
        }
    };
}
@end
