//
//  UpEditDeviceAggregationApi.m
//  UserDomainAPIs
//
//  Created by lubiao on 2025/3/25.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEditDeviceAggregationApi.h"

@interface UpEditDeviceAggregationApi ()

@property (nonatomic, copy) NSString *familyId;

@property (nonatomic, strong) NSArray<NSDictionary *> *aggCards;

@end

@implementation UpEditDeviceAggregationApi

- (instancetype)initWithFamilyId:(NSString *)familyId
                aggregationCards:(NSArray<NSDictionary *> *)aggCards
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.aggCards = aggCards;
    }
    return self;
}

- (NSString *)name
{
    return @"聚合卡片操作";
}

- (NSString *)path
{
    return @"/api-gw/wisdomdevice/device/v4/agg/card/operate";
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"grayMode"] = [self getGrayMode] ? @"true" : @"false";
    return headersDict;
}

- (NSObject *)requestBody
{
    NSMutableDictionary *body = [NSMutableDictionary dictionary];
    if (self.familyId) {
        body[@"familyId"] = self.familyId;
    }

    if (self.aggCards) {
        body[@"aggCard"] = self.aggCards;
    }

    return body;
}

@end

#pragma mark - UpSetAggregationDeviceHomepageHiddenApi

@interface UpSetAggregationDeviceHomepageHiddenApi ()

@property (nonatomic, copy) NSString *familyId;
@property (nonatomic, strong, nullable) NSArray<NSString *> *devicesDisplayed;
@property (nonatomic, strong, nullable) NSArray<NSString *> *devicesHidden;

@end

@implementation UpSetAggregationDeviceHomepageHiddenApi

- (instancetype)initWithFamilyId:(NSString *)familyId
                 devicesDisplayed:(nullable NSArray<NSString *> *)devicesDisplayed
                    devicesHidden:(nullable NSArray<NSString *> *)devicesHidden
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.devicesDisplayed = devicesDisplayed;
        self.devicesHidden = devicesHidden;
    }
    return self;
}

- (NSString *)name
{
    return @"设置聚合设备在首页隐藏与否";
}

- (NSString *)path
{
    return @"/api-gw/wisdomdevice/device/v1/aggdevice/homepage/hidden/setting";
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"grayMode"] = [self getGrayMode] ? @"true" : @"false";
    return headersDict;
}

- (NSObject *)requestBody
{
    NSMutableDictionary *body = [NSMutableDictionary dictionary];
    if (self.familyId) {
        body[@"familyId"] = self.familyId;
    }

    if (self.devicesDisplayed) {
        body[@"devicesDisplayed"] = self.devicesDisplayed;
    }

    if (self.devicesHidden) {
        body[@"devicesHidden"] = self.devicesHidden;
    }

    return body;
}

@end
