//
//  UpEditDeviceAggregationApi.m
//  UserDomainAPIs
//
//  Created by lubiao on 2025/3/25.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpEditDeviceAggregationApi.h"

@interface UpEditDeviceAggregationApi ()

@property (nonatomic, copy) NSString *familyId;

@property (nonatomic, strong) NSArray<NSDictionary *> *aggCards;

@end

@implementation UpEditDeviceAggregationApi

- (instancetype)initWithFamilyId:(NSString *)familyId
                aggregationCards:(NSArray<NSDictionary *> *)aggCards
{
    if (self = [super init]) {
        self.familyId = familyId;
        self.aggCards = aggCards;
    }
    return self;
}

- (NSString *)name
{
    return @"聚合卡片操作";
}

- (NSString *)path
{
    return @"/api-gw/wisdomdevice/device/v4/agg/card/operate";
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"grayMode"] = [self getGrayMode] ? @"true" : @"false";
    return headersDict;
}

- (NSObject *)requestBody
{
    NSMutableDictionary *body = [NSMutableDictionary dictionary];
    if (self.familyId) {
        body[@"familyId"] = self.familyId;
    }

    if (self.aggCards) {
        body[@"aggCard"] = self.aggCards;
    }

    return body;
}

@end
