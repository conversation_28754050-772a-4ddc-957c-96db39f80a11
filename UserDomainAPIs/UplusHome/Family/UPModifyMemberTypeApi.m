//
//  UPModifyMemberTypeApi.m
//  UserDomainAPIs
//
//  Created by l<PERSON><PERSON> on 2025/4/11.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPModifyMemberTypeApi.h"

@interface UPModifyMemberTypeApi ()

@property (nonatomic, copy) NSString *memberId;
@property (nonatomic, copy) NSString *familyId;
@property (nonatomic, assign) NSInteger memberType;

@end

@implementation UPModifyMemberTypeApi

- (instancetype)initWithMemberId:(NSString *)memberId familyId:(NSString *)familyId memberType:(NSInteger)memberType
{
    if (self = [super init]) {
        self.memberId = memberId;
        self.familyId = familyId;
        self.memberType = memberType;
    }
    return self;
}

- (NSString *)name
{
    return @"设置家庭成员在家庭里的权限类型";
}

- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/refactor/v1/family/memberType/modify";
}

- (NSObject *)requestBody
{
    return @{
        @"memberId" : self.memberId ?: @"",
        @"familyId" : self.familyId ?: @"",
        @"memberType" : @(self.memberType)
    };
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headersDict = [super requestHeaders].mutableCopy;
    headersDict[@"supportAccountUserId"] = @"true";
    return headersDict;
}

@end
