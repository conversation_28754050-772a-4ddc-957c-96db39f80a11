//
//  UPAddFloorApi.m
//  UserDomainAPIs
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/5.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPAddFloorApi.h"

@interface UPAddFloorApi ()
@property (nonatomic, copy) NSString *floorOrderId;
@property (nonatomic, copy) NSString *familyId;
@property (nonatomic, copy) NSString *floorName;
@property (nonatomic, copy) NSString *floorLabel;
@property (nonatomic, copy) NSString *floorLogo;
@property (nonatomic, copy) NSString *floorPicture;
@end

@implementation UPAddFloorApi
- (instancetype)initWithfamilyId:(NSString *)familyId floorOrderId:(NSString *)floorOrderId floorName:(NSString *)floorName floorLabel:(NSString *)floorLabel floorLogo:(NSString *)floorLogo floorPicture:(NSString *)floorPicture
{
    if (self = [super init]) {
        _familyId = familyId;
        _floorOrderId = floorOrderId;
        _floorPicture = floorPicture;
        _floorName = floorName;
        _floorLabel = floorLabel;
        _floorLogo = floorLogo;
    }
    return self;
}
- (NSString *)name
{
    return @"创建楼层接口";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v2/floor/create";
}
- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @"",
        @"floorOrderId" : self.floorOrderId ?: @"",
        @"floorName" : self.floorName ?: @"",
        @"floorLabel" : self.floorLabel ?: @"",
        @"floorLogo" : self.floorLogo ?: @"",
        @"floorPicture" : self.floorPicture ?: @""
    };
}
@end
