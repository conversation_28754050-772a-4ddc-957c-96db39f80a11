//
//  UpDestroyFamilyApi.m
//  upuserdomain
//
//  Created by 振兴郑 on 2019/3/15.
//  Copyright © 2019 振兴郑. All rights reserved.
//

#import "UPDestroyFamilyApi.h"
@interface UPDestroyFamilyApi ()
@property (strong, nonatomic) NSString *familyId;
@end

@implementation UPDestroyFamilyApi
- (instancetype)initWithFamilyId:(NSString *)familyId
{
    if (self = [super init]) {

        self.familyId = familyId;
    }
    return self;
}
- (NSString *)name
{
    return @"解散家庭";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/v1/family/destroy";
}
- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId
    };
}
@end
