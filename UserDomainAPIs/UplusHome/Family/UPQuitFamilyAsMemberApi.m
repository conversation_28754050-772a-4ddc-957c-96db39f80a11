//
//  UpQuitFamilyApi.m
//  AFNetworking
//
//  Created by 振兴郑 on 2019/2/19.
//

#import "UPQuitFamilyAsMemberApi.h"

@interface UPQuitFamilyAsMemberApi ()
@property (strong, nonatomic) NSString *familyId;
@end

@implementation UPQuitFamilyAsMemberApi
- (instancetype)initWithfamilyId:(NSString *)familyId
{
    if (self = [super init]) {
        self.familyId = familyId;
    }
    return self;
}
- (NSString *)name
{
    return @"退出家庭";
}
- (NSString *)path
{
    return @"/api-gw/wisdomfamily/family/refactor/v1/member/exit";
}
- (NSObject *)requestBody
{
    return @{
        @"familyId" : self.familyId ?: @""
    };
}
@end
