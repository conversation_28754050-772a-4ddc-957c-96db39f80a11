//
//  YGLogoutApi.m
//  UserDomainAPIs
//
//  Created by 冉东军 on 2022/2/23.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ShopLogoutApi.h"

@interface ShopLogoutApi ()
@property (nonatomic, copy) NSString *accessToken;
@end
@implementation ShopLogoutApi

- (instancetype)initWithAccessToken:(NSString *)accessToken
{
    if (self = [super init]) {
        _accessToken = accessToken;
    }
    return self;
}

- (NSString *)name
{
    return @"退出登录";
}

- (UPRequestMethod)method
{
    return UPRequestMethodGET;
}

- (NSString *)path
{
    return [NSString stringWithFormat:@"/v3/platform/login/logout.json?accessToken=%@", self.accessToken];
}
- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Content-Type" : @"application/json"
    };
}

- (NSTimeInterval)retryDelay
{
    return 3.0;
}
- (NSUInteger)retryTimes
{
    return 2;
}

- (NSObject *)requestBody
{
    return nil;
}

@end
