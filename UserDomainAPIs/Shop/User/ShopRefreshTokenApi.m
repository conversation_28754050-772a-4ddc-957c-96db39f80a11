//
//  ShopRefreshTokenApi.m
//  UserDomainAPIs
//
//  Created by 冉东军 on 2021/12/31.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ShopRefreshTokenApi.h"

@interface ShopRefreshTokenApi ()
@property (nonatomic, copy) NSString *refreshToken;
@end

@implementation ShopRefreshTokenApi

- (instancetype)initWithrefresh_token:(NSString *)refresh_token
{
    if (self = [super init]) {
        _refreshToken = refresh_token;
    }
    return self;
}

- (NSString *)name
{
    return @"刷新token";
}

- (UPRequestMethod)method
{
    return UPRequestMethodGET;
}

- (NSString *)path
{
    return [NSString stringWithFormat:@"/v3/platform/login/refreshToken4zjFrame.json?refreshToken=%@", self.refreshToken];
}
- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return @{
        @"Content-Type" : @"application/json"
    };
}

- (NSTimeInterval)retryDelay
{
    return 3.0;
}
- (NSUInteger)retryTimes
{
    return 2;
}

- (NSObject *)requestBody
{
    return nil;
}

@end
