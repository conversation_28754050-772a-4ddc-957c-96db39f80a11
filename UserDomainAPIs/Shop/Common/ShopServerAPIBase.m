//
//  ShopServerAPIBase.m
//  UserDomainAPIs
//
//  Created by 冉东军 on 2021/12/31.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ShopServerAPIBase.h"
#import "UserCenterAPIBase.h"

@implementation ShopServerAPIBase
- (NSString *)baseURL
{
    if ([UserCenterAPIBase getEnv] == UserCenterAPIEnvProduction) {
        return @"https://m.ehaier.com";
    }
    else if ([UserCenterAPIBase getEnv] == UserCenterAPIEnvAcceptance) {
        return @"https://m-test.ehaier.com";
    }
    return @"https://m.ehaier.com";
}
- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    return [UPCommonServerHeader signHeaderWithBody:self.requestBody];
}

- (NSTimeInterval)timeoutInterval
{
    return 15.0;
}

- (NSUInteger)retryTimes
{
    return 0;
}

- (NSTimeInterval)retryDelay
{
    return 0.0;
}

@end
