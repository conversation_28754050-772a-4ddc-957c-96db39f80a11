# 设置聚合设备在首页隐藏与否接口使用示例

## 接口信息
- **接口名称**: 设置聚合设备在首页隐藏与否
- **接口路径**: POST `/api-gw/wisdomdevice/device/v1/aggdevice/homepage/hidden/setting`
- **API类**: `UpSetAggregationDeviceHomepageHiddenApi`

## 参数说明

| 名称 | 类型 | 是否必须 | 备注 |
|------|------|----------|------|
| familyId | string | 必须 | 家庭id |
| devicesDisplayed | string[] | 非必须 | 需要由"隐藏"变更为"展示"的设备id集合，参与灯光\|窗帘\|环境\|摄像头聚合的deviceId |
| devicesHidden | string[] | 非必须 | 需要由"展示"变更为"隐藏"的设备id集合，参与灯光\|窗帘\|环境\|摄像头聚合的deviceId |

## 使用示例

### 1. 直接使用API类

```objc
#import "UpEditDeviceAggregationApi.h"

// 创建API实例
NSArray<NSString *> *devicesDisplayed = @[@"device1", @"device2"];
NSArray<NSString *> *devicesHidden = @[@"device3", @"device4"];

UpSetAggregationDeviceHomepageHiddenApi *api = 
    [[UpSetAggregationDeviceHomepageHiddenApi alloc] initWithFamilyId:@"familyId123"
                                                     devicesDisplayed:devicesDisplayed
                                                        devicesHidden:devicesHidden];

// 设置响应解析器
api.responseParser = [[CommonDataTransformer alloc] init];

// 发起请求
[DataSourceCallbackFunctions startApi:api 
                              success:^(UserDomainSampleResult *result) {
                                  NSLog(@"设置成功");
                              } 
                              failure:^(UserDomainSampleResult *result) {
                                  NSLog(@"设置失败: %@", result.error.localizedDescription);
                              }];
```

### 2. 通过数据源使用

```objc
#import "UpFamilyDataSource.h"

// 获取数据源
id<UpFamilyDataSource> dataSource = [provider provideFamilyDataSource];

// 调用接口
NSArray<NSString *> *devicesDisplayed = @[@"device1", @"device2"];
NSArray<NSString *> *devicesHidden = @[@"device3", @"device4"];

[dataSource setAggregationDeviceHomepageHidden:@"familyId123"
                              devicesDisplayed:devicesDisplayed
                                 devicesHidden:devicesHidden
                                       success:^(UserDomainSampleResult *result) {
                                           NSLog(@"设置成功");
                                       }
                                       failure:^(UserDomainSampleResult *result) {
                                           NSLog(@"设置失败: %@", result.error.localizedDescription);
                                       }];
```

### 3. 通过Family类使用

```objc
#import "Family.h"
#import "AggregationDeviceHomepageHiddenArgs.h"

// 创建参数对象
AggregationDeviceHomepageHiddenArgs *args = [[AggregationDeviceHomepageHiddenArgs alloc] init];
args.familyId = @"familyId123";
args.devicesDisplayed = @[@"device1", @"device2"];
args.devicesHidden = @[@"device3", @"device4"];

// 调用Family方法
[family setAggregationDeviceHomepageHidden:args
                                   success:^(UserDomainSampleResult *result) {
                                       NSLog(@"设置成功");
                                   }
                                   failure:^(UserDomainSampleResult *result) {
                                       NSLog(@"设置失败: %@", result.error.localizedDescription);
                                   }];
```

## 注意事项

1. `familyId` 参数是必须的，不能为空
2. `devicesDisplayed` 和 `devicesHidden` 至少需要提供其中一个参数
3. 设备ID应该是参与灯光、窗帘、环境或摄像头聚合的deviceId
4. 接口会自动进行参数验证，如果参数不符合要求会返回相应的错误信息

## 实现的文件列表

1. **API层**:
   - `UserDomainAPIs/UplusHome/Family/UpEditDeviceAggregationApi.h` (新增API类声明)
   - `UserDomainAPIs/UplusHome/Family/UpEditDeviceAggregationApi.m` (新增API类实现)

2. **数据源层**:
   - `UserDomainDataSource/Delegate/DataSource/UpFamilyDataSource.h` (新增协议方法)
   - `UserDomainDataSource/IMP/DataSource/SampleImp/FamilyDataSource.m` (新增协议实现)

3. **业务层**:
   - `upuserdomain/IMP/Family/AggregationDeviceHomepageHiddenArgs.h` (参数模型)
   - `upuserdomain/IMP/Family/AggregationDeviceHomepageHiddenArgs.m` (参数模型实现)
   - `upuserdomain/IMP/Operator/Family/UDSetAggregationDeviceHomepageHiddenOp.h` (操作类)
   - `upuserdomain/IMP/Operator/Family/UDSetAggregationDeviceHomepageHiddenOp.m` (操作类实现)
   - `upuserdomain/IMP/Family/Family.m` (Family类新增方法)
   - `upuserdomain/Delegate/Family/UDFamilyDelegate.h` (协议新增方法声明)
