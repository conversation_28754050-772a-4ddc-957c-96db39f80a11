// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		2202C8612922130800955D81 /* UDSaveRoomsOrderOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 2202C85F2922130800955D81 /* UDSaveRoomsOrderOp.h */; };
		2202C8622922130800955D81 /* UDSaveRoomsOrderOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 2202C8602922130800955D81 /* UDSaveRoomsOrderOp.m */; };
		2202C8652922156800955D81 /* UPSaveRoomsOrderApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 2202C8632922156800955D81 /* UPSaveRoomsOrderApi.h */; };
		2202C8662922156800955D81 /* UPSaveRoomsOrderApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 2202C8642922156800955D81 /* UPSaveRoomsOrderApi.m */; };
		22037985270FE9D000FB8EB2 /* features in Resources */ = {isa = PBXBuildFile; fileRef = 22037984270FE9D000FB8EB2 /* features */; };
		221087D927705929002D3A6B /* SECloudDevicePermission.h in Headers */ = {isa = PBXBuildFile; fileRef = 221087D727705929002D3A6B /* SECloudDevicePermission.h */; };
		221087DA27705929002D3A6B /* SECloudDevicePermission.m in Sources */ = {isa = PBXBuildFile; fileRef = 221087D827705929002D3A6B /* SECloudDevicePermission.m */; };
		221087DD27705971002D3A6B /* SECloudDeviceAuth.h in Headers */ = {isa = PBXBuildFile; fileRef = 221087DB27705971002D3A6B /* SECloudDeviceAuth.h */; };
		221087DE27705971002D3A6B /* SECloudDeviceAuth.m in Sources */ = {isa = PBXBuildFile; fileRef = 221087DC27705971002D3A6B /* SECloudDeviceAuth.m */; };
		221AA340277EA1A0008143F4 /* ShopUserDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = 221AA33E277EA1A0008143F4 /* ShopUserDataSource.h */; };
		221AA341277EA1A0008143F4 /* ShopUserDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = 221AA33F277EA1A0008143F4 /* ShopUserDataSource.m */; };
		221AA346277EA6F4008143F4 /* ShopRefreshTokenApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 221AA344277EA6F4008143F4 /* ShopRefreshTokenApi.h */; };
		221AA347277EA6F4008143F4 /* ShopRefreshTokenApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 221AA345277EA6F4008143F4 /* ShopRefreshTokenApi.m */; };
		221AA34B277ED064008143F4 /* ShopServerAPIBase.h in Headers */ = {isa = PBXBuildFile; fileRef = 221AA349277ED064008143F4 /* ShopServerAPIBase.h */; };
		221AA34C277ED064008143F4 /* ShopServerAPIBase.m in Sources */ = {isa = PBXBuildFile; fileRef = 221AA34A277ED064008143F4 /* ShopServerAPIBase.m */; };
		221AA34F277ED317008143F4 /* ShopOauthDataTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = 221AA34D277ED317008143F4 /* ShopOauthDataTransformer.h */; };
		221AA350277ED317008143F4 /* ShopOauthDataTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 221AA34E277ED317008143F4 /* ShopOauthDataTransformer.m */; };
		223223ED28B5F36B00A7A0E7 /* UPModifyVirtualMemberRoleApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 223223EB28B5F36B00A7A0E7 /* UPModifyVirtualMemberRoleApi.h */; };
		223223EE28B5F36B00A7A0E7 /* UPModifyVirtualMemberRoleApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 223223EC28B5F36B00A7A0E7 /* UPModifyVirtualMemberRoleApi.m */; };
		223223F128B5F54500A7A0E7 /* UDModifyVirtualMemberRoleOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 223223EF28B5F54500A7A0E7 /* UDModifyVirtualMemberRoleOp.h */; };
		223223F228B5F54500A7A0E7 /* UDModifyVirtualMemberRoleOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 223223F028B5F54500A7A0E7 /* UDModifyVirtualMemberRoleOp.m */; };
		2252139428A1F1F0001D2832 /* UDModifyMemberRoleOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 2252139228A1F1F0001D2832 /* UDModifyMemberRoleOp.h */; };
		2252139528A1F1F0001D2832 /* UDModifyMemberRoleOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 2252139328A1F1F0001D2832 /* UDModifyMemberRoleOp.m */; };
		2252139828A1F4F3001D2832 /* UPModifyMemberRoleApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 2252139628A1F4F3001D2832 /* UPModifyMemberRoleApi.h */; };
		2252139928A1F4F3001D2832 /* UPModifyMemberRoleApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 2252139728A1F4F3001D2832 /* UPModifyMemberRoleApi.m */; };
		2252139C28A20968001D2832 /* VirtualMemberArgs.h in Headers */ = {isa = PBXBuildFile; fileRef = 2252139A28A20968001D2832 /* VirtualMemberArgs.h */; };
		2252139D28A20968001D2832 /* VirtualMemberArgs.m in Sources */ = {isa = PBXBuildFile; fileRef = 2252139B28A20968001D2832 /* VirtualMemberArgs.m */; };
		225BA55C27F19B3D0076B9ED /* CommonDataTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = 225BA55A27F19B3D0076B9ED /* CommonDataTransformer.h */; };
		225BA55D27F19B3D0076B9ED /* CommonDataTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 225BA55B27F19B3D0076B9ED /* CommonDataTransformer.m */; };
		22B3B32328DD51C7007EF767 /* SEUnbindDeviceTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = 22B3B32128DD51C7007EF767 /* SEUnbindDeviceTransformer.h */; };
		22B3B32428DD51C7007EF767 /* SEUnbindDeviceTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 22B3B32228DD51C7007EF767 /* SEUnbindDeviceTransformer.m */; };
		22BF11B527C5CFC000EB43F5 /* ShopLogoutApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 22BF11B327C5CFC000EB43F5 /* ShopLogoutApi.m */; };
		22BF11B627C5CFC000EB43F5 /* ShopLogoutApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 22BF11B427C5CFC000EB43F5 /* ShopLogoutApi.h */; };
		22EBEA382914A599007DA887 /* UDGetGroupDevicesOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 22EBEA362914A599007DA887 /* UDGetGroupDevicesOp.h */; };
		22EBEA392914A599007DA887 /* UDGetGroupDevicesOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 22EBEA372914A599007DA887 /* UDGetGroupDevicesOp.m */; };
		22EBEA3C2914A91A007DA887 /* UPGetGroupDevicesApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 22EBEA3A2914A91A007DA887 /* UPGetGroupDevicesApi.h */; };
		22EBEA3D2914A91A007DA887 /* UPGetGroupDevicesApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 22EBEA3B2914A91A007DA887 /* UPGetGroupDevicesApi.m */; };
		22F6FDC127953D490048F32F /* UPQueryFirstMemeberApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F6FDBF27953D490048F32F /* UPQueryFirstMemeberApi.h */; };
		22F6FDC227953D490048F32F /* UPQueryFirstMemeberApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F6FDC027953D490048F32F /* UPQueryFirstMemeberApi.m */; };
		22F6FDC527953D810048F32F /* UPAddVirtualMemberApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F6FDC327953D810048F32F /* UPAddVirtualMemberApi.h */; };
		22F6FDC627953D810048F32F /* UPAddVirtualMemberApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F6FDC427953D810048F32F /* UPAddVirtualMemberApi.m */; };
		22F6FDC927953DB20048F32F /* UPModifyVirtualMemberApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F6FDC727953DB20048F32F /* UPModifyVirtualMemberApi.h */; };
		22F6FDCA27953DB20048F32F /* UPModifyVirtualMemberApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F6FDC827953DB20048F32F /* UPModifyVirtualMemberApi.m */; };
		22F6FDCD27965D3B0048F32F /* UDQueryFirstMemeberOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F6FDCB27965D3B0048F32F /* UDQueryFirstMemeberOp.h */; };
		22F6FDCE27965D3B0048F32F /* UDQueryFirstMemeberOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F6FDCC27965D3B0048F32F /* UDQueryFirstMemeberOp.m */; };
		22F6FDD127965D610048F32F /* UDAddVirtualMemberOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F6FDCF27965D610048F32F /* UDAddVirtualMemberOp.h */; };
		22F6FDD227965D610048F32F /* UDAddVirtualMemberOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F6FDD027965D610048F32F /* UDAddVirtualMemberOp.m */; };
		22F6FDD527965D820048F32F /* UDModifyVirtualMemberOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F6FDD327965D820048F32F /* UDModifyVirtualMemberOp.h */; };
		22F6FDD627965D820048F32F /* UDModifyVirtualMemberOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F6FDD427965D820048F32F /* UDModifyVirtualMemberOp.m */; };
		22F6FDD92796A54B0048F32F /* QueryFirstMemebeTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = 22F6FDD72796A54B0048F32F /* QueryFirstMemebeTransformer.h */; };
		22F6FDDA2796A54B0048F32F /* QueryFirstMemebeTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 22F6FDD82796A54B0048F32F /* QueryFirstMemebeTransformer.m */; };
		334AFE1C267343C0003F443B /* SYNServerAPIBase.h in Headers */ = {isa = PBXBuildFile; fileRef = 334AFE1A267343C0003F443B /* SYNServerAPIBase.h */; };
		334AFE1D267343C0003F443B /* SYNServerAPIBase.m in Sources */ = {isa = PBXBuildFile; fileRef = 334AFE1B267343C0003F443B /* SYNServerAPIBase.m */; };
		5449202F8516A4E93B045C5D /* libPods-upuserdomain.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 17C5270E6A526BD76942F08C /* libPods-upuserdomain.a */; };
		6552C1C10E47580097C520E8 /* libPods-UserDomainDataSource.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 947E29A54A41CE9CFD62B7D4 /* libPods-UserDomainDataSource.a */; };
		71035FDF24FC858100A9D036 /* DeviceListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 71035FDE24FC858100A9D036 /* DeviceListViewController.m */; };
		71035FE224FC8AD800A9D036 /* DeviceDetailViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 71035FE124FC8AD800A9D036 /* DeviceDetailViewController.m */; };
		71035FE524FC918A00A9D036 /* FamilySelectionViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 71035FE424FC918A00A9D036 /* FamilySelectionViewController.m */; };
		7155373D24F5F9FA00BAA7D7 /* UserInfoEditViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7155373B24F5F8EA00BAA7D7 /* UserInfoEditViewController.m */; };
		7155374024F5FFF800BAA7D7 /* LoginSettings.m in Sources */ = {isa = PBXBuildFile; fileRef = 7155373F24F5FFF800BAA7D7 /* LoginSettings.m */; };
		7155374324F6467600BAA7D7 /* SEUserLoginApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 7155374224F6467600BAA7D7 /* SEUserLoginApi.m */; };
		71AF42D724B5AF4D007A8372 /* libsqlite3.0.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 71AF42D624B5AF4D007A8372 /* libsqlite3.0.tbd */; };
		71AF42D924B5AF56007A8372 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 71AF42D824B5AF56007A8372 /* libresolv.tbd */; };
		71B045BB24F34ED0001833D0 /* LoginViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 71B045BA24F34ED0001833D0 /* LoginViewController.m */; };
		71B381EB24F4AF7C00F04E48 /* FunctionsTableViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 71B381EA24F4AF7C00F04E48 /* FunctionsTableViewController.m */; };
		71B381EE24F4B43900F04E48 /* UserDetailViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 71B381ED24F4B43900F04E48 /* UserDetailViewController.m */; };
		71ECF944241A2C0D009BBAD0 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 71ECF943241A2C0D009BBAD0 /* AppDelegate.m */; };
		71ECF94A241A2C0D009BBAD0 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 71ECF949241A2C0D009BBAD0 /* ViewController.m */; };
		71ECF94D241A2C0D009BBAD0 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 71ECF94B241A2C0D009BBAD0 /* Main.storyboard */; };
		71ECF94F241A2C0D009BBAD0 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 71ECF94E241A2C0D009BBAD0 /* Assets.xcassets */; };
		71ECF952241A2C0D009BBAD0 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 71ECF950241A2C0D009BBAD0 /* LaunchScreen.storyboard */; };
		71ECF955241A2C0D009BBAD0 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 71ECF954241A2C0D009BBAD0 /* main.m */; };
		72E530E822643E09008992A1 /* CucumberRunner.m in Sources */ = {isa = PBXBuildFile; fileRef = 72E530E722643E09008992A1 /* CucumberRunner.m */; };
		84B5D4AD2ABAE35F00C07E3F /* UPDeviceUpdateAndCheckNameApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 84B5D4AB2ABAE35F00C07E3F /* UPDeviceUpdateAndCheckNameApi.h */; };
		84B5D4AE2ABAE35F00C07E3F /* UPDeviceUpdateAndCheckNameApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 84B5D4AC2ABAE35F00C07E3F /* UPDeviceUpdateAndCheckNameApi.m */; };
		84B5D4B12ABAED2400C07E3F /* UDModifyDeviceNameNewOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 84B5D4AF2ABAED2400C07E3F /* UDModifyDeviceNameNewOp.h */; };
		84B5D4B22ABAED2400C07E3F /* UDModifyDeviceNameNewOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 84B5D4B02ABAED2400C07E3F /* UDModifyDeviceNameNewOp.m */; };
		950119F12DA9189900F92F77 /* UPModifyMemberTypeApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 950119EF2DA9189900F92F77 /* UPModifyMemberTypeApi.h */; };
		950119F22DA9189900F92F77 /* UPModifyMemberTypeApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 950119F02DA9189900F92F77 /* UPModifyMemberTypeApi.m */; };
		950119F52DA919E700F92F77 /* UDModifyMemberTypeOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 950119F32DA919E700F92F77 /* UDModifyMemberTypeOp.h */; };
		950119F62DA919E700F92F77 /* UDModifyMemberTypeOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 950119F42DA919E700F92F77 /* UDModifyMemberTypeOp.m */; };
		9524B7742D9398A2009AA5C4 /* DeviceCardStatusArgs.h in Headers */ = {isa = PBXBuildFile; fileRef = 9524B7722D9398A2009AA5C4 /* DeviceCardStatusArgs.h */; };
		9524B7752D9398A2009AA5C4 /* DeviceCardStatusArgs.m in Sources */ = {isa = PBXBuildFile; fileRef = 9524B7732D9398A2009AA5C4 /* DeviceCardStatusArgs.m */; };
		9524B7782D93991D009AA5C4 /* DeviceCardAggregationArgs.h in Headers */ = {isa = PBXBuildFile; fileRef = 9524B7762D93991D009AA5C4 /* DeviceCardAggregationArgs.h */; };
		9524B7792D93991D009AA5C4 /* DeviceCardAggregationArgs.m in Sources */ = {isa = PBXBuildFile; fileRef = 9524B7772D93991D009AA5C4 /* DeviceCardAggregationArgs.m */; };
		9524B77C2D939A32009AA5C4 /* AggregationSwitchArgs.h in Headers */ = {isa = PBXBuildFile; fileRef = 9524B77A2D939A32009AA5C4 /* AggregationSwitchArgs.h */; };
		9524B77D2D939A32009AA5C4 /* AggregationSwitchArgs.m in Sources */ = {isa = PBXBuildFile; fileRef = 9524B77B2D939A32009AA5C4 /* AggregationSwitchArgs.m */; };
		955ED3F42DA7DBD300E10807 /* UpAddRoomToFamilyNewApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 955ED3F32DA7DBD300E10807 /* UpAddRoomToFamilyNewApi.m */; };
		955ED3F52DA7DBD300E10807 /* UpAddRoomToFamilyNewApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 955ED3F22DA7DBD300E10807 /* UpAddRoomToFamilyNewApi.h */; };
		955ED3F82DA7DDF500E10807 /* RoomNewArgs.h in Headers */ = {isa = PBXBuildFile; fileRef = 955ED3F62DA7DDF500E10807 /* RoomNewArgs.h */; };
		955ED3F92DA7DDF500E10807 /* RoomNewArgs.m in Sources */ = {isa = PBXBuildFile; fileRef = 955ED3F72DA7DDF500E10807 /* RoomNewArgs.m */; };
		955ED3FC2DA7E06F00E10807 /* UDCreateRoomNewOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 955ED3FA2DA7E06F00E10807 /* UDCreateRoomNewOp.h */; };
		955ED3FD2DA7E06F00E10807 /* UDCreateRoomNewOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 955ED3FB2DA7E06F00E10807 /* UDCreateRoomNewOp.m */; };
		95BEBB162D92951E001A07D7 /* UpEditDeviceCardStatusApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 95BEBB142D92951E001A07D7 /* UpEditDeviceCardStatusApi.h */; };
		95BEBB172D92951E001A07D7 /* UpEditDeviceCardStatusApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 95BEBB152D92951E001A07D7 /* UpEditDeviceCardStatusApi.m */; };
		95BEBB1A2D9299DA001A07D7 /* UpEditDeviceAggregationApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 95BEBB182D9299DA001A07D7 /* UpEditDeviceAggregationApi.h */; };
		95BEBB1B2D9299DA001A07D7 /* UpEditDeviceAggregationApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 95BEBB192D9299DA001A07D7 /* UpEditDeviceAggregationApi.m */; };
		95BEBB1E2D929EF7001A07D7 /* UpEditAggregationSwitchApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 95BEBB1C2D929EF7001A07D7 /* UpEditAggregationSwitchApi.h */; };
		95BEBB1F2D929EF7001A07D7 /* UpEditAggregationSwitchApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 95BEBB1D2D929EF7001A07D7 /* UpEditAggregationSwitchApi.m */; };
		95BEBB222D92CCF8001A07D7 /* UDModifyDeviceCardListOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 95BEBB202D92CCF8001A07D7 /* UDModifyDeviceCardListOp.h */; };
		95BEBB232D92CCF8001A07D7 /* UDModifyDeviceCardListOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 95BEBB212D92CCF8001A07D7 /* UDModifyDeviceCardListOp.m */; };
		95BEBB262D92CFEE001A07D7 /* UDModifyDeviceAggOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 95BEBB242D92CFEE001A07D7 /* UDModifyDeviceAggOp.h */; };
		95BEBB272D92CFEE001A07D7 /* UDModifyDeviceAggOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 95BEBB252D92CFEE001A07D7 /* UDModifyDeviceAggOp.m */; };
		95BEBB2A2D92D1E2001A07D7 /* UDModifyAggSwitchOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 95BEBB282D92D1E2001A07D7 /* UDModifyAggSwitchOp.h */; };
		95BEBB2B2D92D1E2001A07D7 /* UDModifyAggSwitchOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 95BEBB292D92D1E2001A07D7 /* UDModifyAggSwitchOp.m */; };
		95EC2CAA2DB61D3D00E22D0E /* UPSaveRoomsOrderNewApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 95EC2CA82DB61D3D00E22D0E /* UPSaveRoomsOrderNewApi.h */; };
		95EC2CAB2DB61D3D00E22D0E /* UPSaveRoomsOrderNewApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 95EC2CA92DB61D3D00E22D0E /* UPSaveRoomsOrderNewApi.m */; };
		95EC2CAE2DB621D400E22D0E /* UDSaveRoomsOrderNewOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 95EC2CAC2DB621D400E22D0E /* UDSaveRoomsOrderNewOp.h */; };
		95EC2CAF2DB621D400E22D0E /* UDSaveRoomsOrderNewOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 95EC2CAD2DB621D400E22D0E /* UDSaveRoomsOrderNewOp.m */; };
		95F07CA82DB255AB007CB875 /* AdminInviteMemberArgs.h in Headers */ = {isa = PBXBuildFile; fileRef = 95F07CA62DB255AB007CB875 /* AdminInviteMemberArgs.h */; };
		95F07CA92DB255AB007CB875 /* AdminInviteMemberArgs.m in Sources */ = {isa = PBXBuildFile; fileRef = 95F07CA72DB255AB007CB875 /* AdminInviteMemberArgs.m */; };
		AD12D5E326438A5F00188BC3 /* APPServerAPIBase.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF424524B5AD6B007A8372 /* APPServerAPIBase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D5E426438A5F00188BC3 /* ZJServerAPIBase.h in Headers */ = {isa = PBXBuildFile; fileRef = F8F22325257B36E900222539 /* ZJServerAPIBase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D5E526438A5F00188BC3 /* UserCenterAPIBase.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF424424B5AD6B007A8372 /* UserCenterAPIBase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D5E626438A5F00188BC3 /* UPDeviceListApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF424924B5AD6B007A8372 /* UPDeviceListApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D5E726438A5F00188BC3 /* UPDeviceUpdateNameApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF424A24B5AD6B007A8372 /* UPDeviceUpdateNameApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D5E826438A5F00188BC3 /* UPUpdateDeviceInfoApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF424724B5AD6B007A8372 /* UPUpdateDeviceInfoApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D5E926438A5F00188BC3 /* UserDomainAPIs.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41BD24B5ACD5007A8372 /* UserDomainAPIs.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D5EA26438AB800188BC3 /* SEUserInfoAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41D024B5AD6A007A8372 /* SEUserInfoAPI.m */; };
		AD12D5EB26438AB800188BC3 /* SEUserLogoutAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41C924B5AD6A007A8372 /* SEUserLogoutAPI.m */; };
		AD12D5EC26438AB800188BC3 /* SEUserModifyAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41CF24B5AD6A007A8372 /* SEUserModifyAPI.m */; };
		AD12D5ED26438AB800188BC3 /* SEUserRefreshTokenAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41CC24B5AD6A007A8372 /* SEUserRefreshTokenAPI.m */; };
		AD12D5EE26438AB800188BC3 /* SEEncryption.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41D524B5AD6A007A8372 /* SEEncryption.m */; };
		AD12D5EF26438AB800188BC3 /* SERequestBase.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41D724B5AD6A007A8372 /* SERequestBase.m */; };
		AD12D5F026438AB800188BC3 /* SERequestConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41D424B5AD6A007A8372 /* SERequestConfig.m */; };
		AD12D5F126438AB800188BC3 /* SECloudDeviceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41DB24B5AD6B007A8372 /* SECloudDeviceInfo.m */; };
		AD12D5F226438AB800188BC3 /* SECloudUserAuthInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41DD24B5AD6B007A8372 /* SECloudUserAuthInfo.m */; };
		AD12D5F326438AB800188BC3 /* SECloudUserInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41DC24B5AD6B007A8372 /* SECloudUserInfo.m */; };
		AD12D5F426438AB800188BC3 /* SEModifyDeviceInfoAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41E324B5AD6B007A8372 /* SEModifyDeviceInfoAPI.m */; };
		AD12D5F526438AB800188BC3 /* SEQueryDeviceListAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41E424B5AD6B007A8372 /* SEQueryDeviceListAPI.m */; };
		AD12D5F626438AB800188BC3 /* SEUnbindDeviceAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41E024B5AD6B007A8372 /* SEUnbindDeviceAPI.m */; };
		AD12D5F726438AB800188BC3 /* UPAddRoomToFamilyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF420E24B5AD6B007A8372 /* UPAddRoomToFamilyApi.m */; };
		AD12D5F826438AB800188BC3 /* UPChangeFamilyAdminApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF420F24B5AD6B007A8372 /* UPChangeFamilyAdminApi.m */; };
		AD12D5F926438AB800188BC3 /* UPCreateFamilyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF420B24B5AD6B007A8372 /* UPCreateFamilyApi.m */; };
		AD12D5FA26438AB800188BC3 /* UPdateFamilyInfoApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41F524B5AD6B007A8372 /* UPdateFamilyInfoApi.m */; };
		AD12D5FB26438AB800188BC3 /* UPDestroyFamilyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41FB24B5AD6B007A8372 /* UPDestroyFamilyApi.m */; };
		AD12D5FC26438AB800188BC3 /* UPFamilyDeleteMemberApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF420024B5AD6B007A8372 /* UPFamilyDeleteMemberApi.m */; };
		AD12D5FD26438AB800188BC3 /* UPFamilyInfoApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41FD24B5AD6B007A8372 /* UPFamilyInfoApi.m */; };
		AD12D5FE26438AB800188BC3 /* UPFamilyInviteMemberApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF420C24B5AD6B007A8372 /* UPFamilyInviteMemberApi.m */; };
		AD12D5FF26438AB800188BC3 /* UPFamilyListApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41FC24B5AD6B007A8372 /* UPFamilyListApi.m */; };
		AD12D60026438AB800188BC3 /* UPFamilyMemberShareDeviceCountApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF420524B5AD6B007A8372 /* UPFamilyMemberShareDeviceCountApi.m */; };
		AD12D60126438AB800188BC3 /* UPFamilyMoveDevicesToNewRoomApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41F924B5AD6B007A8372 /* UPFamilyMoveDevicesToNewRoomApi.m */; };
		AD12D60226438AB800188BC3 /* UPFamilyMoveDevicesToOtherFamilyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41F124B5AD6B007A8372 /* UPFamilyMoveDevicesToOtherFamilyApi.m */; };
		AD12D60326438AB800188BC3 /* UPFamilyRemoveDevicesApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41ED24B5AD6B007A8372 /* UPFamilyRemoveDevicesApi.m */; };
		AD12D60426438AB800188BC3 /* UPFamilyReplyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF421324B5AD6B007A8372 /* UPFamilyReplyApi.m */; };
		AD12D60526438AB800188BC3 /* UPReplyJoinFamilyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 22E6298C25F0C07800912AA8 /* UPReplyJoinFamilyApi.m */; };
		AD12D60626438AB800188BC3 /* UPFamilyRoomListApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF420724B5AD6B007A8372 /* UPFamilyRoomListApi.m */; };
		AD12D60726438AB800188BC3 /* UPFamilyUnbindDevicesApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41E924B5AD6B007A8372 /* UPFamilyUnbindDevicesApi.m */; };
		AD12D60826438AB800188BC3 /* UPQuitFamilyAsAdminApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41EF24B5AD6B007A8372 /* UPQuitFamilyAsAdminApi.m */; };
		AD12D60926438AB800188BC3 /* UPQuitFamilyAsMemberApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF420324B5AD6B007A8372 /* UPQuitFamilyAsMemberApi.m */; };
		AD12D60A26438AB800188BC3 /* UPRemoveRoomFromFamilyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41EA24B5AD6B007A8372 /* UPRemoveRoomFromFamilyApi.m */; };
		************************ /* UPSetDefaultFamilyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF420424B5AD6B007A8372 /* UPSetDefaultFamilyApi.m */; };
		************************ /* UPUpdateFamilyRoomNameApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF41FE24B5AD6B007A8372 /* UPUpdateFamilyRoomNameApi.m */; };
		************************ /* UPAddFloorApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F8F2232A257B388200222539 /* UPAddFloorApi.m */; };
		************************ /* UPEditFloorApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F8F2232E257B389B00222539 /* UPEditFloorApi.m */; };
		************************ /* UPDeleteFloorApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F8F22332257B38AF00222539 /* UPDeleteFloorApi.m */; };
		************************ /* UPUserAccountVerifyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF421B24B5AD6B007A8372 /* UPUserAccountVerifyApi.m */; };
		************************ /* UPUserAddressApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF423B24B5AD6B007A8372 /* UPUserAddressApi.m */; };
		************************ /* UPUserApplitionTokenApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF421A24B5AD6B007A8372 /* UPUserApplitionTokenApi.m */; };
		************************ /* UPUserCreateNewAddressApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF423024B5AD6B007A8372 /* UPUserCreateNewAddressApi.m */; };
		************************ /* UPUserDeleteAddressApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF422D24B5AD6B007A8372 /* UPUserDeleteAddressApi.m */; };
		************************ /* UPUserEditAddressApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF423E24B5AD6B007A8372 /* UPUserEditAddressApi.m */; };
		************************ /* UPUserInfoApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF422324B5AD6B007A8372 /* UPUserInfoApi.m */; };
		************************ /* UPUserLoginLogsApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF421E24B5AD6B007A8372 /* UPUserLoginLogsApi.m */; };
		AD12D61926438AB800188BC3 /* UPUserLoginTerminalApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF421924B5AD6B007A8372 /* UPUserLoginTerminalApi.m */; };
		AD12D61A26438AB800188BC3 /* UPUserQRCancleLoginApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF422C24B5AD6B007A8372 /* UPUserQRCancleLoginApi.m */; };
		AD12D61B26438AB800188BC3 /* UPUserQRConfirmLoginApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF423C24B5AD6B007A8372 /* UPUserQRConfirmLoginApi.m */; };
		AD12D61C26438AB800188BC3 /* UPUserQRloginPollApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF423624B5AD6B007A8372 /* UPUserQRloginPollApi.m */; };
		AD12D61D26438AB800188BC3 /* UPUserQRScanApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF423824B5AD6B007A8372 /* UPUserQRScanApi.m */; };
		AD12D61E26438AB800188BC3 /* UPUserRefreshTokenApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF422A24B5AD6B007A8372 /* UPUserRefreshTokenApi.m */; };
		AD12D61F26438AB800188BC3 /* UPUserServiceOrderApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF421F24B5AD6B007A8372 /* UPUserServiceOrderApi.m */; };
		AD12D62026438AB800188BC3 /* UPUserUpdatePwdApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF422724B5AD6B007A8372 /* UPUserUpdatePwdApi.m */; };
		AD12D62126438AB800188BC3 /* UPUserUpdateUserInfoApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF423F24B5AD6B007A8372 /* UPUserUpdateUserInfoApi.m */; };
		AD12D62226438AB800188BC3 /* UPUserUploadAvatarApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF423724B5AD6B007A8372 /* UPUserUploadAvatarApi.m */; };
		AD12D62326438AB800188BC3 /* UPZJRefreshTokenApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF422B24B5AD6B007A8372 /* UPZJRefreshTokenApi.m */; };
		AD12D62426438AB800188BC3 /* UPZJUserInfoApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF423424B5AD6B007A8372 /* UPZJUserInfoApi.m */; };
		AD12D62526438AB800188BC3 /* UPZJUserLogOutApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 2248A0AD25E10B8B00F7EF79 /* UPZJUserLogOutApi.m */; };
		AD12D62626438AB800188BC3 /* APPServerAPIBase.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF424324B5AD6B007A8372 /* APPServerAPIBase.m */; };
		AD12D62726438AB800188BC3 /* ZJServerAPIBase.m in Sources */ = {isa = PBXBuildFile; fileRef = F8F22326257B36E900222539 /* ZJServerAPIBase.m */; };
		AD12D62826438AB800188BC3 /* UserCenterAPIBase.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF424224B5AD6B007A8372 /* UserCenterAPIBase.m */; };
		AD12D62926438AB800188BC3 /* UPDeviceListApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF424C24B5AD6C007A8372 /* UPDeviceListApi.m */; };
		AD12D62A26438AB800188BC3 /* UPDeviceUpdateNameApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF424824B5AD6B007A8372 /* UPDeviceUpdateNameApi.m */; };
		AD12D62B26438AB800188BC3 /* UPUpdateDeviceInfoApi.m in Sources */ = {isa = PBXBuildFile; fileRef = 71AF424B24B5AD6C007A8372 /* UPUpdateDeviceInfoApi.m */; };
		AD12D62D26438ACD00188BC3 /* readme.md in Resources */ = {isa = PBXBuildFile; fileRef = 71ECF7FD241A109F009BBAD0 /* readme.md */; };
		AD12D62E26438ACD00188BC3 /* release.md in Resources */ = {isa = PBXBuildFile; fileRef = 71ECF7FE241A109F009BBAD0 /* release.md */; };
		AD12D6412643900500188BC3 /* libUserDomainAPIs.a in Frameworks */ = {isa = PBXBuildFile; fileRef = ADB50C7F2643848F00062881 /* libUserDomainAPIs.a */; };
		AD12D6462643910500188BC3 /* UpUserDomainListenerDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D444024CFB4C800211C3F /* UpUserDomainListenerDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6472643910500188BC3 /* UpEventHandlerDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D446B24D1238600211C3F /* UpEventHandlerDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6482643910500188BC3 /* UpEventHandlerManagerDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D449124D1752C00211C3F /* UpEventHandlerManagerDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6492643910500188BC3 /* UDCache.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF30524CAB2BE008705B3 /* UDCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D64A2643910500188BC3 /* UpUserDomainCacheDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D448F24D16D9200211C3F /* UpUserDomainCacheDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D64B2643910500188BC3 /* UDFamilyDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF30924CAB2BE008705B3 /* UDFamilyDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D64C2643910500188BC3 /* UDFamilyInfoDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF30C24CAB2BE008705B3 /* UDFamilyInfoDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D64D2643910500188BC3 /* UDFamilyLocationDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF30824CAB2BE008705B3 /* UDFamilyLocationDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D64E2643910500188BC3 /* UDFamilyMemberDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF30A24CAB2BE008705B3 /* UDFamilyMemberDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D64F2643910500188BC3 /* UDMemberInfoDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF30B24CAB2BE008705B3 /* UDMemberInfoDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6502643910500188BC3 /* UDRoomDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF30724CAB2BE008705B3 /* UDRoomDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6512643910500188BC3 /* UDFloorInfoDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8E2BAB125775667007F3801 /* UDFloorInfoDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6522643910500188BC3 /* FloorArg.h in Headers */ = {isa = PBXBuildFile; fileRef = F81273C52579083400995F1E /* FloorArg.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6532643910500188BC3 /* FloorArg.m in Headers */ = {isa = PBXBuildFile; fileRef = F81273C62579083400995F1E /* FloorArg.m */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6542643910500188BC3 /* UDAddressDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF30E24CAB2BE008705B3 /* UDAddressDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6552643910500188BC3 /* UDLastLoginInfoDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF31124CAB2BE008705B3 /* UDLastLoginInfoDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6562643910500188BC3 /* UDUserAddressDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF31324CAB2BE008705B3 /* UDUserAddressDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6572643910500188BC3 /* UDUserDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF31224CAB2BE008705B3 /* UDUserDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6582643910500188BC3 /* UDUserInfoDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF31424CAB2BE008705B3 /* UDUserInfoDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6592643910500188BC3 /* UDUserLoginLogInfoDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF31024CAB2BE008705B3 /* UDUserLoginLogInfoDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D65A2643910500188BC3 /* UDUserTermDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF30F24CAB2BE008705B3 /* UDUserTermDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D65B2643910500188BC3 /* UPTimeMillisDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF31624CAB2BE008705B3 /* UPTimeMillisDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D65C2643910500188BC3 /* UDDeviceAuthDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF31A24CAB2BE008705B3 /* UDDeviceAuthDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D65D2643910500188BC3 /* UDDeviceDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF31C24CAB2BE008705B3 /* UDDeviceDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D65E2643910500188BC3 /* UDDeviceInfoDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF31824CAB2BE008705B3 /* UDDeviceInfoDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D65F2643910500188BC3 /* UDDeviceOwnerInfoDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF31924CAB2BE008705B3 /* UDDeviceOwnerInfoDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6602643910500188BC3 /* UDDevicePermissionDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF31B24CAB2BE008705B3 /* UDDevicePermissionDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6612643910500188BC3 /* UDAuthDataDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF31E24CAB2BE008705B3 /* UDAuthDataDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6622643910500188BC3 /* UDPlanRefreshTokenTaskDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F87750692589CDDF00D8A76D /* UDPlanRefreshTokenTaskDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6632643910500188BC3 /* UpUserDomainResult.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF32024CAB2BE008705B3 /* UpUserDomainResult.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6642643910500188BC3 /* UpUserDomainDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF32224CAB2BE008705B3 /* UpUserDomainDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6652643910500188BC3 /* UpUserDomainObserver.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF32324CAB2BE008705B3 /* UpUserDomainObserver.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6662643910500188BC3 /* UpUserDomainProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF40624CACBF7008705B3 /* UpUserDomainProvider.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6672643910500188BC3 /* UDUserModelTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = F82E589D24F35F7A0026E454 /* UDUserModelTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6682643910500188BC3 /* UDServerCacheManager.h in Headers */ = {isa = PBXBuildFile; fileRef = F82E58A324F3A4960026E454 /* UDServerCacheManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6692643910500188BC3 /* UpUserDomainStore.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D447524D1475500211C3F /* UpUserDomainStore.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D66A2643910500188BC3 /* UpUserStore.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D449724D19B9000211C3F /* UpUserStore.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D66B2643910500188BC3 /* UpDeviceStore.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D45B624D6DEFD00211C3F /* UpDeviceStore.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D66C2643910500188BC3 /* UpFamilyStore.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D45BA24D6DF0B00211C3F /* UpFamilyStore.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D66D2643910500188BC3 /* UpEvent.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D446724D110ED00211C3F /* UpEvent.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D66E2643910500188BC3 /* UpNotifyEventHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D447124D141D700211C3F /* UpNotifyEventHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D66F2643910500188BC3 /* UpEventHandlerManager.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D45A624D3F1A300211C3F /* UpEventHandlerManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6702643910500188BC3 /* UDOpertateRefreshUserEventHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D6AC6224F4E5CF007EA70A /* UDOpertateRefreshUserEventHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6712643910500188BC3 /* UDOperatePlannedRefreshTokenEventHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = F899FCDF24EBB46200F34E56 /* UDOperatePlannedRefreshTokenEventHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6722643910500188BC3 /* UDOperateRefreshTerminalListEventHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = F8B541B324EE4D620075A070 /* UDOperateRefreshTerminalListEventHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6732643910500188BC3 /* UDOperateRefreshAddressListEventHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = F8B541AF24EE45790075A070 /* UDOperateRefreshAddressListEventHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6742643910500188BC3 /* UDOperateRefreshFamilyListEventHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = F8B541B724EE4E650075A070 /* UDOperateRefreshFamilyListEventHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6762643910500188BC3 /* UDOperateRefreshFamilyDetailEventHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = F8B541BB24EE4E790075A070 /* UDOperateRefreshFamilyDetailEventHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6772643910500188BC3 /* UDOperateRefreshDeviceListEventHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = F8B541BF24EE4ECF0075A070 /* UDOperateRefreshDeviceListEventHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6782643910500188BC3 /* UpListenerTransformObserver.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D45AA24D3FDFD00211C3F /* UpListenerTransformObserver.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6792643910500188BC3 /* UpUserDomainHolder.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF41D24CACD32008705B3 /* UpUserDomainHolder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D67A2643910500188BC3 /* UDCacheIMP.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF33824CAB2BF008705B3 /* UDCacheIMP.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D67B2643910500188BC3 /* UDUserDomainCache.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D45AE24D6D94800211C3F /* UDUserDomainCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D67C2643910500188BC3 /* UserDomainSampleResult.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF38124CAB2BF008705B3 /* UserDomainSampleResult.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D67D2643910500188BC3 /* Device.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF37124CAB2BF008705B3 /* Device.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D67E2643910500188BC3 /* DeviceAuth.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF36E24CAB2BF008705B3 /* DeviceAuth.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D67F2643910500188BC3 /* DeviceAuth+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF37724CAB2BF008705B3 /* DeviceAuth+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6802643910500188BC3 /* UDDeviceInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF37324CAB2BF008705B3 /* UDDeviceInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6812643910500188BC3 /* UDDeviceInfo+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF36D24CAB2BF008705B3 /* UDDeviceInfo+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6822643910500188BC3 /* DeviceOwnerInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF37024CAB2BF008705B3 /* DeviceOwnerInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6832643910500188BC3 /* DeviceOwnerInfo+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF36F24CAB2BF008705B3 /* DeviceOwnerInfo+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6842643910500188BC3 /* DevicePermission.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF37924CAB2BF008705B3 /* DevicePermission.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6852643910500188BC3 /* DevicePermission+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF37424CAB2BF008705B3 /* DevicePermission+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6862643910500188BC3 /* BatchProcessDeviceResult.h in Headers */ = {isa = PBXBuildFile; fileRef = F8FD3879253071EB00D4F9C3 /* BatchProcessDeviceResult.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6872643910500188BC3 /* CreateFamilyArgs.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF34924CAB2BF008705B3 /* CreateFamilyArgs.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6882643910500188BC3 /* Family.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF34B24CAB2BF008705B3 /* Family.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6892643910500188BC3 /* Family+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF34124CAB2BF008705B3 /* Family+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D68A2643910500188BC3 /* FamilyArgs.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF35124CAB2BF008705B3 /* FamilyArgs.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D68B2643910500188BC3 /* FamilyInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF34C24CAB2BF008705B3 /* FamilyInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D68C2643910500188BC3 /* FamilyInfo+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF34E24CAB2BF008705B3 /* FamilyInfo+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D68D2643910500188BC3 /* FamilyLocation.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF33C24CAB2BF008705B3 /* FamilyLocation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D68E2643910500188BC3 /* FamilyLocation+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF34224CAB2BF008705B3 /* FamilyLocation+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D68F2643910500188BC3 /* FamilyMember.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF34324CAB2BF008705B3 /* FamilyMember.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6902643910500188BC3 /* FamilyMember+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF34724CAB2BF008705B3 /* FamilyMember+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6912643910500188BC3 /* MemberInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF34F24CAB2BF008705B3 /* MemberInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6922643910500188BC3 /* MemberInfo+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF33D24CAB2BF008705B3 /* MemberInfo+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6932643910500188BC3 /* Room.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF33E24CAB2BF008705B3 /* Room.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6942643910500188BC3 /* Room+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF34524CAB2BF008705B3 /* Room+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6952643910500188BC3 /* RoomArgs.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF34A24CAB2BF008705B3 /* RoomArgs.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6962643910500188BC3 /* UDFloorInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F81273C925790D7200995F1E /* UDFloorInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6972643910500188BC3 /* UDFloorInfo+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F81273CD25790DBF00995F1E /* UDFloorInfo+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6982643910500188BC3 /* ApplicationOauthData.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF37E24CAB2BF008705B3 /* ApplicationOauthData.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6992643910500188BC3 /* ApplicationOauthData+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF37D24CAB2BF008705B3 /* ApplicationOauthData+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D69A2643910500188BC3 /* UDCreateAddressOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CD11A024E384EB0021DD47 /* UDCreateAddressOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D69B2643910500188BC3 /* UDDeleteAddressOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F805BA7B24E38ECA0056C526 /* UDDeleteAddressOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D69C2643910500188BC3 /* UPEditAddressOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F805BA7F24E3C8FE0056C526 /* UPEditAddressOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D69D2643910500188BC3 /* UDModifyUserInfoOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F805BA8324E3D42B0056C526 /* UDModifyUserInfoOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D69E2643910500188BC3 /* UDPollqrCodeStateOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F805BA8724E404430056C526 /* UDPollqrCodeStateOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D69F2643910500188BC3 /* UDQrCancleLoginOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F805BA8B24E405650056C526 /* UDQrCancleLoginOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6A02643910500188BC3 /* UDQrConfirmLoginOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F805BA8F24E406D80056C526 /* UDQrConfirmLoginOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6A12643910500188BC3 /* UDQrConfirmScanOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F805BA9324E406FA0056C526 /* UDQrConfirmScanOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6A22643910500188BC3 /* UDQueryServiceOrderOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F805BA9724E40FF60056C526 /* UDQueryServiceOrderOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6A32643910500188BC3 /* UDQueryLoginLogsOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F805BA9B24E411A60056C526 /* UDQueryLoginLogsOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6A42643910500188BC3 /* UDRefreshAddressListOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F805BA9F24E413360056C526 /* UDRefreshAddressListOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6A52643910500188BC3 /* UDRefreshTerminalListOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F80B7B5F24E5256F0073FC48 /* UDRefreshTerminalListOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6A62643910500188BC3 /* UDRefreshUserInfoOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F80B7B6324E529AE0073FC48 /* UDRefreshUserInfoOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6A72643910500188BC3 /* UDUpdateAvatarOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F80B7B6F24E53E080073FC48 /* UDUpdateAvatarOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6A92643910500188BC3 /* UDChangeFamilyAdminOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D44B724D26C4000211C3F /* UDChangeFamilyAdminOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6AA2643910500188BC3 /* UDRefreshRoomListOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD62066324D941B200C9B414 /* UDRefreshRoomListOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6AB2643910500188BC3 /* UDCreateFamilyOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD62066724D941C700C9B414 /* UDCreateFamilyOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6AC2643910500188BC3 /* UDCreateRoomOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD62066B24D941D700C9B414 /* UDCreateRoomOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6AD2643910500188BC3 /* UDDeleteFamilyAsAdminOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD62066F24D941E900C9B414 /* UDDeleteFamilyAsAdminOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6AE2643910500188BC3 /* UDDeleteRoomOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD62067324D9420200C9B414 /* UDDeleteRoomOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6AF2643910500188BC3 /* UDEditFamilyInfoOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD62067724D9421100C9B414 /* UDEditFamilyInfoOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6B02643910500188BC3 /* UDEditRoomNameOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD62067B24D9421F00C9B414 /* UDEditRoomNameOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6B12643910500188BC3 /* UDExitFamilyAsAdminOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD62067F24D9422C00C9B414 /* UDExitFamilyAsAdminOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6B22643910500188BC3 /* UDExitFamilyAsMemberOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD62068324D9423D00C9B414 /* UDExitFamilyAsMemberOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6B32643910500188BC3 /* UDDeleteMemberOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F80B7B7724E6532F0073FC48 /* UDDeleteMemberOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6B42643910500188BC3 /* UDInviteFamilyMemberOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD62068724D9425500C9B414 /* UDInviteFamilyMemberOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6B52643910500188BC3 /* UDUnbindDevicesOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F80B7B7B24E659910073FC48 /* UDUnbindDevicesOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6B62643910500188BC3 /* UDMoveDevicesToFamilyOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD62068B24D9426500C9B414 /* UDMoveDevicesToFamilyOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6B72643910500188BC3 /* UDMoveDevicesToRoomOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F80B7B7F24E687440073FC48 /* UDMoveDevicesToRoomOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6B82643910500188BC3 /* UDDeleteDevicesOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F80B7B8324E689FB0073FC48 /* UDDeleteDevicesOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6B92643910500188BC3 /* UDRefreshFamilyListOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F80B7B5B24E50D5F0073FC48 /* UDRefreshFamilyListOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6BA2643910500188BC3 /* UDReplyFamilyInviteOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F80B7B6724E52C500073FC48 /* UDReplyFamilyInviteOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6BB2643910500188BC3 /* UDReplyJoinFamilyOp.h in Headers */ = {isa = PBXBuildFile; fileRef = 22E6298F25F0C8BD00912AA8 /* UDReplyJoinFamilyOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6BC2643910500188BC3 /* UDSetCurrentFamilyOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F80B7B6B24E539CA0073FC48 /* UDSetCurrentFamilyOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6BD2643910500188BC3 /* UDUpdateFamilyDetailOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F80B7B7324E61FE50073FC48 /* UDUpdateFamilyDetailOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6BE2643910500188BC3 /* UDRefreshAllFamilyDetailOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F8B541C324EE5A000075A070 /* UDRefreshAllFamilyDetailOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6BF2643910500188BC3 /* UDAddFloorOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F8304B0E2579D18300E326E5 /* UDAddFloorOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6C02643910500188BC3 /* UDEditFloorOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F8304B122579D1A600E326E5 /* UDEditFloorOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6C12643910500188BC3 /* UDDeleteFloorOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F8304B162579D1C700E326E5 /* UDDeleteFloorOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6C22643910500188BC3 /* UDModifyDeviceNameOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D44A924D253F400211C3F /* UDModifyDeviceNameOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6C32643910500188BC3 /* UDRefreshDeviceListOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D44AB24D253F500211C3F /* UDRefreshDeviceListOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6C42643910500188BC3 /* UDRefreshTokenOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D444F24CFCFAD00211C3F /* UDRefreshTokenOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6C52643910500188BC3 /* UDLogoutOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D44A324D1A40800211C3F /* UDLogoutOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6C62643910500188BC3 /* UDPlannedRefreshAuthDataOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD62068F24D9508500C9B414 /* UDPlannedRefreshAuthDataOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6C82643910500188BC3 /* UDRefreshUserOp.h in Headers */ = {isa = PBXBuildFile; fileRef = AD62069724D950BA00C9B414 /* UDRefreshUserOp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6C92643910600188BC3 /* UDOperatorManager.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D444424CFBF7E00211C3F /* UDOperatorManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6CA2643910600188BC3 /* UDOperator.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF38624CAB2BF008705B3 /* UDOperator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6CB2643910600188BC3 /* UDTimeIMP.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF36A24CAB2BF008705B3 /* UDTimeIMP.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6CE2643910600188BC3 /* User.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF36124CAB2BF008705B3 /* User.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6CF2643910600188BC3 /* UserAddress.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF35E24CAB2BF008705B3 /* UserAddress.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6D02643910600188BC3 /* UserAddress+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF36524CAB2BF008705B3 /* UserAddress+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6D12643910600188BC3 /* UserAddressInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF35F24CAB2BF008705B3 /* UserAddressInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6D22643910600188BC3 /* UserAddressArgs.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF40224CAC4F9008705B3 /* UserAddressArgs.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6D32643910600188BC3 /* UserAddressInfo+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF35C24CAB2BF008705B3 /* UserAddressInfo+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6D42643910600188BC3 /* UserInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF36624CAB2BF008705B3 /* UserInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6D52643910600188BC3 /* UserInfo+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF36324CAB2BF008705B3 /* UserInfo+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6D62643910600188BC3 /* UserInfoArgs.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF36824CAB2BF008705B3 /* UserInfoArgs.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6D72643910600188BC3 /* UserLoginLogInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF35924CAB2BF008705B3 /* UserLoginLogInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6D82643910600188BC3 /* UserLoginLogInfo+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF35524CAB2BF008705B3 /* UserLoginLogInfo+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6D92643910600188BC3 /* UserTerminal.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF36724CAB2BF008705B3 /* UserTerminal.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6DA2643910600188BC3 /* UserTerminal+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF35A24CAB2BF008705B3 /* UserTerminal+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6DC2643910600188BC3 /* UserQRLoginStage.h in Headers */ = {isa = PBXBuildFile; fileRef = F82E589F24F38F370026E454 /* UserQRLoginStage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6DD2643910600188BC3 /* UpUserDomain.h in Headers */ = {isa = PBXBuildFile; fileRef = F8CEF38424CAB2BF008705B3 /* UpUserDomain.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6DE2643910600188BC3 /* UPUserDomainSettings.h in Headers */ = {isa = PBXBuildFile; fileRef = AD5D45C224D6F23600211C3F /* UPUserDomainSettings.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D6DF264391FC00188BC3 /* FloorArg.m in Sources */ = {isa = PBXBuildFile; fileRef = F81273C62579083400995F1E /* FloorArg.m */; };
		AD12D6E0264391FC00188BC3 /* UDServerCacheManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F82E58A424F3A4960026E454 /* UDServerCacheManager.m */; };
		AD12D6E1264391FC00188BC3 /* UpUserDomainStore.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D447624D1475500211C3F /* UpUserDomainStore.m */; };
		AD12D6E2264391FC00188BC3 /* UpUserStore.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D449824D19B9000211C3F /* UpUserStore.m */; };
		AD12D6E3264391FC00188BC3 /* UpDeviceStore.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D45B724D6DEFD00211C3F /* UpDeviceStore.m */; };
		AD12D6E4264391FC00188BC3 /* UpFamilyStore.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D45BB24D6DF0B00211C3F /* UpFamilyStore.m */; };
		AD12D6E5264391FC00188BC3 /* UpEvent.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D446824D110ED00211C3F /* UpEvent.m */; };
		AD12D6E6264391FC00188BC3 /* UpNotifyEventHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D447224D141D700211C3F /* UpNotifyEventHandler.m */; };
		AD12D6E7264391FC00188BC3 /* UpEventHandlerManager.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D45A724D3F1A300211C3F /* UpEventHandlerManager.m */; };
		AD12D6E8264391FC00188BC3 /* UDOpertateRefreshUserEventHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D6AC6324F4E5CF007EA70A /* UDOpertateRefreshUserEventHandler.m */; };
		AD12D6E9264391FC00188BC3 /* UDOperatePlannedRefreshTokenEventHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = F899FCE024EBB46200F34E56 /* UDOperatePlannedRefreshTokenEventHandler.m */; };
		AD12D6EA264391FC00188BC3 /* UDOperateRefreshTerminalListEventHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = F8B541B424EE4D620075A070 /* UDOperateRefreshTerminalListEventHandler.m */; };
		AD12D6EB264391FC00188BC3 /* UDOperateRefreshAddressListEventHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = F8B541B024EE45790075A070 /* UDOperateRefreshAddressListEventHandler.m */; };
		AD12D6EC264391FC00188BC3 /* UDOperateRefreshFamilyListEventHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = F8B541B824EE4E650075A070 /* UDOperateRefreshFamilyListEventHandler.m */; };
		AD12D6EE264391FC00188BC3 /* UDOperateRefreshFamilyDetailEventHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = F8B541BC24EE4E7A0075A070 /* UDOperateRefreshFamilyDetailEventHandler.m */; };
		AD12D6EF264391FC00188BC3 /* UDOperateRefreshDeviceListEventHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = F8B541C024EE4ECF0075A070 /* UDOperateRefreshDeviceListEventHandler.m */; };
		AD12D6F0264391FC00188BC3 /* UpListenerTransformObserver.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D45AB24D3FDFD00211C3F /* UpListenerTransformObserver.m */; };
		AD12D6F1264391FC00188BC3 /* UpUserDomainHolder.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF42124CACD32008705B3 /* UpUserDomainHolder.m */; };
		AD12D6F2264391FC00188BC3 /* UDCacheIMP.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF33724CAB2BF008705B3 /* UDCacheIMP.m */; };
		AD12D6F3264391FC00188BC3 /* UDUserDomainCache.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D45AF24D6D94800211C3F /* UDUserDomainCache.m */; };
		AD12D6F4264391FC00188BC3 /* UserDomainSampleResult.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF38024CAB2BF008705B3 /* UserDomainSampleResult.m */; };
		AD12D6F5264391FC00188BC3 /* Device.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF37824CAB2BF008705B3 /* Device.m */; };
		AD12D6F6264391FC00188BC3 /* DeviceAuth.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF37624CAB2BF008705B3 /* DeviceAuth.m */; };
		AD12D6F7264391FC00188BC3 /* UDDeviceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF37A24CAB2BF008705B3 /* UDDeviceInfo.m */; };
		AD12D6F8264391FC00188BC3 /* DeviceOwnerInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF37524CAB2BF008705B3 /* DeviceOwnerInfo.m */; };
		AD12D6F9264391FC00188BC3 /* DevicePermission.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF37224CAB2BF008705B3 /* DevicePermission.m */; };
		AD12D6FA264391FC00188BC3 /* BatchProcessDeviceResult.m in Sources */ = {isa = PBXBuildFile; fileRef = F8FD387A253071EB00D4F9C3 /* BatchProcessDeviceResult.m */; };
		AD12D6FB264391FC00188BC3 /* CreateFamilyArgs.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF33B24CAB2BF008705B3 /* CreateFamilyArgs.m */; };
		AD12D6FC264391FC00188BC3 /* Family.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF34024CAB2BF008705B3 /* Family.m */; };
		AD12D6FD264391FC00188BC3 /* FamilyArgs.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF34624CAB2BF008705B3 /* FamilyArgs.m */; };
		AD12D6FE264391FC00188BC3 /* FamilyInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF33F24CAB2BF008705B3 /* FamilyInfo.m */; };
		AD12D6FF264391FC00188BC3 /* FamilyLocation.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF34824CAB2BF008705B3 /* FamilyLocation.m */; };
		AD12D700264391FC00188BC3 /* FamilyMember.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF35024CAB2BF008705B3 /* FamilyMember.m */; };
		AD12D701264391FC00188BC3 /* MemberInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF34424CAB2BF008705B3 /* MemberInfo.m */; };
		AD12D702264391FC00188BC3 /* Room.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF34D24CAB2BF008705B3 /* Room.m */; };
		AD12D703264391FC00188BC3 /* RoomArgs.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF33A24CAB2BF008705B3 /* RoomArgs.m */; };
		AD12D704264391FC00188BC3 /* UDFloorInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F81273CA25790D7200995F1E /* UDFloorInfo.m */; };
		AD12D705264391FC00188BC3 /* ApplicationOauthData.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF37C24CAB2BF008705B3 /* ApplicationOauthData.m */; };
		AD12D706264391FC00188BC3 /* UDCreateAddressOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CD11A124E384EB0021DD47 /* UDCreateAddressOp.m */; };
		AD12D707264391FC00188BC3 /* UDDeleteAddressOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F805BA7C24E38ECA0056C526 /* UDDeleteAddressOp.m */; };
		AD12D708264391FC00188BC3 /* UPEditAddressOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F805BA8024E3C8FE0056C526 /* UPEditAddressOp.m */; };
		AD12D709264391FC00188BC3 /* UDModifyUserInfoOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F805BA8424E3D42B0056C526 /* UDModifyUserInfoOp.m */; };
		AD12D70A264391FC00188BC3 /* UDPollqrCodeStateOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F805BA8824E404430056C526 /* UDPollqrCodeStateOp.m */; };
		AD12D70B264391FC00188BC3 /* UDQrCancleLoginOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F805BA8C24E405650056C526 /* UDQrCancleLoginOp.m */; };
		AD12D70C264391FC00188BC3 /* UDQrConfirmLoginOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F805BA9024E406D80056C526 /* UDQrConfirmLoginOp.m */; };
		AD12D70D264391FC00188BC3 /* UDQrConfirmScanOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F805BA9424E406FA0056C526 /* UDQrConfirmScanOp.m */; };
		AD12D70E264391FC00188BC3 /* UDQueryServiceOrderOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F805BA9824E40FF60056C526 /* UDQueryServiceOrderOp.m */; };
		AD12D70F264391FC00188BC3 /* UDQueryLoginLogsOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F805BA9C24E411A60056C526 /* UDQueryLoginLogsOp.m */; };
		AD12D710264391FC00188BC3 /* UDRefreshAddressListOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F805BAA024E413360056C526 /* UDRefreshAddressListOp.m */; };
		AD12D711264391FC00188BC3 /* UDRefreshTerminalListOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F80B7B6024E5256F0073FC48 /* UDRefreshTerminalListOp.m */; };
		AD12D712264391FC00188BC3 /* UDRefreshUserInfoOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F80B7B6424E529AE0073FC48 /* UDRefreshUserInfoOp.m */; };
		AD12D713264391FC00188BC3 /* UDUpdateAvatarOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F80B7B7024E53E080073FC48 /* UDUpdateAvatarOp.m */; };
		AD12D715264391FC00188BC3 /* UDChangeFamilyAdminOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D44B824D26C4000211C3F /* UDChangeFamilyAdminOp.m */; };
		AD12D716264391FC00188BC3 /* UDRefreshRoomListOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD62066424D941B200C9B414 /* UDRefreshRoomListOp.m */; };
		AD12D717264391FC00188BC3 /* UDCreateFamilyOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD62066824D941C700C9B414 /* UDCreateFamilyOp.m */; };
		AD12D718264391FC00188BC3 /* UDCreateRoomOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD62066C24D941D700C9B414 /* UDCreateRoomOp.m */; };
		AD12D719264391FC00188BC3 /* UDDeleteFamilyAsAdminOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD62067024D941E900C9B414 /* UDDeleteFamilyAsAdminOp.m */; };
		AD12D71A264391FC00188BC3 /* UDDeleteRoomOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD62067424D9420200C9B414 /* UDDeleteRoomOp.m */; };
		AD12D71B264391FC00188BC3 /* UDEditFamilyInfoOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD62067824D9421100C9B414 /* UDEditFamilyInfoOp.m */; };
		AD12D71C264391FC00188BC3 /* UDEditRoomNameOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD62067C24D9421F00C9B414 /* UDEditRoomNameOp.m */; };
		AD12D71D264391FC00188BC3 /* UDExitFamilyAsAdminOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD62068024D9422C00C9B414 /* UDExitFamilyAsAdminOp.m */; };
		AD12D71E264391FC00188BC3 /* UDExitFamilyAsMemberOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD62068424D9423D00C9B414 /* UDExitFamilyAsMemberOp.m */; };
		AD12D71F264391FC00188BC3 /* UDDeleteMemberOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F80B7B7824E6532F0073FC48 /* UDDeleteMemberOp.m */; };
		AD12D720264391FC00188BC3 /* UDInviteFamilyMemberOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD62068824D9425500C9B414 /* UDInviteFamilyMemberOp.m */; };
		AD12D721264391FC00188BC3 /* UDUnbindDevicesOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F80B7B7C24E659910073FC48 /* UDUnbindDevicesOp.m */; };
		AD12D722264391FC00188BC3 /* UDMoveDevicesToFamilyOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD62068C24D9426500C9B414 /* UDMoveDevicesToFamilyOp.m */; };
		AD12D723264391FC00188BC3 /* UDMoveDevicesToRoomOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F80B7B8024E687440073FC48 /* UDMoveDevicesToRoomOp.m */; };
		AD12D724264391FC00188BC3 /* UDDeleteDevicesOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F80B7B8424E689FB0073FC48 /* UDDeleteDevicesOp.m */; };
		AD12D725264391FC00188BC3 /* UDRefreshFamilyListOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F80B7B5C24E50D5F0073FC48 /* UDRefreshFamilyListOp.m */; };
		AD12D726264391FC00188BC3 /* UDReplyFamilyInviteOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F80B7B6824E52C500073FC48 /* UDReplyFamilyInviteOp.m */; };
		AD12D727264391FC00188BC3 /* UDReplyJoinFamilyOp.m in Sources */ = {isa = PBXBuildFile; fileRef = 22E6299025F0C8BD00912AA8 /* UDReplyJoinFamilyOp.m */; };
		AD12D728264391FC00188BC3 /* UDSetCurrentFamilyOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F80B7B6C24E539CA0073FC48 /* UDSetCurrentFamilyOp.m */; };
		AD12D729264391FC00188BC3 /* UDUpdateFamilyDetailOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F80B7B7424E61FE50073FC48 /* UDUpdateFamilyDetailOp.m */; };
		AD12D72A264391FC00188BC3 /* UDRefreshAllFamilyDetailOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F8B541C424EE5A000075A070 /* UDRefreshAllFamilyDetailOp.m */; };
		AD12D72B264391FC00188BC3 /* UDAddFloorOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F8304B0F2579D18300E326E5 /* UDAddFloorOp.m */; };
		AD12D72C264391FC00188BC3 /* UDEditFloorOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F8304B132579D1A600E326E5 /* UDEditFloorOp.m */; };
		AD12D72D264391FC00188BC3 /* UDDeleteFloorOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F8304B172579D1C700E326E5 /* UDDeleteFloorOp.m */; };
		AD12D72E264391FC00188BC3 /* UDModifyDeviceNameOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D44AA24D253F500211C3F /* UDModifyDeviceNameOp.m */; };
		AD12D72F264391FC00188BC3 /* UDRefreshDeviceListOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D44A724D253F400211C3F /* UDRefreshDeviceListOp.m */; };
		AD12D730264391FC00188BC3 /* UDRefreshTokenOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D445024CFCFAD00211C3F /* UDRefreshTokenOp.m */; };
		AD12D731264391FC00188BC3 /* UDLogoutOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D44A424D1A40800211C3F /* UDLogoutOp.m */; };
		AD12D732264391FC00188BC3 /* UDPlannedRefreshAuthDataOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD62069024D9508500C9B414 /* UDPlannedRefreshAuthDataOp.m */; };
		AD12D734264391FC00188BC3 /* UDRefreshUserOp.m in Sources */ = {isa = PBXBuildFile; fileRef = AD62069824D950BA00C9B414 /* UDRefreshUserOp.m */; };
		AD12D735264391FC00188BC3 /* UDOperatorManager.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D444524CFBF7E00211C3F /* UDOperatorManager.m */; };
		AD12D736264391FC00188BC3 /* UDOperator.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF38724CAB2BF008705B3 /* UDOperator.m */; };
		AD12D737264391FC00188BC3 /* UDTimeIMP.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF36B24CAB2BF008705B3 /* UDTimeIMP.m */; };
		AD12D739264391FC00188BC3 /* User.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF35624CAB2BF008705B3 /* User.m */; };
		AD12D73A264391FC00188BC3 /* UserAddress.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF35424CAB2BF008705B3 /* UserAddress.m */; };
		AD12D73B264391FC00188BC3 /* UserAddressInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF35324CAB2BF008705B3 /* UserAddressInfo.m */; };
		AD12D73C264391FC00188BC3 /* UserAddressArgs.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF40324CAC4F9008705B3 /* UserAddressArgs.m */; };
		AD12D73D264391FC00188BC3 /* UserInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF35824CAB2BF008705B3 /* UserInfo.m */; };
		AD12D73E264391FC00188BC3 /* UserInfoArgs.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF35B24CAB2BF008705B3 /* UserInfoArgs.m */; };
		AD12D73F264391FC00188BC3 /* UserLoginLogInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF36424CAB2BF008705B3 /* UserLoginLogInfo.m */; };
		AD12D740264391FC00188BC3 /* UserTerminal.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF35D24CAB2BF008705B3 /* UserTerminal.m */; };
		AD12D742264391FC00188BC3 /* UserQRLoginStage.m in Sources */ = {isa = PBXBuildFile; fileRef = F82E58A024F38F370026E454 /* UserQRLoginStage.m */; };
		AD12D743264391FC00188BC3 /* UpUserDomain.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF38324CAB2BF008705B3 /* UpUserDomain.m */; };
		AD12D744264391FC00188BC3 /* UPUserDomainSettings.m in Sources */ = {isa = PBXBuildFile; fileRef = AD5D45C324D6F23600211C3F /* UPUserDomainSettings.m */; };
		AD12D753264394A800188BC3 /* NSArray+UD.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92F10E2632C4A500C9544A /* NSArray+UD.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D754264394A800188BC3 /* NSDictionary+UD.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92F10F2632C4A500C9544A /* NSDictionary+UD.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D755264394A800188BC3 /* NSNumber+UD.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92F1102632C4A500C9544A /* NSNumber+UD.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D756264394A800188BC3 /* NSObject+UD.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92F1122632C4A500C9544A /* NSObject+UD.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D757264394A800188BC3 /* NSString+UD.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92F1112632C4A500C9544A /* NSString+UD.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D758264394A800188BC3 /* UpFamilyDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EED72632A0C000C9544A /* UpFamilyDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D759264394A800188BC3 /* UpUserDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EED82632A0C000C9544A /* UpUserDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D75A264394A800188BC3 /* UpDeviceListDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EED92632A0C000C9544A /* UpDeviceListDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D75B264394A800188BC3 /* CloudBatchProcessDevicesResponse.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EEE32632A0CD00C9544A /* CloudBatchProcessDevicesResponse.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D75C264394A800188BC3 /* CloudDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EEE52632A0CD00C9544A /* CloudDevice.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D75D264394A800188BC3 /* CloudDeviceAuth.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EEE12632A0CD00C9544A /* CloudDeviceAuth.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D75E264394A800188BC3 /* CloudDeviceBaseInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EEE02632A0CD00C9544A /* CloudDeviceBaseInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D75F264394A800188BC3 /* CloudDeviceExtendedInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EEEE2632A0CD00C9544A /* CloudDeviceExtendedInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D760264394A800188BC3 /* CloudDeviceList.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EEE42632A0CD00C9544A /* CloudDeviceList.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D761264394A800188BC3 /* CloudDeviceOwnerInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EEF12632A0CD00C9544A /* CloudDeviceOwnerInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D762264394A800188BC3 /* CloudDevicePermission.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EEEA2632A0CD00C9544A /* CloudDevicePermission.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D763264394A800188BC3 /* CloudQRLoginStage.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EEE82632A0CD00C9544A /* CloudQRLoginStage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D764264394A800188BC3 /* CloudLoginTerminal.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EEFC2632A0CD00C9544A /* CloudLoginTerminal.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D765264394A800188BC3 /* CloudOauthData.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EEFD2632A0CD00C9544A /* CloudOauthData.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D766264394A800188BC3 /* CloudUserAddressInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EEF72632A0CD00C9544A /* CloudUserAddressInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D767264394A800188BC3 /* CloudUserInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EEF62632A0CD00C9544A /* CloudUserInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D768264394A800188BC3 /* CloudUserLoginLogInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EEF32632A0CD00C9544A /* CloudUserLoginLogInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D76A264394A800188BC3 /* CloudFamily.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF0B2632A0CD00C9544A /* CloudFamily.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D76B264394A800188BC3 /* CloudFamilyFirstMember.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF0C2632A0CD00C9544A /* CloudFamilyFirstMember.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D76C264394A800188BC3 /* CloudFamilyList.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF0F2632A0CD00C9544A /* CloudFamilyList.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D76D264394A800188BC3 /* CloudFamilyLocation.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF0A2632A0CD00C9544A /* CloudFamilyLocation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D76E264394A800188BC3 /* CloudFamilyMember.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF092632A0CD00C9544A /* CloudFamilyMember.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D76F264394A800188BC3 /* CloudFamilyMemberInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF022632A0CD00C9544A /* CloudFamilyMemberInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D770264394A800188BC3 /* CloudFloorInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF0E2632A0CD00C9544A /* CloudFloorInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D771264394A800188BC3 /* CloudRoom.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF0D2632A0CD00C9544A /* CloudRoom.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D772264394A800188BC3 /* AddRoomTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF152632A0CD00C9544A /* AddRoomTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D773264394A800188BC3 /* BatchProcessDevicesOfFamilyTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF322632A0CD00C9544A /* BatchProcessDevicesOfFamilyTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D774264394A800188BC3 /* CommonTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF1D2632A0CD00C9544A /* CommonTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D775264394A800188BC3 /* CreateFamilyTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF252632A0CD00C9544A /* CreateFamilyTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D776264394A800188BC3 /* DeviceListTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF132632A0CD00C9544A /* DeviceListTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D777264394A800188BC3 /* FamilyFirstMemberTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF1C2632A0CD00C9544A /* FamilyFirstMemberTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D778264394A800188BC3 /* FamilyInfoTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF172632A0CD00C9544A /* FamilyInfoTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D779264394A800188BC3 /* FamilyListTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF1F2632A0CD00C9544A /* FamilyListTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D77A264394A800188BC3 /* FamilyRoomTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF272632A0CD00C9544A /* FamilyRoomTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D77B264394A800188BC3 /* UpUserServiceOrderTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF1E2632A0CD00C9544A /* UpUserServiceOrderTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D77C264394A800188BC3 /* UPZJOauthDataTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF302632A0CD00C9544A /* UPZJOauthDataTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D77D264394A800188BC3 /* UserAddressTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF262632A0CD00C9544A /* UserAddressTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D77E264394A800188BC3 /* UserInfoTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF312632A0CD00C9544A /* UserInfoTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D77F264394A800188BC3 /* UserLoginLogsTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF1B2632A0CD00C9544A /* UserLoginLogsTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D781264394A800188BC3 /* UserLoginTerminalTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF2D2632A0CD00C9544A /* UserLoginTerminalTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D782264394A800188BC3 /* UserOauthDataTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF332632A0CD00C9544A /* UserOauthDataTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D783264394A800188BC3 /* UserQrLoginPollTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF182632A0CD00C9544A /* UserQrLoginPollTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D784264394A800188BC3 /* DataSourceCallbackFunctions.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF362632A0CD00C9544A /* DataSourceCallbackFunctions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D785264394A800188BC3 /* DeviceListDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF3D2632A0CD00C9544A /* DeviceListDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D786264394A800188BC3 /* FamilyDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF382632A0CD00C9544A /* FamilyDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D787264394A800188BC3 /* UserDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF372632A0CD00C9544A /* UserDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D788264394A800188BC3 /* SEDeviceListDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF422632A0CD00C9544A /* SEDeviceListDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D789264394A800188BC3 /* SEFamilyDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF442632A0CD00C9544A /* SEFamilyDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D78A264394A800188BC3 /* SEUserDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF432632A0CD00C9544A /* SEUserDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D78B264394A800188BC3 /* SEBaseConverter.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF512632A0CD00C9544A /* SEBaseConverter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D78C264394A800188BC3 /* SEDeviceInfoListConverter.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF522632A0CD00C9544A /* SEDeviceInfoListConverter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D78D264394A800188BC3 /* SEFamilyListConverter.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF4D2632A0CD00C9544A /* SEFamilyListConverter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D78E264394A800188BC3 /* SEModifyDeviceConverter.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF482632A0CD00C9544A /* SEModifyDeviceConverter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D78F264394A800188BC3 /* SEUserAuthInfoConverter.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF502632A0CD00C9544A /* SEUserAuthInfoConverter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D790264394A800188BC3 /* SEUserInfoConverter.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EF4E2632A0CD00C9544A /* SEUserInfoConverter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D791264394A800188BC3 /* UserDomainDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = AD92EDCF2632997500C9544A /* UserDomainDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD12D7922643958400188BC3 /* NSArray+UD.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92F1142632C4A500C9544A /* NSArray+UD.m */; };
		AD12D7932643958400188BC3 /* NSDictionary+UD.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92F1132632C4A500C9544A /* NSDictionary+UD.m */; };
		AD12D7942643958400188BC3 /* NSNumber+UD.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92F1152632C4A500C9544A /* NSNumber+UD.m */; };
		AD12D7952643958400188BC3 /* NSObject+UD.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92F10C2632C4A500C9544A /* NSObject+UD.m */; };
		AD12D7962643958400188BC3 /* NSString+UD.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92F10D2632C4A500C9544A /* NSString+UD.m */; };
		AD12D7972643958400188BC3 /* CloudBatchProcessDevicesResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EEE92632A0CD00C9544A /* CloudBatchProcessDevicesResponse.m */; };
		AD12D7982643958400188BC3 /* CloudDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EEEF2632A0CD00C9544A /* CloudDevice.m */; };
		AD12D7992643958400188BC3 /* CloudDeviceAuth.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EEEB2632A0CD00C9544A /* CloudDeviceAuth.m */; };
		AD12D79A2643958400188BC3 /* CloudDeviceBaseInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EEEC2632A0CD00C9544A /* CloudDeviceBaseInfo.m */; };
		AD12D79B2643958400188BC3 /* CloudDeviceExtendedInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EEE62632A0CD00C9544A /* CloudDeviceExtendedInfo.m */; };
		AD12D79C2643958400188BC3 /* CloudDeviceList.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EEED2632A0CD00C9544A /* CloudDeviceList.m */; };
		AD12D79D2643958400188BC3 /* CloudDeviceOwnerInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EEE72632A0CD00C9544A /* CloudDeviceOwnerInfo.m */; };
		AD12D79E2643958400188BC3 /* CloudDevicePermission.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EEE22632A0CD00C9544A /* CloudDevicePermission.m */; };
		AD12D79F2643958400188BC3 /* CloudQRLoginStage.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EEF02632A0CD00C9544A /* CloudQRLoginStage.m */; };
		AD12D7A02643958400188BC3 /* CloudLoginTerminal.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EEF52632A0CD00C9544A /* CloudLoginTerminal.m */; };
		AD12D7A12643958400188BC3 /* CloudOauthData.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EEF82632A0CD00C9544A /* CloudOauthData.m */; };
		AD12D7A22643958400188BC3 /* CloudUserAddressInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EEFE2632A0CD00C9544A /* CloudUserAddressInfo.m */; };
		AD12D7A32643958400188BC3 /* CloudUserInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EEFB2632A0CD00C9544A /* CloudUserInfo.m */; };
		AD12D7A42643958400188BC3 /* CloudUserLoginLogInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EEF92632A0CD00C9544A /* CloudUserLoginLogInfo.m */; };
		AD12D7A62643958400188BC3 /* CloudFamily.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF052632A0CD00C9544A /* CloudFamily.m */; };
		AD12D7A72643958400188BC3 /* CloudFamilyFirstMember.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF042632A0CD00C9544A /* CloudFamilyFirstMember.m */; };
		AD12D7A82643958400188BC3 /* CloudFamilyList.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF062632A0CD00C9544A /* CloudFamilyList.m */; };
		AD12D7A92643958400188BC3 /* CloudFamilyLocation.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF002632A0CD00C9544A /* CloudFamilyLocation.m */; };
		AD12D7AA2643958400188BC3 /* CloudFamilyMember.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF012632A0CD00C9544A /* CloudFamilyMember.m */; };
		AD12D7AB2643958400188BC3 /* CloudFamilyMemberInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF082632A0CD00C9544A /* CloudFamilyMemberInfo.m */; };
		AD12D7AC2643958400188BC3 /* CloudFloorInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF072632A0CD00C9544A /* CloudFloorInfo.m */; };
		AD12D7AD2643958400188BC3 /* CloudRoom.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF032632A0CD00C9544A /* CloudRoom.m */; };
		AD12D7AE2643958400188BC3 /* AddRoomTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF282632A0CD00C9544A /* AddRoomTransformer.m */; };
		AD12D7AF2643958400188BC3 /* BatchProcessDevicesOfFamilyTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF212632A0CD00C9544A /* BatchProcessDevicesOfFamilyTransformer.m */; };
		AD12D7B02643958400188BC3 /* CommonTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF2A2632A0CD00C9544A /* CommonTransformer.m */; };
		AD12D7B12643958400188BC3 /* CreateFamilyTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF122632A0CD00C9544A /* CreateFamilyTransformer.m */; };
		AD12D7B22643958400188BC3 /* DeviceListTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF242632A0CD00C9544A /* DeviceListTransformer.m */; };
		AD12D7B32643958400188BC3 /* FamilyFirstMemberTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF2C2632A0CD00C9544A /* FamilyFirstMemberTransformer.m */; };
		AD12D7B42643958400188BC3 /* FamilyInfoTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF2E2632A0CD00C9544A /* FamilyInfoTransformer.m */; };
		AD12D7B52643958400188BC3 /* FamilyListTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF342632A0CD00C9544A /* FamilyListTransformer.m */; };
		AD12D7B62643958400188BC3 /* FamilyRoomTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF162632A0CD00C9544A /* FamilyRoomTransformer.m */; };
		AD12D7B72643958400188BC3 /* UpUserServiceOrderTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF292632A0CD00C9544A /* UpUserServiceOrderTransformer.m */; };
		AD12D7B82643958400188BC3 /* UPZJOauthDataTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF192632A0CD00C9544A /* UPZJOauthDataTransformer.m */; };
		AD12D7B92643958400188BC3 /* UserAddressTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF112632A0CD00C9544A /* UserAddressTransformer.m */; };
		AD12D7BA2643958400188BC3 /* UserInfoTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF222632A0CD00C9544A /* UserInfoTransformer.m */; };
		AD12D7BB2643958400188BC3 /* UserLoginLogsTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF2B2632A0CD00C9544A /* UserLoginLogsTransformer.m */; };
		AD12D7BD2643958400188BC3 /* UserLoginTerminalTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF1A2632A0CD00C9544A /* UserLoginTerminalTransformer.m */; };
		AD12D7BE2643958400188BC3 /* UserOauthDataTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF202632A0CD00C9544A /* UserOauthDataTransformer.m */; };
		AD12D7BF2643958400188BC3 /* UserQrLoginPollTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF2F2632A0CD00C9544A /* UserQrLoginPollTransformer.m */; };
		AD12D7C02643958400188BC3 /* DataSourceCallbackFunctions.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF3A2632A0CD00C9544A /* DataSourceCallbackFunctions.m */; };
		AD12D7C12643958400188BC3 /* DeviceListDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF392632A0CD00C9544A /* DeviceListDataSource.m */; };
		AD12D7C22643958400188BC3 /* FamilyDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF3B2632A0CD00C9544A /* FamilyDataSource.m */; };
		AD12D7C32643958400188BC3 /* UserDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF3C2632A0CD00C9544A /* UserDataSource.m */; };
		AD12D7C42643958400188BC3 /* SEDeviceListDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF452632A0CD00C9544A /* SEDeviceListDataSource.m */; };
		AD12D7C52643958400188BC3 /* SEFamilyDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF412632A0CD00C9544A /* SEFamilyDataSource.m */; };
		AD12D7C62643958400188BC3 /* SEUserDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF402632A0CD00C9544A /* SEUserDataSource.m */; };
		AD12D7C72643958400188BC3 /* SEBaseConverter.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF4C2632A0CD00C9544A /* SEBaseConverter.m */; };
		AD12D7C82643958400188BC3 /* SEDeviceInfoListConverter.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF4B2632A0CD00C9544A /* SEDeviceInfoListConverter.m */; };
		AD12D7C92643958400188BC3 /* SEFamilyListConverter.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF472632A0CD00C9544A /* SEFamilyListConverter.m */; };
		AD12D7CA2643958400188BC3 /* SEModifyDeviceConverter.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF4F2632A0CD00C9544A /* SEModifyDeviceConverter.m */; };
		AD12D7CB2643958400188BC3 /* SEUserAuthInfoConverter.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF4A2632A0CD00C9544A /* SEUserAuthInfoConverter.m */; };
		AD12D7CC2643958400188BC3 /* SEUserInfoConverter.m in Sources */ = {isa = PBXBuildFile; fileRef = AD92EF492632A0CD00C9544A /* SEUserInfoConverter.m */; };
		AD12D7CD2643963A00188BC3 /* libupuserdomain.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AD12D63826438F9F00188BC3 /* libupuserdomain.a */; };
		AD12D7CF2643967500188BC3 /* libUserDomainDataSource.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AD12D7492643933E00188BC3 /* libUserDomainDataSource.a */; };
		AD12D7D02643979700188BC3 /* libupuserdomain.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AD12D63826438F9F00188BC3 /* libupuserdomain.a */; };
		AD7AF97624F7AB960074F36F /* FamilyMemberViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = AD7AF97524F7AB960074F36F /* FamilyMemberViewController.m */; };
		AD7AF97924F7D8720074F36F /* CommonEditCell.m in Sources */ = {isa = PBXBuildFile; fileRef = AD7AF97824F7D8720074F36F /* CommonEditCell.m */; };
		AD7AF97C24F7E48D0074F36F /* CreatFamilyViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = AD7AF97B24F7E48D0074F36F /* CreatFamilyViewController.m */; };
		AD7AF97F24F8AEF20074F36F /* DeviceActionView.m in Sources */ = {isa = PBXBuildFile; fileRef = AD7AF97E24F8AEF20074F36F /* DeviceActionView.m */; };
		AD81E85B24F4A3DD002430CA /* FamilyListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = AD81E85A24F4A3DD002430CA /* FamilyListViewController.m */; };
		AD81E85E24F4DEA9002430CA /* FamilyDetailViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = AD81E85D24F4DEA8002430CA /* FamilyDetailViewController.m */; };
		AD81E86324F60E4A002430CA /* MyFamilyController.m in Sources */ = {isa = PBXBuildFile; fileRef = AD81E86224F60E4A002430CA /* MyFamilyController.m */; };
		AD81E86924F63522002430CA /* AddressPickView.m in Sources */ = {isa = PBXBuildFile; fileRef = AD81E86824F63522002430CA /* AddressPickView.m */; };
		AD81E86B24F637E6002430CA /* Address.plist in Resources */ = {isa = PBXBuildFile; fileRef = AD81E86A24F637E6002430CA /* Address.plist */; };
		AD81E86E24F64DDD002430CA /* FamilyDevicesViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = AD81E86D24F64DDD002430CA /* FamilyDevicesViewController.m */; };
		AD81E87124F75B40002430CA /* FamilyOwnerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = AD81E87024F75B40002430CA /* FamilyOwnerViewController.m */; };
		ADB50D132643894800062881 /* SEUserInfoAPI.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41CA24B5AD6A007A8372 /* SEUserInfoAPI.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D142643894800062881 /* SEUserLogoutAPI.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41CD24B5AD6A007A8372 /* SEUserLogoutAPI.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D152643894800062881 /* SEUserModifyAPI.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41CB24B5AD6A007A8372 /* SEUserModifyAPI.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D162643894800062881 /* SEUserRefreshTokenAPI.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41CE24B5AD6A007A8372 /* SEUserRefreshTokenAPI.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D172643896100062881 /* SEEncryption.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41D224B5AD6A007A8372 /* SEEncryption.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D182643896100062881 /* SERequestBase.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41D324B5AD6A007A8372 /* SERequestBase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D192643896100062881 /* SERequestConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41D624B5AD6A007A8372 /* SERequestConfig.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D1A2643899A00062881 /* SECloudDeviceInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41DE24B5AD6B007A8372 /* SECloudDeviceInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D1B2643899A00062881 /* SECloudUserAuthInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41D924B5AD6B007A8372 /* SECloudUserAuthInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D1C2643899A00062881 /* SECloudUserInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41DA24B5AD6B007A8372 /* SECloudUserInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D1D2643899A00062881 /* SEModifyDeviceInfoAPI.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41E224B5AD6B007A8372 /* SEModifyDeviceInfoAPI.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D1E2643899A00062881 /* SEQueryDeviceListAPI.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41E124B5AD6B007A8372 /* SEQueryDeviceListAPI.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D1F2643899A00062881 /* SEUnbindDeviceAPI.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41E524B5AD6B007A8372 /* SEUnbindDeviceAPI.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D202643899A00062881 /* UPAddRoomToFamilyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41F424B5AD6B007A8372 /* UPAddRoomToFamilyApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D212643899A00062881 /* UPChangeFamilyAdminApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41F324B5AD6B007A8372 /* UPChangeFamilyAdminApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D222643899A00062881 /* UPCreateFamilyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41F724B5AD6B007A8372 /* UPCreateFamilyApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D232643899A00062881 /* UPdateFamilyInfoApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF420D24B5AD6B007A8372 /* UPdateFamilyInfoApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D242643899A00062881 /* UPDestroyFamilyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF421224B5AD6B007A8372 /* UPDestroyFamilyApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D252643899A00062881 /* UPFamilyDeleteMemberApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41EC24B5AD6B007A8372 /* UPFamilyDeleteMemberApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D262643899A00062881 /* UPFamilyInfoApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF421024B5AD6B007A8372 /* UPFamilyInfoApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D272643899A00062881 /* UPFamilyInviteMemberApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41F624B5AD6B007A8372 /* UPFamilyInviteMemberApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D282643899A00062881 /* UPFamilyListApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF421124B5AD6B007A8372 /* UPFamilyListApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D292643899A00062881 /* UPFamilyMemberShareDeviceCountApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41F224B5AD6B007A8372 /* UPFamilyMemberShareDeviceCountApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D2A2643899A00062881 /* UPFamilyMoveDevicesToNewRoomApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF420924B5AD6B007A8372 /* UPFamilyMoveDevicesToNewRoomApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D2B2643899A00062881 /* UPFamilyMoveDevicesToOtherFamilyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF420624B5AD6B007A8372 /* UPFamilyMoveDevicesToOtherFamilyApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D2C2643899A00062881 /* UPFamilyRemoveDevicesApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41FF24B5AD6B007A8372 /* UPFamilyRemoveDevicesApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D2D2643899A00062881 /* UPFamilyReplyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41FA24B5AD6B007A8372 /* UPFamilyReplyApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D2E2643899A00062881 /* UPReplyJoinFamilyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 22E6298B25F0C07800912AA8 /* UPReplyJoinFamilyApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D2F2643899A00062881 /* UPFamilyRoomListApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41F024B5AD6B007A8372 /* UPFamilyRoomListApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D302643899A00062881 /* UPFamilyUnbindDevicesApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF420224B5AD6B007A8372 /* UPFamilyUnbindDevicesApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D312643899A00062881 /* UPQuitFamilyAsAdminApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF420824B5AD6B007A8372 /* UPQuitFamilyAsAdminApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D322643899A00062881 /* UPQuitFamilyAsMemberApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41EB24B5AD6B007A8372 /* UPQuitFamilyAsMemberApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D332643899A00062881 /* UPRemoveRoomFromFamilyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF420124B5AD6B007A8372 /* UPRemoveRoomFromFamilyApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D342643899A00062881 /* UPSetDefaultFamilyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41E824B5AD6B007A8372 /* UPSetDefaultFamilyApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D352643899A00062881 /* UPUpdateFamilyRoomNameApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF41EE24B5AD6B007A8372 /* UPUpdateFamilyRoomNameApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D362643899A00062881 /* UPAddFloorApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F8F22329257B388200222539 /* UPAddFloorApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D372643899A00062881 /* UPEditFloorApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F8F2232D257B389B00222539 /* UPEditFloorApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D382643899A00062881 /* UPDeleteFloorApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F8F22331257B38AF00222539 /* UPDeleteFloorApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D39264389C900062881 /* UPUserAccountVerifyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF423224B5AD6B007A8372 /* UPUserAccountVerifyApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D3A264389C900062881 /* UPUserAddressApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF422124B5AD6B007A8372 /* UPUserAddressApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D3B264389C900062881 /* UPUserApplitionTokenApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF423324B5AD6B007A8372 /* UPUserApplitionTokenApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D3C264389C900062881 /* UPUserCreateNewAddressApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF421D24B5AD6B007A8372 /* UPUserCreateNewAddressApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D3D264389C900062881 /* UPUserDeleteAddressApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF421524B5AD6B007A8372 /* UPUserDeleteAddressApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D3E264389C900062881 /* UPUserEditAddressApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF422924B5AD6B007A8372 /* UPUserEditAddressApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D3F264389C900062881 /* UPUserInfoApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF423924B5AD6B007A8372 /* UPUserInfoApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D40264389C900062881 /* UPUserLoginLogsApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF422E24B5AD6B007A8372 /* UPUserLoginLogsApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D42264389C900062881 /* UPUserLoginTerminalApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF423524B5AD6B007A8372 /* UPUserLoginTerminalApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D43264389C900062881 /* UPUserQRCancleLoginApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF421624B5AD6B007A8372 /* UPUserQRCancleLoginApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D44264389C900062881 /* UPUserQRConfirmLoginApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF422024B5AD6B007A8372 /* UPUserQRConfirmLoginApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D45264389C900062881 /* UPUserQRloginPollApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF422624B5AD6B007A8372 /* UPUserQRloginPollApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D46264389C900062881 /* UPUserQRScanApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF422424B5AD6B007A8372 /* UPUserQRScanApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D47264389C900062881 /* UPUserRefreshTokenApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF423D24B5AD6B007A8372 /* UPUserRefreshTokenApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D48264389C900062881 /* UPUserServiceOrderApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF422F24B5AD6B007A8372 /* UPUserServiceOrderApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D49264389C900062881 /* UPUserUpdatePwdApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF424024B5AD6B007A8372 /* UPUserUpdatePwdApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D4A264389C900062881 /* UPUserUpdateUserInfoApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF422824B5AD6B007A8372 /* UPUserUpdateUserInfoApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D4B264389C900062881 /* UPUserUploadAvatarApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF422524B5AD6B007A8372 /* UPUserUploadAvatarApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D4C264389CA00062881 /* UPZJRefreshTokenApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF421724B5AD6B007A8372 /* UPZJRefreshTokenApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D4D264389CA00062881 /* UPZJUserInfoApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 71AF421824B5AD6B007A8372 /* UPZJUserInfoApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADB50D4E264389CA00062881 /* UPZJUserLogOutApi.h in Headers */ = {isa = PBXBuildFile; fileRef = 2248A0AC25E10B8B00F7EF79 /* UPZJUserLogOutApi.h */; settings = {ATTRIBUTES = (Public, ); }; };
		ADCBD89124F9072C00BC30AC /* FamilyPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = ADCBD89024F9072C00BC30AC /* FamilyPickerView.m */; };
		ADCBD89424FCA61500BC30AC /* RoomPickerView.m in Sources */ = {isa = PBXBuildFile; fileRef = ADCBD89324FCA61500BC30AC /* RoomPickerView.m */; };
		ADCBD89724FE1A1600BC30AC /* RoomDevicesViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = ADCBD89624FE1A1600BC30AC /* RoomDevicesViewController.m */; };
		ADCBD89924FE3BFD00BC30AC /* city-v3.0.json in Resources */ = {isa = PBXBuildFile; fileRef = ADCBD89824FE3BFD00BC30AC /* city-v3.0.json */; };
		ADCBD89C24FE52F700BC30AC /* AddressViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = ADCBD89B24FE52F700BC30AC /* AddressViewController.m */; };
		ADF38BB224DC1C5900551FF7 /* UPUserLoginApi.m in Sources */ = {isa = PBXBuildFile; fileRef = ADF38BB024DC1C5900551FF7 /* UPUserLoginApi.m */; };
		CA270EB1E3B64167B156FE10 /* libPods-UserDomainAPIs.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B9688A3885B290FC6C821B3F /* libPods-UserDomainAPIs.a */; };
		CA6D5A33F79303932A2E30A5 /* libPods-Debugger.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 85381D37955E48B54DB8EABE /* libPods-Debugger.a */; };
		D0A3580E7BD567A3D8141D5B /* libPods-upuserdomainTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 8350DF3AA434AA3EE03764DA /* libPods-upuserdomainTests.a */; };
		F70AC2A72833ADD500EFF594 /* SEFamilyMoveDevicesToOtherFamilyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F70AC2A52833ADD500EFF594 /* SEFamilyMoveDevicesToOtherFamilyApi.h */; };
		F70AC2A82833ADD500EFF594 /* SEFamilyMoveDevicesToOtherFamilyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F70AC2A62833ADD500EFF594 /* SEFamilyMoveDevicesToOtherFamilyApi.m */; };
		F76DC4CA282BB6B800E2EFF7 /* FamilyRelatedTransform.h in Headers */ = {isa = PBXBuildFile; fileRef = F76DC4C8282BB6B800E2EFF7 /* FamilyRelatedTransform.h */; };
		F76DC4CB282BB6B800E2EFF7 /* FamilyRelatedTransform.m in Sources */ = {isa = PBXBuildFile; fileRef = F76DC4C9282BB6B800E2EFF7 /* FamilyRelatedTransform.m */; };
		F79092F3282A108900EE61D2 /* SEFamilyListApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F79092F1282A108900EE61D2 /* SEFamilyListApi.h */; };
		F79092F4282A108900EE61D2 /* SEFamilyListApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F79092F2282A108900EE61D2 /* SEFamilyListApi.m */; };
		F79092F7282A122900EE61D2 /* SERemoveRoomFromFamilyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F79092F5282A122900EE61D2 /* SERemoveRoomFromFamilyApi.h */; };
		F79092F8282A122900EE61D2 /* SERemoveRoomFromFamilyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F79092F6282A122900EE61D2 /* SERemoveRoomFromFamilyApi.m */; };
		F79092FB282A127900EE61D2 /* SEAddRoomToFamilyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F79092F9282A127900EE61D2 /* SEAddRoomToFamilyApi.h */; };
		F79092FC282A127900EE61D2 /* SEAddRoomToFamilyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F79092FA282A127900EE61D2 /* SEAddRoomToFamilyApi.m */; };
		F79092FF282A12A400EE61D2 /* SECreateFamilyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F79092FD282A12A400EE61D2 /* SECreateFamilyApi.h */; };
		F7909300282A12A400EE61D2 /* SECreateFamilyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F79092FE282A12A400EE61D2 /* SECreateFamilyApi.m */; };
		F7909303282A12E000EE61D2 /* SEFamilyInfoApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F7909301282A12E000EE61D2 /* SEFamilyInfoApi.h */; };
		F7909304282A12E000EE61D2 /* SEFamilyInfoApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F7909302282A12E000EE61D2 /* SEFamilyInfoApi.m */; };
		F7909308282A302500EE61D2 /* SEFamilyRoomListApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F7909306282A302500EE61D2 /* SEFamilyRoomListApi.h */; };
		F7909309282A302500EE61D2 /* SEFamilyRoomListApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F7909307282A302500EE61D2 /* SEFamilyRoomListApi.m */; };
		F790930C282A309300EE61D2 /* SEUpdateFamilyInfoApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F790930A282A309300EE61D2 /* SEUpdateFamilyInfoApi.h */; };
		F790930D282A309300EE61D2 /* SEUpdateFamilyInfoApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F790930B282A309300EE61D2 /* SEUpdateFamilyInfoApi.m */; };
		F7909310282A30D100EE61D2 /* SEDestroyFamilyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F790930E282A30D100EE61D2 /* SEDestroyFamilyApi.h */; };
		F7909311282A30D100EE61D2 /* SEDestroyFamilyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F790930F282A30D100EE61D2 /* SEDestroyFamilyApi.m */; };
		F7909314282A30FB00EE61D2 /* SEUpdateFamilyRoomNameApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F7909312282A30FB00EE61D2 /* SEUpdateFamilyRoomNameApi.h */; };
		F7909315282A30FB00EE61D2 /* SEUpdateFamilyRoomNameApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F7909313282A30FB00EE61D2 /* SEUpdateFamilyRoomNameApi.m */; };
		F7909318282A312200EE61D2 /* SESetDefaultFamilyApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F7909316282A312200EE61D2 /* SESetDefaultFamilyApi.h */; };
		F7909319282A312200EE61D2 /* SESetDefaultFamilyApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F7909317282A312200EE61D2 /* SESetDefaultFamilyApi.m */; };
		F790931C282A316100EE61D2 /* SEFamilyMoveDevicesToNewRoomApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F790931A282A316100EE61D2 /* SEFamilyMoveDevicesToNewRoomApi.h */; };
		F790931D282A316100EE61D2 /* SEFamilyMoveDevicesToNewRoomApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F790931B282A316100EE61D2 /* SEFamilyMoveDevicesToNewRoomApi.m */; };
		F7909328282B650300EE61D2 /* SEFamilyInfoTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = F7909326282B650300EE61D2 /* SEFamilyInfoTransformer.h */; };
		F7909329282B650300EE61D2 /* SEFamilyInfoTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = F7909327282B650300EE61D2 /* SEFamilyInfoTransformer.m */; };
		F790932C282B816200EE61D2 /* SEAddRoomTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = F790932A282B816200EE61D2 /* SEAddRoomTransformer.h */; };
		F790932D282B816200EE61D2 /* SEAddRoomTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = F790932B282B816200EE61D2 /* SEAddRoomTransformer.m */; };
		F7909330282B81EE00EE61D2 /* SECreateFamilyTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = F790932E282B81EE00EE61D2 /* SECreateFamilyTransformer.h */; };
		F7909331282B81EE00EE61D2 /* SECreateFamilyTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = F790932F282B81EE00EE61D2 /* SECreateFamilyTransformer.m */; };
		F7909334282B836900EE61D2 /* SEFamilyRoomTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = F7909332282B836900EE61D2 /* SEFamilyRoomTransformer.h */; };
		F7909335282B836900EE61D2 /* SEFamilyRoomTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = F7909333282B836900EE61D2 /* SEFamilyRoomTransformer.m */; };
		F7909338282B846C00EE61D2 /* SEBatchProcessDevicesOfFamilyTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = F7909336282B846C00EE61D2 /* SEBatchProcessDevicesOfFamilyTransformer.h */; };
		F7909339282B846C00EE61D2 /* SEBatchProcessDevicesOfFamilyTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = F7909337282B846C00EE61D2 /* SEBatchProcessDevicesOfFamilyTransformer.m */; };
		F854517B24F50C0F007F3114 /* UPUploadRecordApis.m in Sources */ = {isa = PBXBuildFile; fileRef = F854517A24F50C0F007F3114 /* UPUploadRecordApis.m */; };
		F899FCE924ECBCAF00F34E56 /* UserdomainSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F899FCE824ECBCAF00F34E56 /* UserdomainSteps.m */; };
		F8AEC6882D7EF832008C92C5 /* UDConfirmDeviceSharingRelationOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F8AEC6862D7EF832008C92C5 /* UDConfirmDeviceSharingRelationOp.h */; };
		F8AEC6892D7EF832008C92C5 /* UDConfirmDeviceSharingRelationOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F8AEC6872D7EF832008C92C5 /* UDConfirmDeviceSharingRelationOp.m */; };
		F8AEC68C2D7EFB09008C92C5 /* UDCancelDeviceSharingRelationOp.m in Sources */ = {isa = PBXBuildFile; fileRef = F8AEC68B2D7EFB09008C92C5 /* UDCancelDeviceSharingRelationOp.m */; };
		F8AEC68D2D7EFB09008C92C5 /* UDCancelDeviceSharingRelationOp.h in Headers */ = {isa = PBXBuildFile; fileRef = F8AEC68A2D7EFB09008C92C5 /* UDCancelDeviceSharingRelationOp.h */; };
		F8AEC6902D7EFD76008C92C5 /* UPConfirmDeviceSharingRelationApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F8AEC68E2D7EFD76008C92C5 /* UPConfirmDeviceSharingRelationApi.h */; };
		F8AEC6912D7EFD76008C92C5 /* UPConfirmDeviceSharingRelationApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F8AEC68F2D7EFD76008C92C5 /* UPConfirmDeviceSharingRelationApi.m */; };
		F8AEC6942D7F0E99008C92C5 /* UPCancelDeviceSharingRelationApi.h in Headers */ = {isa = PBXBuildFile; fileRef = F8AEC6922D7F0E99008C92C5 /* UPCancelDeviceSharingRelationApi.h */; };
		F8AEC6952D7F0E99008C92C5 /* UPCancelDeviceSharingRelationApi.m in Sources */ = {isa = PBXBuildFile; fileRef = F8AEC6932D7F0E99008C92C5 /* UPCancelDeviceSharingRelationApi.m */; };
		F8AEC6982D80287E008C92C5 /* CloudShareDeviceCardInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F8AEC6972D80287E008C92C5 /* CloudShareDeviceCardInfo.m */; };
		F8AEC6992D80287E008C92C5 /* CloudShareDeviceCardInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8AEC6962D80287E008C92C5 /* CloudShareDeviceCardInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F8AEC69B2D802C91008C92C5 /* UDShareDeviceCardInfoDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8AEC69A2D802C91008C92C5 /* UDShareDeviceCardInfoDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F8AEC69E2D8032BE008C92C5 /* DeviceShareDeviceCardInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8AEC69C2D8032BE008C92C5 /* DeviceShareDeviceCardInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F8AEC69F2D8032BE008C92C5 /* DeviceShareDeviceCardInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F8AEC69D2D8032BE008C92C5 /* DeviceShareDeviceCardInfo.m */; };
		F8AEC6A32D80333F008C92C5 /* DeviceShareDeviceCardInfo+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = F8AEC6A02D80333F008C92C5 /* DeviceShareDeviceCardInfo+PrivateExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F8CEF18424C7D686008705B3 /* UserdomainHolderTest.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF18324C7D686008705B3 /* UserdomainHolderTest.m */; };
		F8CEF18A24C7D6C8008705B3 /* StepsUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF18924C7D6C8008705B3 /* StepsUtils.m */; };
		F8CEF18D24C7D6FD008705B3 /* InitializationSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF18C24C7D6FD008705B3 /* InitializationSteps.m */; };
		F8CEF1AE24C7E347008705B3 /* UserSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF1AD24C7E347008705B3 /* UserSteps.m */; };
		F8CEF1B124C7E359008705B3 /* FamilySteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF1B024C7E359008705B3 /* FamilySteps.m */; };
		F8CEF1B424C7E366008705B3 /* DeviceSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF1B324C7E366008705B3 /* DeviceSteps.m */; };
		F8CEF1B724C7E37A008705B3 /* CacheSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF1B624C7E37A008705B3 /* CacheSteps.m */; };
		F8CEF1BD24C7E3AF008705B3 /* TimeSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF1BC24C7E3AF008705B3 /* TimeSteps.m */; };
		F8CEF1C024C7E3BE008705B3 /* EventHandleSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF1BF24C7E3BE008705B3 /* EventHandleSteps.m */; };
		F8CEF1C324C7E3EC008705B3 /* RefreshTokenSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF1C224C7E3EC008705B3 /* RefreshTokenSteps.m */; };
		F8CEF1C624C7E411008705B3 /* SignleLoginSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8CEF1C524C7E411008705B3 /* SignleLoginSteps.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		AD8DD1F42643F12900086CBF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 72321F752255DE1E005F6A86 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = ADB50C7E2643848F00062881;
			remoteInfo = UserDomainAPIs;
		};
		AD8DD1F62643F23300086CBF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 72321F752255DE1E005F6A86 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AD12D7482643933E00188BC3;
			remoteInfo = UserDomainDataSource;
		};
		AD8DD1F82643F23D00086CBF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 72321F752255DE1E005F6A86 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AD12D63726438F9F00188BC3;
			remoteInfo = upuserdomain;
		};
		AD8DD1FA2643F24200086CBF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 72321F752255DE1E005F6A86 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AD12D63726438F9F00188BC3;
			remoteInfo = upuserdomain;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		AD12D63626438F9F00188BC3 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD12D7472643933E00188BC3 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD92EDE42632997500C9544A /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		ADB50C7D2643848F00062881 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		03975CE6D71736702F84FEE9 /* Pods-upuserdomainTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-upuserdomainTests.debug.xcconfig"; path = "Target Support Files/Pods-upuserdomainTests/Pods-upuserdomainTests.debug.xcconfig"; sourceTree = "<group>"; };
		17C5270E6A526BD76942F08C /* libPods-upuserdomain.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-upuserdomain.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		17FE49FAA2DB41B7CEB10E77 /* Pods-UserDomainDataSource.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UserDomainDataSource.debug.xcconfig"; path = "Target Support Files/Pods-UserDomainDataSource/Pods-UserDomainDataSource.debug.xcconfig"; sourceTree = "<group>"; };
		1D284CED5D98610D13AD2490 /* Pods-UserDomain.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UserDomain.debug.xcconfig"; path = "Target Support Files/Pods-UserDomain/Pods-UserDomain.debug.xcconfig"; sourceTree = "<group>"; };
		2202C85F2922130800955D81 /* UDSaveRoomsOrderOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDSaveRoomsOrderOp.h; sourceTree = "<group>"; };
		2202C8602922130800955D81 /* UDSaveRoomsOrderOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDSaveRoomsOrderOp.m; sourceTree = "<group>"; };
		2202C8632922156800955D81 /* UPSaveRoomsOrderApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPSaveRoomsOrderApi.h; sourceTree = "<group>"; };
		2202C8642922156800955D81 /* UPSaveRoomsOrderApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPSaveRoomsOrderApi.m; sourceTree = "<group>"; };
		22037984270FE9D000FB8EB2 /* features */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = features; sourceTree = SOURCE_ROOT; };
		221087D727705929002D3A6B /* SECloudDevicePermission.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SECloudDevicePermission.h; sourceTree = "<group>"; };
		221087D827705929002D3A6B /* SECloudDevicePermission.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SECloudDevicePermission.m; sourceTree = "<group>"; };
		221087DB27705971002D3A6B /* SECloudDeviceAuth.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SECloudDeviceAuth.h; sourceTree = "<group>"; };
		221087DC27705971002D3A6B /* SECloudDeviceAuth.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SECloudDeviceAuth.m; sourceTree = "<group>"; };
		221AA33E277EA1A0008143F4 /* ShopUserDataSource.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ShopUserDataSource.h; sourceTree = "<group>"; };
		221AA33F277EA1A0008143F4 /* ShopUserDataSource.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ShopUserDataSource.m; sourceTree = "<group>"; };
		221AA344277EA6F4008143F4 /* ShopRefreshTokenApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ShopRefreshTokenApi.h; sourceTree = "<group>"; };
		221AA345277EA6F4008143F4 /* ShopRefreshTokenApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ShopRefreshTokenApi.m; sourceTree = "<group>"; };
		221AA349277ED064008143F4 /* ShopServerAPIBase.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ShopServerAPIBase.h; sourceTree = "<group>"; };
		221AA34A277ED064008143F4 /* ShopServerAPIBase.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ShopServerAPIBase.m; sourceTree = "<group>"; };
		221AA34D277ED317008143F4 /* ShopOauthDataTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ShopOauthDataTransformer.h; sourceTree = "<group>"; };
		221AA34E277ED317008143F4 /* ShopOauthDataTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ShopOauthDataTransformer.m; sourceTree = "<group>"; };
		223223EB28B5F36B00A7A0E7 /* UPModifyVirtualMemberRoleApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPModifyVirtualMemberRoleApi.h; sourceTree = "<group>"; };
		223223EC28B5F36B00A7A0E7 /* UPModifyVirtualMemberRoleApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPModifyVirtualMemberRoleApi.m; sourceTree = "<group>"; };
		223223EF28B5F54500A7A0E7 /* UDModifyVirtualMemberRoleOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDModifyVirtualMemberRoleOp.h; sourceTree = "<group>"; };
		223223F028B5F54500A7A0E7 /* UDModifyVirtualMemberRoleOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDModifyVirtualMemberRoleOp.m; sourceTree = "<group>"; };
		2248A0AC25E10B8B00F7EF79 /* UPZJUserLogOutApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPZJUserLogOutApi.h; sourceTree = "<group>"; };
		2248A0AD25E10B8B00F7EF79 /* UPZJUserLogOutApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPZJUserLogOutApi.m; sourceTree = "<group>"; };
		2252139228A1F1F0001D2832 /* UDModifyMemberRoleOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDModifyMemberRoleOp.h; sourceTree = "<group>"; };
		2252139328A1F1F0001D2832 /* UDModifyMemberRoleOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDModifyMemberRoleOp.m; sourceTree = "<group>"; };
		2252139628A1F4F3001D2832 /* UPModifyMemberRoleApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPModifyMemberRoleApi.h; sourceTree = "<group>"; };
		2252139728A1F4F3001D2832 /* UPModifyMemberRoleApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPModifyMemberRoleApi.m; sourceTree = "<group>"; };
		2252139A28A20968001D2832 /* VirtualMemberArgs.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = VirtualMemberArgs.h; sourceTree = "<group>"; };
		2252139B28A20968001D2832 /* VirtualMemberArgs.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = VirtualMemberArgs.m; sourceTree = "<group>"; };
		225BA55A27F19B3D0076B9ED /* CommonDataTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommonDataTransformer.h; sourceTree = "<group>"; };
		225BA55B27F19B3D0076B9ED /* CommonDataTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommonDataTransformer.m; sourceTree = "<group>"; };
		22B3B32128DD51C7007EF767 /* SEUnbindDeviceTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEUnbindDeviceTransformer.h; sourceTree = "<group>"; };
		22B3B32228DD51C7007EF767 /* SEUnbindDeviceTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEUnbindDeviceTransformer.m; sourceTree = "<group>"; };
		22BF11B327C5CFC000EB43F5 /* ShopLogoutApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ShopLogoutApi.m; sourceTree = "<group>"; };
		22BF11B427C5CFC000EB43F5 /* ShopLogoutApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ShopLogoutApi.h; sourceTree = "<group>"; };
		22E6298B25F0C07800912AA8 /* UPReplyJoinFamilyApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPReplyJoinFamilyApi.h; sourceTree = "<group>"; };
		22E6298C25F0C07800912AA8 /* UPReplyJoinFamilyApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPReplyJoinFamilyApi.m; sourceTree = "<group>"; };
		22E6298F25F0C8BD00912AA8 /* UDReplyJoinFamilyOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDReplyJoinFamilyOp.h; sourceTree = "<group>"; };
		22E6299025F0C8BD00912AA8 /* UDReplyJoinFamilyOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDReplyJoinFamilyOp.m; sourceTree = "<group>"; };
		22EBEA362914A599007DA887 /* UDGetGroupDevicesOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDGetGroupDevicesOp.h; sourceTree = "<group>"; };
		22EBEA372914A599007DA887 /* UDGetGroupDevicesOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDGetGroupDevicesOp.m; sourceTree = "<group>"; };
		22EBEA3A2914A91A007DA887 /* UPGetGroupDevicesApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPGetGroupDevicesApi.h; sourceTree = "<group>"; };
		22EBEA3B2914A91A007DA887 /* UPGetGroupDevicesApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPGetGroupDevicesApi.m; sourceTree = "<group>"; };
		22F6FDBF27953D490048F32F /* UPQueryFirstMemeberApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPQueryFirstMemeberApi.h; sourceTree = "<group>"; };
		22F6FDC027953D490048F32F /* UPQueryFirstMemeberApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPQueryFirstMemeberApi.m; sourceTree = "<group>"; };
		22F6FDC327953D810048F32F /* UPAddVirtualMemberApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPAddVirtualMemberApi.h; sourceTree = "<group>"; };
		22F6FDC427953D810048F32F /* UPAddVirtualMemberApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPAddVirtualMemberApi.m; sourceTree = "<group>"; };
		22F6FDC727953DB20048F32F /* UPModifyVirtualMemberApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPModifyVirtualMemberApi.h; sourceTree = "<group>"; };
		22F6FDC827953DB20048F32F /* UPModifyVirtualMemberApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPModifyVirtualMemberApi.m; sourceTree = "<group>"; };
		22F6FDCB27965D3B0048F32F /* UDQueryFirstMemeberOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDQueryFirstMemeberOp.h; sourceTree = "<group>"; };
		22F6FDCC27965D3B0048F32F /* UDQueryFirstMemeberOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDQueryFirstMemeberOp.m; sourceTree = "<group>"; };
		22F6FDCF27965D610048F32F /* UDAddVirtualMemberOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDAddVirtualMemberOp.h; sourceTree = "<group>"; };
		22F6FDD027965D610048F32F /* UDAddVirtualMemberOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDAddVirtualMemberOp.m; sourceTree = "<group>"; };
		22F6FDD327965D820048F32F /* UDModifyVirtualMemberOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDModifyVirtualMemberOp.h; sourceTree = "<group>"; };
		22F6FDD427965D820048F32F /* UDModifyVirtualMemberOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDModifyVirtualMemberOp.m; sourceTree = "<group>"; };
		22F6FDD72796A54B0048F32F /* QueryFirstMemebeTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QueryFirstMemebeTransformer.h; sourceTree = "<group>"; };
		22F6FDD82796A54B0048F32F /* QueryFirstMemebeTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QueryFirstMemebeTransformer.m; sourceTree = "<group>"; };
		28B017AD855424FCE3E0C745 /* Pods-Debugger.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Debugger.release.xcconfig"; path = "Target Support Files/Pods-Debugger/Pods-Debugger.release.xcconfig"; sourceTree = "<group>"; };
		2F763910D9DB3EF678D1714A /* Pods-upuserdomainTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-upuserdomainTests.release.xcconfig"; path = "Target Support Files/Pods-upuserdomainTests/Pods-upuserdomainTests.release.xcconfig"; sourceTree = "<group>"; };
		2FEB87918B976CE9D9222179 /* Pods-UserDomainDataSource.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UserDomainDataSource.release.xcconfig"; path = "Target Support Files/Pods-UserDomainDataSource/Pods-UserDomainDataSource.release.xcconfig"; sourceTree = "<group>"; };
		334AFE1A267343C0003F443B /* SYNServerAPIBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SYNServerAPIBase.h; sourceTree = "<group>"; };
		334AFE1B267343C0003F443B /* SYNServerAPIBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SYNServerAPIBase.m; sourceTree = "<group>"; };
		52E71B0A3E6331C9E1C03E93 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS12.2.sdk/System/Library/Frameworks/CoreTelephony.framework; sourceTree = DEVELOPER_DIR; };
		54F389628B7448F69016EEC7 /* Pods-Debugger.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Debugger.debug.xcconfig"; path = "Target Support Files/Pods-Debugger/Pods-Debugger.debug.xcconfig"; sourceTree = "<group>"; };
		634BFDFEBFFECDC5A9F6AE90 /* Pods-UserDomain.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UserDomain.release.xcconfig"; path = "Target Support Files/Pods-UserDomain/Pods-UserDomain.release.xcconfig"; sourceTree = "<group>"; };
		71035FDD24FC858100A9D036 /* DeviceListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DeviceListViewController.h; sourceTree = "<group>"; };
		71035FDE24FC858100A9D036 /* DeviceListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DeviceListViewController.m; sourceTree = "<group>"; };
		71035FE024FC8AD800A9D036 /* DeviceDetailViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DeviceDetailViewController.h; sourceTree = "<group>"; };
		71035FE124FC8AD800A9D036 /* DeviceDetailViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DeviceDetailViewController.m; sourceTree = "<group>"; };
		71035FE324FC918A00A9D036 /* FamilySelectionViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FamilySelectionViewController.h; sourceTree = "<group>"; };
		71035FE424FC918A00A9D036 /* FamilySelectionViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FamilySelectionViewController.m; sourceTree = "<group>"; };
		7155373A24F5F8EA00BAA7D7 /* UserInfoEditViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserInfoEditViewController.h; sourceTree = "<group>"; };
		7155373B24F5F8EA00BAA7D7 /* UserInfoEditViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserInfoEditViewController.m; sourceTree = "<group>"; };
		7155373E24F5FFF800BAA7D7 /* LoginSettings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LoginSettings.h; sourceTree = "<group>"; };
		7155373F24F5FFF800BAA7D7 /* LoginSettings.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LoginSettings.m; sourceTree = "<group>"; };
		7155374124F6467600BAA7D7 /* SEUserLoginApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SEUserLoginApi.h; sourceTree = "<group>"; };
		7155374224F6467600BAA7D7 /* SEUserLoginApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SEUserLoginApi.m; sourceTree = "<group>"; };
		71AF41BD24B5ACD5007A8372 /* UserDomainAPIs.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserDomainAPIs.h; sourceTree = "<group>"; };
		71AF41BE24B5ACD5007A8372 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		71AF41C924B5AD6A007A8372 /* SEUserLogoutAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEUserLogoutAPI.m; sourceTree = "<group>"; };
		71AF41CA24B5AD6A007A8372 /* SEUserInfoAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEUserInfoAPI.h; sourceTree = "<group>"; };
		71AF41CB24B5AD6A007A8372 /* SEUserModifyAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEUserModifyAPI.h; sourceTree = "<group>"; };
		71AF41CC24B5AD6A007A8372 /* SEUserRefreshTokenAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEUserRefreshTokenAPI.m; sourceTree = "<group>"; };
		71AF41CD24B5AD6A007A8372 /* SEUserLogoutAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEUserLogoutAPI.h; sourceTree = "<group>"; };
		71AF41CE24B5AD6A007A8372 /* SEUserRefreshTokenAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEUserRefreshTokenAPI.h; sourceTree = "<group>"; };
		71AF41CF24B5AD6A007A8372 /* SEUserModifyAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEUserModifyAPI.m; sourceTree = "<group>"; };
		71AF41D024B5AD6A007A8372 /* SEUserInfoAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEUserInfoAPI.m; sourceTree = "<group>"; };
		71AF41D224B5AD6A007A8372 /* SEEncryption.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEEncryption.h; sourceTree = "<group>"; };
		71AF41D324B5AD6A007A8372 /* SERequestBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SERequestBase.h; sourceTree = "<group>"; };
		71AF41D424B5AD6A007A8372 /* SERequestConfig.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SERequestConfig.m; sourceTree = "<group>"; };
		71AF41D524B5AD6A007A8372 /* SEEncryption.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEEncryption.m; sourceTree = "<group>"; };
		71AF41D624B5AD6A007A8372 /* SERequestConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SERequestConfig.h; sourceTree = "<group>"; };
		71AF41D724B5AD6A007A8372 /* SERequestBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SERequestBase.m; sourceTree = "<group>"; };
		71AF41D924B5AD6B007A8372 /* SECloudUserAuthInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SECloudUserAuthInfo.h; sourceTree = "<group>"; };
		71AF41DA24B5AD6B007A8372 /* SECloudUserInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SECloudUserInfo.h; sourceTree = "<group>"; };
		71AF41DB24B5AD6B007A8372 /* SECloudDeviceInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SECloudDeviceInfo.m; sourceTree = "<group>"; };
		71AF41DC24B5AD6B007A8372 /* SECloudUserInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SECloudUserInfo.m; sourceTree = "<group>"; };
		71AF41DD24B5AD6B007A8372 /* SECloudUserAuthInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SECloudUserAuthInfo.m; sourceTree = "<group>"; };
		71AF41DE24B5AD6B007A8372 /* SECloudDeviceInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SECloudDeviceInfo.h; sourceTree = "<group>"; };
		71AF41E024B5AD6B007A8372 /* SEUnbindDeviceAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEUnbindDeviceAPI.m; sourceTree = "<group>"; };
		71AF41E124B5AD6B007A8372 /* SEQueryDeviceListAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEQueryDeviceListAPI.h; sourceTree = "<group>"; };
		71AF41E224B5AD6B007A8372 /* SEModifyDeviceInfoAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEModifyDeviceInfoAPI.h; sourceTree = "<group>"; };
		71AF41E324B5AD6B007A8372 /* SEModifyDeviceInfoAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEModifyDeviceInfoAPI.m; sourceTree = "<group>"; };
		71AF41E424B5AD6B007A8372 /* SEQueryDeviceListAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEQueryDeviceListAPI.m; sourceTree = "<group>"; };
		71AF41E524B5AD6B007A8372 /* SEUnbindDeviceAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEUnbindDeviceAPI.h; sourceTree = "<group>"; };
		71AF41E824B5AD6B007A8372 /* UPSetDefaultFamilyApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPSetDefaultFamilyApi.h; sourceTree = "<group>"; };
		71AF41E924B5AD6B007A8372 /* UPFamilyUnbindDevicesApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPFamilyUnbindDevicesApi.m; sourceTree = "<group>"; };
		71AF41EA24B5AD6B007A8372 /* UPRemoveRoomFromFamilyApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPRemoveRoomFromFamilyApi.m; sourceTree = "<group>"; };
		71AF41EB24B5AD6B007A8372 /* UPQuitFamilyAsMemberApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPQuitFamilyAsMemberApi.h; sourceTree = "<group>"; };
		71AF41EC24B5AD6B007A8372 /* UPFamilyDeleteMemberApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFamilyDeleteMemberApi.h; sourceTree = "<group>"; };
		71AF41ED24B5AD6B007A8372 /* UPFamilyRemoveDevicesApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPFamilyRemoveDevicesApi.m; sourceTree = "<group>"; };
		71AF41EE24B5AD6B007A8372 /* UPUpdateFamilyRoomNameApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUpdateFamilyRoomNameApi.h; sourceTree = "<group>"; };
		71AF41EF24B5AD6B007A8372 /* UPQuitFamilyAsAdminApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPQuitFamilyAsAdminApi.m; sourceTree = "<group>"; };
		71AF41F024B5AD6B007A8372 /* UPFamilyRoomListApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFamilyRoomListApi.h; sourceTree = "<group>"; };
		71AF41F124B5AD6B007A8372 /* UPFamilyMoveDevicesToOtherFamilyApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPFamilyMoveDevicesToOtherFamilyApi.m; sourceTree = "<group>"; };
		71AF41F224B5AD6B007A8372 /* UPFamilyMemberShareDeviceCountApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFamilyMemberShareDeviceCountApi.h; sourceTree = "<group>"; };
		71AF41F324B5AD6B007A8372 /* UPChangeFamilyAdminApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPChangeFamilyAdminApi.h; sourceTree = "<group>"; };
		71AF41F424B5AD6B007A8372 /* UPAddRoomToFamilyApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPAddRoomToFamilyApi.h; sourceTree = "<group>"; };
		71AF41F524B5AD6B007A8372 /* UPdateFamilyInfoApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPdateFamilyInfoApi.m; sourceTree = "<group>"; };
		71AF41F624B5AD6B007A8372 /* UPFamilyInviteMemberApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFamilyInviteMemberApi.h; sourceTree = "<group>"; };
		71AF41F724B5AD6B007A8372 /* UPCreateFamilyApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPCreateFamilyApi.h; sourceTree = "<group>"; };
		71AF41F924B5AD6B007A8372 /* UPFamilyMoveDevicesToNewRoomApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPFamilyMoveDevicesToNewRoomApi.m; sourceTree = "<group>"; };
		71AF41FA24B5AD6B007A8372 /* UPFamilyReplyApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFamilyReplyApi.h; sourceTree = "<group>"; };
		71AF41FB24B5AD6B007A8372 /* UPDestroyFamilyApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPDestroyFamilyApi.m; sourceTree = "<group>"; };
		71AF41FC24B5AD6B007A8372 /* UPFamilyListApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPFamilyListApi.m; sourceTree = "<group>"; };
		71AF41FD24B5AD6B007A8372 /* UPFamilyInfoApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPFamilyInfoApi.m; sourceTree = "<group>"; };
		71AF41FE24B5AD6B007A8372 /* UPUpdateFamilyRoomNameApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUpdateFamilyRoomNameApi.m; sourceTree = "<group>"; };
		71AF41FF24B5AD6B007A8372 /* UPFamilyRemoveDevicesApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFamilyRemoveDevicesApi.h; sourceTree = "<group>"; };
		71AF420024B5AD6B007A8372 /* UPFamilyDeleteMemberApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPFamilyDeleteMemberApi.m; sourceTree = "<group>"; };
		71AF420124B5AD6B007A8372 /* UPRemoveRoomFromFamilyApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPRemoveRoomFromFamilyApi.h; sourceTree = "<group>"; };
		71AF420224B5AD6B007A8372 /* UPFamilyUnbindDevicesApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFamilyUnbindDevicesApi.h; sourceTree = "<group>"; };
		71AF420324B5AD6B007A8372 /* UPQuitFamilyAsMemberApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPQuitFamilyAsMemberApi.m; sourceTree = "<group>"; };
		71AF420424B5AD6B007A8372 /* UPSetDefaultFamilyApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPSetDefaultFamilyApi.m; sourceTree = "<group>"; };
		71AF420524B5AD6B007A8372 /* UPFamilyMemberShareDeviceCountApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPFamilyMemberShareDeviceCountApi.m; sourceTree = "<group>"; };
		71AF420624B5AD6B007A8372 /* UPFamilyMoveDevicesToOtherFamilyApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFamilyMoveDevicesToOtherFamilyApi.h; sourceTree = "<group>"; };
		71AF420724B5AD6B007A8372 /* UPFamilyRoomListApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPFamilyRoomListApi.m; sourceTree = "<group>"; };
		71AF420824B5AD6B007A8372 /* UPQuitFamilyAsAdminApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPQuitFamilyAsAdminApi.h; sourceTree = "<group>"; };
		71AF420924B5AD6B007A8372 /* UPFamilyMoveDevicesToNewRoomApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFamilyMoveDevicesToNewRoomApi.h; sourceTree = "<group>"; };
		71AF420B24B5AD6B007A8372 /* UPCreateFamilyApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPCreateFamilyApi.m; sourceTree = "<group>"; };
		71AF420C24B5AD6B007A8372 /* UPFamilyInviteMemberApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPFamilyInviteMemberApi.m; sourceTree = "<group>"; };
		71AF420D24B5AD6B007A8372 /* UPdateFamilyInfoApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPdateFamilyInfoApi.h; sourceTree = "<group>"; };
		71AF420E24B5AD6B007A8372 /* UPAddRoomToFamilyApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPAddRoomToFamilyApi.m; sourceTree = "<group>"; };
		71AF420F24B5AD6B007A8372 /* UPChangeFamilyAdminApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPChangeFamilyAdminApi.m; sourceTree = "<group>"; };
		71AF421024B5AD6B007A8372 /* UPFamilyInfoApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFamilyInfoApi.h; sourceTree = "<group>"; };
		71AF421124B5AD6B007A8372 /* UPFamilyListApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFamilyListApi.h; sourceTree = "<group>"; };
		71AF421224B5AD6B007A8372 /* UPDestroyFamilyApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPDestroyFamilyApi.h; sourceTree = "<group>"; };
		71AF421324B5AD6B007A8372 /* UPFamilyReplyApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPFamilyReplyApi.m; sourceTree = "<group>"; };
		71AF421524B5AD6B007A8372 /* UPUserDeleteAddressApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserDeleteAddressApi.h; sourceTree = "<group>"; };
		71AF421624B5AD6B007A8372 /* UPUserQRCancleLoginApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserQRCancleLoginApi.h; sourceTree = "<group>"; };
		71AF421724B5AD6B007A8372 /* UPZJRefreshTokenApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPZJRefreshTokenApi.h; sourceTree = "<group>"; };
		71AF421824B5AD6B007A8372 /* UPZJUserInfoApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPZJUserInfoApi.h; sourceTree = "<group>"; };
		71AF421924B5AD6B007A8372 /* UPUserLoginTerminalApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserLoginTerminalApi.m; sourceTree = "<group>"; };
		71AF421A24B5AD6B007A8372 /* UPUserApplitionTokenApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserApplitionTokenApi.m; sourceTree = "<group>"; };
		71AF421B24B5AD6B007A8372 /* UPUserAccountVerifyApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserAccountVerifyApi.m; sourceTree = "<group>"; };
		71AF421D24B5AD6B007A8372 /* UPUserCreateNewAddressApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserCreateNewAddressApi.h; sourceTree = "<group>"; };
		71AF421E24B5AD6B007A8372 /* UPUserLoginLogsApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserLoginLogsApi.m; sourceTree = "<group>"; };
		71AF421F24B5AD6B007A8372 /* UPUserServiceOrderApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserServiceOrderApi.m; sourceTree = "<group>"; };
		71AF422024B5AD6B007A8372 /* UPUserQRConfirmLoginApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserQRConfirmLoginApi.h; sourceTree = "<group>"; };
		71AF422124B5AD6B007A8372 /* UPUserAddressApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserAddressApi.h; sourceTree = "<group>"; };
		71AF422324B5AD6B007A8372 /* UPUserInfoApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserInfoApi.m; sourceTree = "<group>"; };
		71AF422424B5AD6B007A8372 /* UPUserQRScanApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserQRScanApi.h; sourceTree = "<group>"; };
		71AF422524B5AD6B007A8372 /* UPUserUploadAvatarApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserUploadAvatarApi.h; sourceTree = "<group>"; };
		71AF422624B5AD6B007A8372 /* UPUserQRloginPollApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserQRloginPollApi.h; sourceTree = "<group>"; };
		71AF422724B5AD6B007A8372 /* UPUserUpdatePwdApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserUpdatePwdApi.m; sourceTree = "<group>"; };
		71AF422824B5AD6B007A8372 /* UPUserUpdateUserInfoApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserUpdateUserInfoApi.h; sourceTree = "<group>"; };
		71AF422924B5AD6B007A8372 /* UPUserEditAddressApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserEditAddressApi.h; sourceTree = "<group>"; };
		71AF422A24B5AD6B007A8372 /* UPUserRefreshTokenApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserRefreshTokenApi.m; sourceTree = "<group>"; };
		71AF422B24B5AD6B007A8372 /* UPZJRefreshTokenApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPZJRefreshTokenApi.m; sourceTree = "<group>"; };
		71AF422C24B5AD6B007A8372 /* UPUserQRCancleLoginApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserQRCancleLoginApi.m; sourceTree = "<group>"; };
		71AF422D24B5AD6B007A8372 /* UPUserDeleteAddressApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserDeleteAddressApi.m; sourceTree = "<group>"; };
		71AF422E24B5AD6B007A8372 /* UPUserLoginLogsApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserLoginLogsApi.h; sourceTree = "<group>"; };
		71AF422F24B5AD6B007A8372 /* UPUserServiceOrderApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserServiceOrderApi.h; sourceTree = "<group>"; };
		71AF423024B5AD6B007A8372 /* UPUserCreateNewAddressApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserCreateNewAddressApi.m; sourceTree = "<group>"; };
		71AF423224B5AD6B007A8372 /* UPUserAccountVerifyApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserAccountVerifyApi.h; sourceTree = "<group>"; };
		71AF423324B5AD6B007A8372 /* UPUserApplitionTokenApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserApplitionTokenApi.h; sourceTree = "<group>"; };
		71AF423424B5AD6B007A8372 /* UPZJUserInfoApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPZJUserInfoApi.m; sourceTree = "<group>"; };
		71AF423524B5AD6B007A8372 /* UPUserLoginTerminalApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserLoginTerminalApi.h; sourceTree = "<group>"; };
		71AF423624B5AD6B007A8372 /* UPUserQRloginPollApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserQRloginPollApi.m; sourceTree = "<group>"; };
		71AF423724B5AD6B007A8372 /* UPUserUploadAvatarApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserUploadAvatarApi.m; sourceTree = "<group>"; };
		71AF423824B5AD6B007A8372 /* UPUserQRScanApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserQRScanApi.m; sourceTree = "<group>"; };
		71AF423924B5AD6B007A8372 /* UPUserInfoApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserInfoApi.h; sourceTree = "<group>"; };
		71AF423B24B5AD6B007A8372 /* UPUserAddressApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserAddressApi.m; sourceTree = "<group>"; };
		71AF423C24B5AD6B007A8372 /* UPUserQRConfirmLoginApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserQRConfirmLoginApi.m; sourceTree = "<group>"; };
		71AF423D24B5AD6B007A8372 /* UPUserRefreshTokenApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserRefreshTokenApi.h; sourceTree = "<group>"; };
		71AF423E24B5AD6B007A8372 /* UPUserEditAddressApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserEditAddressApi.m; sourceTree = "<group>"; };
		71AF423F24B5AD6B007A8372 /* UPUserUpdateUserInfoApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserUpdateUserInfoApi.m; sourceTree = "<group>"; };
		71AF424024B5AD6B007A8372 /* UPUserUpdatePwdApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserUpdatePwdApi.h; sourceTree = "<group>"; };
		71AF424224B5AD6B007A8372 /* UserCenterAPIBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserCenterAPIBase.m; sourceTree = "<group>"; };
		71AF424324B5AD6B007A8372 /* APPServerAPIBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = APPServerAPIBase.m; sourceTree = "<group>"; };
		71AF424424B5AD6B007A8372 /* UserCenterAPIBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserCenterAPIBase.h; sourceTree = "<group>"; };
		71AF424524B5AD6B007A8372 /* APPServerAPIBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = APPServerAPIBase.h; sourceTree = "<group>"; };
		71AF424724B5AD6B007A8372 /* UPUpdateDeviceInfoApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUpdateDeviceInfoApi.h; sourceTree = "<group>"; };
		71AF424824B5AD6B007A8372 /* UPDeviceUpdateNameApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPDeviceUpdateNameApi.m; sourceTree = "<group>"; };
		71AF424924B5AD6B007A8372 /* UPDeviceListApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPDeviceListApi.h; sourceTree = "<group>"; };
		71AF424A24B5AD6B007A8372 /* UPDeviceUpdateNameApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPDeviceUpdateNameApi.h; sourceTree = "<group>"; };
		71AF424B24B5AD6C007A8372 /* UPUpdateDeviceInfoApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUpdateDeviceInfoApi.m; sourceTree = "<group>"; };
		71AF424C24B5AD6C007A8372 /* UPDeviceListApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPDeviceListApi.m; sourceTree = "<group>"; };
		71AF42CC24B5AF44007A8372 /* AlicloudBeacon.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AlicloudBeacon.framework; path = Pods/upnetwork/upnetwork/HTTPDns/SDKs/AlicloudBeacon.framework; sourceTree = "<group>"; };
		71AF42CE24B5AF44007A8372 /* AlicloudHttpDNS.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AlicloudHttpDNS.framework; path = Pods/upnetwork/upnetwork/HTTPDns/SDKs/AlicloudHttpDNS.framework; sourceTree = "<group>"; };
		71AF42D024B5AF44007A8372 /* AlicloudUtils.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AlicloudUtils.framework; path = Pods/upnetwork/upnetwork/HTTPDns/SDKs/AlicloudUtils.framework; sourceTree = "<group>"; };
		71AF42D224B5AF44007A8372 /* UTDID.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UTDID.framework; path = Pods/upnetwork/upnetwork/HTTPDns/SDKs/UTDID.framework; sourceTree = "<group>"; };
		71AF42D424B5AF44007A8372 /* UTMini.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UTMini.framework; path = Pods/upnetwork/upnetwork/HTTPDns/SDKs/UTMini.framework; sourceTree = "<group>"; };
		71AF42D624B5AF4D007A8372 /* libsqlite3.0.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.0.tbd; path = usr/lib/libsqlite3.0.tbd; sourceTree = SDKROOT; };
		71AF42D824B5AF56007A8372 /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		71B045B924F34ED0001833D0 /* LoginViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LoginViewController.h; sourceTree = "<group>"; };
		71B045BA24F34ED0001833D0 /* LoginViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LoginViewController.m; sourceTree = "<group>"; };
		71B381E924F4AF7C00F04E48 /* FunctionsTableViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FunctionsTableViewController.h; sourceTree = "<group>"; };
		71B381EA24F4AF7C00F04E48 /* FunctionsTableViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FunctionsTableViewController.m; sourceTree = "<group>"; };
		71B381EC24F4B43900F04E48 /* UserDetailViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserDetailViewController.h; sourceTree = "<group>"; };
		71B381ED24F4B43900F04E48 /* UserDetailViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserDetailViewController.m; sourceTree = "<group>"; };
		71B381F024F505CA00F04E48 /* UserInfoEditViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserInfoEditViewController.m; sourceTree = "<group>"; };
		71ECF7FD241A109F009BBAD0 /* readme.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = readme.md; sourceTree = "<group>"; };
		71ECF7FE241A109F009BBAD0 /* release.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = release.md; sourceTree = "<group>"; };
		71ECF7FF241A10BA009BBAD0 /* upuserdomain.podspec */ = {isa = PBXFileReference; lastKnownFileType = text; path = upuserdomain.podspec; sourceTree = SOURCE_ROOT; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		71ECF940241A2C0D009BBAD0 /* Debugger.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Debugger.app; sourceTree = BUILT_PRODUCTS_DIR; };
		71ECF942241A2C0D009BBAD0 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		71ECF943241A2C0D009BBAD0 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		71ECF948241A2C0D009BBAD0 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		71ECF949241A2C0D009BBAD0 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		71ECF94C241A2C0D009BBAD0 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		71ECF94E241A2C0D009BBAD0 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		71ECF951241A2C0D009BBAD0 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		71ECF953241A2C0D009BBAD0 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		71ECF954241A2C0D009BBAD0 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		72321F952255DE1F005F6A86 /* upuserdomainTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = upuserdomainTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		72321F9B2255DE1F005F6A86 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		72E530E722643E09008992A1 /* CucumberRunner.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CucumberRunner.m; sourceTree = "<group>"; };
		8350DF3AA434AA3EE03764DA /* libPods-upuserdomainTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-upuserdomainTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		84B5D4AB2ABAE35F00C07E3F /* UPDeviceUpdateAndCheckNameApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPDeviceUpdateAndCheckNameApi.h; sourceTree = "<group>"; };
		84B5D4AC2ABAE35F00C07E3F /* UPDeviceUpdateAndCheckNameApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPDeviceUpdateAndCheckNameApi.m; sourceTree = "<group>"; };
		84B5D4AF2ABAED2400C07E3F /* UDModifyDeviceNameNewOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDModifyDeviceNameNewOp.h; sourceTree = "<group>"; };
		84B5D4B02ABAED2400C07E3F /* UDModifyDeviceNameNewOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDModifyDeviceNameNewOp.m; sourceTree = "<group>"; };
		85381D37955E48B54DB8EABE /* libPods-Debugger.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Debugger.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		85439034EEEFBA9A132FD802 /* Pods-UserDomainAPIs.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UserDomainAPIs.release.xcconfig"; path = "Target Support Files/Pods-UserDomainAPIs/Pods-UserDomainAPIs.release.xcconfig"; sourceTree = "<group>"; };
		947E29A54A41CE9CFD62B7D4 /* libPods-UserDomainDataSource.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UserDomainDataSource.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		950119EF2DA9189900F92F77 /* UPModifyMemberTypeApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPModifyMemberTypeApi.h; sourceTree = "<group>"; };
		950119F02DA9189900F92F77 /* UPModifyMemberTypeApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPModifyMemberTypeApi.m; sourceTree = "<group>"; };
		950119F32DA919E700F92F77 /* UDModifyMemberTypeOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDModifyMemberTypeOp.h; sourceTree = "<group>"; };
		950119F42DA919E700F92F77 /* UDModifyMemberTypeOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDModifyMemberTypeOp.m; sourceTree = "<group>"; };
		9524B7722D9398A2009AA5C4 /* DeviceCardStatusArgs.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DeviceCardStatusArgs.h; sourceTree = "<group>"; };
		9524B7732D9398A2009AA5C4 /* DeviceCardStatusArgs.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DeviceCardStatusArgs.m; sourceTree = "<group>"; };
		9524B7762D93991D009AA5C4 /* DeviceCardAggregationArgs.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DeviceCardAggregationArgs.h; sourceTree = "<group>"; };
		9524B7772D93991D009AA5C4 /* DeviceCardAggregationArgs.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DeviceCardAggregationArgs.m; sourceTree = "<group>"; };
		9524B77A2D939A32009AA5C4 /* AggregationSwitchArgs.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AggregationSwitchArgs.h; sourceTree = "<group>"; };
		9524B77B2D939A32009AA5C4 /* AggregationSwitchArgs.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AggregationSwitchArgs.m; sourceTree = "<group>"; };
		955ED3F22DA7DBD300E10807 /* UpAddRoomToFamilyNewApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAddRoomToFamilyNewApi.h; sourceTree = "<group>"; };
		955ED3F32DA7DBD300E10807 /* UpAddRoomToFamilyNewApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpAddRoomToFamilyNewApi.m; sourceTree = "<group>"; };
		955ED3F62DA7DDF500E10807 /* RoomNewArgs.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RoomNewArgs.h; sourceTree = "<group>"; };
		955ED3F72DA7DDF500E10807 /* RoomNewArgs.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RoomNewArgs.m; sourceTree = "<group>"; };
		955ED3FA2DA7E06F00E10807 /* UDCreateRoomNewOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDCreateRoomNewOp.h; sourceTree = "<group>"; };
		955ED3FB2DA7E06F00E10807 /* UDCreateRoomNewOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDCreateRoomNewOp.m; sourceTree = "<group>"; };
		95BEBB142D92951E001A07D7 /* UpEditDeviceCardStatusApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpEditDeviceCardStatusApi.h; sourceTree = "<group>"; };
		95BEBB152D92951E001A07D7 /* UpEditDeviceCardStatusApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpEditDeviceCardStatusApi.m; sourceTree = "<group>"; };
		95BEBB182D9299DA001A07D7 /* UpEditDeviceAggregationApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpEditDeviceAggregationApi.h; sourceTree = "<group>"; };
		95BEBB192D9299DA001A07D7 /* UpEditDeviceAggregationApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpEditDeviceAggregationApi.m; sourceTree = "<group>"; };
		95BEBB1C2D929EF7001A07D7 /* UpEditAggregationSwitchApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpEditAggregationSwitchApi.h; sourceTree = "<group>"; };
		95BEBB1D2D929EF7001A07D7 /* UpEditAggregationSwitchApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpEditAggregationSwitchApi.m; sourceTree = "<group>"; };
		95BEBB202D92CCF8001A07D7 /* UDModifyDeviceCardListOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDModifyDeviceCardListOp.h; sourceTree = "<group>"; };
		95BEBB212D92CCF8001A07D7 /* UDModifyDeviceCardListOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDModifyDeviceCardListOp.m; sourceTree = "<group>"; };
		95BEBB242D92CFEE001A07D7 /* UDModifyDeviceAggOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDModifyDeviceAggOp.h; sourceTree = "<group>"; };
		95BEBB252D92CFEE001A07D7 /* UDModifyDeviceAggOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDModifyDeviceAggOp.m; sourceTree = "<group>"; };
		95BEBB282D92D1E2001A07D7 /* UDModifyAggSwitchOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDModifyAggSwitchOp.h; sourceTree = "<group>"; };
		95BEBB292D92D1E2001A07D7 /* UDModifyAggSwitchOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDModifyAggSwitchOp.m; sourceTree = "<group>"; };
		95EC2CA82DB61D3D00E22D0E /* UPSaveRoomsOrderNewApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPSaveRoomsOrderNewApi.h; sourceTree = "<group>"; };
		95EC2CA92DB61D3D00E22D0E /* UPSaveRoomsOrderNewApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPSaveRoomsOrderNewApi.m; sourceTree = "<group>"; };
		95EC2CAC2DB621D400E22D0E /* UDSaveRoomsOrderNewOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDSaveRoomsOrderNewOp.h; sourceTree = "<group>"; };
		95EC2CAD2DB621D400E22D0E /* UDSaveRoomsOrderNewOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDSaveRoomsOrderNewOp.m; sourceTree = "<group>"; };
		95F07CA62DB255AB007CB875 /* AdminInviteMemberArgs.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AdminInviteMemberArgs.h; sourceTree = "<group>"; };
		95F07CA72DB255AB007CB875 /* AdminInviteMemberArgs.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AdminInviteMemberArgs.m; sourceTree = "<group>"; };
		9B86F7B4C8358BB49AA1BEAC /* libPods-UserDomain.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UserDomain.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		9CB172DB8237553ABC5D5DA7 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS12.2.sdk/System/Library/Frameworks/SystemConfiguration.framework; sourceTree = DEVELOPER_DIR; };
		A65CF39308F3D3BA350A8097 /* Pods-UserDomainAPIs.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UserDomainAPIs.debug.xcconfig"; path = "Target Support Files/Pods-UserDomainAPIs/Pods-UserDomainAPIs.debug.xcconfig"; sourceTree = "<group>"; };
		AD12D63226438B7F00188BC3 /* libupnetwork.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libupnetwork.a; sourceTree = BUILT_PRODUCTS_DIR; };
		AD12D63826438F9F00188BC3 /* libupuserdomain.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libupuserdomain.a; sourceTree = BUILT_PRODUCTS_DIR; };
		AD12D7492643933E00188BC3 /* libUserDomainDataSource.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libUserDomainDataSource.a; sourceTree = BUILT_PRODUCTS_DIR; };
		AD5D444024CFB4C800211C3F /* UpUserDomainListenerDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpUserDomainListenerDelegate.h; sourceTree = "<group>"; };
		AD5D444424CFBF7E00211C3F /* UDOperatorManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDOperatorManager.h; sourceTree = "<group>"; };
		AD5D444524CFBF7E00211C3F /* UDOperatorManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDOperatorManager.m; sourceTree = "<group>"; };
		AD5D444F24CFCFAD00211C3F /* UDRefreshTokenOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDRefreshTokenOp.h; sourceTree = "<group>"; };
		AD5D445024CFCFAD00211C3F /* UDRefreshTokenOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDRefreshTokenOp.m; sourceTree = "<group>"; };
		AD5D446724D110ED00211C3F /* UpEvent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpEvent.h; sourceTree = "<group>"; };
		AD5D446824D110ED00211C3F /* UpEvent.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpEvent.m; sourceTree = "<group>"; };
		AD5D446B24D1238600211C3F /* UpEventHandlerDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpEventHandlerDelegate.h; sourceTree = "<group>"; };
		AD5D447124D141D700211C3F /* UpNotifyEventHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpNotifyEventHandler.h; sourceTree = "<group>"; };
		AD5D447224D141D700211C3F /* UpNotifyEventHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpNotifyEventHandler.m; sourceTree = "<group>"; };
		AD5D447524D1475500211C3F /* UpUserDomainStore.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpUserDomainStore.h; sourceTree = "<group>"; };
		AD5D447624D1475500211C3F /* UpUserDomainStore.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpUserDomainStore.m; sourceTree = "<group>"; };
		AD5D448F24D16D9200211C3F /* UpUserDomainCacheDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpUserDomainCacheDelegate.h; sourceTree = "<group>"; };
		AD5D449124D1752C00211C3F /* UpEventHandlerManagerDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpEventHandlerManagerDelegate.h; sourceTree = "<group>"; };
		AD5D449724D19B9000211C3F /* UpUserStore.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpUserStore.h; sourceTree = "<group>"; };
		AD5D449824D19B9000211C3F /* UpUserStore.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpUserStore.m; sourceTree = "<group>"; };
		AD5D44A324D1A40800211C3F /* UDLogoutOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDLogoutOp.h; sourceTree = "<group>"; };
		AD5D44A424D1A40800211C3F /* UDLogoutOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDLogoutOp.m; sourceTree = "<group>"; };
		AD5D44A724D253F400211C3F /* UDRefreshDeviceListOp.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UDRefreshDeviceListOp.m; sourceTree = "<group>"; };
		AD5D44A924D253F400211C3F /* UDModifyDeviceNameOp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDModifyDeviceNameOp.h; sourceTree = "<group>"; };
		AD5D44AA24D253F500211C3F /* UDModifyDeviceNameOp.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UDModifyDeviceNameOp.m; sourceTree = "<group>"; };
		AD5D44AB24D253F500211C3F /* UDRefreshDeviceListOp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDRefreshDeviceListOp.h; sourceTree = "<group>"; };
		AD5D44B724D26C4000211C3F /* UDChangeFamilyAdminOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDChangeFamilyAdminOp.h; sourceTree = "<group>"; };
		AD5D44B824D26C4000211C3F /* UDChangeFamilyAdminOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDChangeFamilyAdminOp.m; sourceTree = "<group>"; };
		AD5D45A024D2EEC800211C3F /* libupnetwork.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libupnetwork.a; sourceTree = BUILT_PRODUCTS_DIR; };
		AD5D45A624D3F1A300211C3F /* UpEventHandlerManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpEventHandlerManager.h; sourceTree = "<group>"; };
		AD5D45A724D3F1A300211C3F /* UpEventHandlerManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpEventHandlerManager.m; sourceTree = "<group>"; };
		AD5D45AA24D3FDFD00211C3F /* UpListenerTransformObserver.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpListenerTransformObserver.h; sourceTree = "<group>"; };
		AD5D45AB24D3FDFD00211C3F /* UpListenerTransformObserver.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpListenerTransformObserver.m; sourceTree = "<group>"; };
		AD5D45AE24D6D94800211C3F /* UDUserDomainCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDUserDomainCache.h; sourceTree = "<group>"; };
		AD5D45AF24D6D94800211C3F /* UDUserDomainCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDUserDomainCache.m; sourceTree = "<group>"; };
		AD5D45B624D6DEFD00211C3F /* UpDeviceStore.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpDeviceStore.h; sourceTree = "<group>"; };
		AD5D45B724D6DEFD00211C3F /* UpDeviceStore.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpDeviceStore.m; sourceTree = "<group>"; };
		AD5D45BA24D6DF0B00211C3F /* UpFamilyStore.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpFamilyStore.h; sourceTree = "<group>"; };
		AD5D45BB24D6DF0B00211C3F /* UpFamilyStore.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpFamilyStore.m; sourceTree = "<group>"; };
		AD5D45C224D6F23600211C3F /* UPUserDomainSettings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPUserDomainSettings.h; sourceTree = "<group>"; };
		AD5D45C324D6F23600211C3F /* UPUserDomainSettings.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPUserDomainSettings.m; sourceTree = "<group>"; };
		AD62066324D941B200C9B414 /* UDRefreshRoomListOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDRefreshRoomListOp.h; sourceTree = "<group>"; };
		AD62066424D941B200C9B414 /* UDRefreshRoomListOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDRefreshRoomListOp.m; sourceTree = "<group>"; };
		AD62066724D941C700C9B414 /* UDCreateFamilyOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDCreateFamilyOp.h; sourceTree = "<group>"; };
		AD62066824D941C700C9B414 /* UDCreateFamilyOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDCreateFamilyOp.m; sourceTree = "<group>"; };
		AD62066B24D941D700C9B414 /* UDCreateRoomOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDCreateRoomOp.h; sourceTree = "<group>"; };
		AD62066C24D941D700C9B414 /* UDCreateRoomOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDCreateRoomOp.m; sourceTree = "<group>"; };
		AD62066F24D941E900C9B414 /* UDDeleteFamilyAsAdminOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDDeleteFamilyAsAdminOp.h; sourceTree = "<group>"; };
		AD62067024D941E900C9B414 /* UDDeleteFamilyAsAdminOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDDeleteFamilyAsAdminOp.m; sourceTree = "<group>"; };
		AD62067324D9420200C9B414 /* UDDeleteRoomOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDDeleteRoomOp.h; sourceTree = "<group>"; };
		AD62067424D9420200C9B414 /* UDDeleteRoomOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDDeleteRoomOp.m; sourceTree = "<group>"; };
		AD62067724D9421100C9B414 /* UDEditFamilyInfoOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDEditFamilyInfoOp.h; sourceTree = "<group>"; };
		AD62067824D9421100C9B414 /* UDEditFamilyInfoOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDEditFamilyInfoOp.m; sourceTree = "<group>"; };
		AD62067B24D9421F00C9B414 /* UDEditRoomNameOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDEditRoomNameOp.h; sourceTree = "<group>"; };
		AD62067C24D9421F00C9B414 /* UDEditRoomNameOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDEditRoomNameOp.m; sourceTree = "<group>"; };
		AD62067F24D9422C00C9B414 /* UDExitFamilyAsAdminOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDExitFamilyAsAdminOp.h; sourceTree = "<group>"; };
		AD62068024D9422C00C9B414 /* UDExitFamilyAsAdminOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDExitFamilyAsAdminOp.m; sourceTree = "<group>"; };
		AD62068324D9423D00C9B414 /* UDExitFamilyAsMemberOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDExitFamilyAsMemberOp.h; sourceTree = "<group>"; };
		AD62068424D9423D00C9B414 /* UDExitFamilyAsMemberOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDExitFamilyAsMemberOp.m; sourceTree = "<group>"; };
		AD62068724D9425500C9B414 /* UDInviteFamilyMemberOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDInviteFamilyMemberOp.h; sourceTree = "<group>"; };
		AD62068824D9425500C9B414 /* UDInviteFamilyMemberOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDInviteFamilyMemberOp.m; sourceTree = "<group>"; };
		AD62068B24D9426500C9B414 /* UDMoveDevicesToFamilyOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDMoveDevicesToFamilyOp.h; sourceTree = "<group>"; };
		AD62068C24D9426500C9B414 /* UDMoveDevicesToFamilyOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDMoveDevicesToFamilyOp.m; sourceTree = "<group>"; };
		AD62068F24D9508500C9B414 /* UDPlannedRefreshAuthDataOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDPlannedRefreshAuthDataOp.h; sourceTree = "<group>"; };
		AD62069024D9508500C9B414 /* UDPlannedRefreshAuthDataOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDPlannedRefreshAuthDataOp.m; sourceTree = "<group>"; };
		AD62069724D950BA00C9B414 /* UDRefreshUserOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDRefreshUserOp.h; sourceTree = "<group>"; };
		AD62069824D950BA00C9B414 /* UDRefreshUserOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDRefreshUserOp.m; sourceTree = "<group>"; };
		AD7AF97424F7AB960074F36F /* FamilyMemberViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FamilyMemberViewController.h; sourceTree = "<group>"; };
		AD7AF97524F7AB960074F36F /* FamilyMemberViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FamilyMemberViewController.m; sourceTree = "<group>"; };
		AD7AF97724F7D8720074F36F /* CommonEditCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommonEditCell.h; sourceTree = "<group>"; };
		AD7AF97824F7D8720074F36F /* CommonEditCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommonEditCell.m; sourceTree = "<group>"; };
		AD7AF97A24F7E48D0074F36F /* CreatFamilyViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CreatFamilyViewController.h; sourceTree = "<group>"; };
		AD7AF97B24F7E48D0074F36F /* CreatFamilyViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CreatFamilyViewController.m; sourceTree = "<group>"; };
		AD7AF97D24F8AEF20074F36F /* DeviceActionView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DeviceActionView.h; sourceTree = "<group>"; };
		AD7AF97E24F8AEF20074F36F /* DeviceActionView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DeviceActionView.m; sourceTree = "<group>"; };
		AD81E85924F4A3DD002430CA /* FamilyListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FamilyListViewController.h; sourceTree = "<group>"; };
		AD81E85A24F4A3DD002430CA /* FamilyListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FamilyListViewController.m; sourceTree = "<group>"; };
		AD81E85C24F4DEA8002430CA /* FamilyDetailViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FamilyDetailViewController.h; sourceTree = "<group>"; };
		AD81E85D24F4DEA8002430CA /* FamilyDetailViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FamilyDetailViewController.m; sourceTree = "<group>"; };
		AD81E86124F60E4A002430CA /* MyFamilyController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyFamilyController.h; sourceTree = "<group>"; };
		AD81E86224F60E4A002430CA /* MyFamilyController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyFamilyController.m; sourceTree = "<group>"; };
		AD81E86724F63522002430CA /* AddressPickView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AddressPickView.h; sourceTree = "<group>"; };
		AD81E86824F63522002430CA /* AddressPickView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AddressPickView.m; sourceTree = "<group>"; };
		AD81E86A24F637E6002430CA /* Address.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Address.plist; sourceTree = "<group>"; };
		AD81E86C24F64DDD002430CA /* FamilyDevicesViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FamilyDevicesViewController.h; sourceTree = "<group>"; };
		AD81E86D24F64DDD002430CA /* FamilyDevicesViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FamilyDevicesViewController.m; sourceTree = "<group>"; };
		AD81E86F24F75B40002430CA /* FamilyOwnerViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FamilyOwnerViewController.h; sourceTree = "<group>"; };
		AD81E87024F75B40002430CA /* FamilyOwnerViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FamilyOwnerViewController.m; sourceTree = "<group>"; };
		AD92EDCF2632997500C9544A /* UserDomainDataSource.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserDomainDataSource.h; sourceTree = "<group>"; };
		AD92EDD02632997500C9544A /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		AD92EED72632A0C000C9544A /* UpFamilyDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpFamilyDataSource.h; sourceTree = "<group>"; };
		AD92EED82632A0C000C9544A /* UpUserDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpUserDataSource.h; sourceTree = "<group>"; };
		AD92EED92632A0C000C9544A /* UpDeviceListDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpDeviceListDataSource.h; sourceTree = "<group>"; };
		AD92EEE02632A0CD00C9544A /* CloudDeviceBaseInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudDeviceBaseInfo.h; sourceTree = "<group>"; };
		AD92EEE12632A0CD00C9544A /* CloudDeviceAuth.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudDeviceAuth.h; sourceTree = "<group>"; };
		AD92EEE22632A0CD00C9544A /* CloudDevicePermission.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudDevicePermission.m; sourceTree = "<group>"; };
		AD92EEE32632A0CD00C9544A /* CloudBatchProcessDevicesResponse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudBatchProcessDevicesResponse.h; sourceTree = "<group>"; };
		AD92EEE42632A0CD00C9544A /* CloudDeviceList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudDeviceList.h; sourceTree = "<group>"; };
		AD92EEE52632A0CD00C9544A /* CloudDevice.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudDevice.h; sourceTree = "<group>"; };
		AD92EEE62632A0CD00C9544A /* CloudDeviceExtendedInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudDeviceExtendedInfo.m; sourceTree = "<group>"; };
		AD92EEE72632A0CD00C9544A /* CloudDeviceOwnerInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudDeviceOwnerInfo.m; sourceTree = "<group>"; };
		AD92EEE82632A0CD00C9544A /* CloudQRLoginStage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudQRLoginStage.h; sourceTree = "<group>"; };
		AD92EEE92632A0CD00C9544A /* CloudBatchProcessDevicesResponse.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudBatchProcessDevicesResponse.m; sourceTree = "<group>"; };
		AD92EEEA2632A0CD00C9544A /* CloudDevicePermission.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudDevicePermission.h; sourceTree = "<group>"; };
		AD92EEEB2632A0CD00C9544A /* CloudDeviceAuth.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudDeviceAuth.m; sourceTree = "<group>"; };
		AD92EEEC2632A0CD00C9544A /* CloudDeviceBaseInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudDeviceBaseInfo.m; sourceTree = "<group>"; };
		AD92EEED2632A0CD00C9544A /* CloudDeviceList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudDeviceList.m; sourceTree = "<group>"; };
		AD92EEEE2632A0CD00C9544A /* CloudDeviceExtendedInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudDeviceExtendedInfo.h; sourceTree = "<group>"; };
		AD92EEEF2632A0CD00C9544A /* CloudDevice.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudDevice.m; sourceTree = "<group>"; };
		AD92EEF02632A0CD00C9544A /* CloudQRLoginStage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudQRLoginStage.m; sourceTree = "<group>"; };
		AD92EEF12632A0CD00C9544A /* CloudDeviceOwnerInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudDeviceOwnerInfo.h; sourceTree = "<group>"; };
		AD92EEF32632A0CD00C9544A /* CloudUserLoginLogInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudUserLoginLogInfo.h; sourceTree = "<group>"; };
		AD92EEF52632A0CD00C9544A /* CloudLoginTerminal.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudLoginTerminal.m; sourceTree = "<group>"; };
		AD92EEF62632A0CD00C9544A /* CloudUserInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudUserInfo.h; sourceTree = "<group>"; };
		AD92EEF72632A0CD00C9544A /* CloudUserAddressInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudUserAddressInfo.h; sourceTree = "<group>"; };
		AD92EEF82632A0CD00C9544A /* CloudOauthData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudOauthData.m; sourceTree = "<group>"; };
		AD92EEF92632A0CD00C9544A /* CloudUserLoginLogInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudUserLoginLogInfo.m; sourceTree = "<group>"; };
		AD92EEFB2632A0CD00C9544A /* CloudUserInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudUserInfo.m; sourceTree = "<group>"; };
		AD92EEFC2632A0CD00C9544A /* CloudLoginTerminal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudLoginTerminal.h; sourceTree = "<group>"; };
		AD92EEFD2632A0CD00C9544A /* CloudOauthData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudOauthData.h; sourceTree = "<group>"; };
		AD92EEFE2632A0CD00C9544A /* CloudUserAddressInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudUserAddressInfo.m; sourceTree = "<group>"; };
		AD92EF002632A0CD00C9544A /* CloudFamilyLocation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudFamilyLocation.m; sourceTree = "<group>"; };
		AD92EF012632A0CD00C9544A /* CloudFamilyMember.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudFamilyMember.m; sourceTree = "<group>"; };
		AD92EF022632A0CD00C9544A /* CloudFamilyMemberInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudFamilyMemberInfo.h; sourceTree = "<group>"; };
		AD92EF032632A0CD00C9544A /* CloudRoom.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudRoom.m; sourceTree = "<group>"; };
		AD92EF042632A0CD00C9544A /* CloudFamilyFirstMember.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudFamilyFirstMember.m; sourceTree = "<group>"; };
		AD92EF052632A0CD00C9544A /* CloudFamily.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudFamily.m; sourceTree = "<group>"; };
		AD92EF062632A0CD00C9544A /* CloudFamilyList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudFamilyList.m; sourceTree = "<group>"; };
		AD92EF072632A0CD00C9544A /* CloudFloorInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudFloorInfo.m; sourceTree = "<group>"; };
		AD92EF082632A0CD00C9544A /* CloudFamilyMemberInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CloudFamilyMemberInfo.m; sourceTree = "<group>"; };
		AD92EF092632A0CD00C9544A /* CloudFamilyMember.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudFamilyMember.h; sourceTree = "<group>"; };
		AD92EF0A2632A0CD00C9544A /* CloudFamilyLocation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudFamilyLocation.h; sourceTree = "<group>"; };
		AD92EF0B2632A0CD00C9544A /* CloudFamily.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudFamily.h; sourceTree = "<group>"; };
		AD92EF0C2632A0CD00C9544A /* CloudFamilyFirstMember.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudFamilyFirstMember.h; sourceTree = "<group>"; };
		AD92EF0D2632A0CD00C9544A /* CloudRoom.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudRoom.h; sourceTree = "<group>"; };
		AD92EF0E2632A0CD00C9544A /* CloudFloorInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudFloorInfo.h; sourceTree = "<group>"; };
		AD92EF0F2632A0CD00C9544A /* CloudFamilyList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CloudFamilyList.h; sourceTree = "<group>"; };
		AD92EF112632A0CD00C9544A /* UserAddressTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserAddressTransformer.m; sourceTree = "<group>"; };
		AD92EF122632A0CD00C9544A /* CreateFamilyTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CreateFamilyTransformer.m; sourceTree = "<group>"; };
		AD92EF132632A0CD00C9544A /* DeviceListTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceListTransformer.h; sourceTree = "<group>"; };
		AD92EF152632A0CD00C9544A /* AddRoomTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AddRoomTransformer.h; sourceTree = "<group>"; };
		AD92EF162632A0CD00C9544A /* FamilyRoomTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FamilyRoomTransformer.m; sourceTree = "<group>"; };
		AD92EF172632A0CD00C9544A /* FamilyInfoTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FamilyInfoTransformer.h; sourceTree = "<group>"; };
		AD92EF182632A0CD00C9544A /* UserQrLoginPollTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserQrLoginPollTransformer.h; sourceTree = "<group>"; };
		AD92EF192632A0CD00C9544A /* UPZJOauthDataTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPZJOauthDataTransformer.m; sourceTree = "<group>"; };
		AD92EF1A2632A0CD00C9544A /* UserLoginTerminalTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserLoginTerminalTransformer.m; sourceTree = "<group>"; };
		AD92EF1B2632A0CD00C9544A /* UserLoginLogsTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserLoginLogsTransformer.h; sourceTree = "<group>"; };
		AD92EF1C2632A0CD00C9544A /* FamilyFirstMemberTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FamilyFirstMemberTransformer.h; sourceTree = "<group>"; };
		AD92EF1D2632A0CD00C9544A /* CommonTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CommonTransformer.h; sourceTree = "<group>"; };
		AD92EF1E2632A0CD00C9544A /* UpUserServiceOrderTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpUserServiceOrderTransformer.h; sourceTree = "<group>"; };
		AD92EF1F2632A0CD00C9544A /* FamilyListTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FamilyListTransformer.h; sourceTree = "<group>"; };
		AD92EF202632A0CD00C9544A /* UserOauthDataTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserOauthDataTransformer.m; sourceTree = "<group>"; };
		AD92EF212632A0CD00C9544A /* BatchProcessDevicesOfFamilyTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BatchProcessDevicesOfFamilyTransformer.m; sourceTree = "<group>"; };
		AD92EF222632A0CD00C9544A /* UserInfoTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserInfoTransformer.m; sourceTree = "<group>"; };
		AD92EF242632A0CD00C9544A /* DeviceListTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceListTransformer.m; sourceTree = "<group>"; };
		AD92EF252632A0CD00C9544A /* CreateFamilyTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CreateFamilyTransformer.h; sourceTree = "<group>"; };
		AD92EF262632A0CD00C9544A /* UserAddressTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserAddressTransformer.h; sourceTree = "<group>"; };
		AD92EF272632A0CD00C9544A /* FamilyRoomTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FamilyRoomTransformer.h; sourceTree = "<group>"; };
		AD92EF282632A0CD00C9544A /* AddRoomTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AddRoomTransformer.m; sourceTree = "<group>"; };
		AD92EF292632A0CD00C9544A /* UpUserServiceOrderTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpUserServiceOrderTransformer.m; sourceTree = "<group>"; };
		AD92EF2A2632A0CD00C9544A /* CommonTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CommonTransformer.m; sourceTree = "<group>"; };
		AD92EF2B2632A0CD00C9544A /* UserLoginLogsTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserLoginLogsTransformer.m; sourceTree = "<group>"; };
		AD92EF2C2632A0CD00C9544A /* FamilyFirstMemberTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FamilyFirstMemberTransformer.m; sourceTree = "<group>"; };
		AD92EF2D2632A0CD00C9544A /* UserLoginTerminalTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserLoginTerminalTransformer.h; sourceTree = "<group>"; };
		AD92EF2E2632A0CD00C9544A /* FamilyInfoTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FamilyInfoTransformer.m; sourceTree = "<group>"; };
		AD92EF2F2632A0CD00C9544A /* UserQrLoginPollTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserQrLoginPollTransformer.m; sourceTree = "<group>"; };
		AD92EF302632A0CD00C9544A /* UPZJOauthDataTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPZJOauthDataTransformer.h; sourceTree = "<group>"; };
		AD92EF312632A0CD00C9544A /* UserInfoTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserInfoTransformer.h; sourceTree = "<group>"; };
		AD92EF322632A0CD00C9544A /* BatchProcessDevicesOfFamilyTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BatchProcessDevicesOfFamilyTransformer.h; sourceTree = "<group>"; };
		AD92EF332632A0CD00C9544A /* UserOauthDataTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserOauthDataTransformer.h; sourceTree = "<group>"; };
		AD92EF342632A0CD00C9544A /* FamilyListTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FamilyListTransformer.m; sourceTree = "<group>"; };
		AD92EF362632A0CD00C9544A /* DataSourceCallbackFunctions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DataSourceCallbackFunctions.h; sourceTree = "<group>"; };
		AD92EF372632A0CD00C9544A /* UserDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserDataSource.h; sourceTree = "<group>"; };
		AD92EF382632A0CD00C9544A /* FamilyDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FamilyDataSource.h; sourceTree = "<group>"; };
		AD92EF392632A0CD00C9544A /* DeviceListDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceListDataSource.m; sourceTree = "<group>"; };
		AD92EF3A2632A0CD00C9544A /* DataSourceCallbackFunctions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DataSourceCallbackFunctions.m; sourceTree = "<group>"; };
		AD92EF3B2632A0CD00C9544A /* FamilyDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FamilyDataSource.m; sourceTree = "<group>"; };
		AD92EF3C2632A0CD00C9544A /* UserDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserDataSource.m; sourceTree = "<group>"; };
		AD92EF3D2632A0CD00C9544A /* DeviceListDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceListDataSource.h; sourceTree = "<group>"; };
		AD92EF402632A0CD00C9544A /* SEUserDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEUserDataSource.m; sourceTree = "<group>"; };
		AD92EF412632A0CD00C9544A /* SEFamilyDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEFamilyDataSource.m; sourceTree = "<group>"; };
		AD92EF422632A0CD00C9544A /* SEDeviceListDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEDeviceListDataSource.h; sourceTree = "<group>"; };
		AD92EF432632A0CD00C9544A /* SEUserDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEUserDataSource.h; sourceTree = "<group>"; };
		AD92EF442632A0CD00C9544A /* SEFamilyDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEFamilyDataSource.h; sourceTree = "<group>"; };
		AD92EF452632A0CD00C9544A /* SEDeviceListDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEDeviceListDataSource.m; sourceTree = "<group>"; };
		AD92EF472632A0CD00C9544A /* SEFamilyListConverter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEFamilyListConverter.m; sourceTree = "<group>"; };
		AD92EF482632A0CD00C9544A /* SEModifyDeviceConverter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEModifyDeviceConverter.h; sourceTree = "<group>"; };
		AD92EF492632A0CD00C9544A /* SEUserInfoConverter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEUserInfoConverter.m; sourceTree = "<group>"; };
		AD92EF4A2632A0CD00C9544A /* SEUserAuthInfoConverter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEUserAuthInfoConverter.m; sourceTree = "<group>"; };
		AD92EF4B2632A0CD00C9544A /* SEDeviceInfoListConverter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEDeviceInfoListConverter.m; sourceTree = "<group>"; };
		AD92EF4C2632A0CD00C9544A /* SEBaseConverter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEBaseConverter.m; sourceTree = "<group>"; };
		AD92EF4D2632A0CD00C9544A /* SEFamilyListConverter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEFamilyListConverter.h; sourceTree = "<group>"; };
		AD92EF4E2632A0CD00C9544A /* SEUserInfoConverter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEUserInfoConverter.h; sourceTree = "<group>"; };
		AD92EF4F2632A0CD00C9544A /* SEModifyDeviceConverter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEModifyDeviceConverter.m; sourceTree = "<group>"; };
		AD92EF502632A0CD00C9544A /* SEUserAuthInfoConverter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEUserAuthInfoConverter.h; sourceTree = "<group>"; };
		AD92EF512632A0CD00C9544A /* SEBaseConverter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEBaseConverter.h; sourceTree = "<group>"; };
		AD92EF522632A0CD00C9544A /* SEDeviceInfoListConverter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEDeviceInfoListConverter.h; sourceTree = "<group>"; };
		AD92EFC62632A1A200C9544A /* libupnetwork.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libupnetwork.a; sourceTree = BUILT_PRODUCTS_DIR; };
		AD92EFC82632A1AA00C9544A /* libMJExtension.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libMJExtension.a; sourceTree = BUILT_PRODUCTS_DIR; };
		AD92F10C2632C4A500C9544A /* NSObject+UD.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSObject+UD.m"; sourceTree = "<group>"; };
		AD92F10D2632C4A500C9544A /* NSString+UD.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+UD.m"; sourceTree = "<group>"; };
		AD92F10E2632C4A500C9544A /* NSArray+UD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSArray+UD.h"; sourceTree = "<group>"; };
		AD92F10F2632C4A500C9544A /* NSDictionary+UD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDictionary+UD.h"; sourceTree = "<group>"; };
		AD92F1102632C4A500C9544A /* NSNumber+UD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSNumber+UD.h"; sourceTree = "<group>"; };
		AD92F1112632C4A500C9544A /* NSString+UD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+UD.h"; sourceTree = "<group>"; };
		AD92F1122632C4A500C9544A /* NSObject+UD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSObject+UD.h"; sourceTree = "<group>"; };
		AD92F1132632C4A500C9544A /* NSDictionary+UD.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDictionary+UD.m"; sourceTree = "<group>"; };
		AD92F1142632C4A500C9544A /* NSArray+UD.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSArray+UD.m"; sourceTree = "<group>"; };
		AD92F1152632C4A500C9544A /* NSNumber+UD.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSNumber+UD.m"; sourceTree = "<group>"; };
		ADB50C7F2643848F00062881 /* libUserDomainAPIs.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libUserDomainAPIs.a; sourceTree = BUILT_PRODUCTS_DIR; };
		ADCBD88F24F9072C00BC30AC /* FamilyPickerView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FamilyPickerView.h; sourceTree = "<group>"; };
		ADCBD89024F9072C00BC30AC /* FamilyPickerView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FamilyPickerView.m; sourceTree = "<group>"; };
		ADCBD89224FCA61500BC30AC /* RoomPickerView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RoomPickerView.h; sourceTree = "<group>"; };
		ADCBD89324FCA61500BC30AC /* RoomPickerView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RoomPickerView.m; sourceTree = "<group>"; };
		ADCBD89524FE1A1600BC30AC /* RoomDevicesViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RoomDevicesViewController.h; sourceTree = "<group>"; };
		ADCBD89624FE1A1600BC30AC /* RoomDevicesViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RoomDevicesViewController.m; sourceTree = "<group>"; };
		ADCBD89824FE3BFD00BC30AC /* city-v3.0.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = "city-v3.0.json"; sourceTree = "<group>"; };
		ADCBD89A24FE52F700BC30AC /* AddressViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AddressViewController.h; sourceTree = "<group>"; };
		ADCBD89B24FE52F700BC30AC /* AddressViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AddressViewController.m; sourceTree = "<group>"; };
		ADF38BB024DC1C5900551FF7 /* UPUserLoginApi.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUserLoginApi.m; sourceTree = "<group>"; };
		ADF38BB124DC1C5900551FF7 /* UPUserLoginApi.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUserLoginApi.h; sourceTree = "<group>"; };
		B5E2BFCAC9B6AB3C013BF2CD /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS12.2.sdk/System/Library/Frameworks/CoreMotion.framework; sourceTree = DEVELOPER_DIR; };
		B9688A3885B290FC6C821B3F /* libPods-UserDomainAPIs.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UserDomainAPIs.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		BD365F3E3A34F003F0F44B09 /* Pods-upuserdomain.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-upuserdomain.debug.xcconfig"; path = "Target Support Files/Pods-upuserdomain/Pods-upuserdomain.debug.xcconfig"; sourceTree = "<group>"; };
		BF0FDC8008868F802BADFABE /* Pods-upuserdomain.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-upuserdomain.release.xcconfig"; path = "Target Support Files/Pods-upuserdomain/Pods-upuserdomain.release.xcconfig"; sourceTree = "<group>"; };
		C3B22BE281A41A9A2B67A2CC /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS12.2.sdk/System/Library/Frameworks/WebKit.framework; sourceTree = DEVELOPER_DIR; };
		D571FB811C964A785935D95F /* MessageUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MessageUI.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS12.2.sdk/System/Library/Frameworks/MessageUI.framework; sourceTree = DEVELOPER_DIR; };
		F70AC2A52833ADD500EFF594 /* SEFamilyMoveDevicesToOtherFamilyApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SEFamilyMoveDevicesToOtherFamilyApi.h; sourceTree = "<group>"; };
		F70AC2A62833ADD500EFF594 /* SEFamilyMoveDevicesToOtherFamilyApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SEFamilyMoveDevicesToOtherFamilyApi.m; sourceTree = "<group>"; };
		F76DC4C8282BB6B800E2EFF7 /* FamilyRelatedTransform.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FamilyRelatedTransform.h; sourceTree = "<group>"; };
		F76DC4C9282BB6B800E2EFF7 /* FamilyRelatedTransform.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FamilyRelatedTransform.m; sourceTree = "<group>"; };
		F79092F1282A108900EE61D2 /* SEFamilyListApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SEFamilyListApi.h; sourceTree = "<group>"; };
		F79092F2282A108900EE61D2 /* SEFamilyListApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SEFamilyListApi.m; sourceTree = "<group>"; };
		F79092F5282A122900EE61D2 /* SERemoveRoomFromFamilyApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SERemoveRoomFromFamilyApi.h; sourceTree = "<group>"; };
		F79092F6282A122900EE61D2 /* SERemoveRoomFromFamilyApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SERemoveRoomFromFamilyApi.m; sourceTree = "<group>"; };
		F79092F9282A127900EE61D2 /* SEAddRoomToFamilyApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SEAddRoomToFamilyApi.h; sourceTree = "<group>"; };
		F79092FA282A127900EE61D2 /* SEAddRoomToFamilyApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SEAddRoomToFamilyApi.m; sourceTree = "<group>"; };
		F79092FD282A12A400EE61D2 /* SECreateFamilyApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SECreateFamilyApi.h; sourceTree = "<group>"; };
		F79092FE282A12A400EE61D2 /* SECreateFamilyApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SECreateFamilyApi.m; sourceTree = "<group>"; };
		F7909301282A12E000EE61D2 /* SEFamilyInfoApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SEFamilyInfoApi.h; sourceTree = "<group>"; };
		F7909302282A12E000EE61D2 /* SEFamilyInfoApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SEFamilyInfoApi.m; sourceTree = "<group>"; };
		F7909306282A302500EE61D2 /* SEFamilyRoomListApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SEFamilyRoomListApi.h; sourceTree = "<group>"; };
		F7909307282A302500EE61D2 /* SEFamilyRoomListApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SEFamilyRoomListApi.m; sourceTree = "<group>"; };
		F790930A282A309300EE61D2 /* SEUpdateFamilyInfoApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SEUpdateFamilyInfoApi.h; sourceTree = "<group>"; };
		F790930B282A309300EE61D2 /* SEUpdateFamilyInfoApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SEUpdateFamilyInfoApi.m; sourceTree = "<group>"; };
		F790930E282A30D100EE61D2 /* SEDestroyFamilyApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SEDestroyFamilyApi.h; sourceTree = "<group>"; };
		F790930F282A30D100EE61D2 /* SEDestroyFamilyApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SEDestroyFamilyApi.m; sourceTree = "<group>"; };
		F7909312282A30FB00EE61D2 /* SEUpdateFamilyRoomNameApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SEUpdateFamilyRoomNameApi.h; sourceTree = "<group>"; };
		F7909313282A30FB00EE61D2 /* SEUpdateFamilyRoomNameApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SEUpdateFamilyRoomNameApi.m; sourceTree = "<group>"; };
		F7909316282A312200EE61D2 /* SESetDefaultFamilyApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SESetDefaultFamilyApi.h; sourceTree = "<group>"; };
		F7909317282A312200EE61D2 /* SESetDefaultFamilyApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SESetDefaultFamilyApi.m; sourceTree = "<group>"; };
		F790931A282A316100EE61D2 /* SEFamilyMoveDevicesToNewRoomApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SEFamilyMoveDevicesToNewRoomApi.h; sourceTree = "<group>"; };
		F790931B282A316100EE61D2 /* SEFamilyMoveDevicesToNewRoomApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SEFamilyMoveDevicesToNewRoomApi.m; sourceTree = "<group>"; };
		F7909326282B650300EE61D2 /* SEFamilyInfoTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SEFamilyInfoTransformer.h; sourceTree = "<group>"; };
		F7909327282B650300EE61D2 /* SEFamilyInfoTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SEFamilyInfoTransformer.m; sourceTree = "<group>"; };
		F790932A282B816200EE61D2 /* SEAddRoomTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SEAddRoomTransformer.h; sourceTree = "<group>"; };
		F790932B282B816200EE61D2 /* SEAddRoomTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SEAddRoomTransformer.m; sourceTree = "<group>"; };
		F790932E282B81EE00EE61D2 /* SECreateFamilyTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SECreateFamilyTransformer.h; sourceTree = "<group>"; };
		F790932F282B81EE00EE61D2 /* SECreateFamilyTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SECreateFamilyTransformer.m; sourceTree = "<group>"; };
		F7909332282B836900EE61D2 /* SEFamilyRoomTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SEFamilyRoomTransformer.h; sourceTree = "<group>"; };
		F7909333282B836900EE61D2 /* SEFamilyRoomTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SEFamilyRoomTransformer.m; sourceTree = "<group>"; };
		F7909336282B846C00EE61D2 /* SEBatchProcessDevicesOfFamilyTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SEBatchProcessDevicesOfFamilyTransformer.h; sourceTree = "<group>"; };
		F7909337282B846C00EE61D2 /* SEBatchProcessDevicesOfFamilyTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SEBatchProcessDevicesOfFamilyTransformer.m; sourceTree = "<group>"; };
		F805BA7B24E38ECA0056C526 /* UDDeleteAddressOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDDeleteAddressOp.h; sourceTree = "<group>"; };
		F805BA7C24E38ECA0056C526 /* UDDeleteAddressOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDDeleteAddressOp.m; sourceTree = "<group>"; };
		F805BA7F24E3C8FE0056C526 /* UPEditAddressOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPEditAddressOp.h; sourceTree = "<group>"; };
		F805BA8024E3C8FE0056C526 /* UPEditAddressOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPEditAddressOp.m; sourceTree = "<group>"; };
		F805BA8324E3D42B0056C526 /* UDModifyUserInfoOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDModifyUserInfoOp.h; sourceTree = "<group>"; };
		F805BA8424E3D42B0056C526 /* UDModifyUserInfoOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDModifyUserInfoOp.m; sourceTree = "<group>"; };
		F805BA8724E404430056C526 /* UDPollqrCodeStateOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDPollqrCodeStateOp.h; sourceTree = "<group>"; };
		F805BA8824E404430056C526 /* UDPollqrCodeStateOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDPollqrCodeStateOp.m; sourceTree = "<group>"; };
		F805BA8B24E405650056C526 /* UDQrCancleLoginOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDQrCancleLoginOp.h; sourceTree = "<group>"; };
		F805BA8C24E405650056C526 /* UDQrCancleLoginOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDQrCancleLoginOp.m; sourceTree = "<group>"; };
		F805BA8F24E406D80056C526 /* UDQrConfirmLoginOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDQrConfirmLoginOp.h; sourceTree = "<group>"; };
		F805BA9024E406D80056C526 /* UDQrConfirmLoginOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDQrConfirmLoginOp.m; sourceTree = "<group>"; };
		F805BA9324E406FA0056C526 /* UDQrConfirmScanOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDQrConfirmScanOp.h; sourceTree = "<group>"; };
		F805BA9424E406FA0056C526 /* UDQrConfirmScanOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDQrConfirmScanOp.m; sourceTree = "<group>"; };
		F805BA9724E40FF60056C526 /* UDQueryServiceOrderOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDQueryServiceOrderOp.h; sourceTree = "<group>"; };
		F805BA9824E40FF60056C526 /* UDQueryServiceOrderOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDQueryServiceOrderOp.m; sourceTree = "<group>"; };
		F805BA9B24E411A60056C526 /* UDQueryLoginLogsOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDQueryLoginLogsOp.h; sourceTree = "<group>"; };
		F805BA9C24E411A60056C526 /* UDQueryLoginLogsOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDQueryLoginLogsOp.m; sourceTree = "<group>"; };
		F805BA9F24E413360056C526 /* UDRefreshAddressListOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDRefreshAddressListOp.h; sourceTree = "<group>"; };
		F805BAA024E413360056C526 /* UDRefreshAddressListOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDRefreshAddressListOp.m; sourceTree = "<group>"; };
		F80B7B5B24E50D5F0073FC48 /* UDRefreshFamilyListOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDRefreshFamilyListOp.h; sourceTree = "<group>"; };
		F80B7B5C24E50D5F0073FC48 /* UDRefreshFamilyListOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDRefreshFamilyListOp.m; sourceTree = "<group>"; };
		F80B7B5F24E5256F0073FC48 /* UDRefreshTerminalListOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDRefreshTerminalListOp.h; sourceTree = "<group>"; };
		F80B7B6024E5256F0073FC48 /* UDRefreshTerminalListOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDRefreshTerminalListOp.m; sourceTree = "<group>"; };
		F80B7B6324E529AE0073FC48 /* UDRefreshUserInfoOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDRefreshUserInfoOp.h; sourceTree = "<group>"; };
		F80B7B6424E529AE0073FC48 /* UDRefreshUserInfoOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDRefreshUserInfoOp.m; sourceTree = "<group>"; };
		F80B7B6724E52C500073FC48 /* UDReplyFamilyInviteOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDReplyFamilyInviteOp.h; sourceTree = "<group>"; };
		F80B7B6824E52C500073FC48 /* UDReplyFamilyInviteOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDReplyFamilyInviteOp.m; sourceTree = "<group>"; };
		F80B7B6B24E539CA0073FC48 /* UDSetCurrentFamilyOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDSetCurrentFamilyOp.h; sourceTree = "<group>"; };
		F80B7B6C24E539CA0073FC48 /* UDSetCurrentFamilyOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDSetCurrentFamilyOp.m; sourceTree = "<group>"; };
		F80B7B6F24E53E080073FC48 /* UDUpdateAvatarOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDUpdateAvatarOp.h; sourceTree = "<group>"; };
		F80B7B7024E53E080073FC48 /* UDUpdateAvatarOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDUpdateAvatarOp.m; sourceTree = "<group>"; };
		F80B7B7324E61FE50073FC48 /* UDUpdateFamilyDetailOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDUpdateFamilyDetailOp.h; sourceTree = "<group>"; };
		F80B7B7424E61FE50073FC48 /* UDUpdateFamilyDetailOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDUpdateFamilyDetailOp.m; sourceTree = "<group>"; };
		F80B7B7724E6532F0073FC48 /* UDDeleteMemberOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDDeleteMemberOp.h; sourceTree = "<group>"; };
		F80B7B7824E6532F0073FC48 /* UDDeleteMemberOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDDeleteMemberOp.m; sourceTree = "<group>"; };
		F80B7B7B24E659910073FC48 /* UDUnbindDevicesOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDUnbindDevicesOp.h; sourceTree = "<group>"; };
		F80B7B7C24E659910073FC48 /* UDUnbindDevicesOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDUnbindDevicesOp.m; sourceTree = "<group>"; };
		F80B7B7F24E687440073FC48 /* UDMoveDevicesToRoomOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDMoveDevicesToRoomOp.h; sourceTree = "<group>"; };
		F80B7B8024E687440073FC48 /* UDMoveDevicesToRoomOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDMoveDevicesToRoomOp.m; sourceTree = "<group>"; };
		F80B7B8324E689FB0073FC48 /* UDDeleteDevicesOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDDeleteDevicesOp.h; sourceTree = "<group>"; };
		F80B7B8424E689FB0073FC48 /* UDDeleteDevicesOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDDeleteDevicesOp.m; sourceTree = "<group>"; };
		F81273C52579083400995F1E /* FloorArg.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FloorArg.h; sourceTree = "<group>"; };
		F81273C62579083400995F1E /* FloorArg.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FloorArg.m; sourceTree = "<group>"; };
		F81273C925790D7200995F1E /* UDFloorInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDFloorInfo.h; sourceTree = "<group>"; };
		F81273CA25790D7200995F1E /* UDFloorInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDFloorInfo.m; sourceTree = "<group>"; };
		F81273CD25790DBF00995F1E /* UDFloorInfo+PrivateExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UDFloorInfo+PrivateExtension.h"; sourceTree = "<group>"; };
		F81D1EC724FE2E670041830C /* UPMacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPMacros.h; sourceTree = "<group>"; };
		F82E589D24F35F7A0026E454 /* UDUserModelTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDUserModelTransformer.h; sourceTree = "<group>"; };
		F82E589F24F38F370026E454 /* UserQRLoginStage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserQRLoginStage.h; sourceTree = "<group>"; };
		F82E58A024F38F370026E454 /* UserQRLoginStage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserQRLoginStage.m; sourceTree = "<group>"; };
		F82E58A324F3A4960026E454 /* UDServerCacheManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDServerCacheManager.h; sourceTree = "<group>"; };
		F82E58A424F3A4960026E454 /* UDServerCacheManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDServerCacheManager.m; sourceTree = "<group>"; };
		F8304B0E2579D18300E326E5 /* UDAddFloorOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDAddFloorOp.h; sourceTree = "<group>"; };
		F8304B0F2579D18300E326E5 /* UDAddFloorOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDAddFloorOp.m; sourceTree = "<group>"; };
		F8304B122579D1A600E326E5 /* UDEditFloorOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDEditFloorOp.h; sourceTree = "<group>"; };
		F8304B132579D1A600E326E5 /* UDEditFloorOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDEditFloorOp.m; sourceTree = "<group>"; };
		F8304B162579D1C700E326E5 /* UDDeleteFloorOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDDeleteFloorOp.h; sourceTree = "<group>"; };
		F8304B172579D1C700E326E5 /* UDDeleteFloorOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDDeleteFloorOp.m; sourceTree = "<group>"; };
		F854517924F50C0F007F3114 /* UPUploadRecordApis.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPUploadRecordApis.h; sourceTree = "<group>"; };
		F854517A24F50C0F007F3114 /* UPUploadRecordApis.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPUploadRecordApis.m; sourceTree = "<group>"; };
		F87750692589CDDF00D8A76D /* UDPlanRefreshTokenTaskDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDPlanRefreshTokenTaskDelegate.h; sourceTree = "<group>"; };
		F899FCDF24EBB46200F34E56 /* UDOperatePlannedRefreshTokenEventHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDOperatePlannedRefreshTokenEventHandler.h; sourceTree = "<group>"; };
		F899FCE024EBB46200F34E56 /* UDOperatePlannedRefreshTokenEventHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDOperatePlannedRefreshTokenEventHandler.m; sourceTree = "<group>"; };
		F899FCE724ECBCAF00F34E56 /* UserdomainSteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserdomainSteps.h; sourceTree = "<group>"; };
		F899FCE824ECBCAF00F34E56 /* UserdomainSteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserdomainSteps.m; sourceTree = "<group>"; };
		F8AEC6862D7EF832008C92C5 /* UDConfirmDeviceSharingRelationOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDConfirmDeviceSharingRelationOp.h; sourceTree = "<group>"; };
		F8AEC6872D7EF832008C92C5 /* UDConfirmDeviceSharingRelationOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDConfirmDeviceSharingRelationOp.m; sourceTree = "<group>"; };
		F8AEC68A2D7EFB09008C92C5 /* UDCancelDeviceSharingRelationOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDCancelDeviceSharingRelationOp.h; sourceTree = "<group>"; };
		F8AEC68B2D7EFB09008C92C5 /* UDCancelDeviceSharingRelationOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDCancelDeviceSharingRelationOp.m; sourceTree = "<group>"; };
		F8AEC68E2D7EFD76008C92C5 /* UPConfirmDeviceSharingRelationApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPConfirmDeviceSharingRelationApi.h; sourceTree = "<group>"; };
		F8AEC68F2D7EFD76008C92C5 /* UPConfirmDeviceSharingRelationApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPConfirmDeviceSharingRelationApi.m; sourceTree = "<group>"; };
		F8AEC6922D7F0E99008C92C5 /* UPCancelDeviceSharingRelationApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPCancelDeviceSharingRelationApi.h; sourceTree = "<group>"; };
		F8AEC6932D7F0E99008C92C5 /* UPCancelDeviceSharingRelationApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPCancelDeviceSharingRelationApi.m; sourceTree = "<group>"; };
		F8AEC6962D80287E008C92C5 /* CloudShareDeviceCardInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CloudShareDeviceCardInfo.h; sourceTree = "<group>"; };
		F8AEC6972D80287E008C92C5 /* CloudShareDeviceCardInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CloudShareDeviceCardInfo.m; sourceTree = "<group>"; };
		F8AEC69A2D802C91008C92C5 /* UDShareDeviceCardInfoDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDShareDeviceCardInfoDelegate.h; sourceTree = "<group>"; };
		F8AEC69C2D8032BE008C92C5 /* DeviceShareDeviceCardInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DeviceShareDeviceCardInfo.h; sourceTree = "<group>"; };
		F8AEC69D2D8032BE008C92C5 /* DeviceShareDeviceCardInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DeviceShareDeviceCardInfo.m; sourceTree = "<group>"; };
		F8AEC6A02D80333F008C92C5 /* DeviceShareDeviceCardInfo+PrivateExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "DeviceShareDeviceCardInfo+PrivateExtension.h"; sourceTree = "<group>"; };
		F8B541AF24EE45790075A070 /* UDOperateRefreshAddressListEventHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDOperateRefreshAddressListEventHandler.h; sourceTree = "<group>"; };
		F8B541B024EE45790075A070 /* UDOperateRefreshAddressListEventHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDOperateRefreshAddressListEventHandler.m; sourceTree = "<group>"; };
		F8B541B324EE4D620075A070 /* UDOperateRefreshTerminalListEventHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDOperateRefreshTerminalListEventHandler.h; sourceTree = "<group>"; };
		F8B541B424EE4D620075A070 /* UDOperateRefreshTerminalListEventHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDOperateRefreshTerminalListEventHandler.m; sourceTree = "<group>"; };
		F8B541B724EE4E650075A070 /* UDOperateRefreshFamilyListEventHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDOperateRefreshFamilyListEventHandler.h; sourceTree = "<group>"; };
		F8B541B824EE4E650075A070 /* UDOperateRefreshFamilyListEventHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDOperateRefreshFamilyListEventHandler.m; sourceTree = "<group>"; };
		F8B541BB24EE4E790075A070 /* UDOperateRefreshFamilyDetailEventHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDOperateRefreshFamilyDetailEventHandler.h; sourceTree = "<group>"; };
		F8B541BC24EE4E7A0075A070 /* UDOperateRefreshFamilyDetailEventHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDOperateRefreshFamilyDetailEventHandler.m; sourceTree = "<group>"; };
		F8B541BF24EE4ECF0075A070 /* UDOperateRefreshDeviceListEventHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDOperateRefreshDeviceListEventHandler.h; sourceTree = "<group>"; };
		F8B541C024EE4ECF0075A070 /* UDOperateRefreshDeviceListEventHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDOperateRefreshDeviceListEventHandler.m; sourceTree = "<group>"; };
		F8B541C324EE5A000075A070 /* UDRefreshAllFamilyDetailOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDRefreshAllFamilyDetailOp.h; sourceTree = "<group>"; };
		F8B541C424EE5A000075A070 /* UDRefreshAllFamilyDetailOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDRefreshAllFamilyDetailOp.m; sourceTree = "<group>"; };
		F8CD11A024E384EB0021DD47 /* UDCreateAddressOp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDCreateAddressOp.h; sourceTree = "<group>"; };
		F8CD11A124E384EB0021DD47 /* UDCreateAddressOp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDCreateAddressOp.m; sourceTree = "<group>"; };
		F8CEF18224C7D686008705B3 /* UserdomainHolderTest.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserdomainHolderTest.h; sourceTree = "<group>"; };
		F8CEF18324C7D686008705B3 /* UserdomainHolderTest.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserdomainHolderTest.m; sourceTree = "<group>"; };
		F8CEF18824C7D6C8008705B3 /* StepsUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = StepsUtils.h; sourceTree = "<group>"; };
		F8CEF18924C7D6C8008705B3 /* StepsUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = StepsUtils.m; sourceTree = "<group>"; };
		F8CEF18B24C7D6FD008705B3 /* InitializationSteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InitializationSteps.h; sourceTree = "<group>"; };
		F8CEF18C24C7D6FD008705B3 /* InitializationSteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InitializationSteps.m; sourceTree = "<group>"; };
		F8CEF1AC24C7E347008705B3 /* UserSteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserSteps.h; sourceTree = "<group>"; };
		F8CEF1AD24C7E347008705B3 /* UserSteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserSteps.m; sourceTree = "<group>"; };
		F8CEF1AF24C7E359008705B3 /* FamilySteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FamilySteps.h; sourceTree = "<group>"; };
		F8CEF1B024C7E359008705B3 /* FamilySteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FamilySteps.m; sourceTree = "<group>"; };
		F8CEF1B224C7E366008705B3 /* DeviceSteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DeviceSteps.h; sourceTree = "<group>"; };
		F8CEF1B324C7E366008705B3 /* DeviceSteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DeviceSteps.m; sourceTree = "<group>"; };
		F8CEF1B524C7E37A008705B3 /* CacheSteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CacheSteps.h; sourceTree = "<group>"; };
		F8CEF1B624C7E37A008705B3 /* CacheSteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CacheSteps.m; sourceTree = "<group>"; };
		F8CEF1BB24C7E3AF008705B3 /* TimeSteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TimeSteps.h; sourceTree = "<group>"; };
		F8CEF1BC24C7E3AF008705B3 /* TimeSteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TimeSteps.m; sourceTree = "<group>"; };
		F8CEF1BE24C7E3BE008705B3 /* EventHandleSteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EventHandleSteps.h; sourceTree = "<group>"; };
		F8CEF1BF24C7E3BE008705B3 /* EventHandleSteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EventHandleSteps.m; sourceTree = "<group>"; };
		F8CEF1C124C7E3EC008705B3 /* RefreshTokenSteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RefreshTokenSteps.h; sourceTree = "<group>"; };
		F8CEF1C224C7E3EC008705B3 /* RefreshTokenSteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RefreshTokenSteps.m; sourceTree = "<group>"; };
		F8CEF1C424C7E411008705B3 /* SignleLoginSteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SignleLoginSteps.h; sourceTree = "<group>"; };
		F8CEF1C524C7E411008705B3 /* SignleLoginSteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SignleLoginSteps.m; sourceTree = "<group>"; };
		F8CEF1FC24CAB243008705B3 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		F8CEF30524CAB2BE008705B3 /* UDCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDCache.h; sourceTree = "<group>"; };
		F8CEF30724CAB2BE008705B3 /* UDRoomDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDRoomDelegate.h; sourceTree = "<group>"; };
		F8CEF30824CAB2BE008705B3 /* UDFamilyLocationDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDFamilyLocationDelegate.h; sourceTree = "<group>"; };
		F8CEF30924CAB2BE008705B3 /* UDFamilyDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDFamilyDelegate.h; sourceTree = "<group>"; };
		F8CEF30A24CAB2BE008705B3 /* UDFamilyMemberDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDFamilyMemberDelegate.h; sourceTree = "<group>"; };
		F8CEF30B24CAB2BE008705B3 /* UDMemberInfoDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDMemberInfoDelegate.h; sourceTree = "<group>"; };
		F8CEF30C24CAB2BE008705B3 /* UDFamilyInfoDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDFamilyInfoDelegate.h; sourceTree = "<group>"; };
		F8CEF30E24CAB2BE008705B3 /* UDAddressDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDAddressDelegate.h; sourceTree = "<group>"; };
		F8CEF30F24CAB2BE008705B3 /* UDUserTermDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDUserTermDelegate.h; sourceTree = "<group>"; };
		F8CEF31024CAB2BE008705B3 /* UDUserLoginLogInfoDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDUserLoginLogInfoDelegate.h; sourceTree = "<group>"; };
		F8CEF31124CAB2BE008705B3 /* UDLastLoginInfoDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDLastLoginInfoDelegate.h; sourceTree = "<group>"; };
		F8CEF31224CAB2BE008705B3 /* UDUserDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDUserDelegate.h; sourceTree = "<group>"; };
		F8CEF31324CAB2BE008705B3 /* UDUserAddressDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDUserAddressDelegate.h; sourceTree = "<group>"; };
		F8CEF31424CAB2BE008705B3 /* UDUserInfoDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDUserInfoDelegate.h; sourceTree = "<group>"; };
		F8CEF31624CAB2BE008705B3 /* UPTimeMillisDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPTimeMillisDelegate.h; sourceTree = "<group>"; };
		F8CEF31824CAB2BE008705B3 /* UDDeviceInfoDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDDeviceInfoDelegate.h; sourceTree = "<group>"; };
		F8CEF31924CAB2BE008705B3 /* UDDeviceOwnerInfoDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDDeviceOwnerInfoDelegate.h; sourceTree = "<group>"; };
		F8CEF31A24CAB2BE008705B3 /* UDDeviceAuthDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDDeviceAuthDelegate.h; sourceTree = "<group>"; };
		F8CEF31B24CAB2BE008705B3 /* UDDevicePermissionDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDDevicePermissionDelegate.h; sourceTree = "<group>"; };
		F8CEF31C24CAB2BE008705B3 /* UDDeviceDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDDeviceDelegate.h; sourceTree = "<group>"; };
		F8CEF31E24CAB2BE008705B3 /* UDAuthDataDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDAuthDataDelegate.h; sourceTree = "<group>"; };
		F8CEF32024CAB2BE008705B3 /* UpUserDomainResult.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpUserDomainResult.h; sourceTree = "<group>"; };
		F8CEF32224CAB2BE008705B3 /* UpUserDomainDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpUserDomainDelegate.h; sourceTree = "<group>"; };
		F8CEF32324CAB2BE008705B3 /* UpUserDomainObserver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpUserDomainObserver.h; sourceTree = "<group>"; };
		F8CEF33724CAB2BF008705B3 /* UDCacheIMP.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UDCacheIMP.m; sourceTree = "<group>"; };
		F8CEF33824CAB2BF008705B3 /* UDCacheIMP.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDCacheIMP.h; sourceTree = "<group>"; };
		F8CEF33A24CAB2BF008705B3 /* RoomArgs.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RoomArgs.m; sourceTree = "<group>"; };
		F8CEF33B24CAB2BF008705B3 /* CreateFamilyArgs.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CreateFamilyArgs.m; sourceTree = "<group>"; };
		F8CEF33C24CAB2BF008705B3 /* FamilyLocation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FamilyLocation.h; sourceTree = "<group>"; };
		F8CEF33D24CAB2BF008705B3 /* MemberInfo+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "MemberInfo+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF33E24CAB2BF008705B3 /* Room.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Room.h; sourceTree = "<group>"; };
		F8CEF33F24CAB2BF008705B3 /* FamilyInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FamilyInfo.m; sourceTree = "<group>"; };
		F8CEF34024CAB2BF008705B3 /* Family.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Family.m; sourceTree = "<group>"; };
		F8CEF34124CAB2BF008705B3 /* Family+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "Family+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF34224CAB2BF008705B3 /* FamilyLocation+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "FamilyLocation+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF34324CAB2BF008705B3 /* FamilyMember.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FamilyMember.h; sourceTree = "<group>"; };
		F8CEF34424CAB2BF008705B3 /* MemberInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MemberInfo.m; sourceTree = "<group>"; };
		F8CEF34524CAB2BF008705B3 /* Room+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "Room+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF34624CAB2BF008705B3 /* FamilyArgs.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FamilyArgs.m; sourceTree = "<group>"; };
		F8CEF34724CAB2BF008705B3 /* FamilyMember+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "FamilyMember+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF34824CAB2BF008705B3 /* FamilyLocation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FamilyLocation.m; sourceTree = "<group>"; };
		F8CEF34924CAB2BF008705B3 /* CreateFamilyArgs.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CreateFamilyArgs.h; sourceTree = "<group>"; };
		F8CEF34A24CAB2BF008705B3 /* RoomArgs.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RoomArgs.h; sourceTree = "<group>"; };
		F8CEF34B24CAB2BF008705B3 /* Family.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Family.h; sourceTree = "<group>"; };
		F8CEF34C24CAB2BF008705B3 /* FamilyInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FamilyInfo.h; sourceTree = "<group>"; };
		F8CEF34D24CAB2BF008705B3 /* Room.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Room.m; sourceTree = "<group>"; };
		F8CEF34E24CAB2BF008705B3 /* FamilyInfo+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "FamilyInfo+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF34F24CAB2BF008705B3 /* MemberInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MemberInfo.h; sourceTree = "<group>"; };
		F8CEF35024CAB2BF008705B3 /* FamilyMember.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FamilyMember.m; sourceTree = "<group>"; };
		F8CEF35124CAB2BF008705B3 /* FamilyArgs.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FamilyArgs.h; sourceTree = "<group>"; };
		F8CEF35324CAB2BF008705B3 /* UserAddressInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserAddressInfo.m; sourceTree = "<group>"; };
		F8CEF35424CAB2BF008705B3 /* UserAddress.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserAddress.m; sourceTree = "<group>"; };
		F8CEF35524CAB2BF008705B3 /* UserLoginLogInfo+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UserLoginLogInfo+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF35624CAB2BF008705B3 /* User.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = User.m; sourceTree = "<group>"; };
		F8CEF35824CAB2BF008705B3 /* UserInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserInfo.m; sourceTree = "<group>"; };
		F8CEF35924CAB2BF008705B3 /* UserLoginLogInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserLoginLogInfo.h; sourceTree = "<group>"; };
		F8CEF35A24CAB2BF008705B3 /* UserTerminal+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UserTerminal+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF35B24CAB2BF008705B3 /* UserInfoArgs.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserInfoArgs.m; sourceTree = "<group>"; };
		F8CEF35C24CAB2BF008705B3 /* UserAddressInfo+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UserAddressInfo+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF35D24CAB2BF008705B3 /* UserTerminal.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserTerminal.m; sourceTree = "<group>"; };
		F8CEF35E24CAB2BF008705B3 /* UserAddress.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserAddress.h; sourceTree = "<group>"; };
		F8CEF35F24CAB2BF008705B3 /* UserAddressInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserAddressInfo.h; sourceTree = "<group>"; };
		F8CEF36124CAB2BF008705B3 /* User.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = User.h; sourceTree = "<group>"; };
		F8CEF36324CAB2BF008705B3 /* UserInfo+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UserInfo+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF36424CAB2BF008705B3 /* UserLoginLogInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserLoginLogInfo.m; sourceTree = "<group>"; };
		F8CEF36524CAB2BF008705B3 /* UserAddress+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UserAddress+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF36624CAB2BF008705B3 /* UserInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserInfo.h; sourceTree = "<group>"; };
		F8CEF36724CAB2BF008705B3 /* UserTerminal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserTerminal.h; sourceTree = "<group>"; };
		F8CEF36824CAB2BF008705B3 /* UserInfoArgs.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserInfoArgs.h; sourceTree = "<group>"; };
		F8CEF36A24CAB2BF008705B3 /* UDTimeIMP.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDTimeIMP.h; sourceTree = "<group>"; };
		F8CEF36B24CAB2BF008705B3 /* UDTimeIMP.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UDTimeIMP.m; sourceTree = "<group>"; };
		F8CEF36D24CAB2BF008705B3 /* UDDeviceInfo+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UDDeviceInfo+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF36E24CAB2BF008705B3 /* DeviceAuth.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceAuth.h; sourceTree = "<group>"; };
		F8CEF36F24CAB2BF008705B3 /* DeviceOwnerInfo+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "DeviceOwnerInfo+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF37024CAB2BF008705B3 /* DeviceOwnerInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DeviceOwnerInfo.h; sourceTree = "<group>"; };
		F8CEF37124CAB2BF008705B3 /* Device.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Device.h; sourceTree = "<group>"; };
		F8CEF37224CAB2BF008705B3 /* DevicePermission.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DevicePermission.m; sourceTree = "<group>"; };
		F8CEF37324CAB2BF008705B3 /* UDDeviceInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDDeviceInfo.h; sourceTree = "<group>"; };
		F8CEF37424CAB2BF008705B3 /* DevicePermission+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "DevicePermission+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF37524CAB2BF008705B3 /* DeviceOwnerInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceOwnerInfo.m; sourceTree = "<group>"; };
		F8CEF37624CAB2BF008705B3 /* DeviceAuth.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DeviceAuth.m; sourceTree = "<group>"; };
		F8CEF37724CAB2BF008705B3 /* DeviceAuth+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "DeviceAuth+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF37824CAB2BF008705B3 /* Device.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Device.m; sourceTree = "<group>"; };
		F8CEF37924CAB2BF008705B3 /* DevicePermission.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DevicePermission.h; sourceTree = "<group>"; };
		F8CEF37A24CAB2BF008705B3 /* UDDeviceInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UDDeviceInfo.m; sourceTree = "<group>"; };
		F8CEF37C24CAB2BF008705B3 /* ApplicationOauthData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ApplicationOauthData.m; sourceTree = "<group>"; };
		F8CEF37D24CAB2BF008705B3 /* ApplicationOauthData+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "ApplicationOauthData+PrivateExtension.h"; sourceTree = "<group>"; };
		F8CEF37E24CAB2BF008705B3 /* ApplicationOauthData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ApplicationOauthData.h; sourceTree = "<group>"; };
		F8CEF38024CAB2BF008705B3 /* UserDomainSampleResult.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserDomainSampleResult.m; sourceTree = "<group>"; };
		F8CEF38124CAB2BF008705B3 /* UserDomainSampleResult.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserDomainSampleResult.h; sourceTree = "<group>"; };
		F8CEF38324CAB2BF008705B3 /* UpUserDomain.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpUserDomain.m; sourceTree = "<group>"; };
		F8CEF38424CAB2BF008705B3 /* UpUserDomain.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpUserDomain.h; sourceTree = "<group>"; };
		F8CEF38624CAB2BF008705B3 /* UDOperator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UDOperator.h; sourceTree = "<group>"; };
		F8CEF38724CAB2BF008705B3 /* UDOperator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UDOperator.m; sourceTree = "<group>"; };
		F8CEF40224CAC4F9008705B3 /* UserAddressArgs.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserAddressArgs.h; sourceTree = "<group>"; };
		F8CEF40324CAC4F9008705B3 /* UserAddressArgs.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserAddressArgs.m; sourceTree = "<group>"; };
		F8CEF40624CACBF7008705B3 /* UpUserDomainProvider.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpUserDomainProvider.h; sourceTree = "<group>"; };
		F8CEF41D24CACD32008705B3 /* UpUserDomainHolder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpUserDomainHolder.h; sourceTree = "<group>"; };
		F8CEF42124CACD32008705B3 /* UpUserDomainHolder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpUserDomainHolder.m; sourceTree = "<group>"; };
		F8D6AC6224F4E5CF007EA70A /* UDOpertateRefreshUserEventHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDOpertateRefreshUserEventHandler.h; sourceTree = "<group>"; };
		F8D6AC6324F4E5CF007EA70A /* UDOpertateRefreshUserEventHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UDOpertateRefreshUserEventHandler.m; sourceTree = "<group>"; };
		F8E2BAB125775667007F3801 /* UDFloorInfoDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UDFloorInfoDelegate.h; sourceTree = "<group>"; };
		F8F22325257B36E900222539 /* ZJServerAPIBase.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ZJServerAPIBase.h; sourceTree = "<group>"; };
		F8F22326257B36E900222539 /* ZJServerAPIBase.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ZJServerAPIBase.m; sourceTree = "<group>"; };
		F8F22329257B388200222539 /* UPAddFloorApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPAddFloorApi.h; sourceTree = "<group>"; };
		F8F2232A257B388200222539 /* UPAddFloorApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPAddFloorApi.m; sourceTree = "<group>"; };
		F8F2232D257B389B00222539 /* UPEditFloorApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPEditFloorApi.h; sourceTree = "<group>"; };
		F8F2232E257B389B00222539 /* UPEditFloorApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPEditFloorApi.m; sourceTree = "<group>"; };
		F8F22331257B38AF00222539 /* UPDeleteFloorApi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPDeleteFloorApi.h; sourceTree = "<group>"; };
		F8F22332257B38AF00222539 /* UPDeleteFloorApi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPDeleteFloorApi.m; sourceTree = "<group>"; };
		F8FD3879253071EB00D4F9C3 /* BatchProcessDeviceResult.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BatchProcessDeviceResult.h; sourceTree = "<group>"; };
		F8FD387A253071EB00D4F9C3 /* BatchProcessDeviceResult.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BatchProcessDeviceResult.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		71ECF93D241A2C0D009BBAD0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD12D7CD2643963A00188BC3 /* libupuserdomain.a in Frameworks */,
				71AF42D924B5AF56007A8372 /* libresolv.tbd in Frameworks */,
				71AF42D724B5AF4D007A8372 /* libsqlite3.0.tbd in Frameworks */,
				CA6D5A33F79303932A2E30A5 /* libPods-Debugger.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		72321F922255DE1F005F6A86 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD12D7D02643979700188BC3 /* libupuserdomain.a in Frameworks */,
				D0A3580E7BD567A3D8141D5B /* libPods-upuserdomainTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD12D63526438F9F00188BC3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD12D7CF2643967500188BC3 /* libUserDomainDataSource.a in Frameworks */,
				AD12D6412643900500188BC3 /* libUserDomainAPIs.a in Frameworks */,
				5449202F8516A4E93B045C5D /* libPods-upuserdomain.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD12D7462643933E00188BC3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6552C1C10E47580097C520E8 /* libPods-UserDomainDataSource.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		ADB50C7C2643848F00062881 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CA270EB1E3B64167B156FE10 /* libPods-UserDomainAPIs.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		221AA33B277EA0FE008143F4 /* ShopDataSource */ = {
			isa = PBXGroup;
			children = (
				221AA33D277EA149008143F4 /* Transformer */,
				221AA33C277EA136008143F4 /* IMP */,
			);
			path = ShopDataSource;
			sourceTree = "<group>";
		};
		221AA33C277EA136008143F4 /* IMP */ = {
			isa = PBXGroup;
			children = (
				221AA33E277EA1A0008143F4 /* ShopUserDataSource.h */,
				221AA33F277EA1A0008143F4 /* ShopUserDataSource.m */,
			);
			path = IMP;
			sourceTree = "<group>";
		};
		221AA33D277EA149008143F4 /* Transformer */ = {
			isa = PBXGroup;
			children = (
				221AA34D277ED317008143F4 /* ShopOauthDataTransformer.h */,
				221AA34E277ED317008143F4 /* ShopOauthDataTransformer.m */,
			);
			path = Transformer;
			sourceTree = "<group>";
		};
		221AA342277EA67F008143F4 /* Shop */ = {
			isa = PBXGroup;
			children = (
				221AA348277ED040008143F4 /* Common */,
				221AA343277EA689008143F4 /* User */,
			);
			path = Shop;
			sourceTree = "<group>";
		};
		221AA343277EA689008143F4 /* User */ = {
			isa = PBXGroup;
			children = (
				221AA344277EA6F4008143F4 /* ShopRefreshTokenApi.h */,
				221AA345277EA6F4008143F4 /* ShopRefreshTokenApi.m */,
				22BF11B427C5CFC000EB43F5 /* ShopLogoutApi.h */,
				22BF11B327C5CFC000EB43F5 /* ShopLogoutApi.m */,
			);
			path = User;
			sourceTree = "<group>";
		};
		221AA348277ED040008143F4 /* Common */ = {
			isa = PBXGroup;
			children = (
				221AA349277ED064008143F4 /* ShopServerAPIBase.h */,
				221AA34A277ED064008143F4 /* ShopServerAPIBase.m */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		3F94A21CF5B513327D3D9B8C /* Pods */ = {
			isa = PBXGroup;
			children = (
				BD365F3E3A34F003F0F44B09 /* Pods-upuserdomain.debug.xcconfig */,
				BF0FDC8008868F802BADFABE /* Pods-upuserdomain.release.xcconfig */,
				03975CE6D71736702F84FEE9 /* Pods-upuserdomainTests.debug.xcconfig */,
				2F763910D9DB3EF678D1714A /* Pods-upuserdomainTests.release.xcconfig */,
				54F389628B7448F69016EEC7 /* Pods-Debugger.debug.xcconfig */,
				28B017AD855424FCE3E0C745 /* Pods-Debugger.release.xcconfig */,
				A65CF39308F3D3BA350A8097 /* Pods-UserDomainAPIs.debug.xcconfig */,
				85439034EEEFBA9A132FD802 /* Pods-UserDomainAPIs.release.xcconfig */,
				1D284CED5D98610D13AD2490 /* Pods-UserDomain.debug.xcconfig */,
				634BFDFEBFFECDC5A9F6AE90 /* Pods-UserDomain.release.xcconfig */,
				17FE49FAA2DB41B7CEB10E77 /* Pods-UserDomainDataSource.debug.xcconfig */,
				2FEB87918B976CE9D9222179 /* Pods-UserDomainDataSource.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		7155373624F5F86C00BAA7D7 /* Recovered References */ = {
			isa = PBXGroup;
			children = (
				71B381F024F505CA00F04E48 /* UserInfoEditViewController.m */,
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		71AF41BC24B5ACD5007A8372 /* UserDomainAPIs */ = {
			isa = PBXGroup;
			children = (
				221AA342277EA67F008143F4 /* Shop */,
				71AF41C724B5AD6A007A8372 /* Seasia */,
				71AF41E624B5AD6B007A8372 /* UplusHome */,
				71AF41BD24B5ACD5007A8372 /* UserDomainAPIs.h */,
				71AF41BE24B5ACD5007A8372 /* Info.plist */,
			);
			path = UserDomainAPIs;
			sourceTree = "<group>";
		};
		71AF41C724B5AD6A007A8372 /* Seasia */ = {
			isa = PBXGroup;
			children = (
				F79092F0282A101100EE61D2 /* Family */,
				71AF41C824B5AD6A007A8372 /* User */,
				71AF41D124B5AD6A007A8372 /* Common */,
				71AF41D824B5AD6A007A8372 /* Model */,
				71AF41DF24B5AD6B007A8372 /* Device */,
			);
			path = Seasia;
			sourceTree = "<group>";
		};
		71AF41C824B5AD6A007A8372 /* User */ = {
			isa = PBXGroup;
			children = (
				71AF41CA24B5AD6A007A8372 /* SEUserInfoAPI.h */,
				71AF41D024B5AD6A007A8372 /* SEUserInfoAPI.m */,
				71AF41CD24B5AD6A007A8372 /* SEUserLogoutAPI.h */,
				71AF41C924B5AD6A007A8372 /* SEUserLogoutAPI.m */,
				71AF41CB24B5AD6A007A8372 /* SEUserModifyAPI.h */,
				71AF41CF24B5AD6A007A8372 /* SEUserModifyAPI.m */,
				71AF41CE24B5AD6A007A8372 /* SEUserRefreshTokenAPI.h */,
				71AF41CC24B5AD6A007A8372 /* SEUserRefreshTokenAPI.m */,
			);
			path = User;
			sourceTree = "<group>";
		};
		71AF41D124B5AD6A007A8372 /* Common */ = {
			isa = PBXGroup;
			children = (
				71AF41D224B5AD6A007A8372 /* SEEncryption.h */,
				71AF41D524B5AD6A007A8372 /* SEEncryption.m */,
				71AF41D324B5AD6A007A8372 /* SERequestBase.h */,
				71AF41D724B5AD6A007A8372 /* SERequestBase.m */,
				71AF41D624B5AD6A007A8372 /* SERequestConfig.h */,
				71AF41D424B5AD6A007A8372 /* SERequestConfig.m */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		71AF41D824B5AD6A007A8372 /* Model */ = {
			isa = PBXGroup;
			children = (
				71AF41DE24B5AD6B007A8372 /* SECloudDeviceInfo.h */,
				71AF41DB24B5AD6B007A8372 /* SECloudDeviceInfo.m */,
				71AF41D924B5AD6B007A8372 /* SECloudUserAuthInfo.h */,
				71AF41DD24B5AD6B007A8372 /* SECloudUserAuthInfo.m */,
				71AF41DA24B5AD6B007A8372 /* SECloudUserInfo.h */,
				71AF41DC24B5AD6B007A8372 /* SECloudUserInfo.m */,
				221087D727705929002D3A6B /* SECloudDevicePermission.h */,
				221087D827705929002D3A6B /* SECloudDevicePermission.m */,
				221087DB27705971002D3A6B /* SECloudDeviceAuth.h */,
				221087DC27705971002D3A6B /* SECloudDeviceAuth.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		71AF41DF24B5AD6B007A8372 /* Device */ = {
			isa = PBXGroup;
			children = (
				71AF41E224B5AD6B007A8372 /* SEModifyDeviceInfoAPI.h */,
				71AF41E324B5AD6B007A8372 /* SEModifyDeviceInfoAPI.m */,
				71AF41E124B5AD6B007A8372 /* SEQueryDeviceListAPI.h */,
				71AF41E424B5AD6B007A8372 /* SEQueryDeviceListAPI.m */,
				71AF41E524B5AD6B007A8372 /* SEUnbindDeviceAPI.h */,
				71AF41E024B5AD6B007A8372 /* SEUnbindDeviceAPI.m */,
			);
			path = Device;
			sourceTree = "<group>";
		};
		71AF41E624B5AD6B007A8372 /* UplusHome */ = {
			isa = PBXGroup;
			children = (
				71AF41E724B5AD6B007A8372 /* Family */,
				71AF421424B5AD6B007A8372 /* User */,
				71AF424124B5AD6B007A8372 /* Common */,
				71AF424624B5AD6B007A8372 /* Device */,
			);
			path = UplusHome;
			sourceTree = "<group>";
		};
		71AF41E724B5AD6B007A8372 /* Family */ = {
			isa = PBXGroup;
			children = (
				71AF41F424B5AD6B007A8372 /* UPAddRoomToFamilyApi.h */,
				71AF420E24B5AD6B007A8372 /* UPAddRoomToFamilyApi.m */,
				955ED3F22DA7DBD300E10807 /* UpAddRoomToFamilyNewApi.h */,
				955ED3F32DA7DBD300E10807 /* UpAddRoomToFamilyNewApi.m */,
				71AF41F324B5AD6B007A8372 /* UPChangeFamilyAdminApi.h */,
				71AF420F24B5AD6B007A8372 /* UPChangeFamilyAdminApi.m */,
				71AF41F724B5AD6B007A8372 /* UPCreateFamilyApi.h */,
				71AF420B24B5AD6B007A8372 /* UPCreateFamilyApi.m */,
				71AF420D24B5AD6B007A8372 /* UPdateFamilyInfoApi.h */,
				71AF41F524B5AD6B007A8372 /* UPdateFamilyInfoApi.m */,
				71AF421224B5AD6B007A8372 /* UPDestroyFamilyApi.h */,
				71AF41FB24B5AD6B007A8372 /* UPDestroyFamilyApi.m */,
				71AF41EC24B5AD6B007A8372 /* UPFamilyDeleteMemberApi.h */,
				71AF420024B5AD6B007A8372 /* UPFamilyDeleteMemberApi.m */,
				71AF421024B5AD6B007A8372 /* UPFamilyInfoApi.h */,
				71AF41FD24B5AD6B007A8372 /* UPFamilyInfoApi.m */,
				22F6FDBF27953D490048F32F /* UPQueryFirstMemeberApi.h */,
				22F6FDC027953D490048F32F /* UPQueryFirstMemeberApi.m */,
				22F6FDC327953D810048F32F /* UPAddVirtualMemberApi.h */,
				22F6FDC427953D810048F32F /* UPAddVirtualMemberApi.m */,
				22F6FDC727953DB20048F32F /* UPModifyVirtualMemberApi.h */,
				22F6FDC827953DB20048F32F /* UPModifyVirtualMemberApi.m */,
				223223EB28B5F36B00A7A0E7 /* UPModifyVirtualMemberRoleApi.h */,
				223223EC28B5F36B00A7A0E7 /* UPModifyVirtualMemberRoleApi.m */,
				2252139628A1F4F3001D2832 /* UPModifyMemberRoleApi.h */,
				2252139728A1F4F3001D2832 /* UPModifyMemberRoleApi.m */,
				950119EF2DA9189900F92F77 /* UPModifyMemberTypeApi.h */,
				950119F02DA9189900F92F77 /* UPModifyMemberTypeApi.m */,
				71AF41F624B5AD6B007A8372 /* UPFamilyInviteMemberApi.h */,
				71AF420C24B5AD6B007A8372 /* UPFamilyInviteMemberApi.m */,
				71AF421124B5AD6B007A8372 /* UPFamilyListApi.h */,
				71AF41FC24B5AD6B007A8372 /* UPFamilyListApi.m */,
				71AF41F224B5AD6B007A8372 /* UPFamilyMemberShareDeviceCountApi.h */,
				71AF420524B5AD6B007A8372 /* UPFamilyMemberShareDeviceCountApi.m */,
				71AF420924B5AD6B007A8372 /* UPFamilyMoveDevicesToNewRoomApi.h */,
				71AF41F924B5AD6B007A8372 /* UPFamilyMoveDevicesToNewRoomApi.m */,
				71AF420624B5AD6B007A8372 /* UPFamilyMoveDevicesToOtherFamilyApi.h */,
				71AF41F124B5AD6B007A8372 /* UPFamilyMoveDevicesToOtherFamilyApi.m */,
				71AF41FF24B5AD6B007A8372 /* UPFamilyRemoveDevicesApi.h */,
				71AF41ED24B5AD6B007A8372 /* UPFamilyRemoveDevicesApi.m */,
				71AF41FA24B5AD6B007A8372 /* UPFamilyReplyApi.h */,
				71AF421324B5AD6B007A8372 /* UPFamilyReplyApi.m */,
				22E6298B25F0C07800912AA8 /* UPReplyJoinFamilyApi.h */,
				22E6298C25F0C07800912AA8 /* UPReplyJoinFamilyApi.m */,
				71AF41F024B5AD6B007A8372 /* UPFamilyRoomListApi.h */,
				71AF420724B5AD6B007A8372 /* UPFamilyRoomListApi.m */,
				71AF420224B5AD6B007A8372 /* UPFamilyUnbindDevicesApi.h */,
				71AF41E924B5AD6B007A8372 /* UPFamilyUnbindDevicesApi.m */,
				71AF420824B5AD6B007A8372 /* UPQuitFamilyAsAdminApi.h */,
				71AF41EF24B5AD6B007A8372 /* UPQuitFamilyAsAdminApi.m */,
				71AF41EB24B5AD6B007A8372 /* UPQuitFamilyAsMemberApi.h */,
				71AF420324B5AD6B007A8372 /* UPQuitFamilyAsMemberApi.m */,
				71AF420124B5AD6B007A8372 /* UPRemoveRoomFromFamilyApi.h */,
				71AF41EA24B5AD6B007A8372 /* UPRemoveRoomFromFamilyApi.m */,
				71AF41E824B5AD6B007A8372 /* UPSetDefaultFamilyApi.h */,
				71AF420424B5AD6B007A8372 /* UPSetDefaultFamilyApi.m */,
				71AF41EE24B5AD6B007A8372 /* UPUpdateFamilyRoomNameApi.h */,
				71AF41FE24B5AD6B007A8372 /* UPUpdateFamilyRoomNameApi.m */,
				F8F22329257B388200222539 /* UPAddFloorApi.h */,
				F8F2232A257B388200222539 /* UPAddFloorApi.m */,
				F8F2232D257B389B00222539 /* UPEditFloorApi.h */,
				F8F2232E257B389B00222539 /* UPEditFloorApi.m */,
				F8F22331257B38AF00222539 /* UPDeleteFloorApi.h */,
				F8F22332257B38AF00222539 /* UPDeleteFloorApi.m */,
				2202C8632922156800955D81 /* UPSaveRoomsOrderApi.h */,
				2202C8642922156800955D81 /* UPSaveRoomsOrderApi.m */,
				95EC2CA82DB61D3D00E22D0E /* UPSaveRoomsOrderNewApi.h */,
				95EC2CA92DB61D3D00E22D0E /* UPSaveRoomsOrderNewApi.m */,
				95BEBB142D92951E001A07D7 /* UpEditDeviceCardStatusApi.h */,
				95BEBB152D92951E001A07D7 /* UpEditDeviceCardStatusApi.m */,
				95BEBB182D9299DA001A07D7 /* UpEditDeviceAggregationApi.h */,
				95BEBB192D9299DA001A07D7 /* UpEditDeviceAggregationApi.m */,
			);
			path = Family;
			sourceTree = "<group>";
		};
		71AF421424B5AD6B007A8372 /* User */ = {
			isa = PBXGroup;
			children = (
				71AF423224B5AD6B007A8372 /* UPUserAccountVerifyApi.h */,
				71AF421B24B5AD6B007A8372 /* UPUserAccountVerifyApi.m */,
				71AF422124B5AD6B007A8372 /* UPUserAddressApi.h */,
				71AF423B24B5AD6B007A8372 /* UPUserAddressApi.m */,
				71AF423324B5AD6B007A8372 /* UPUserApplitionTokenApi.h */,
				71AF421A24B5AD6B007A8372 /* UPUserApplitionTokenApi.m */,
				71AF421D24B5AD6B007A8372 /* UPUserCreateNewAddressApi.h */,
				71AF423024B5AD6B007A8372 /* UPUserCreateNewAddressApi.m */,
				71AF421524B5AD6B007A8372 /* UPUserDeleteAddressApi.h */,
				71AF422D24B5AD6B007A8372 /* UPUserDeleteAddressApi.m */,
				71AF422924B5AD6B007A8372 /* UPUserEditAddressApi.h */,
				71AF423E24B5AD6B007A8372 /* UPUserEditAddressApi.m */,
				71AF423924B5AD6B007A8372 /* UPUserInfoApi.h */,
				71AF422324B5AD6B007A8372 /* UPUserInfoApi.m */,
				71AF422E24B5AD6B007A8372 /* UPUserLoginLogsApi.h */,
				71AF421E24B5AD6B007A8372 /* UPUserLoginLogsApi.m */,
				71AF423524B5AD6B007A8372 /* UPUserLoginTerminalApi.h */,
				71AF421924B5AD6B007A8372 /* UPUserLoginTerminalApi.m */,
				71AF421624B5AD6B007A8372 /* UPUserQRCancleLoginApi.h */,
				71AF422C24B5AD6B007A8372 /* UPUserQRCancleLoginApi.m */,
				71AF422024B5AD6B007A8372 /* UPUserQRConfirmLoginApi.h */,
				71AF423C24B5AD6B007A8372 /* UPUserQRConfirmLoginApi.m */,
				71AF422624B5AD6B007A8372 /* UPUserQRloginPollApi.h */,
				71AF423624B5AD6B007A8372 /* UPUserQRloginPollApi.m */,
				71AF422424B5AD6B007A8372 /* UPUserQRScanApi.h */,
				71AF423824B5AD6B007A8372 /* UPUserQRScanApi.m */,
				71AF423D24B5AD6B007A8372 /* UPUserRefreshTokenApi.h */,
				71AF422A24B5AD6B007A8372 /* UPUserRefreshTokenApi.m */,
				71AF422F24B5AD6B007A8372 /* UPUserServiceOrderApi.h */,
				71AF421F24B5AD6B007A8372 /* UPUserServiceOrderApi.m */,
				71AF424024B5AD6B007A8372 /* UPUserUpdatePwdApi.h */,
				71AF422724B5AD6B007A8372 /* UPUserUpdatePwdApi.m */,
				71AF422824B5AD6B007A8372 /* UPUserUpdateUserInfoApi.h */,
				71AF423F24B5AD6B007A8372 /* UPUserUpdateUserInfoApi.m */,
				71AF422524B5AD6B007A8372 /* UPUserUploadAvatarApi.h */,
				71AF423724B5AD6B007A8372 /* UPUserUploadAvatarApi.m */,
				71AF421724B5AD6B007A8372 /* UPZJRefreshTokenApi.h */,
				71AF422B24B5AD6B007A8372 /* UPZJRefreshTokenApi.m */,
				71AF421824B5AD6B007A8372 /* UPZJUserInfoApi.h */,
				71AF423424B5AD6B007A8372 /* UPZJUserInfoApi.m */,
				2248A0AC25E10B8B00F7EF79 /* UPZJUserLogOutApi.h */,
				2248A0AD25E10B8B00F7EF79 /* UPZJUserLogOutApi.m */,
				95BEBB1C2D929EF7001A07D7 /* UpEditAggregationSwitchApi.h */,
				95BEBB1D2D929EF7001A07D7 /* UpEditAggregationSwitchApi.m */,
				F8AEC68E2D7EFD76008C92C5 /* UPConfirmDeviceSharingRelationApi.h */,
				F8AEC68F2D7EFD76008C92C5 /* UPConfirmDeviceSharingRelationApi.m */,
				F8AEC6922D7F0E99008C92C5 /* UPCancelDeviceSharingRelationApi.h */,
				F8AEC6932D7F0E99008C92C5 /* UPCancelDeviceSharingRelationApi.m */,
			);
			path = User;
			sourceTree = "<group>";
		};
		71AF424124B5AD6B007A8372 /* Common */ = {
			isa = PBXGroup;
			children = (
				334AFE1A267343C0003F443B /* SYNServerAPIBase.h */,
				334AFE1B267343C0003F443B /* SYNServerAPIBase.m */,
				71AF424524B5AD6B007A8372 /* APPServerAPIBase.h */,
				71AF424324B5AD6B007A8372 /* APPServerAPIBase.m */,
				F8F22325257B36E900222539 /* ZJServerAPIBase.h */,
				F8F22326257B36E900222539 /* ZJServerAPIBase.m */,
				71AF424424B5AD6B007A8372 /* UserCenterAPIBase.h */,
				71AF424224B5AD6B007A8372 /* UserCenterAPIBase.m */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		71AF424624B5AD6B007A8372 /* Device */ = {
			isa = PBXGroup;
			children = (
				71AF424924B5AD6B007A8372 /* UPDeviceListApi.h */,
				71AF424C24B5AD6C007A8372 /* UPDeviceListApi.m */,
				71AF424A24B5AD6B007A8372 /* UPDeviceUpdateNameApi.h */,
				71AF424824B5AD6B007A8372 /* UPDeviceUpdateNameApi.m */,
				71AF424724B5AD6B007A8372 /* UPUpdateDeviceInfoApi.h */,
				71AF424B24B5AD6C007A8372 /* UPUpdateDeviceInfoApi.m */,
				22EBEA3A2914A91A007DA887 /* UPGetGroupDevicesApi.h */,
				22EBEA3B2914A91A007DA887 /* UPGetGroupDevicesApi.m */,
				84B5D4AB2ABAE35F00C07E3F /* UPDeviceUpdateAndCheckNameApi.h */,
				84B5D4AC2ABAE35F00C07E3F /* UPDeviceUpdateAndCheckNameApi.m */,
			);
			path = Device;
			sourceTree = "<group>";
		};
		71ECF7FC241A109F009BBAD0 /* doc */ = {
			isa = PBXGroup;
			children = (
				71ECF7FD241A109F009BBAD0 /* readme.md */,
				71ECF7FE241A109F009BBAD0 /* release.md */,
				71ECF7FF241A10BA009BBAD0 /* upuserdomain.podspec */,
			);
			path = doc;
			sourceTree = "<group>";
		};
		71ECF941241A2C0D009BBAD0 /* Debugger */ = {
			isa = PBXGroup;
			children = (
				F81D1EC724FE2E670041830C /* UPMacros.h */,
				AD81E87224F75B5D002430CA /* Family */,
				AD81E86624F634BE002430CA /* Pickview */,
				71ECF942241A2C0D009BBAD0 /* AppDelegate.h */,
				71ECF943241A2C0D009BBAD0 /* AppDelegate.m */,
				7155373E24F5FFF800BAA7D7 /* LoginSettings.h */,
				7155373F24F5FFF800BAA7D7 /* LoginSettings.m */,
				71ECF948241A2C0D009BBAD0 /* ViewController.h */,
				71ECF949241A2C0D009BBAD0 /* ViewController.m */,
				71B045B924F34ED0001833D0 /* LoginViewController.h */,
				71B045BA24F34ED0001833D0 /* LoginViewController.m */,
				71B381E924F4AF7C00F04E48 /* FunctionsTableViewController.h */,
				71B381EA24F4AF7C00F04E48 /* FunctionsTableViewController.m */,
				71B381EC24F4B43900F04E48 /* UserDetailViewController.h */,
				71B381ED24F4B43900F04E48 /* UserDetailViewController.m */,
				7155373A24F5F8EA00BAA7D7 /* UserInfoEditViewController.h */,
				7155373B24F5F8EA00BAA7D7 /* UserInfoEditViewController.m */,
				71035FDD24FC858100A9D036 /* DeviceListViewController.h */,
				71035FDE24FC858100A9D036 /* DeviceListViewController.m */,
				71035FE024FC8AD800A9D036 /* DeviceDetailViewController.h */,
				71035FE124FC8AD800A9D036 /* DeviceDetailViewController.m */,
				71035FE324FC918A00A9D036 /* FamilySelectionViewController.h */,
				71035FE424FC918A00A9D036 /* FamilySelectionViewController.m */,
				ADF38BB124DC1C5900551FF7 /* UPUserLoginApi.h */,
				ADF38BB024DC1C5900551FF7 /* UPUserLoginApi.m */,
				7155374124F6467600BAA7D7 /* SEUserLoginApi.h */,
				7155374224F6467600BAA7D7 /* SEUserLoginApi.m */,
				F854517924F50C0F007F3114 /* UPUploadRecordApis.h */,
				F854517A24F50C0F007F3114 /* UPUploadRecordApis.m */,
				71ECF94B241A2C0D009BBAD0 /* Main.storyboard */,
				71ECF94E241A2C0D009BBAD0 /* Assets.xcassets */,
				71ECF950241A2C0D009BBAD0 /* LaunchScreen.storyboard */,
				71ECF953241A2C0D009BBAD0 /* Info.plist */,
				71ECF954241A2C0D009BBAD0 /* main.m */,
			);
			path = Debugger;
			sourceTree = "<group>";
		};
		72321F742255DE1E005F6A86 = {
			isa = PBXGroup;
			children = (
				71ECF7FC241A109F009BBAD0 /* doc */,
				71AF41BC24B5ACD5007A8372 /* UserDomainAPIs */,
				72321F982255DE1F005F6A86 /* upuserdomainTests */,
				71ECF941241A2C0D009BBAD0 /* Debugger */,
				F8CEF1FA24CAB243008705B3 /* upuserdomain */,
				AD92EDCE2632997500C9544A /* UserDomainDataSource */,
				72321F7E2255DE1E005F6A86 /* Products */,
				3F94A21CF5B513327D3D9B8C /* Pods */,
				E3541BAE0177991F3911D15F /* Frameworks */,
				7155373624F5F86C00BAA7D7 /* Recovered References */,
			);
			sourceTree = "<group>";
		};
		72321F7E2255DE1E005F6A86 /* Products */ = {
			isa = PBXGroup;
			children = (
				72321F952255DE1F005F6A86 /* upuserdomainTests.xctest */,
				71ECF940241A2C0D009BBAD0 /* Debugger.app */,
				ADB50C7F2643848F00062881 /* libUserDomainAPIs.a */,
				AD12D63826438F9F00188BC3 /* libupuserdomain.a */,
				AD12D7492643933E00188BC3 /* libUserDomainDataSource.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		72321F982255DE1F005F6A86 /* upuserdomainTests */ = {
			isa = PBXGroup;
			children = (
				22037984270FE9D000FB8EB2 /* features */,
				724E9E77228505D800B66E9A /* Steps */,
				724E9E75228505B800B66E9A /* Holder */,
				724E9E76228505CB00B66E9A /* Utils */,
				72E530E722643E09008992A1 /* CucumberRunner.m */,
				72321F9B2255DE1F005F6A86 /* Info.plist */,
			);
			path = upuserdomainTests;
			sourceTree = "<group>";
		};
		724E9E75228505B800B66E9A /* Holder */ = {
			isa = PBXGroup;
			children = (
				F8CEF18224C7D686008705B3 /* UserdomainHolderTest.h */,
				F8CEF18324C7D686008705B3 /* UserdomainHolderTest.m */,
			);
			path = Holder;
			sourceTree = "<group>";
		};
		724E9E76228505CB00B66E9A /* Utils */ = {
			isa = PBXGroup;
			children = (
				F8CEF18824C7D6C8008705B3 /* StepsUtils.h */,
				F8CEF18924C7D6C8008705B3 /* StepsUtils.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		724E9E77228505D800B66E9A /* Steps */ = {
			isa = PBXGroup;
			children = (
				F8CEF18B24C7D6FD008705B3 /* InitializationSteps.h */,
				F8CEF18C24C7D6FD008705B3 /* InitializationSteps.m */,
				F8CEF1AC24C7E347008705B3 /* UserSteps.h */,
				F8CEF1AD24C7E347008705B3 /* UserSteps.m */,
				F8CEF1AF24C7E359008705B3 /* FamilySteps.h */,
				F8CEF1B024C7E359008705B3 /* FamilySteps.m */,
				F8CEF1B224C7E366008705B3 /* DeviceSteps.h */,
				F8CEF1B324C7E366008705B3 /* DeviceSteps.m */,
				F8CEF1B524C7E37A008705B3 /* CacheSteps.h */,
				F8CEF1B624C7E37A008705B3 /* CacheSteps.m */,
				F8CEF1BB24C7E3AF008705B3 /* TimeSteps.h */,
				F8CEF1BC24C7E3AF008705B3 /* TimeSteps.m */,
				F8CEF1BE24C7E3BE008705B3 /* EventHandleSteps.h */,
				F8CEF1BF24C7E3BE008705B3 /* EventHandleSteps.m */,
				F8CEF1C124C7E3EC008705B3 /* RefreshTokenSteps.h */,
				F8CEF1C224C7E3EC008705B3 /* RefreshTokenSteps.m */,
				F8CEF1C424C7E411008705B3 /* SignleLoginSteps.h */,
				F8CEF1C524C7E411008705B3 /* SignleLoginSteps.m */,
				F899FCE724ECBCAF00F34E56 /* UserdomainSteps.h */,
				F899FCE824ECBCAF00F34E56 /* UserdomainSteps.m */,
			);
			path = Steps;
			sourceTree = "<group>";
		};
		AD5D443F24CFB4B000211C3F /* Event */ = {
			isa = PBXGroup;
			children = (
				AD5D446724D110ED00211C3F /* UpEvent.h */,
				AD5D446824D110ED00211C3F /* UpEvent.m */,
				AD5D447124D141D700211C3F /* UpNotifyEventHandler.h */,
				AD5D447224D141D700211C3F /* UpNotifyEventHandler.m */,
				AD5D45A624D3F1A300211C3F /* UpEventHandlerManager.h */,
				AD5D45A724D3F1A300211C3F /* UpEventHandlerManager.m */,
				F8D6AC6224F4E5CF007EA70A /* UDOpertateRefreshUserEventHandler.h */,
				F8D6AC6324F4E5CF007EA70A /* UDOpertateRefreshUserEventHandler.m */,
				F899FCDF24EBB46200F34E56 /* UDOperatePlannedRefreshTokenEventHandler.h */,
				F899FCE024EBB46200F34E56 /* UDOperatePlannedRefreshTokenEventHandler.m */,
				F8B541B324EE4D620075A070 /* UDOperateRefreshTerminalListEventHandler.h */,
				F8B541B424EE4D620075A070 /* UDOperateRefreshTerminalListEventHandler.m */,
				F8B541AF24EE45790075A070 /* UDOperateRefreshAddressListEventHandler.h */,
				F8B541B024EE45790075A070 /* UDOperateRefreshAddressListEventHandler.m */,
				F8B541B724EE4E650075A070 /* UDOperateRefreshFamilyListEventHandler.h */,
				F8B541B824EE4E650075A070 /* UDOperateRefreshFamilyListEventHandler.m */,
				F8B541BB24EE4E790075A070 /* UDOperateRefreshFamilyDetailEventHandler.h */,
				F8B541BC24EE4E7A0075A070 /* UDOperateRefreshFamilyDetailEventHandler.m */,
				F8B541BF24EE4ECF0075A070 /* UDOperateRefreshDeviceListEventHandler.h */,
				F8B541C024EE4ECF0075A070 /* UDOperateRefreshDeviceListEventHandler.m */,
				AD5D45AA24D3FDFD00211C3F /* UpListenerTransformObserver.h */,
				AD5D45AB24D3FDFD00211C3F /* UpListenerTransformObserver.m */,
			);
			path = Event;
			sourceTree = "<group>";
		};
		AD5D444924CFC8AB00211C3F /* Authdata */ = {
			isa = PBXGroup;
			children = (
				AD5D444F24CFCFAD00211C3F /* UDRefreshTokenOp.h */,
				AD5D445024CFCFAD00211C3F /* UDRefreshTokenOp.m */,
				AD5D44A324D1A40800211C3F /* UDLogoutOp.h */,
				AD5D44A424D1A40800211C3F /* UDLogoutOp.m */,
				AD62068F24D9508500C9B414 /* UDPlannedRefreshAuthDataOp.h */,
				AD62069024D9508500C9B414 /* UDPlannedRefreshAuthDataOp.m */,
				AD62069724D950BA00C9B414 /* UDRefreshUserOp.h */,
				AD62069824D950BA00C9B414 /* UDRefreshUserOp.m */,
			);
			path = Authdata;
			sourceTree = "<group>";
		};
		AD5D444A24CFC8F800211C3F /* Device */ = {
			isa = PBXGroup;
			children = (
				AD5D44A924D253F400211C3F /* UDModifyDeviceNameOp.h */,
				AD5D44AA24D253F500211C3F /* UDModifyDeviceNameOp.m */,
				AD5D44AB24D253F500211C3F /* UDRefreshDeviceListOp.h */,
				AD5D44A724D253F400211C3F /* UDRefreshDeviceListOp.m */,
				22EBEA362914A599007DA887 /* UDGetGroupDevicesOp.h */,
				22EBEA372914A599007DA887 /* UDGetGroupDevicesOp.m */,
				84B5D4AF2ABAED2400C07E3F /* UDModifyDeviceNameNewOp.h */,
				84B5D4B02ABAED2400C07E3F /* UDModifyDeviceNameNewOp.m */,
			);
			path = Device;
			sourceTree = "<group>";
		};
		AD5D444B24CFC90B00211C3F /* Family */ = {
			isa = PBXGroup;
			children = (
				AD5D44B724D26C4000211C3F /* UDChangeFamilyAdminOp.h */,
				AD5D44B824D26C4000211C3F /* UDChangeFamilyAdminOp.m */,
				AD62066324D941B200C9B414 /* UDRefreshRoomListOp.h */,
				AD62066424D941B200C9B414 /* UDRefreshRoomListOp.m */,
				AD62066724D941C700C9B414 /* UDCreateFamilyOp.h */,
				AD62066824D941C700C9B414 /* UDCreateFamilyOp.m */,
				AD62066B24D941D700C9B414 /* UDCreateRoomOp.h */,
				AD62066C24D941D700C9B414 /* UDCreateRoomOp.m */,
				955ED3FA2DA7E06F00E10807 /* UDCreateRoomNewOp.h */,
				955ED3FB2DA7E06F00E10807 /* UDCreateRoomNewOp.m */,
				AD62066F24D941E900C9B414 /* UDDeleteFamilyAsAdminOp.h */,
				AD62067024D941E900C9B414 /* UDDeleteFamilyAsAdminOp.m */,
				AD62067324D9420200C9B414 /* UDDeleteRoomOp.h */,
				AD62067424D9420200C9B414 /* UDDeleteRoomOp.m */,
				AD62067724D9421100C9B414 /* UDEditFamilyInfoOp.h */,
				AD62067824D9421100C9B414 /* UDEditFamilyInfoOp.m */,
				AD62067B24D9421F00C9B414 /* UDEditRoomNameOp.h */,
				AD62067C24D9421F00C9B414 /* UDEditRoomNameOp.m */,
				AD62067F24D9422C00C9B414 /* UDExitFamilyAsAdminOp.h */,
				AD62068024D9422C00C9B414 /* UDExitFamilyAsAdminOp.m */,
				AD62068324D9423D00C9B414 /* UDExitFamilyAsMemberOp.h */,
				AD62068424D9423D00C9B414 /* UDExitFamilyAsMemberOp.m */,
				F80B7B7724E6532F0073FC48 /* UDDeleteMemberOp.h */,
				F80B7B7824E6532F0073FC48 /* UDDeleteMemberOp.m */,
				AD62068724D9425500C9B414 /* UDInviteFamilyMemberOp.h */,
				AD62068824D9425500C9B414 /* UDInviteFamilyMemberOp.m */,
				F80B7B7B24E659910073FC48 /* UDUnbindDevicesOp.h */,
				F80B7B7C24E659910073FC48 /* UDUnbindDevicesOp.m */,
				AD62068B24D9426500C9B414 /* UDMoveDevicesToFamilyOp.h */,
				AD62068C24D9426500C9B414 /* UDMoveDevicesToFamilyOp.m */,
				F80B7B7F24E687440073FC48 /* UDMoveDevicesToRoomOp.h */,
				F80B7B8024E687440073FC48 /* UDMoveDevicesToRoomOp.m */,
				F80B7B8324E689FB0073FC48 /* UDDeleteDevicesOp.h */,
				F80B7B8424E689FB0073FC48 /* UDDeleteDevicesOp.m */,
				F80B7B5B24E50D5F0073FC48 /* UDRefreshFamilyListOp.h */,
				F80B7B5C24E50D5F0073FC48 /* UDRefreshFamilyListOp.m */,
				F80B7B6724E52C500073FC48 /* UDReplyFamilyInviteOp.h */,
				F80B7B6824E52C500073FC48 /* UDReplyFamilyInviteOp.m */,
				22E6298F25F0C8BD00912AA8 /* UDReplyJoinFamilyOp.h */,
				22E6299025F0C8BD00912AA8 /* UDReplyJoinFamilyOp.m */,
				F80B7B6B24E539CA0073FC48 /* UDSetCurrentFamilyOp.h */,
				F80B7B6C24E539CA0073FC48 /* UDSetCurrentFamilyOp.m */,
				F80B7B7324E61FE50073FC48 /* UDUpdateFamilyDetailOp.h */,
				F80B7B7424E61FE50073FC48 /* UDUpdateFamilyDetailOp.m */,
				F8B541C324EE5A000075A070 /* UDRefreshAllFamilyDetailOp.h */,
				F8B541C424EE5A000075A070 /* UDRefreshAllFamilyDetailOp.m */,
				F8304B0E2579D18300E326E5 /* UDAddFloorOp.h */,
				F8304B0F2579D18300E326E5 /* UDAddFloorOp.m */,
				F8304B122579D1A600E326E5 /* UDEditFloorOp.h */,
				F8304B132579D1A600E326E5 /* UDEditFloorOp.m */,
				F8304B162579D1C700E326E5 /* UDDeleteFloorOp.h */,
				F8304B172579D1C700E326E5 /* UDDeleteFloorOp.m */,
				22F6FDCB27965D3B0048F32F /* UDQueryFirstMemeberOp.h */,
				22F6FDCC27965D3B0048F32F /* UDQueryFirstMemeberOp.m */,
				22F6FDCF27965D610048F32F /* UDAddVirtualMemberOp.h */,
				22F6FDD027965D610048F32F /* UDAddVirtualMemberOp.m */,
				22F6FDD327965D820048F32F /* UDModifyVirtualMemberOp.h */,
				22F6FDD427965D820048F32F /* UDModifyVirtualMemberOp.m */,
				2252139228A1F1F0001D2832 /* UDModifyMemberRoleOp.h */,
				2252139328A1F1F0001D2832 /* UDModifyMemberRoleOp.m */,
				950119F32DA919E700F92F77 /* UDModifyMemberTypeOp.h */,
				950119F42DA919E700F92F77 /* UDModifyMemberTypeOp.m */,
				223223EF28B5F54500A7A0E7 /* UDModifyVirtualMemberRoleOp.h */,
				223223F028B5F54500A7A0E7 /* UDModifyVirtualMemberRoleOp.m */,
				2202C85F2922130800955D81 /* UDSaveRoomsOrderOp.h */,
				2202C8602922130800955D81 /* UDSaveRoomsOrderOp.m */,
				95EC2CAC2DB621D400E22D0E /* UDSaveRoomsOrderNewOp.h */,
				95EC2CAD2DB621D400E22D0E /* UDSaveRoomsOrderNewOp.m */,
				95BEBB202D92CCF8001A07D7 /* UDModifyDeviceCardListOp.h */,
				95BEBB212D92CCF8001A07D7 /* UDModifyDeviceCardListOp.m */,
				95BEBB242D92CFEE001A07D7 /* UDModifyDeviceAggOp.h */,
				95BEBB252D92CFEE001A07D7 /* UDModifyDeviceAggOp.m */,
			);
			path = Family;
			sourceTree = "<group>";
		};
		AD5D446124D10E2600211C3F /* Store */ = {
			isa = PBXGroup;
			children = (
				AD5D447524D1475500211C3F /* UpUserDomainStore.h */,
				AD5D447624D1475500211C3F /* UpUserDomainStore.m */,
				AD5D449724D19B9000211C3F /* UpUserStore.h */,
				AD5D449824D19B9000211C3F /* UpUserStore.m */,
				AD5D45B624D6DEFD00211C3F /* UpDeviceStore.h */,
				AD5D45B724D6DEFD00211C3F /* UpDeviceStore.m */,
				AD5D45BA24D6DF0B00211C3F /* UpFamilyStore.h */,
				AD5D45BB24D6DF0B00211C3F /* UpFamilyStore.m */,
			);
			path = Store;
			sourceTree = "<group>";
		};
		AD5D448E24D16D0B00211C3F /* Event */ = {
			isa = PBXGroup;
			children = (
				AD5D444024CFB4C800211C3F /* UpUserDomainListenerDelegate.h */,
				AD5D446B24D1238600211C3F /* UpEventHandlerDelegate.h */,
				AD5D449124D1752C00211C3F /* UpEventHandlerManagerDelegate.h */,
			);
			path = Event;
			sourceTree = "<group>";
		};
		AD81E86624F634BE002430CA /* Pickview */ = {
			isa = PBXGroup;
			children = (
				ADCBD89824FE3BFD00BC30AC /* city-v3.0.json */,
				AD81E86A24F637E6002430CA /* Address.plist */,
				AD81E86724F63522002430CA /* AddressPickView.h */,
				AD81E86824F63522002430CA /* AddressPickView.m */,
				ADCBD88F24F9072C00BC30AC /* FamilyPickerView.h */,
				ADCBD89024F9072C00BC30AC /* FamilyPickerView.m */,
				ADCBD89224FCA61500BC30AC /* RoomPickerView.h */,
				ADCBD89324FCA61500BC30AC /* RoomPickerView.m */,
			);
			path = Pickview;
			sourceTree = "<group>";
		};
		AD81E87224F75B5D002430CA /* Family */ = {
			isa = PBXGroup;
			children = (
				AD81E85924F4A3DD002430CA /* FamilyListViewController.h */,
				AD81E85A24F4A3DD002430CA /* FamilyListViewController.m */,
				AD7AF97A24F7E48D0074F36F /* CreatFamilyViewController.h */,
				AD7AF97B24F7E48D0074F36F /* CreatFamilyViewController.m */,
				AD81E85C24F4DEA8002430CA /* FamilyDetailViewController.h */,
				AD81E85D24F4DEA8002430CA /* FamilyDetailViewController.m */,
				AD81E86124F60E4A002430CA /* MyFamilyController.h */,
				AD81E86224F60E4A002430CA /* MyFamilyController.m */,
				AD81E86F24F75B40002430CA /* FamilyOwnerViewController.h */,
				AD81E87024F75B40002430CA /* FamilyOwnerViewController.m */,
				AD81E86C24F64DDD002430CA /* FamilyDevicesViewController.h */,
				AD81E86D24F64DDD002430CA /* FamilyDevicesViewController.m */,
				AD7AF97424F7AB960074F36F /* FamilyMemberViewController.h */,
				AD7AF97524F7AB960074F36F /* FamilyMemberViewController.m */,
				ADCBD89524FE1A1600BC30AC /* RoomDevicesViewController.h */,
				ADCBD89624FE1A1600BC30AC /* RoomDevicesViewController.m */,
				ADCBD89A24FE52F700BC30AC /* AddressViewController.h */,
				ADCBD89B24FE52F700BC30AC /* AddressViewController.m */,
				AD7AF97724F7D8720074F36F /* CommonEditCell.h */,
				AD7AF97824F7D8720074F36F /* CommonEditCell.m */,
				AD7AF97D24F8AEF20074F36F /* DeviceActionView.h */,
				AD7AF97E24F8AEF20074F36F /* DeviceActionView.m */,
			);
			path = Family;
			sourceTree = "<group>";
		};
		AD92EDCE2632997500C9544A /* UserDomainDataSource */ = {
			isa = PBXGroup;
			children = (
				AD92F10B2632C4A500C9544A /* Category */,
				AD92EDEC26329A6500C9544A /* Delegate */,
				AD92EDED26329A7A00C9544A /* IMP */,
				AD92EDCF2632997500C9544A /* UserDomainDataSource.h */,
				AD92EDD02632997500C9544A /* Info.plist */,
			);
			path = UserDomainDataSource;
			sourceTree = "<group>";
		};
		AD92EDEC26329A6500C9544A /* Delegate */ = {
			isa = PBXGroup;
			children = (
				AD92EED62632A0C000C9544A /* DataSource */,
			);
			path = Delegate;
			sourceTree = "<group>";
		};
		AD92EDED26329A7A00C9544A /* IMP */ = {
			isa = PBXGroup;
			children = (
				F76DC4C6282BB35100E2EFF7 /* Common */,
				221AA33B277EA0FE008143F4 /* ShopDataSource */,
				AD92EEDD2632A0CD00C9544A /* DataSource */,
				AD92EF3E2632A0CD00C9544A /* SEDataSource */,
			);
			path = IMP;
			sourceTree = "<group>";
		};
		AD92EED62632A0C000C9544A /* DataSource */ = {
			isa = PBXGroup;
			children = (
				AD92EED72632A0C000C9544A /* UpFamilyDataSource.h */,
				AD92EED82632A0C000C9544A /* UpUserDataSource.h */,
				AD92EED92632A0C000C9544A /* UpDeviceListDataSource.h */,
			);
			path = DataSource;
			sourceTree = "<group>";
		};
		AD92EEDD2632A0CD00C9544A /* DataSource */ = {
			isa = PBXGroup;
			children = (
				AD92EEDE2632A0CD00C9544A /* CloudModel */,
				AD92EF102632A0CD00C9544A /* Transformer */,
				AD92EF352632A0CD00C9544A /* SampleImp */,
			);
			path = DataSource;
			sourceTree = "<group>";
		};
		AD92EEDE2632A0CD00C9544A /* CloudModel */ = {
			isa = PBXGroup;
			children = (
				AD92EEDF2632A0CD00C9544A /* CloudDevice */,
				AD92EEF22632A0CD00C9544A /* CloudUser */,
				AD92EEFF2632A0CD00C9544A /* CloudFamily */,
			);
			path = CloudModel;
			sourceTree = "<group>";
		};
		AD92EEDF2632A0CD00C9544A /* CloudDevice */ = {
			isa = PBXGroup;
			children = (
				AD92EEE32632A0CD00C9544A /* CloudBatchProcessDevicesResponse.h */,
				AD92EEE92632A0CD00C9544A /* CloudBatchProcessDevicesResponse.m */,
				AD92EEE52632A0CD00C9544A /* CloudDevice.h */,
				AD92EEEF2632A0CD00C9544A /* CloudDevice.m */,
				AD92EEE12632A0CD00C9544A /* CloudDeviceAuth.h */,
				AD92EEEB2632A0CD00C9544A /* CloudDeviceAuth.m */,
				AD92EEE02632A0CD00C9544A /* CloudDeviceBaseInfo.h */,
				AD92EEEC2632A0CD00C9544A /* CloudDeviceBaseInfo.m */,
				AD92EEEE2632A0CD00C9544A /* CloudDeviceExtendedInfo.h */,
				AD92EEE62632A0CD00C9544A /* CloudDeviceExtendedInfo.m */,
				AD92EEE42632A0CD00C9544A /* CloudDeviceList.h */,
				AD92EEED2632A0CD00C9544A /* CloudDeviceList.m */,
				AD92EEF12632A0CD00C9544A /* CloudDeviceOwnerInfo.h */,
				AD92EEE72632A0CD00C9544A /* CloudDeviceOwnerInfo.m */,
				AD92EEEA2632A0CD00C9544A /* CloudDevicePermission.h */,
				AD92EEE22632A0CD00C9544A /* CloudDevicePermission.m */,
				AD92EEE82632A0CD00C9544A /* CloudQRLoginStage.h */,
				AD92EEF02632A0CD00C9544A /* CloudQRLoginStage.m */,
				F8AEC6962D80287E008C92C5 /* CloudShareDeviceCardInfo.h */,
				F8AEC6972D80287E008C92C5 /* CloudShareDeviceCardInfo.m */,
			);
			path = CloudDevice;
			sourceTree = "<group>";
		};
		AD92EEF22632A0CD00C9544A /* CloudUser */ = {
			isa = PBXGroup;
			children = (
				AD92EEFC2632A0CD00C9544A /* CloudLoginTerminal.h */,
				AD92EEF52632A0CD00C9544A /* CloudLoginTerminal.m */,
				AD92EEFD2632A0CD00C9544A /* CloudOauthData.h */,
				AD92EEF82632A0CD00C9544A /* CloudOauthData.m */,
				AD92EEF72632A0CD00C9544A /* CloudUserAddressInfo.h */,
				AD92EEFE2632A0CD00C9544A /* CloudUserAddressInfo.m */,
				AD92EEF62632A0CD00C9544A /* CloudUserInfo.h */,
				AD92EEFB2632A0CD00C9544A /* CloudUserInfo.m */,
				AD92EEF32632A0CD00C9544A /* CloudUserLoginLogInfo.h */,
				AD92EEF92632A0CD00C9544A /* CloudUserLoginLogInfo.m */,
			);
			path = CloudUser;
			sourceTree = "<group>";
		};
		AD92EEFF2632A0CD00C9544A /* CloudFamily */ = {
			isa = PBXGroup;
			children = (
				AD92EF0B2632A0CD00C9544A /* CloudFamily.h */,
				AD92EF052632A0CD00C9544A /* CloudFamily.m */,
				AD92EF0C2632A0CD00C9544A /* CloudFamilyFirstMember.h */,
				AD92EF042632A0CD00C9544A /* CloudFamilyFirstMember.m */,
				AD92EF0F2632A0CD00C9544A /* CloudFamilyList.h */,
				AD92EF062632A0CD00C9544A /* CloudFamilyList.m */,
				AD92EF0A2632A0CD00C9544A /* CloudFamilyLocation.h */,
				AD92EF002632A0CD00C9544A /* CloudFamilyLocation.m */,
				AD92EF092632A0CD00C9544A /* CloudFamilyMember.h */,
				AD92EF012632A0CD00C9544A /* CloudFamilyMember.m */,
				AD92EF022632A0CD00C9544A /* CloudFamilyMemberInfo.h */,
				AD92EF082632A0CD00C9544A /* CloudFamilyMemberInfo.m */,
				AD92EF0E2632A0CD00C9544A /* CloudFloorInfo.h */,
				AD92EF072632A0CD00C9544A /* CloudFloorInfo.m */,
				AD92EF0D2632A0CD00C9544A /* CloudRoom.h */,
				AD92EF032632A0CD00C9544A /* CloudRoom.m */,
			);
			path = CloudFamily;
			sourceTree = "<group>";
		};
		AD92EF102632A0CD00C9544A /* Transformer */ = {
			isa = PBXGroup;
			children = (
				AD92EF152632A0CD00C9544A /* AddRoomTransformer.h */,
				AD92EF282632A0CD00C9544A /* AddRoomTransformer.m */,
				AD92EF322632A0CD00C9544A /* BatchProcessDevicesOfFamilyTransformer.h */,
				AD92EF212632A0CD00C9544A /* BatchProcessDevicesOfFamilyTransformer.m */,
				AD92EF1D2632A0CD00C9544A /* CommonTransformer.h */,
				AD92EF2A2632A0CD00C9544A /* CommonTransformer.m */,
				225BA55A27F19B3D0076B9ED /* CommonDataTransformer.h */,
				225BA55B27F19B3D0076B9ED /* CommonDataTransformer.m */,
				AD92EF252632A0CD00C9544A /* CreateFamilyTransformer.h */,
				AD92EF122632A0CD00C9544A /* CreateFamilyTransformer.m */,
				AD92EF132632A0CD00C9544A /* DeviceListTransformer.h */,
				AD92EF242632A0CD00C9544A /* DeviceListTransformer.m */,
				AD92EF1C2632A0CD00C9544A /* FamilyFirstMemberTransformer.h */,
				AD92EF2C2632A0CD00C9544A /* FamilyFirstMemberTransformer.m */,
				AD92EF172632A0CD00C9544A /* FamilyInfoTransformer.h */,
				AD92EF2E2632A0CD00C9544A /* FamilyInfoTransformer.m */,
				AD92EF1F2632A0CD00C9544A /* FamilyListTransformer.h */,
				AD92EF342632A0CD00C9544A /* FamilyListTransformer.m */,
				AD92EF272632A0CD00C9544A /* FamilyRoomTransformer.h */,
				AD92EF162632A0CD00C9544A /* FamilyRoomTransformer.m */,
				AD92EF1E2632A0CD00C9544A /* UpUserServiceOrderTransformer.h */,
				AD92EF292632A0CD00C9544A /* UpUserServiceOrderTransformer.m */,
				AD92EF302632A0CD00C9544A /* UPZJOauthDataTransformer.h */,
				AD92EF192632A0CD00C9544A /* UPZJOauthDataTransformer.m */,
				AD92EF262632A0CD00C9544A /* UserAddressTransformer.h */,
				AD92EF112632A0CD00C9544A /* UserAddressTransformer.m */,
				AD92EF312632A0CD00C9544A /* UserInfoTransformer.h */,
				AD92EF222632A0CD00C9544A /* UserInfoTransformer.m */,
				AD92EF1B2632A0CD00C9544A /* UserLoginLogsTransformer.h */,
				AD92EF2B2632A0CD00C9544A /* UserLoginLogsTransformer.m */,
				AD92EF2D2632A0CD00C9544A /* UserLoginTerminalTransformer.h */,
				AD92EF1A2632A0CD00C9544A /* UserLoginTerminalTransformer.m */,
				AD92EF332632A0CD00C9544A /* UserOauthDataTransformer.h */,
				AD92EF202632A0CD00C9544A /* UserOauthDataTransformer.m */,
				AD92EF182632A0CD00C9544A /* UserQrLoginPollTransformer.h */,
				AD92EF2F2632A0CD00C9544A /* UserQrLoginPollTransformer.m */,
				22F6FDD72796A54B0048F32F /* QueryFirstMemebeTransformer.h */,
				22F6FDD82796A54B0048F32F /* QueryFirstMemebeTransformer.m */,
			);
			path = Transformer;
			sourceTree = "<group>";
		};
		AD92EF352632A0CD00C9544A /* SampleImp */ = {
			isa = PBXGroup;
			children = (
				AD92EF362632A0CD00C9544A /* DataSourceCallbackFunctions.h */,
				AD92EF3A2632A0CD00C9544A /* DataSourceCallbackFunctions.m */,
				AD92EF3D2632A0CD00C9544A /* DeviceListDataSource.h */,
				AD92EF392632A0CD00C9544A /* DeviceListDataSource.m */,
				AD92EF382632A0CD00C9544A /* FamilyDataSource.h */,
				AD92EF3B2632A0CD00C9544A /* FamilyDataSource.m */,
				AD92EF372632A0CD00C9544A /* UserDataSource.h */,
				AD92EF3C2632A0CD00C9544A /* UserDataSource.m */,
			);
			path = SampleImp;
			sourceTree = "<group>";
		};
		AD92EF3E2632A0CD00C9544A /* SEDataSource */ = {
			isa = PBXGroup;
			children = (
				AD92EF3F2632A0CD00C9544A /* IMP */,
				AD92EF462632A0CD00C9544A /* Converter */,
			);
			path = SEDataSource;
			sourceTree = "<group>";
		};
		AD92EF3F2632A0CD00C9544A /* IMP */ = {
			isa = PBXGroup;
			children = (
				AD92EF422632A0CD00C9544A /* SEDeviceListDataSource.h */,
				AD92EF452632A0CD00C9544A /* SEDeviceListDataSource.m */,
				AD92EF442632A0CD00C9544A /* SEFamilyDataSource.h */,
				AD92EF412632A0CD00C9544A /* SEFamilyDataSource.m */,
				AD92EF432632A0CD00C9544A /* SEUserDataSource.h */,
				AD92EF402632A0CD00C9544A /* SEUserDataSource.m */,
			);
			path = IMP;
			sourceTree = "<group>";
		};
		AD92EF462632A0CD00C9544A /* Converter */ = {
			isa = PBXGroup;
			children = (
				AD92EF512632A0CD00C9544A /* SEBaseConverter.h */,
				AD92EF4C2632A0CD00C9544A /* SEBaseConverter.m */,
				AD92EF522632A0CD00C9544A /* SEDeviceInfoListConverter.h */,
				AD92EF4B2632A0CD00C9544A /* SEDeviceInfoListConverter.m */,
				AD92EF482632A0CD00C9544A /* SEModifyDeviceConverter.h */,
				AD92EF4F2632A0CD00C9544A /* SEModifyDeviceConverter.m */,
				AD92EF502632A0CD00C9544A /* SEUserAuthInfoConverter.h */,
				AD92EF4A2632A0CD00C9544A /* SEUserAuthInfoConverter.m */,
				AD92EF4E2632A0CD00C9544A /* SEUserInfoConverter.h */,
				AD92EF492632A0CD00C9544A /* SEUserInfoConverter.m */,
				F7909326282B650300EE61D2 /* SEFamilyInfoTransformer.h */,
				F7909327282B650300EE61D2 /* SEFamilyInfoTransformer.m */,
				AD92EF4D2632A0CD00C9544A /* SEFamilyListConverter.h */,
				AD92EF472632A0CD00C9544A /* SEFamilyListConverter.m */,
				F790932A282B816200EE61D2 /* SEAddRoomTransformer.h */,
				F790932B282B816200EE61D2 /* SEAddRoomTransformer.m */,
				F790932E282B81EE00EE61D2 /* SECreateFamilyTransformer.h */,
				F790932F282B81EE00EE61D2 /* SECreateFamilyTransformer.m */,
				F7909332282B836900EE61D2 /* SEFamilyRoomTransformer.h */,
				F7909333282B836900EE61D2 /* SEFamilyRoomTransformer.m */,
				F7909336282B846C00EE61D2 /* SEBatchProcessDevicesOfFamilyTransformer.h */,
				F7909337282B846C00EE61D2 /* SEBatchProcessDevicesOfFamilyTransformer.m */,
				22B3B32128DD51C7007EF767 /* SEUnbindDeviceTransformer.h */,
				22B3B32228DD51C7007EF767 /* SEUnbindDeviceTransformer.m */,
			);
			path = Converter;
			sourceTree = "<group>";
		};
		AD92F1092632C44500C9544A /* CacheManager */ = {
			isa = PBXGroup;
			children = (
				F82E58A324F3A4960026E454 /* UDServerCacheManager.h */,
				F82E58A424F3A4960026E454 /* UDServerCacheManager.m */,
			);
			path = CacheManager;
			sourceTree = "<group>";
		};
		AD92F10B2632C4A500C9544A /* Category */ = {
			isa = PBXGroup;
			children = (
				AD92F10E2632C4A500C9544A /* NSArray+UD.h */,
				AD92F1142632C4A500C9544A /* NSArray+UD.m */,
				AD92F10F2632C4A500C9544A /* NSDictionary+UD.h */,
				AD92F1132632C4A500C9544A /* NSDictionary+UD.m */,
				AD92F1102632C4A500C9544A /* NSNumber+UD.h */,
				AD92F1152632C4A500C9544A /* NSNumber+UD.m */,
				AD92F1122632C4A500C9544A /* NSObject+UD.h */,
				AD92F10C2632C4A500C9544A /* NSObject+UD.m */,
				AD92F1112632C4A500C9544A /* NSString+UD.h */,
				AD92F10D2632C4A500C9544A /* NSString+UD.m */,
			);
			path = Category;
			sourceTree = "<group>";
		};
		CE9B6D9E401DF3D4E0390A7D /* iOS */ = {
			isa = PBXGroup;
			children = (
				C3B22BE281A41A9A2B67A2CC /* WebKit.framework */,
				B5E2BFCAC9B6AB3C013BF2CD /* CoreMotion.framework */,
				52E71B0A3E6331C9E1C03E93 /* CoreTelephony.framework */,
				9CB172DB8237553ABC5D5DA7 /* SystemConfiguration.framework */,
				D571FB811C964A785935D95F /* MessageUI.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		E3541BAE0177991F3911D15F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				AD12D63226438B7F00188BC3 /* libupnetwork.a */,
				AD92EFC82632A1AA00C9544A /* libMJExtension.a */,
				AD92EFC62632A1A200C9544A /* libupnetwork.a */,
				AD5D45A024D2EEC800211C3F /* libupnetwork.a */,
				71AF42D824B5AF56007A8372 /* libresolv.tbd */,
				71AF42D624B5AF4D007A8372 /* libsqlite3.0.tbd */,
				71AF42CC24B5AF44007A8372 /* AlicloudBeacon.framework */,
				71AF42CE24B5AF44007A8372 /* AlicloudHttpDNS.framework */,
				71AF42D024B5AF44007A8372 /* AlicloudUtils.framework */,
				71AF42D224B5AF44007A8372 /* UTDID.framework */,
				71AF42D424B5AF44007A8372 /* UTMini.framework */,
				17C5270E6A526BD76942F08C /* libPods-upuserdomain.a */,
				8350DF3AA434AA3EE03764DA /* libPods-upuserdomainTests.a */,
				CE9B6D9E401DF3D4E0390A7D /* iOS */,
				85381D37955E48B54DB8EABE /* libPods-Debugger.a */,
				B9688A3885B290FC6C821B3F /* libPods-UserDomainAPIs.a */,
				9B86F7B4C8358BB49AA1BEAC /* libPods-UserDomain.a */,
				947E29A54A41CE9CFD62B7D4 /* libPods-UserDomainDataSource.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		F76DC4C6282BB35100E2EFF7 /* Common */ = {
			isa = PBXGroup;
			children = (
				F76DC4C7282BB35B00E2EFF7 /* ModelTransform */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		F76DC4C7282BB35B00E2EFF7 /* ModelTransform */ = {
			isa = PBXGroup;
			children = (
				F76DC4C8282BB6B800E2EFF7 /* FamilyRelatedTransform.h */,
				F76DC4C9282BB6B800E2EFF7 /* FamilyRelatedTransform.m */,
			);
			path = ModelTransform;
			sourceTree = "<group>";
		};
		F79092F0282A101100EE61D2 /* Family */ = {
			isa = PBXGroup;
			children = (
				F79092F9282A127900EE61D2 /* SEAddRoomToFamilyApi.h */,
				F79092FA282A127900EE61D2 /* SEAddRoomToFamilyApi.m */,
				F79092FD282A12A400EE61D2 /* SECreateFamilyApi.h */,
				F79092FE282A12A400EE61D2 /* SECreateFamilyApi.m */,
				F790930E282A30D100EE61D2 /* SEDestroyFamilyApi.h */,
				F790930F282A30D100EE61D2 /* SEDestroyFamilyApi.m */,
				F7909301282A12E000EE61D2 /* SEFamilyInfoApi.h */,
				F7909302282A12E000EE61D2 /* SEFamilyInfoApi.m */,
				F79092F1282A108900EE61D2 /* SEFamilyListApi.h */,
				F79092F2282A108900EE61D2 /* SEFamilyListApi.m */,
				F790931A282A316100EE61D2 /* SEFamilyMoveDevicesToNewRoomApi.h */,
				F790931B282A316100EE61D2 /* SEFamilyMoveDevicesToNewRoomApi.m */,
				F7909306282A302500EE61D2 /* SEFamilyRoomListApi.h */,
				F7909307282A302500EE61D2 /* SEFamilyRoomListApi.m */,
				F79092F5282A122900EE61D2 /* SERemoveRoomFromFamilyApi.h */,
				F79092F6282A122900EE61D2 /* SERemoveRoomFromFamilyApi.m */,
				F7909316282A312200EE61D2 /* SESetDefaultFamilyApi.h */,
				F7909317282A312200EE61D2 /* SESetDefaultFamilyApi.m */,
				F790930A282A309300EE61D2 /* SEUpdateFamilyInfoApi.h */,
				F790930B282A309300EE61D2 /* SEUpdateFamilyInfoApi.m */,
				F7909312282A30FB00EE61D2 /* SEUpdateFamilyRoomNameApi.h */,
				F7909313282A30FB00EE61D2 /* SEUpdateFamilyRoomNameApi.m */,
				F70AC2A52833ADD500EFF594 /* SEFamilyMoveDevicesToOtherFamilyApi.h */,
				F70AC2A62833ADD500EFF594 /* SEFamilyMoveDevicesToOtherFamilyApi.m */,
			);
			path = Family;
			sourceTree = "<group>";
		};
		F8CD119F24E384CF0021DD47 /* User */ = {
			isa = PBXGroup;
			children = (
				F8CD11A024E384EB0021DD47 /* UDCreateAddressOp.h */,
				F8CD11A124E384EB0021DD47 /* UDCreateAddressOp.m */,
				F805BA7B24E38ECA0056C526 /* UDDeleteAddressOp.h */,
				F805BA7C24E38ECA0056C526 /* UDDeleteAddressOp.m */,
				F805BA7F24E3C8FE0056C526 /* UPEditAddressOp.h */,
				F805BA8024E3C8FE0056C526 /* UPEditAddressOp.m */,
				F805BA8324E3D42B0056C526 /* UDModifyUserInfoOp.h */,
				F805BA8424E3D42B0056C526 /* UDModifyUserInfoOp.m */,
				F805BA8724E404430056C526 /* UDPollqrCodeStateOp.h */,
				F805BA8824E404430056C526 /* UDPollqrCodeStateOp.m */,
				F805BA8B24E405650056C526 /* UDQrCancleLoginOp.h */,
				F805BA8C24E405650056C526 /* UDQrCancleLoginOp.m */,
				F805BA8F24E406D80056C526 /* UDQrConfirmLoginOp.h */,
				F805BA9024E406D80056C526 /* UDQrConfirmLoginOp.m */,
				F805BA9324E406FA0056C526 /* UDQrConfirmScanOp.h */,
				F805BA9424E406FA0056C526 /* UDQrConfirmScanOp.m */,
				F805BA9724E40FF60056C526 /* UDQueryServiceOrderOp.h */,
				F805BA9824E40FF60056C526 /* UDQueryServiceOrderOp.m */,
				F805BA9B24E411A60056C526 /* UDQueryLoginLogsOp.h */,
				F805BA9C24E411A60056C526 /* UDQueryLoginLogsOp.m */,
				F805BA9F24E413360056C526 /* UDRefreshAddressListOp.h */,
				F805BAA024E413360056C526 /* UDRefreshAddressListOp.m */,
				F80B7B5F24E5256F0073FC48 /* UDRefreshTerminalListOp.h */,
				F80B7B6024E5256F0073FC48 /* UDRefreshTerminalListOp.m */,
				F80B7B6324E529AE0073FC48 /* UDRefreshUserInfoOp.h */,
				F80B7B6424E529AE0073FC48 /* UDRefreshUserInfoOp.m */,
				F80B7B6F24E53E080073FC48 /* UDUpdateAvatarOp.h */,
				F80B7B7024E53E080073FC48 /* UDUpdateAvatarOp.m */,
				95BEBB282D92D1E2001A07D7 /* UDModifyAggSwitchOp.h */,
				95BEBB292D92D1E2001A07D7 /* UDModifyAggSwitchOp.m */,
				F8AEC6862D7EF832008C92C5 /* UDConfirmDeviceSharingRelationOp.h */,
				F8AEC6872D7EF832008C92C5 /* UDConfirmDeviceSharingRelationOp.m */,
				F8AEC68A2D7EFB09008C92C5 /* UDCancelDeviceSharingRelationOp.h */,
				F8AEC68B2D7EFB09008C92C5 /* UDCancelDeviceSharingRelationOp.m */,
			);
			path = User;
			sourceTree = "<group>";
		};
		F8CEF1FA24CAB243008705B3 /* upuserdomain */ = {
			isa = PBXGroup;
			children = (
				F8CEF30324CAB2BE008705B3 /* Delegate */,
				F8CEF33524CAB2BF008705B3 /* IMP */,
				F8CEF1FC24CAB243008705B3 /* Info.plist */,
			);
			path = upuserdomain;
			sourceTree = "<group>";
		};
		F8CEF30324CAB2BE008705B3 /* Delegate */ = {
			isa = PBXGroup;
			children = (
				AD5D448E24D16D0B00211C3F /* Event */,
				F8CEF30424CAB2BE008705B3 /* Cache */,
				F8CEF30624CAB2BE008705B3 /* Family */,
				F8CEF30D24CAB2BE008705B3 /* User */,
				F8CEF31524CAB2BE008705B3 /* Time */,
				F8CEF31724CAB2BE008705B3 /* Device */,
				F8CEF31D24CAB2BE008705B3 /* OauthData */,
				F8CEF31F24CAB2BE008705B3 /* Callback */,
				F8CEF32124CAB2BE008705B3 /* Userdomain */,
			);
			path = Delegate;
			sourceTree = "<group>";
		};
		F8CEF30424CAB2BE008705B3 /* Cache */ = {
			isa = PBXGroup;
			children = (
				F8CEF30524CAB2BE008705B3 /* UDCache.h */,
				AD5D448F24D16D9200211C3F /* UpUserDomainCacheDelegate.h */,
			);
			path = Cache;
			sourceTree = "<group>";
		};
		F8CEF30624CAB2BE008705B3 /* Family */ = {
			isa = PBXGroup;
			children = (
				F8CEF30924CAB2BE008705B3 /* UDFamilyDelegate.h */,
				F8CEF30C24CAB2BE008705B3 /* UDFamilyInfoDelegate.h */,
				F8CEF30824CAB2BE008705B3 /* UDFamilyLocationDelegate.h */,
				F8CEF30A24CAB2BE008705B3 /* UDFamilyMemberDelegate.h */,
				F8CEF30B24CAB2BE008705B3 /* UDMemberInfoDelegate.h */,
				F8CEF30724CAB2BE008705B3 /* UDRoomDelegate.h */,
				F8E2BAB125775667007F3801 /* UDFloorInfoDelegate.h */,
				F81273C52579083400995F1E /* FloorArg.h */,
				F81273C62579083400995F1E /* FloorArg.m */,
			);
			path = Family;
			sourceTree = "<group>";
		};
		F8CEF30D24CAB2BE008705B3 /* User */ = {
			isa = PBXGroup;
			children = (
				F8CEF30E24CAB2BE008705B3 /* UDAddressDelegate.h */,
				F8CEF31124CAB2BE008705B3 /* UDLastLoginInfoDelegate.h */,
				F8CEF31324CAB2BE008705B3 /* UDUserAddressDelegate.h */,
				F8CEF31224CAB2BE008705B3 /* UDUserDelegate.h */,
				F8CEF31424CAB2BE008705B3 /* UDUserInfoDelegate.h */,
				F8CEF31024CAB2BE008705B3 /* UDUserLoginLogInfoDelegate.h */,
				F8CEF30F24CAB2BE008705B3 /* UDUserTermDelegate.h */,
			);
			path = User;
			sourceTree = "<group>";
		};
		F8CEF31524CAB2BE008705B3 /* Time */ = {
			isa = PBXGroup;
			children = (
				F8CEF31624CAB2BE008705B3 /* UPTimeMillisDelegate.h */,
			);
			path = Time;
			sourceTree = "<group>";
		};
		F8CEF31724CAB2BE008705B3 /* Device */ = {
			isa = PBXGroup;
			children = (
				F8CEF31A24CAB2BE008705B3 /* UDDeviceAuthDelegate.h */,
				F8CEF31C24CAB2BE008705B3 /* UDDeviceDelegate.h */,
				F8CEF31824CAB2BE008705B3 /* UDDeviceInfoDelegate.h */,
				F8CEF31924CAB2BE008705B3 /* UDDeviceOwnerInfoDelegate.h */,
				F8CEF31B24CAB2BE008705B3 /* UDDevicePermissionDelegate.h */,
				F8AEC69A2D802C91008C92C5 /* UDShareDeviceCardInfoDelegate.h */,
			);
			path = Device;
			sourceTree = "<group>";
		};
		F8CEF31D24CAB2BE008705B3 /* OauthData */ = {
			isa = PBXGroup;
			children = (
				F8CEF31E24CAB2BE008705B3 /* UDAuthDataDelegate.h */,
				F87750692589CDDF00D8A76D /* UDPlanRefreshTokenTaskDelegate.h */,
			);
			path = OauthData;
			sourceTree = "<group>";
		};
		F8CEF31F24CAB2BE008705B3 /* Callback */ = {
			isa = PBXGroup;
			children = (
				F8CEF32024CAB2BE008705B3 /* UpUserDomainResult.h */,
			);
			path = Callback;
			sourceTree = "<group>";
		};
		F8CEF32124CAB2BE008705B3 /* Userdomain */ = {
			isa = PBXGroup;
			children = (
				F8CEF32224CAB2BE008705B3 /* UpUserDomainDelegate.h */,
				F8CEF32324CAB2BE008705B3 /* UpUserDomainObserver.h */,
				F8CEF40624CACBF7008705B3 /* UpUserDomainProvider.h */,
				F82E589D24F35F7A0026E454 /* UDUserModelTransformer.h */,
			);
			path = Userdomain;
			sourceTree = "<group>";
		};
		F8CEF33524CAB2BF008705B3 /* IMP */ = {
			isa = PBXGroup;
			children = (
				AD92F1092632C44500C9544A /* CacheManager */,
				AD5D446124D10E2600211C3F /* Store */,
				AD5D443F24CFB4B000211C3F /* Event */,
				F8CEF41C24CACD32008705B3 /* Holders */,
				F8CEF33624CAB2BF008705B3 /* Cache */,
				F8CEF37F24CAB2BF008705B3 /* Callback */,
				F8CEF36C24CAB2BF008705B3 /* Device */,
				F8CEF33924CAB2BF008705B3 /* Family */,
				F8CEF37B24CAB2BF008705B3 /* OauthData */,
				F8CEF38524CAB2BF008705B3 /* Operator */,
				F8CEF36924CAB2BF008705B3 /* Time */,
				F8CEF35224CAB2BF008705B3 /* User */,
				F8CEF38224CAB2BF008705B3 /* Userdomain */,
			);
			path = IMP;
			sourceTree = "<group>";
		};
		F8CEF33624CAB2BF008705B3 /* Cache */ = {
			isa = PBXGroup;
			children = (
				F8CEF33824CAB2BF008705B3 /* UDCacheIMP.h */,
				F8CEF33724CAB2BF008705B3 /* UDCacheIMP.m */,
				AD5D45AE24D6D94800211C3F /* UDUserDomainCache.h */,
				AD5D45AF24D6D94800211C3F /* UDUserDomainCache.m */,
			);
			path = Cache;
			sourceTree = "<group>";
		};
		F8CEF33924CAB2BF008705B3 /* Family */ = {
			isa = PBXGroup;
			children = (
				F8FD3879253071EB00D4F9C3 /* BatchProcessDeviceResult.h */,
				F8FD387A253071EB00D4F9C3 /* BatchProcessDeviceResult.m */,
				F8CEF34924CAB2BF008705B3 /* CreateFamilyArgs.h */,
				F8CEF33B24CAB2BF008705B3 /* CreateFamilyArgs.m */,
				F8CEF34B24CAB2BF008705B3 /* Family.h */,
				F8CEF34024CAB2BF008705B3 /* Family.m */,
				F8CEF34124CAB2BF008705B3 /* Family+PrivateExtension.h */,
				F8CEF35124CAB2BF008705B3 /* FamilyArgs.h */,
				F8CEF34624CAB2BF008705B3 /* FamilyArgs.m */,
				F8CEF34C24CAB2BF008705B3 /* FamilyInfo.h */,
				F8CEF33F24CAB2BF008705B3 /* FamilyInfo.m */,
				F8CEF34E24CAB2BF008705B3 /* FamilyInfo+PrivateExtension.h */,
				F8CEF33C24CAB2BF008705B3 /* FamilyLocation.h */,
				F8CEF34824CAB2BF008705B3 /* FamilyLocation.m */,
				F8CEF34224CAB2BF008705B3 /* FamilyLocation+PrivateExtension.h */,
				F8CEF34324CAB2BF008705B3 /* FamilyMember.h */,
				F8CEF35024CAB2BF008705B3 /* FamilyMember.m */,
				F8CEF34724CAB2BF008705B3 /* FamilyMember+PrivateExtension.h */,
				F8CEF34F24CAB2BF008705B3 /* MemberInfo.h */,
				F8CEF34424CAB2BF008705B3 /* MemberInfo.m */,
				F8CEF33D24CAB2BF008705B3 /* MemberInfo+PrivateExtension.h */,
				F8CEF33E24CAB2BF008705B3 /* Room.h */,
				F8CEF34D24CAB2BF008705B3 /* Room.m */,
				F8CEF34524CAB2BF008705B3 /* Room+PrivateExtension.h */,
				F8CEF34A24CAB2BF008705B3 /* RoomArgs.h */,
				F8CEF33A24CAB2BF008705B3 /* RoomArgs.m */,
				955ED3F62DA7DDF500E10807 /* RoomNewArgs.h */,
				955ED3F72DA7DDF500E10807 /* RoomNewArgs.m */,
				2252139A28A20968001D2832 /* VirtualMemberArgs.h */,
				2252139B28A20968001D2832 /* VirtualMemberArgs.m */,
				F81273C925790D7200995F1E /* UDFloorInfo.h */,
				F81273CA25790D7200995F1E /* UDFloorInfo.m */,
				F81273CD25790DBF00995F1E /* UDFloorInfo+PrivateExtension.h */,
				9524B7722D9398A2009AA5C4 /* DeviceCardStatusArgs.h */,
				9524B7732D9398A2009AA5C4 /* DeviceCardStatusArgs.m */,
				9524B7762D93991D009AA5C4 /* DeviceCardAggregationArgs.h */,
				9524B7772D93991D009AA5C4 /* DeviceCardAggregationArgs.m */,
				95F07CA62DB255AB007CB875 /* AdminInviteMemberArgs.h */,
				95F07CA72DB255AB007CB875 /* AdminInviteMemberArgs.m */,
			);
			path = Family;
			sourceTree = "<group>";
		};
		F8CEF35224CAB2BF008705B3 /* User */ = {
			isa = PBXGroup;
			children = (
				F8CEF36124CAB2BF008705B3 /* User.h */,
				F8CEF35624CAB2BF008705B3 /* User.m */,
				F8CEF35E24CAB2BF008705B3 /* UserAddress.h */,
				F8CEF35424CAB2BF008705B3 /* UserAddress.m */,
				F8CEF36524CAB2BF008705B3 /* UserAddress+PrivateExtension.h */,
				F8CEF35F24CAB2BF008705B3 /* UserAddressInfo.h */,
				F8CEF35324CAB2BF008705B3 /* UserAddressInfo.m */,
				F8CEF40224CAC4F9008705B3 /* UserAddressArgs.h */,
				F8CEF40324CAC4F9008705B3 /* UserAddressArgs.m */,
				F8CEF35C24CAB2BF008705B3 /* UserAddressInfo+PrivateExtension.h */,
				F8CEF36624CAB2BF008705B3 /* UserInfo.h */,
				F8CEF35824CAB2BF008705B3 /* UserInfo.m */,
				F8CEF36324CAB2BF008705B3 /* UserInfo+PrivateExtension.h */,
				F8CEF36824CAB2BF008705B3 /* UserInfoArgs.h */,
				F8CEF35B24CAB2BF008705B3 /* UserInfoArgs.m */,
				F8CEF35924CAB2BF008705B3 /* UserLoginLogInfo.h */,
				F8CEF36424CAB2BF008705B3 /* UserLoginLogInfo.m */,
				F8CEF35524CAB2BF008705B3 /* UserLoginLogInfo+PrivateExtension.h */,
				F8CEF36724CAB2BF008705B3 /* UserTerminal.h */,
				F8CEF35D24CAB2BF008705B3 /* UserTerminal.m */,
				F8CEF35A24CAB2BF008705B3 /* UserTerminal+PrivateExtension.h */,
				F82E589F24F38F370026E454 /* UserQRLoginStage.h */,
				F82E58A024F38F370026E454 /* UserQRLoginStage.m */,
				9524B77A2D939A32009AA5C4 /* AggregationSwitchArgs.h */,
				9524B77B2D939A32009AA5C4 /* AggregationSwitchArgs.m */,
			);
			path = User;
			sourceTree = "<group>";
		};
		F8CEF36924CAB2BF008705B3 /* Time */ = {
			isa = PBXGroup;
			children = (
				F8CEF36A24CAB2BF008705B3 /* UDTimeIMP.h */,
				F8CEF36B24CAB2BF008705B3 /* UDTimeIMP.m */,
			);
			path = Time;
			sourceTree = "<group>";
		};
		F8CEF36C24CAB2BF008705B3 /* Device */ = {
			isa = PBXGroup;
			children = (
				F8CEF37124CAB2BF008705B3 /* Device.h */,
				F8CEF37824CAB2BF008705B3 /* Device.m */,
				F8CEF36E24CAB2BF008705B3 /* DeviceAuth.h */,
				F8CEF37624CAB2BF008705B3 /* DeviceAuth.m */,
				F8CEF37724CAB2BF008705B3 /* DeviceAuth+PrivateExtension.h */,
				F8CEF37324CAB2BF008705B3 /* UDDeviceInfo.h */,
				F8CEF37A24CAB2BF008705B3 /* UDDeviceInfo.m */,
				F8CEF36D24CAB2BF008705B3 /* UDDeviceInfo+PrivateExtension.h */,
				F8CEF37024CAB2BF008705B3 /* DeviceOwnerInfo.h */,
				F8CEF37524CAB2BF008705B3 /* DeviceOwnerInfo.m */,
				F8CEF36F24CAB2BF008705B3 /* DeviceOwnerInfo+PrivateExtension.h */,
				F8CEF37924CAB2BF008705B3 /* DevicePermission.h */,
				F8CEF37224CAB2BF008705B3 /* DevicePermission.m */,
				F8CEF37424CAB2BF008705B3 /* DevicePermission+PrivateExtension.h */,
				F8AEC69C2D8032BE008C92C5 /* DeviceShareDeviceCardInfo.h */,
				F8AEC69D2D8032BE008C92C5 /* DeviceShareDeviceCardInfo.m */,
				F8AEC6A02D80333F008C92C5 /* DeviceShareDeviceCardInfo+PrivateExtension.h */,
			);
			path = Device;
			sourceTree = "<group>";
		};
		F8CEF37B24CAB2BF008705B3 /* OauthData */ = {
			isa = PBXGroup;
			children = (
				F8CEF37E24CAB2BF008705B3 /* ApplicationOauthData.h */,
				F8CEF37C24CAB2BF008705B3 /* ApplicationOauthData.m */,
				F8CEF37D24CAB2BF008705B3 /* ApplicationOauthData+PrivateExtension.h */,
			);
			path = OauthData;
			sourceTree = "<group>";
		};
		F8CEF37F24CAB2BF008705B3 /* Callback */ = {
			isa = PBXGroup;
			children = (
				F8CEF38124CAB2BF008705B3 /* UserDomainSampleResult.h */,
				F8CEF38024CAB2BF008705B3 /* UserDomainSampleResult.m */,
			);
			path = Callback;
			sourceTree = "<group>";
		};
		F8CEF38224CAB2BF008705B3 /* Userdomain */ = {
			isa = PBXGroup;
			children = (
				F8CEF38424CAB2BF008705B3 /* UpUserDomain.h */,
				F8CEF38324CAB2BF008705B3 /* UpUserDomain.m */,
				AD5D45C224D6F23600211C3F /* UPUserDomainSettings.h */,
				AD5D45C324D6F23600211C3F /* UPUserDomainSettings.m */,
			);
			path = Userdomain;
			sourceTree = "<group>";
		};
		F8CEF38524CAB2BF008705B3 /* Operator */ = {
			isa = PBXGroup;
			children = (
				F8CD119F24E384CF0021DD47 /* User */,
				AD5D444B24CFC90B00211C3F /* Family */,
				AD5D444A24CFC8F800211C3F /* Device */,
				AD5D444924CFC8AB00211C3F /* Authdata */,
				AD5D444424CFBF7E00211C3F /* UDOperatorManager.h */,
				AD5D444524CFBF7E00211C3F /* UDOperatorManager.m */,
				F8CEF38624CAB2BF008705B3 /* UDOperator.h */,
				F8CEF38724CAB2BF008705B3 /* UDOperator.m */,
			);
			path = Operator;
			sourceTree = "<group>";
		};
		F8CEF41C24CACD32008705B3 /* Holders */ = {
			isa = PBXGroup;
			children = (
				F8CEF41D24CACD32008705B3 /* UpUserDomainHolder.h */,
				F8CEF42124CACD32008705B3 /* UpUserDomainHolder.m */,
			);
			path = Holders;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		AD12D6432643900A00188BC3 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F8AEC69B2D802C91008C92C5 /* UDShareDeviceCardInfoDelegate.h in Headers */,
				F8AEC69E2D8032BE008C92C5 /* DeviceShareDeviceCardInfo.h in Headers */,
				F8AEC6A32D80333F008C92C5 /* DeviceShareDeviceCardInfo+PrivateExtension.h in Headers */,
				AD12D6462643910500188BC3 /* UpUserDomainListenerDelegate.h in Headers */,
				AD12D6472643910500188BC3 /* UpEventHandlerDelegate.h in Headers */,
				AD12D6482643910500188BC3 /* UpEventHandlerManagerDelegate.h in Headers */,
				AD12D6492643910500188BC3 /* UDCache.h in Headers */,
				AD12D64A2643910500188BC3 /* UpUserDomainCacheDelegate.h in Headers */,
				AD12D64B2643910500188BC3 /* UDFamilyDelegate.h in Headers */,
				AD12D64C2643910500188BC3 /* UDFamilyInfoDelegate.h in Headers */,
				950119F52DA919E700F92F77 /* UDModifyMemberTypeOp.h in Headers */,
				AD12D64D2643910500188BC3 /* UDFamilyLocationDelegate.h in Headers */,
				AD12D64E2643910500188BC3 /* UDFamilyMemberDelegate.h in Headers */,
				AD12D64F2643910500188BC3 /* UDMemberInfoDelegate.h in Headers */,
				AD12D6502643910500188BC3 /* UDRoomDelegate.h in Headers */,
				AD12D6512643910500188BC3 /* UDFloorInfoDelegate.h in Headers */,
				AD12D6522643910500188BC3 /* FloorArg.h in Headers */,
				AD12D6532643910500188BC3 /* FloorArg.m in Headers */,
				AD12D6542643910500188BC3 /* UDAddressDelegate.h in Headers */,
				AD12D6552643910500188BC3 /* UDLastLoginInfoDelegate.h in Headers */,
				223223F128B5F54500A7A0E7 /* UDModifyVirtualMemberRoleOp.h in Headers */,
				AD12D6562643910500188BC3 /* UDUserAddressDelegate.h in Headers */,
				AD12D6572643910500188BC3 /* UDUserDelegate.h in Headers */,
				9524B7782D93991D009AA5C4 /* DeviceCardAggregationArgs.h in Headers */,
				AD12D6582643910500188BC3 /* UDUserInfoDelegate.h in Headers */,
				AD12D6592643910500188BC3 /* UDUserLoginLogInfoDelegate.h in Headers */,
				AD12D65A2643910500188BC3 /* UDUserTermDelegate.h in Headers */,
				AD12D65B2643910500188BC3 /* UPTimeMillisDelegate.h in Headers */,
				AD12D65C2643910500188BC3 /* UDDeviceAuthDelegate.h in Headers */,
				AD12D65D2643910500188BC3 /* UDDeviceDelegate.h in Headers */,
				AD12D65E2643910500188BC3 /* UDDeviceInfoDelegate.h in Headers */,
				AD12D65F2643910500188BC3 /* UDDeviceOwnerInfoDelegate.h in Headers */,
				AD12D6602643910500188BC3 /* UDDevicePermissionDelegate.h in Headers */,
				AD12D6612643910500188BC3 /* UDAuthDataDelegate.h in Headers */,
				AD12D6622643910500188BC3 /* UDPlanRefreshTokenTaskDelegate.h in Headers */,
				AD12D6632643910500188BC3 /* UpUserDomainResult.h in Headers */,
				AD12D6642643910500188BC3 /* UpUserDomainDelegate.h in Headers */,
				AD12D6652643910500188BC3 /* UpUserDomainObserver.h in Headers */,
				F8AEC6882D7EF832008C92C5 /* UDConfirmDeviceSharingRelationOp.h in Headers */,
				AD12D6662643910500188BC3 /* UpUserDomainProvider.h in Headers */,
				AD12D6672643910500188BC3 /* UDUserModelTransformer.h in Headers */,
				AD12D6682643910500188BC3 /* UDServerCacheManager.h in Headers */,
				AD12D6692643910500188BC3 /* UpUserDomainStore.h in Headers */,
				AD12D66A2643910500188BC3 /* UpUserStore.h in Headers */,
				AD12D66B2643910500188BC3 /* UpDeviceStore.h in Headers */,
				AD12D66C2643910500188BC3 /* UpFamilyStore.h in Headers */,
				AD12D66D2643910500188BC3 /* UpEvent.h in Headers */,
				AD12D66E2643910500188BC3 /* UpNotifyEventHandler.h in Headers */,
				AD12D66F2643910500188BC3 /* UpEventHandlerManager.h in Headers */,
				AD12D6702643910500188BC3 /* UDOpertateRefreshUserEventHandler.h in Headers */,
				AD12D6712643910500188BC3 /* UDOperatePlannedRefreshTokenEventHandler.h in Headers */,
				AD12D6722643910500188BC3 /* UDOperateRefreshTerminalListEventHandler.h in Headers */,
				AD12D6732643910500188BC3 /* UDOperateRefreshAddressListEventHandler.h in Headers */,
				AD12D6742643910500188BC3 /* UDOperateRefreshFamilyListEventHandler.h in Headers */,
				AD12D6762643910500188BC3 /* UDOperateRefreshFamilyDetailEventHandler.h in Headers */,
				AD12D6772643910500188BC3 /* UDOperateRefreshDeviceListEventHandler.h in Headers */,
				22EBEA382914A599007DA887 /* UDGetGroupDevicesOp.h in Headers */,
				AD12D6782643910500188BC3 /* UpListenerTransformObserver.h in Headers */,
				AD12D6792643910500188BC3 /* UpUserDomainHolder.h in Headers */,
				AD12D67A2643910500188BC3 /* UDCacheIMP.h in Headers */,
				AD12D67B2643910500188BC3 /* UDUserDomainCache.h in Headers */,
				AD12D67C2643910500188BC3 /* UserDomainSampleResult.h in Headers */,
				AD12D67D2643910500188BC3 /* Device.h in Headers */,
				AD12D67E2643910500188BC3 /* DeviceAuth.h in Headers */,
				AD12D67F2643910500188BC3 /* DeviceAuth+PrivateExtension.h in Headers */,
				AD12D6802643910500188BC3 /* UDDeviceInfo.h in Headers */,
				AD12D6812643910500188BC3 /* UDDeviceInfo+PrivateExtension.h in Headers */,
				2202C8612922130800955D81 /* UDSaveRoomsOrderOp.h in Headers */,
				AD12D6822643910500188BC3 /* DeviceOwnerInfo.h in Headers */,
				AD12D6832643910500188BC3 /* DeviceOwnerInfo+PrivateExtension.h in Headers */,
				22F6FDCD27965D3B0048F32F /* UDQueryFirstMemeberOp.h in Headers */,
				AD12D6842643910500188BC3 /* DevicePermission.h in Headers */,
				AD12D6852643910500188BC3 /* DevicePermission+PrivateExtension.h in Headers */,
				AD12D6862643910500188BC3 /* BatchProcessDeviceResult.h in Headers */,
				AD12D6872643910500188BC3 /* CreateFamilyArgs.h in Headers */,
				AD12D6882643910500188BC3 /* Family.h in Headers */,
				AD12D6892643910500188BC3 /* Family+PrivateExtension.h in Headers */,
				AD12D68A2643910500188BC3 /* FamilyArgs.h in Headers */,
				AD12D68B2643910500188BC3 /* FamilyInfo.h in Headers */,
				AD12D68C2643910500188BC3 /* FamilyInfo+PrivateExtension.h in Headers */,
				AD12D68D2643910500188BC3 /* FamilyLocation.h in Headers */,
				AD12D68E2643910500188BC3 /* FamilyLocation+PrivateExtension.h in Headers */,
				AD12D68F2643910500188BC3 /* FamilyMember.h in Headers */,
				AD12D6902643910500188BC3 /* FamilyMember+PrivateExtension.h in Headers */,
				F8AEC68D2D7EFB09008C92C5 /* UDCancelDeviceSharingRelationOp.h in Headers */,
				AD12D6912643910500188BC3 /* MemberInfo.h in Headers */,
				AD12D6922643910500188BC3 /* MemberInfo+PrivateExtension.h in Headers */,
				AD12D6932643910500188BC3 /* Room.h in Headers */,
				AD12D6942643910500188BC3 /* Room+PrivateExtension.h in Headers */,
				AD12D6952643910500188BC3 /* RoomArgs.h in Headers */,
				AD12D6962643910500188BC3 /* UDFloorInfo.h in Headers */,
				AD12D6972643910500188BC3 /* UDFloorInfo+PrivateExtension.h in Headers */,
				AD12D6982643910500188BC3 /* ApplicationOauthData.h in Headers */,
				AD12D6992643910500188BC3 /* ApplicationOauthData+PrivateExtension.h in Headers */,
				AD12D69A2643910500188BC3 /* UDCreateAddressOp.h in Headers */,
				AD12D69B2643910500188BC3 /* UDDeleteAddressOp.h in Headers */,
				AD12D69C2643910500188BC3 /* UPEditAddressOp.h in Headers */,
				AD12D69D2643910500188BC3 /* UDModifyUserInfoOp.h in Headers */,
				AD12D69E2643910500188BC3 /* UDPollqrCodeStateOp.h in Headers */,
				AD12D69F2643910500188BC3 /* UDQrCancleLoginOp.h in Headers */,
				AD12D6A02643910500188BC3 /* UDQrConfirmLoginOp.h in Headers */,
				AD12D6A12643910500188BC3 /* UDQrConfirmScanOp.h in Headers */,
				AD12D6A22643910500188BC3 /* UDQueryServiceOrderOp.h in Headers */,
				AD12D6A32643910500188BC3 /* UDQueryLoginLogsOp.h in Headers */,
				84B5D4B12ABAED2400C07E3F /* UDModifyDeviceNameNewOp.h in Headers */,
				95BEBB2A2D92D1E2001A07D7 /* UDModifyAggSwitchOp.h in Headers */,
				95EC2CAE2DB621D400E22D0E /* UDSaveRoomsOrderNewOp.h in Headers */,
				AD12D6A42643910500188BC3 /* UDRefreshAddressListOp.h in Headers */,
				AD12D6A52643910500188BC3 /* UDRefreshTerminalListOp.h in Headers */,
				AD12D6A62643910500188BC3 /* UDRefreshUserInfoOp.h in Headers */,
				AD12D6A72643910500188BC3 /* UDUpdateAvatarOp.h in Headers */,
				AD12D6A92643910500188BC3 /* UDChangeFamilyAdminOp.h in Headers */,
				AD12D6AA2643910500188BC3 /* UDRefreshRoomListOp.h in Headers */,
				9524B77C2D939A32009AA5C4 /* AggregationSwitchArgs.h in Headers */,
				AD12D6AB2643910500188BC3 /* UDCreateFamilyOp.h in Headers */,
				AD12D6AC2643910500188BC3 /* UDCreateRoomOp.h in Headers */,
				AD12D6AD2643910500188BC3 /* UDDeleteFamilyAsAdminOp.h in Headers */,
				AD12D6AE2643910500188BC3 /* UDDeleteRoomOp.h in Headers */,
				AD12D6AF2643910500188BC3 /* UDEditFamilyInfoOp.h in Headers */,
				AD12D6B02643910500188BC3 /* UDEditRoomNameOp.h in Headers */,
				AD12D6B12643910500188BC3 /* UDExitFamilyAsAdminOp.h in Headers */,
				955ED3F82DA7DDF500E10807 /* RoomNewArgs.h in Headers */,
				AD12D6B22643910500188BC3 /* UDExitFamilyAsMemberOp.h in Headers */,
				AD12D6B32643910500188BC3 /* UDDeleteMemberOp.h in Headers */,
				AD12D6B42643910500188BC3 /* UDInviteFamilyMemberOp.h in Headers */,
				AD12D6B52643910500188BC3 /* UDUnbindDevicesOp.h in Headers */,
				AD12D6B62643910500188BC3 /* UDMoveDevicesToFamilyOp.h in Headers */,
				AD12D6B72643910500188BC3 /* UDMoveDevicesToRoomOp.h in Headers */,
				AD12D6B82643910500188BC3 /* UDDeleteDevicesOp.h in Headers */,
				AD12D6B92643910500188BC3 /* UDRefreshFamilyListOp.h in Headers */,
				AD12D6BA2643910500188BC3 /* UDReplyFamilyInviteOp.h in Headers */,
				AD12D6BB2643910500188BC3 /* UDReplyJoinFamilyOp.h in Headers */,
				AD12D6BC2643910500188BC3 /* UDSetCurrentFamilyOp.h in Headers */,
				AD12D6BD2643910500188BC3 /* UDUpdateFamilyDetailOp.h in Headers */,
				AD12D6BE2643910500188BC3 /* UDRefreshAllFamilyDetailOp.h in Headers */,
				AD12D6BF2643910500188BC3 /* UDAddFloorOp.h in Headers */,
				AD12D6C02643910500188BC3 /* UDEditFloorOp.h in Headers */,
				AD12D6C12643910500188BC3 /* UDDeleteFloorOp.h in Headers */,
				AD12D6C22643910500188BC3 /* UDModifyDeviceNameOp.h in Headers */,
				AD12D6C32643910500188BC3 /* UDRefreshDeviceListOp.h in Headers */,
				AD12D6C42643910500188BC3 /* UDRefreshTokenOp.h in Headers */,
				955ED3FC2DA7E06F00E10807 /* UDCreateRoomNewOp.h in Headers */,
				95BEBB262D92CFEE001A07D7 /* UDModifyDeviceAggOp.h in Headers */,
				95BEBB222D92CCF8001A07D7 /* UDModifyDeviceCardListOp.h in Headers */,
				AD12D6C52643910500188BC3 /* UDLogoutOp.h in Headers */,
				AD12D6C62643910500188BC3 /* UDPlannedRefreshAuthDataOp.h in Headers */,
				9524B7742D9398A2009AA5C4 /* DeviceCardStatusArgs.h in Headers */,
				AD12D6C82643910500188BC3 /* UDRefreshUserOp.h in Headers */,
				AD12D6C92643910600188BC3 /* UDOperatorManager.h in Headers */,
				AD12D6CA2643910600188BC3 /* UDOperator.h in Headers */,
				AD12D6CB2643910600188BC3 /* UDTimeIMP.h in Headers */,
				95F07CA82DB255AB007CB875 /* AdminInviteMemberArgs.h in Headers */,
				AD12D6CE2643910600188BC3 /* User.h in Headers */,
				AD12D6CF2643910600188BC3 /* UserAddress.h in Headers */,
				AD12D6D02643910600188BC3 /* UserAddress+PrivateExtension.h in Headers */,
				AD12D6D12643910600188BC3 /* UserAddressInfo.h in Headers */,
				AD12D6D22643910600188BC3 /* UserAddressArgs.h in Headers */,
				22F6FDD527965D820048F32F /* UDModifyVirtualMemberOp.h in Headers */,
				2252139428A1F1F0001D2832 /* UDModifyMemberRoleOp.h in Headers */,
				AD12D6D32643910600188BC3 /* UserAddressInfo+PrivateExtension.h in Headers */,
				AD12D6D42643910600188BC3 /* UserInfo.h in Headers */,
				AD12D6D52643910600188BC3 /* UserInfo+PrivateExtension.h in Headers */,
				AD12D6D62643910600188BC3 /* UserInfoArgs.h in Headers */,
				AD12D6D72643910600188BC3 /* UserLoginLogInfo.h in Headers */,
				AD12D6D82643910600188BC3 /* UserLoginLogInfo+PrivateExtension.h in Headers */,
				AD12D6D92643910600188BC3 /* UserTerminal.h in Headers */,
				AD12D6DA2643910600188BC3 /* UserTerminal+PrivateExtension.h in Headers */,
				2252139C28A20968001D2832 /* VirtualMemberArgs.h in Headers */,
				AD12D6DC2643910600188BC3 /* UserQRLoginStage.h in Headers */,
				22F6FDD127965D610048F32F /* UDAddVirtualMemberOp.h in Headers */,
				AD12D6DD2643910600188BC3 /* UpUserDomain.h in Headers */,
				AD12D6DE2643910600188BC3 /* UPUserDomainSettings.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD12D7522643936500188BC3 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F8AEC6992D80287E008C92C5 /* CloudShareDeviceCardInfo.h in Headers */,
				F76DC4CA282BB6B800E2EFF7 /* FamilyRelatedTransform.h in Headers */,
				AD12D753264394A800188BC3 /* NSArray+UD.h in Headers */,
				AD12D754264394A800188BC3 /* NSDictionary+UD.h in Headers */,
				AD12D755264394A800188BC3 /* NSNumber+UD.h in Headers */,
				AD12D756264394A800188BC3 /* NSObject+UD.h in Headers */,
				AD12D757264394A800188BC3 /* NSString+UD.h in Headers */,
				AD12D758264394A800188BC3 /* UpFamilyDataSource.h in Headers */,
				AD12D759264394A800188BC3 /* UpUserDataSource.h in Headers */,
				AD12D75A264394A800188BC3 /* UpDeviceListDataSource.h in Headers */,
				AD12D75B264394A800188BC3 /* CloudBatchProcessDevicesResponse.h in Headers */,
				AD12D75C264394A800188BC3 /* CloudDevice.h in Headers */,
				AD12D75D264394A800188BC3 /* CloudDeviceAuth.h in Headers */,
				AD12D75E264394A800188BC3 /* CloudDeviceBaseInfo.h in Headers */,
				AD12D75F264394A800188BC3 /* CloudDeviceExtendedInfo.h in Headers */,
				AD12D760264394A800188BC3 /* CloudDeviceList.h in Headers */,
				221AA340277EA1A0008143F4 /* ShopUserDataSource.h in Headers */,
				AD12D761264394A800188BC3 /* CloudDeviceOwnerInfo.h in Headers */,
				22F6FDD92796A54B0048F32F /* QueryFirstMemebeTransformer.h in Headers */,
				AD12D762264394A800188BC3 /* CloudDevicePermission.h in Headers */,
				AD12D763264394A800188BC3 /* CloudQRLoginStage.h in Headers */,
				AD12D764264394A800188BC3 /* CloudLoginTerminal.h in Headers */,
				AD12D765264394A800188BC3 /* CloudOauthData.h in Headers */,
				AD12D766264394A800188BC3 /* CloudUserAddressInfo.h in Headers */,
				221AA34F277ED317008143F4 /* ShopOauthDataTransformer.h in Headers */,
				AD12D767264394A800188BC3 /* CloudUserInfo.h in Headers */,
				AD12D768264394A800188BC3 /* CloudUserLoginLogInfo.h in Headers */,
				AD12D76A264394A800188BC3 /* CloudFamily.h in Headers */,
				AD12D76B264394A800188BC3 /* CloudFamilyFirstMember.h in Headers */,
				F7909330282B81EE00EE61D2 /* SECreateFamilyTransformer.h in Headers */,
				F7909334282B836900EE61D2 /* SEFamilyRoomTransformer.h in Headers */,
				AD12D76C264394A800188BC3 /* CloudFamilyList.h in Headers */,
				AD12D76D264394A800188BC3 /* CloudFamilyLocation.h in Headers */,
				AD12D76E264394A800188BC3 /* CloudFamilyMember.h in Headers */,
				AD12D76F264394A800188BC3 /* CloudFamilyMemberInfo.h in Headers */,
				AD12D770264394A800188BC3 /* CloudFloorInfo.h in Headers */,
				AD12D771264394A800188BC3 /* CloudRoom.h in Headers */,
				AD12D772264394A800188BC3 /* AddRoomTransformer.h in Headers */,
				AD12D773264394A800188BC3 /* BatchProcessDevicesOfFamilyTransformer.h in Headers */,
				AD12D774264394A800188BC3 /* CommonTransformer.h in Headers */,
				AD12D775264394A800188BC3 /* CreateFamilyTransformer.h in Headers */,
				AD12D776264394A800188BC3 /* DeviceListTransformer.h in Headers */,
				AD12D777264394A800188BC3 /* FamilyFirstMemberTransformer.h in Headers */,
				AD12D778264394A800188BC3 /* FamilyInfoTransformer.h in Headers */,
				AD12D779264394A800188BC3 /* FamilyListTransformer.h in Headers */,
				AD12D77A264394A800188BC3 /* FamilyRoomTransformer.h in Headers */,
				AD12D77B264394A800188BC3 /* UpUserServiceOrderTransformer.h in Headers */,
				AD12D77C264394A800188BC3 /* UPZJOauthDataTransformer.h in Headers */,
				225BA55C27F19B3D0076B9ED /* CommonDataTransformer.h in Headers */,
				AD12D77D264394A800188BC3 /* UserAddressTransformer.h in Headers */,
				AD12D77E264394A800188BC3 /* UserInfoTransformer.h in Headers */,
				F7909328282B650300EE61D2 /* SEFamilyInfoTransformer.h in Headers */,
				AD12D77F264394A800188BC3 /* UserLoginLogsTransformer.h in Headers */,
				AD12D781264394A800188BC3 /* UserLoginTerminalTransformer.h in Headers */,
				AD12D782264394A800188BC3 /* UserOauthDataTransformer.h in Headers */,
				AD12D783264394A800188BC3 /* UserQrLoginPollTransformer.h in Headers */,
				AD12D784264394A800188BC3 /* DataSourceCallbackFunctions.h in Headers */,
				F790932C282B816200EE61D2 /* SEAddRoomTransformer.h in Headers */,
				AD12D785264394A800188BC3 /* DeviceListDataSource.h in Headers */,
				AD12D786264394A800188BC3 /* FamilyDataSource.h in Headers */,
				AD12D787264394A800188BC3 /* UserDataSource.h in Headers */,
				AD12D788264394A800188BC3 /* SEDeviceListDataSource.h in Headers */,
				AD12D789264394A800188BC3 /* SEFamilyDataSource.h in Headers */,
				AD12D78A264394A800188BC3 /* SEUserDataSource.h in Headers */,
				AD12D78B264394A800188BC3 /* SEBaseConverter.h in Headers */,
				AD12D78C264394A800188BC3 /* SEDeviceInfoListConverter.h in Headers */,
				AD12D78D264394A800188BC3 /* SEFamilyListConverter.h in Headers */,
				AD12D78E264394A800188BC3 /* SEModifyDeviceConverter.h in Headers */,
				F7909338282B846C00EE61D2 /* SEBatchProcessDevicesOfFamilyTransformer.h in Headers */,
				22B3B32328DD51C7007EF767 /* SEUnbindDeviceTransformer.h in Headers */,
				AD12D78F264394A800188BC3 /* SEUserAuthInfoConverter.h in Headers */,
				AD12D790264394A800188BC3 /* SEUserInfoConverter.h in Headers */,
				AD12D791264394A800188BC3 /* UserDomainDataSource.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		ADB50D122643893A00062881 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				ADB50D132643894800062881 /* SEUserInfoAPI.h in Headers */,
				ADB50D142643894800062881 /* SEUserLogoutAPI.h in Headers */,
				95EC2CAA2DB61D3D00E22D0E /* UPSaveRoomsOrderNewApi.h in Headers */,
				ADB50D152643894800062881 /* SEUserModifyAPI.h in Headers */,
				95BEBB162D92951E001A07D7 /* UpEditDeviceCardStatusApi.h in Headers */,
				ADB50D162643894800062881 /* SEUserRefreshTokenAPI.h in Headers */,
				ADB50D172643896100062881 /* SEEncryption.h in Headers */,
				ADB50D182643896100062881 /* SERequestBase.h in Headers */,
				ADB50D1A2643899A00062881 /* SECloudDeviceInfo.h in Headers */,
				ADB50D1B2643899A00062881 /* SECloudUserAuthInfo.h in Headers */,
				F7909308282A302500EE61D2 /* SEFamilyRoomListApi.h in Headers */,
				22F6FDC127953D490048F32F /* UPQueryFirstMemeberApi.h in Headers */,
				F79092FF282A12A400EE61D2 /* SECreateFamilyApi.h in Headers */,
				ADB50D1C2643899A00062881 /* SECloudUserInfo.h in Headers */,
				ADB50D1D2643899A00062881 /* SEModifyDeviceInfoAPI.h in Headers */,
				22F6FDC927953DB20048F32F /* UPModifyVirtualMemberApi.h in Headers */,
				ADB50D1E2643899A00062881 /* SEQueryDeviceListAPI.h in Headers */,
				F790930C282A309300EE61D2 /* SEUpdateFamilyInfoApi.h in Headers */,
				ADB50D1F2643899A00062881 /* SEUnbindDeviceAPI.h in Headers */,
				ADB50D202643899A00062881 /* UPAddRoomToFamilyApi.h in Headers */,
				F7909314282A30FB00EE61D2 /* SEUpdateFamilyRoomNameApi.h in Headers */,
				221AA34B277ED064008143F4 /* ShopServerAPIBase.h in Headers */,
				ADB50D212643899A00062881 /* UPChangeFamilyAdminApi.h in Headers */,
				ADB50D222643899A00062881 /* UPCreateFamilyApi.h in Headers */,
				ADB50D232643899A00062881 /* UPdateFamilyInfoApi.h in Headers */,
				ADB50D242643899A00062881 /* UPDestroyFamilyApi.h in Headers */,
				221087D927705929002D3A6B /* SECloudDevicePermission.h in Headers */,
				ADB50D252643899A00062881 /* UPFamilyDeleteMemberApi.h in Headers */,
				ADB50D262643899A00062881 /* UPFamilyInfoApi.h in Headers */,
				ADB50D272643899A00062881 /* UPFamilyInviteMemberApi.h in Headers */,
				ADB50D282643899A00062881 /* UPFamilyListApi.h in Headers */,
				ADB50D292643899A00062881 /* UPFamilyMemberShareDeviceCountApi.h in Headers */,
				ADB50D2A2643899A00062881 /* UPFamilyMoveDevicesToNewRoomApi.h in Headers */,
				F8AEC6942D7F0E99008C92C5 /* UPCancelDeviceSharingRelationApi.h in Headers */,
				221AA346277EA6F4008143F4 /* ShopRefreshTokenApi.h in Headers */,
				ADB50D2B2643899A00062881 /* UPFamilyMoveDevicesToOtherFamilyApi.h in Headers */,
				ADB50D2C2643899A00062881 /* UPFamilyRemoveDevicesApi.h in Headers */,
				95BEBB1A2D9299DA001A07D7 /* UpEditDeviceAggregationApi.h in Headers */,
				ADB50D2D2643899A00062881 /* UPFamilyReplyApi.h in Headers */,
				ADB50D2E2643899A00062881 /* UPReplyJoinFamilyApi.h in Headers */,
				ADB50D2F2643899A00062881 /* UPFamilyRoomListApi.h in Headers */,
				ADB50D302643899A00062881 /* UPFamilyUnbindDevicesApi.h in Headers */,
				F79092FB282A127900EE61D2 /* SEAddRoomToFamilyApi.h in Headers */,
				ADB50D312643899A00062881 /* UPQuitFamilyAsAdminApi.h in Headers */,
				ADB50D322643899A00062881 /* UPQuitFamilyAsMemberApi.h in Headers */,
				ADB50D332643899A00062881 /* UPRemoveRoomFromFamilyApi.h in Headers */,
				221087DD27705971002D3A6B /* SECloudDeviceAuth.h in Headers */,
				ADB50D342643899A00062881 /* UPSetDefaultFamilyApi.h in Headers */,
				22EBEA3C2914A91A007DA887 /* UPGetGroupDevicesApi.h in Headers */,
				ADB50D352643899A00062881 /* UPUpdateFamilyRoomNameApi.h in Headers */,
				ADB50D362643899A00062881 /* UPAddFloorApi.h in Headers */,
				223223ED28B5F36B00A7A0E7 /* UPModifyVirtualMemberRoleApi.h in Headers */,
				ADB50D372643899A00062881 /* UPEditFloorApi.h in Headers */,
				ADB50D382643899A00062881 /* UPDeleteFloorApi.h in Headers */,
				22BF11B627C5CFC000EB43F5 /* ShopLogoutApi.h in Headers */,
				ADB50D39264389C900062881 /* UPUserAccountVerifyApi.h in Headers */,
				334AFE1C267343C0003F443B /* SYNServerAPIBase.h in Headers */,
				ADB50D3A264389C900062881 /* UPUserAddressApi.h in Headers */,
				ADB50D3B264389C900062881 /* UPUserApplitionTokenApi.h in Headers */,
				F7909318282A312200EE61D2 /* SESetDefaultFamilyApi.h in Headers */,
				ADB50D3C264389C900062881 /* UPUserCreateNewAddressApi.h in Headers */,
				ADB50D3D264389C900062881 /* UPUserDeleteAddressApi.h in Headers */,
				2252139828A1F4F3001D2832 /* UPModifyMemberRoleApi.h in Headers */,
				F79092F3282A108900EE61D2 /* SEFamilyListApi.h in Headers */,
				ADB50D3E264389C900062881 /* UPUserEditAddressApi.h in Headers */,
				F7909310282A30D100EE61D2 /* SEDestroyFamilyApi.h in Headers */,
				ADB50D3F264389C900062881 /* UPUserInfoApi.h in Headers */,
				ADB50D40264389C900062881 /* UPUserLoginLogsApi.h in Headers */,
				22F6FDC527953D810048F32F /* UPAddVirtualMemberApi.h in Headers */,
				F70AC2A72833ADD500EFF594 /* SEFamilyMoveDevicesToOtherFamilyApi.h in Headers */,
				950119F12DA9189900F92F77 /* UPModifyMemberTypeApi.h in Headers */,
				ADB50D42264389C900062881 /* UPUserLoginTerminalApi.h in Headers */,
				ADB50D43264389C900062881 /* UPUserQRCancleLoginApi.h in Headers */,
				ADB50D44264389C900062881 /* UPUserQRConfirmLoginApi.h in Headers */,
				95BEBB1E2D929EF7001A07D7 /* UpEditAggregationSwitchApi.h in Headers */,
				ADB50D45264389C900062881 /* UPUserQRloginPollApi.h in Headers */,
				ADB50D46264389C900062881 /* UPUserQRScanApi.h in Headers */,
				ADB50D47264389C900062881 /* UPUserRefreshTokenApi.h in Headers */,
				ADB50D48264389C900062881 /* UPUserServiceOrderApi.h in Headers */,
				ADB50D49264389C900062881 /* UPUserUpdatePwdApi.h in Headers */,
				2202C8652922156800955D81 /* UPSaveRoomsOrderApi.h in Headers */,
				ADB50D4A264389C900062881 /* UPUserUpdateUserInfoApi.h in Headers */,
				ADB50D4B264389C900062881 /* UPUserUploadAvatarApi.h in Headers */,
				F79092F7282A122900EE61D2 /* SERemoveRoomFromFamilyApi.h in Headers */,
				ADB50D4C264389CA00062881 /* UPZJRefreshTokenApi.h in Headers */,
				84B5D4AD2ABAE35F00C07E3F /* UPDeviceUpdateAndCheckNameApi.h in Headers */,
				ADB50D4D264389CA00062881 /* UPZJUserInfoApi.h in Headers */,
				ADB50D4E264389CA00062881 /* UPZJUserLogOutApi.h in Headers */,
				AD12D5E326438A5F00188BC3 /* APPServerAPIBase.h in Headers */,
				AD12D5E426438A5F00188BC3 /* ZJServerAPIBase.h in Headers */,
				AD12D5E526438A5F00188BC3 /* UserCenterAPIBase.h in Headers */,
				955ED3F52DA7DBD300E10807 /* UpAddRoomToFamilyNewApi.h in Headers */,
				AD12D5E626438A5F00188BC3 /* UPDeviceListApi.h in Headers */,
				AD12D5E726438A5F00188BC3 /* UPDeviceUpdateNameApi.h in Headers */,
				AD12D5E826438A5F00188BC3 /* UPUpdateDeviceInfoApi.h in Headers */,
				AD12D5E926438A5F00188BC3 /* UserDomainAPIs.h in Headers */,
				ADB50D192643896100062881 /* SERequestConfig.h in Headers */,
				F790931C282A316100EE61D2 /* SEFamilyMoveDevicesToNewRoomApi.h in Headers */,
				F8AEC6902D7EFD76008C92C5 /* UPConfirmDeviceSharingRelationApi.h in Headers */,
				F7909303282A12E000EE61D2 /* SEFamilyInfoApi.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		71ECF93F241A2C0D009BBAD0 /* Debugger */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 71ECF958241A2C0D009BBAD0 /* Build configuration list for PBXNativeTarget "Debugger" */;
			buildPhases = (
				D89AB4EFEB3D9A7870B24C9E /* [CP] Check Pods Manifest.lock */,
				71ECF93C241A2C0D009BBAD0 /* Sources */,
				71ECF93D241A2C0D009BBAD0 /* Frameworks */,
				71ECF93E241A2C0D009BBAD0 /* Resources */,
				4C27CA7E4F1DC4A716F4ADEC /* [CP] Copy Pods Resources */,
				AD92EDE42632997500C9544A /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				AD8DD1F92643F23D00086CBF /* PBXTargetDependency */,
			);
			name = Debugger;
			productName = Debugger;
			productReference = 71ECF940241A2C0D009BBAD0 /* Debugger.app */;
			productType = "com.apple.product-type.application";
		};
		72321F942255DE1F005F6A86 /* upuserdomainTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 72321FAC2255DE1F005F6A86 /* Build configuration list for PBXNativeTarget "upuserdomainTests" */;
			buildPhases = (
				137C7BEF49733333221BC79B /* [CP] Check Pods Manifest.lock */,
				72321F912255DE1F005F6A86 /* Sources */,
				72321F922255DE1F005F6A86 /* Frameworks */,
				72321F932255DE1F005F6A86 /* Resources */,
				13CD3AFEDB09FC18FF2006FD /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AD8DD1FB2643F24200086CBF /* PBXTargetDependency */,
			);
			name = upuserdomainTests;
			productName = upuserdomainTests;
			productReference = 72321F952255DE1F005F6A86 /* upuserdomainTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		AD12D63726438F9F00188BC3 /* upuserdomain */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AD12D63E26438F9F00188BC3 /* Build configuration list for PBXNativeTarget "upuserdomain" */;
			buildPhases = (
				A92EE8C589D67217279360BE /* [CP] Check Pods Manifest.lock */,
				AD12D6432643900A00188BC3 /* Headers */,
				AD12D63426438F9F00188BC3 /* Sources */,
				AD12D63526438F9F00188BC3 /* Frameworks */,
				AD12D63626438F9F00188BC3 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
				AD8DD1F72643F23300086CBF /* PBXTargetDependency */,
				AD8DD1F52643F12900086CBF /* PBXTargetDependency */,
			);
			name = upuserdomain;
			productName = upuserdomain;
			productReference = AD12D63826438F9F00188BC3 /* libupuserdomain.a */;
			productType = "com.apple.product-type.library.static";
		};
		AD12D7482643933E00188BC3 /* UserDomainDataSource */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AD12D74F2643933E00188BC3 /* Build configuration list for PBXNativeTarget "UserDomainDataSource" */;
			buildPhases = (
				81B34B023C400BD365EB747B /* [CP] Check Pods Manifest.lock */,
				AD12D7522643936500188BC3 /* Headers */,
				AD12D7452643933E00188BC3 /* Sources */,
				AD12D7462643933E00188BC3 /* Frameworks */,
				AD12D7472643933E00188BC3 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UserDomainDataSource;
			productName = UserDomainDataSource;
			productReference = AD12D7492643933E00188BC3 /* libUserDomainDataSource.a */;
			productType = "com.apple.product-type.library.static";
		};
		ADB50C7E2643848F00062881 /* UserDomainAPIs */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = ADB50C852643848F00062881 /* Build configuration list for PBXNativeTarget "UserDomainAPIs" */;
			buildPhases = (
				398F37C66869EA37605C27C2 /* [CP] Check Pods Manifest.lock */,
				ADB50D122643893A00062881 /* Headers */,
				ADB50C7B2643848F00062881 /* Sources */,
				ADB50C7C2643848F00062881 /* Frameworks */,
				ADB50C7D2643848F00062881 /* CopyFiles */,
				AD12D62C26438AC700188BC3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UserDomainAPIs;
			productName = UserDomainAPIs;
			productReference = ADB50C7F2643848F00062881 /* libUserDomainAPIs.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		72321F752255DE1E005F6A86 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				DefaultBuildSystemTypeForWorkspace = Original;
				LastUpgradeCheck = 1010;
				ORGANIZATIONNAME = "海尔优家智能科技（北京）有限公司";
				TargetAttributes = {
					71ECF93F241A2C0D009BBAD0 = {
						CreatedOnToolsVersion = 11.0;
					};
					72321F942255DE1F005F6A86 = {
						CreatedOnToolsVersion = 10.1;
					};
					AD12D63726438F9F00188BC3 = {
						CreatedOnToolsVersion = 12.3;
					};
					AD12D7482643933E00188BC3 = {
						CreatedOnToolsVersion = 12.3;
					};
					ADB50C7E2643848F00062881 = {
						CreatedOnToolsVersion = 12.3;
					};
				};
			};
			buildConfigurationList = 72321F782255DE1E005F6A86 /* Build configuration list for PBXProject "upuserdomain" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 72321F742255DE1E005F6A86;
			productRefGroup = 72321F7E2255DE1E005F6A86 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				72321F942255DE1F005F6A86 /* upuserdomainTests */,
				71ECF93F241A2C0D009BBAD0 /* Debugger */,
				ADB50C7E2643848F00062881 /* UserDomainAPIs */,
				AD12D63726438F9F00188BC3 /* upuserdomain */,
				AD12D7482643933E00188BC3 /* UserDomainDataSource */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		71ECF93E241A2C0D009BBAD0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				71ECF952241A2C0D009BBAD0 /* LaunchScreen.storyboard in Resources */,
				AD81E86B24F637E6002430CA /* Address.plist in Resources */,
				71ECF94F241A2C0D009BBAD0 /* Assets.xcassets in Resources */,
				ADCBD89924FE3BFD00BC30AC /* city-v3.0.json in Resources */,
				71ECF94D241A2C0D009BBAD0 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		72321F932255DE1F005F6A86 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22037985270FE9D000FB8EB2 /* features in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD12D62C26438AC700188BC3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD12D62D26438ACD00188BC3 /* readme.md in Resources */,
				AD12D62E26438ACD00188BC3 /* release.md in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		137C7BEF49733333221BC79B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-upuserdomainTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		13CD3AFEDB09FC18FF2006FD /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-upuserdomainTests/Pods-upuserdomainTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-upuserdomainTests/Pods-upuserdomainTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-upuserdomainTests/Pods-upuserdomainTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		398F37C66869EA37605C27C2 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UserDomainAPIs-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		4C27CA7E4F1DC4A716F4ADEC /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Debugger/Pods-Debugger-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Debugger/Pods-Debugger-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Debugger/Pods-Debugger-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		81B34B023C400BD365EB747B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UserDomainDataSource-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		A92EE8C589D67217279360BE /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-upuserdomain-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D89AB4EFEB3D9A7870B24C9E /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Debugger-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		71ECF93C241A2C0D009BBAD0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7155373D24F5F9FA00BAA7D7 /* UserInfoEditViewController.m in Sources */,
				7155374324F6467600BAA7D7 /* SEUserLoginApi.m in Sources */,
				71035FE524FC918A00A9D036 /* FamilySelectionViewController.m in Sources */,
				AD7AF97624F7AB960074F36F /* FamilyMemberViewController.m in Sources */,
				AD81E85B24F4A3DD002430CA /* FamilyListViewController.m in Sources */,
				AD7AF97F24F8AEF20074F36F /* DeviceActionView.m in Sources */,
				ADCBD89424FCA61500BC30AC /* RoomPickerView.m in Sources */,
				AD81E87124F75B40002430CA /* FamilyOwnerViewController.m in Sources */,
				71035FDF24FC858100A9D036 /* DeviceListViewController.m in Sources */,
				71ECF94A241A2C0D009BBAD0 /* ViewController.m in Sources */,
				AD7AF97C24F7E48D0074F36F /* CreatFamilyViewController.m in Sources */,
				71B381EE24F4B43900F04E48 /* UserDetailViewController.m in Sources */,
				7155374024F5FFF800BAA7D7 /* LoginSettings.m in Sources */,
				71B381EB24F4AF7C00F04E48 /* FunctionsTableViewController.m in Sources */,
				AD7AF97924F7D8720074F36F /* CommonEditCell.m in Sources */,
				ADCBD89C24FE52F700BC30AC /* AddressViewController.m in Sources */,
				71ECF944241A2C0D009BBAD0 /* AppDelegate.m in Sources */,
				AD81E86924F63522002430CA /* AddressPickView.m in Sources */,
				ADCBD89124F9072C00BC30AC /* FamilyPickerView.m in Sources */,
				ADF38BB224DC1C5900551FF7 /* UPUserLoginApi.m in Sources */,
				71035FE224FC8AD800A9D036 /* DeviceDetailViewController.m in Sources */,
				AD81E86E24F64DDD002430CA /* FamilyDevicesViewController.m in Sources */,
				F854517B24F50C0F007F3114 /* UPUploadRecordApis.m in Sources */,
				AD81E85E24F4DEA9002430CA /* FamilyDetailViewController.m in Sources */,
				ADCBD89724FE1A1600BC30AC /* RoomDevicesViewController.m in Sources */,
				71ECF955241A2C0D009BBAD0 /* main.m in Sources */,
				71B045BB24F34ED0001833D0 /* LoginViewController.m in Sources */,
				AD81E86324F60E4A002430CA /* MyFamilyController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		72321F912255DE1F005F6A86 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F8CEF1C624C7E411008705B3 /* SignleLoginSteps.m in Sources */,
				F8CEF1B724C7E37A008705B3 /* CacheSteps.m in Sources */,
				F8CEF1C324C7E3EC008705B3 /* RefreshTokenSteps.m in Sources */,
				F8CEF1C024C7E3BE008705B3 /* EventHandleSteps.m in Sources */,
				F8CEF1AE24C7E347008705B3 /* UserSteps.m in Sources */,
				F8CEF1B424C7E366008705B3 /* DeviceSteps.m in Sources */,
				F8CEF18424C7D686008705B3 /* UserdomainHolderTest.m in Sources */,
				F8CEF18D24C7D6FD008705B3 /* InitializationSteps.m in Sources */,
				F8CEF1BD24C7E3AF008705B3 /* TimeSteps.m in Sources */,
				F8CEF1B124C7E359008705B3 /* FamilySteps.m in Sources */,
				72E530E822643E09008992A1 /* CucumberRunner.m in Sources */,
				F8CEF18A24C7D6C8008705B3 /* StepsUtils.m in Sources */,
				F899FCE924ECBCAF00F34E56 /* UserdomainSteps.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD12D63426438F9F00188BC3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD12D6DF264391FC00188BC3 /* FloorArg.m in Sources */,
				AD12D6E0264391FC00188BC3 /* UDServerCacheManager.m in Sources */,
				AD12D6E1264391FC00188BC3 /* UpUserDomainStore.m in Sources */,
				AD12D6E2264391FC00188BC3 /* UpUserStore.m in Sources */,
				AD12D6E3264391FC00188BC3 /* UpDeviceStore.m in Sources */,
				AD12D6E4264391FC00188BC3 /* UpFamilyStore.m in Sources */,
				AD12D6E5264391FC00188BC3 /* UpEvent.m in Sources */,
				AD12D6E6264391FC00188BC3 /* UpNotifyEventHandler.m in Sources */,
				AD12D6E7264391FC00188BC3 /* UpEventHandlerManager.m in Sources */,
				AD12D6E8264391FC00188BC3 /* UDOpertateRefreshUserEventHandler.m in Sources */,
				9524B7752D9398A2009AA5C4 /* DeviceCardStatusArgs.m in Sources */,
				AD12D6E9264391FC00188BC3 /* UDOperatePlannedRefreshTokenEventHandler.m in Sources */,
				AD12D6EA264391FC00188BC3 /* UDOperateRefreshTerminalListEventHandler.m in Sources */,
				AD12D6EB264391FC00188BC3 /* UDOperateRefreshAddressListEventHandler.m in Sources */,
				223223F228B5F54500A7A0E7 /* UDModifyVirtualMemberRoleOp.m in Sources */,
				AD12D6EC264391FC00188BC3 /* UDOperateRefreshFamilyListEventHandler.m in Sources */,
				AD12D6EE264391FC00188BC3 /* UDOperateRefreshFamilyDetailEventHandler.m in Sources */,
				AD12D6EF264391FC00188BC3 /* UDOperateRefreshDeviceListEventHandler.m in Sources */,
				AD12D6F0264391FC00188BC3 /* UpListenerTransformObserver.m in Sources */,
				AD12D6F1264391FC00188BC3 /* UpUserDomainHolder.m in Sources */,
				AD12D6F2264391FC00188BC3 /* UDCacheIMP.m in Sources */,
				AD12D6F3264391FC00188BC3 /* UDUserDomainCache.m in Sources */,
				AD12D6F4264391FC00188BC3 /* UserDomainSampleResult.m in Sources */,
				AD12D6F5264391FC00188BC3 /* Device.m in Sources */,
				AD12D6F6264391FC00188BC3 /* DeviceAuth.m in Sources */,
				AD12D6F7264391FC00188BC3 /* UDDeviceInfo.m in Sources */,
				AD12D6F8264391FC00188BC3 /* DeviceOwnerInfo.m in Sources */,
				AD12D6F9264391FC00188BC3 /* DevicePermission.m in Sources */,
				AD12D6FA264391FC00188BC3 /* BatchProcessDeviceResult.m in Sources */,
				95BEBB232D92CCF8001A07D7 /* UDModifyDeviceCardListOp.m in Sources */,
				F8AEC69F2D8032BE008C92C5 /* DeviceShareDeviceCardInfo.m in Sources */,
				AD12D6FB264391FC00188BC3 /* CreateFamilyArgs.m in Sources */,
				AD12D6FC264391FC00188BC3 /* Family.m in Sources */,
				AD12D6FD264391FC00188BC3 /* FamilyArgs.m in Sources */,
				AD12D6FE264391FC00188BC3 /* FamilyInfo.m in Sources */,
				AD12D6FF264391FC00188BC3 /* FamilyLocation.m in Sources */,
				22F6FDCE27965D3B0048F32F /* UDQueryFirstMemeberOp.m in Sources */,
				AD12D700264391FC00188BC3 /* FamilyMember.m in Sources */,
				AD12D701264391FC00188BC3 /* MemberInfo.m in Sources */,
				AD12D702264391FC00188BC3 /* Room.m in Sources */,
				22F6FDD627965D820048F32F /* UDModifyVirtualMemberOp.m in Sources */,
				AD12D703264391FC00188BC3 /* RoomArgs.m in Sources */,
				AD12D704264391FC00188BC3 /* UDFloorInfo.m in Sources */,
				AD12D705264391FC00188BC3 /* ApplicationOauthData.m in Sources */,
				955ED3F92DA7DDF500E10807 /* RoomNewArgs.m in Sources */,
				AD12D706264391FC00188BC3 /* UDCreateAddressOp.m in Sources */,
				9524B7792D93991D009AA5C4 /* DeviceCardAggregationArgs.m in Sources */,
				AD12D707264391FC00188BC3 /* UDDeleteAddressOp.m in Sources */,
				AD12D708264391FC00188BC3 /* UPEditAddressOp.m in Sources */,
				AD12D709264391FC00188BC3 /* UDModifyUserInfoOp.m in Sources */,
				AD12D70A264391FC00188BC3 /* UDPollqrCodeStateOp.m in Sources */,
				AD12D70B264391FC00188BC3 /* UDQrCancleLoginOp.m in Sources */,
				AD12D70C264391FC00188BC3 /* UDQrConfirmLoginOp.m in Sources */,
				AD12D70D264391FC00188BC3 /* UDQrConfirmScanOp.m in Sources */,
				AD12D70E264391FC00188BC3 /* UDQueryServiceOrderOp.m in Sources */,
				AD12D70F264391FC00188BC3 /* UDQueryLoginLogsOp.m in Sources */,
				AD12D710264391FC00188BC3 /* UDRefreshAddressListOp.m in Sources */,
				AD12D711264391FC00188BC3 /* UDRefreshTerminalListOp.m in Sources */,
				AD12D712264391FC00188BC3 /* UDRefreshUserInfoOp.m in Sources */,
				AD12D713264391FC00188BC3 /* UDUpdateAvatarOp.m in Sources */,
				95BEBB2B2D92D1E2001A07D7 /* UDModifyAggSwitchOp.m in Sources */,
				AD12D715264391FC00188BC3 /* UDChangeFamilyAdminOp.m in Sources */,
				F8AEC68C2D7EFB09008C92C5 /* UDCancelDeviceSharingRelationOp.m in Sources */,
				AD12D716264391FC00188BC3 /* UDRefreshRoomListOp.m in Sources */,
				2252139D28A20968001D2832 /* VirtualMemberArgs.m in Sources */,
				95BEBB272D92CFEE001A07D7 /* UDModifyDeviceAggOp.m in Sources */,
				AD12D717264391FC00188BC3 /* UDCreateFamilyOp.m in Sources */,
				AD12D718264391FC00188BC3 /* UDCreateRoomOp.m in Sources */,
				AD12D719264391FC00188BC3 /* UDDeleteFamilyAsAdminOp.m in Sources */,
				AD12D71A264391FC00188BC3 /* UDDeleteRoomOp.m in Sources */,
				2202C8622922130800955D81 /* UDSaveRoomsOrderOp.m in Sources */,
				AD12D71B264391FC00188BC3 /* UDEditFamilyInfoOp.m in Sources */,
				AD12D71C264391FC00188BC3 /* UDEditRoomNameOp.m in Sources */,
				F8AEC6892D7EF832008C92C5 /* UDConfirmDeviceSharingRelationOp.m in Sources */,
				AD12D71D264391FC00188BC3 /* UDExitFamilyAsAdminOp.m in Sources */,
				AD12D71E264391FC00188BC3 /* UDExitFamilyAsMemberOp.m in Sources */,
				AD12D71F264391FC00188BC3 /* UDDeleteMemberOp.m in Sources */,
				AD12D720264391FC00188BC3 /* UDInviteFamilyMemberOp.m in Sources */,
				AD12D721264391FC00188BC3 /* UDUnbindDevicesOp.m in Sources */,
				AD12D722264391FC00188BC3 /* UDMoveDevicesToFamilyOp.m in Sources */,
				AD12D723264391FC00188BC3 /* UDMoveDevicesToRoomOp.m in Sources */,
				AD12D724264391FC00188BC3 /* UDDeleteDevicesOp.m in Sources */,
				955ED3FD2DA7E06F00E10807 /* UDCreateRoomNewOp.m in Sources */,
				AD12D725264391FC00188BC3 /* UDRefreshFamilyListOp.m in Sources */,
				AD12D726264391FC00188BC3 /* UDReplyFamilyInviteOp.m in Sources */,
				AD12D727264391FC00188BC3 /* UDReplyJoinFamilyOp.m in Sources */,
				AD12D728264391FC00188BC3 /* UDSetCurrentFamilyOp.m in Sources */,
				AD12D729264391FC00188BC3 /* UDUpdateFamilyDetailOp.m in Sources */,
				AD12D72A264391FC00188BC3 /* UDRefreshAllFamilyDetailOp.m in Sources */,
				950119F62DA919E700F92F77 /* UDModifyMemberTypeOp.m in Sources */,
				AD12D72B264391FC00188BC3 /* UDAddFloorOp.m in Sources */,
				AD12D72C264391FC00188BC3 /* UDEditFloorOp.m in Sources */,
				AD12D72D264391FC00188BC3 /* UDDeleteFloorOp.m in Sources */,
				AD12D72E264391FC00188BC3 /* UDModifyDeviceNameOp.m in Sources */,
				AD12D72F264391FC00188BC3 /* UDRefreshDeviceListOp.m in Sources */,
				AD12D730264391FC00188BC3 /* UDRefreshTokenOp.m in Sources */,
				AD12D731264391FC00188BC3 /* UDLogoutOp.m in Sources */,
				AD12D732264391FC00188BC3 /* UDPlannedRefreshAuthDataOp.m in Sources */,
				AD12D734264391FC00188BC3 /* UDRefreshUserOp.m in Sources */,
				AD12D735264391FC00188BC3 /* UDOperatorManager.m in Sources */,
				95F07CA92DB255AB007CB875 /* AdminInviteMemberArgs.m in Sources */,
				AD12D736264391FC00188BC3 /* UDOperator.m in Sources */,
				AD12D737264391FC00188BC3 /* UDTimeIMP.m in Sources */,
				95EC2CAF2DB621D400E22D0E /* UDSaveRoomsOrderNewOp.m in Sources */,
				AD12D739264391FC00188BC3 /* User.m in Sources */,
				AD12D73A264391FC00188BC3 /* UserAddress.m in Sources */,
				AD12D73B264391FC00188BC3 /* UserAddressInfo.m in Sources */,
				84B5D4B22ABAED2400C07E3F /* UDModifyDeviceNameNewOp.m in Sources */,
				2252139528A1F1F0001D2832 /* UDModifyMemberRoleOp.m in Sources */,
				AD12D73C264391FC00188BC3 /* UserAddressArgs.m in Sources */,
				22EBEA392914A599007DA887 /* UDGetGroupDevicesOp.m in Sources */,
				AD12D73D264391FC00188BC3 /* UserInfo.m in Sources */,
				AD12D73E264391FC00188BC3 /* UserInfoArgs.m in Sources */,
				9524B77D2D939A32009AA5C4 /* AggregationSwitchArgs.m in Sources */,
				22F6FDD227965D610048F32F /* UDAddVirtualMemberOp.m in Sources */,
				AD12D73F264391FC00188BC3 /* UserLoginLogInfo.m in Sources */,
				AD12D740264391FC00188BC3 /* UserTerminal.m in Sources */,
				AD12D742264391FC00188BC3 /* UserQRLoginStage.m in Sources */,
				AD12D743264391FC00188BC3 /* UpUserDomain.m in Sources */,
				AD12D744264391FC00188BC3 /* UPUserDomainSettings.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD12D7452643933E00188BC3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD12D7922643958400188BC3 /* NSArray+UD.m in Sources */,
				F7909329282B650300EE61D2 /* SEFamilyInfoTransformer.m in Sources */,
				AD12D7932643958400188BC3 /* NSDictionary+UD.m in Sources */,
				AD12D7942643958400188BC3 /* NSNumber+UD.m in Sources */,
				AD12D7952643958400188BC3 /* NSObject+UD.m in Sources */,
				22F6FDDA2796A54B0048F32F /* QueryFirstMemebeTransformer.m in Sources */,
				AD12D7962643958400188BC3 /* NSString+UD.m in Sources */,
				AD12D7972643958400188BC3 /* CloudBatchProcessDevicesResponse.m in Sources */,
				AD12D7982643958400188BC3 /* CloudDevice.m in Sources */,
				AD12D7992643958400188BC3 /* CloudDeviceAuth.m in Sources */,
				AD12D79A2643958400188BC3 /* CloudDeviceBaseInfo.m in Sources */,
				AD12D79B2643958400188BC3 /* CloudDeviceExtendedInfo.m in Sources */,
				AD12D79C2643958400188BC3 /* CloudDeviceList.m in Sources */,
				AD12D79D2643958400188BC3 /* CloudDeviceOwnerInfo.m in Sources */,
				AD12D79E2643958400188BC3 /* CloudDevicePermission.m in Sources */,
				AD12D79F2643958400188BC3 /* CloudQRLoginStage.m in Sources */,
				221AA350277ED317008143F4 /* ShopOauthDataTransformer.m in Sources */,
				AD12D7A02643958400188BC3 /* CloudLoginTerminal.m in Sources */,
				F76DC4CB282BB6B800E2EFF7 /* FamilyRelatedTransform.m in Sources */,
				AD12D7A12643958400188BC3 /* CloudOauthData.m in Sources */,
				AD12D7A22643958400188BC3 /* CloudUserAddressInfo.m in Sources */,
				AD12D7A32643958400188BC3 /* CloudUserInfo.m in Sources */,
				AD12D7A42643958400188BC3 /* CloudUserLoginLogInfo.m in Sources */,
				AD12D7A62643958400188BC3 /* CloudFamily.m in Sources */,
				F8AEC6982D80287E008C92C5 /* CloudShareDeviceCardInfo.m in Sources */,
				F7909331282B81EE00EE61D2 /* SECreateFamilyTransformer.m in Sources */,
				AD12D7A72643958400188BC3 /* CloudFamilyFirstMember.m in Sources */,
				F7909335282B836900EE61D2 /* SEFamilyRoomTransformer.m in Sources */,
				AD12D7A82643958400188BC3 /* CloudFamilyList.m in Sources */,
				AD12D7A92643958400188BC3 /* CloudFamilyLocation.m in Sources */,
				AD12D7AA2643958400188BC3 /* CloudFamilyMember.m in Sources */,
				AD12D7AB2643958400188BC3 /* CloudFamilyMemberInfo.m in Sources */,
				AD12D7AC2643958400188BC3 /* CloudFloorInfo.m in Sources */,
				AD12D7AD2643958400188BC3 /* CloudRoom.m in Sources */,
				AD12D7AE2643958400188BC3 /* AddRoomTransformer.m in Sources */,
				AD12D7AF2643958400188BC3 /* BatchProcessDevicesOfFamilyTransformer.m in Sources */,
				AD12D7B02643958400188BC3 /* CommonTransformer.m in Sources */,
				AD12D7B12643958400188BC3 /* CreateFamilyTransformer.m in Sources */,
				AD12D7B22643958400188BC3 /* DeviceListTransformer.m in Sources */,
				AD12D7B32643958400188BC3 /* FamilyFirstMemberTransformer.m in Sources */,
				225BA55D27F19B3D0076B9ED /* CommonDataTransformer.m in Sources */,
				AD12D7B42643958400188BC3 /* FamilyInfoTransformer.m in Sources */,
				AD12D7B52643958400188BC3 /* FamilyListTransformer.m in Sources */,
				AD12D7B62643958400188BC3 /* FamilyRoomTransformer.m in Sources */,
				AD12D7B72643958400188BC3 /* UpUserServiceOrderTransformer.m in Sources */,
				AD12D7B82643958400188BC3 /* UPZJOauthDataTransformer.m in Sources */,
				F790932D282B816200EE61D2 /* SEAddRoomTransformer.m in Sources */,
				AD12D7B92643958400188BC3 /* UserAddressTransformer.m in Sources */,
				AD12D7BA2643958400188BC3 /* UserInfoTransformer.m in Sources */,
				AD12D7BB2643958400188BC3 /* UserLoginLogsTransformer.m in Sources */,
				AD12D7BD2643958400188BC3 /* UserLoginTerminalTransformer.m in Sources */,
				AD12D7BE2643958400188BC3 /* UserOauthDataTransformer.m in Sources */,
				F7909339282B846C00EE61D2 /* SEBatchProcessDevicesOfFamilyTransformer.m in Sources */,
				22B3B32428DD51C7007EF767 /* SEUnbindDeviceTransformer.m in Sources */,
				AD12D7BF2643958400188BC3 /* UserQrLoginPollTransformer.m in Sources */,
				AD12D7C02643958400188BC3 /* DataSourceCallbackFunctions.m in Sources */,
				AD12D7C12643958400188BC3 /* DeviceListDataSource.m in Sources */,
				AD12D7C22643958400188BC3 /* FamilyDataSource.m in Sources */,
				AD12D7C32643958400188BC3 /* UserDataSource.m in Sources */,
				AD12D7C42643958400188BC3 /* SEDeviceListDataSource.m in Sources */,
				AD12D7C52643958400188BC3 /* SEFamilyDataSource.m in Sources */,
				AD12D7C62643958400188BC3 /* SEUserDataSource.m in Sources */,
				AD12D7C72643958400188BC3 /* SEBaseConverter.m in Sources */,
				AD12D7C82643958400188BC3 /* SEDeviceInfoListConverter.m in Sources */,
				AD12D7C92643958400188BC3 /* SEFamilyListConverter.m in Sources */,
				AD12D7CA2643958400188BC3 /* SEModifyDeviceConverter.m in Sources */,
				AD12D7CB2643958400188BC3 /* SEUserAuthInfoConverter.m in Sources */,
				AD12D7CC2643958400188BC3 /* SEUserInfoConverter.m in Sources */,
				221AA341277EA1A0008143F4 /* ShopUserDataSource.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		ADB50C7B2643848F00062881 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD12D5EA26438AB800188BC3 /* SEUserInfoAPI.m in Sources */,
				AD12D5EB26438AB800188BC3 /* SEUserLogoutAPI.m in Sources */,
				AD12D5EC26438AB800188BC3 /* SEUserModifyAPI.m in Sources */,
				AD12D5ED26438AB800188BC3 /* SEUserRefreshTokenAPI.m in Sources */,
				AD12D5EE26438AB800188BC3 /* SEEncryption.m in Sources */,
				22F6FDC227953D490048F32F /* UPQueryFirstMemeberApi.m in Sources */,
				95BEBB1F2D929EF7001A07D7 /* UpEditAggregationSwitchApi.m in Sources */,
				AD12D5EF26438AB800188BC3 /* SERequestBase.m in Sources */,
				AD12D5F026438AB800188BC3 /* SERequestConfig.m in Sources */,
				F790931D282A316100EE61D2 /* SEFamilyMoveDevicesToNewRoomApi.m in Sources */,
				AD12D5F126438AB800188BC3 /* SECloudDeviceInfo.m in Sources */,
				955ED3F42DA7DBD300E10807 /* UpAddRoomToFamilyNewApi.m in Sources */,
				2202C8662922156800955D81 /* UPSaveRoomsOrderApi.m in Sources */,
				AD12D5F226438AB800188BC3 /* SECloudUserAuthInfo.m in Sources */,
				AD12D5F326438AB800188BC3 /* SECloudUserInfo.m in Sources */,
				F8AEC6952D7F0E99008C92C5 /* UPCancelDeviceSharingRelationApi.m in Sources */,
				AD12D5F426438AB800188BC3 /* SEModifyDeviceInfoAPI.m in Sources */,
				F7909304282A12E000EE61D2 /* SEFamilyInfoApi.m in Sources */,
				AD12D5F526438AB800188BC3 /* SEQueryDeviceListAPI.m in Sources */,
				95BEBB1B2D9299DA001A07D7 /* UpEditDeviceAggregationApi.m in Sources */,
				F70AC2A82833ADD500EFF594 /* SEFamilyMoveDevicesToOtherFamilyApi.m in Sources */,
				AD12D5F626438AB800188BC3 /* SEUnbindDeviceAPI.m in Sources */,
				AD12D5F726438AB800188BC3 /* UPAddRoomToFamilyApi.m in Sources */,
				AD12D5F826438AB800188BC3 /* UPChangeFamilyAdminApi.m in Sources */,
				AD12D5F926438AB800188BC3 /* UPCreateFamilyApi.m in Sources */,
				AD12D5FA26438AB800188BC3 /* UPdateFamilyInfoApi.m in Sources */,
				AD12D5FB26438AB800188BC3 /* UPDestroyFamilyApi.m in Sources */,
				AD12D5FC26438AB800188BC3 /* UPFamilyDeleteMemberApi.m in Sources */,
				AD12D5FD26438AB800188BC3 /* UPFamilyInfoApi.m in Sources */,
				AD12D5FE26438AB800188BC3 /* UPFamilyInviteMemberApi.m in Sources */,
				AD12D5FF26438AB800188BC3 /* UPFamilyListApi.m in Sources */,
				AD12D60026438AB800188BC3 /* UPFamilyMemberShareDeviceCountApi.m in Sources */,
				22F6FDCA27953DB20048F32F /* UPModifyVirtualMemberApi.m in Sources */,
				AD12D60126438AB800188BC3 /* UPFamilyMoveDevicesToNewRoomApi.m in Sources */,
				AD12D60226438AB800188BC3 /* UPFamilyMoveDevicesToOtherFamilyApi.m in Sources */,
				AD12D60326438AB800188BC3 /* UPFamilyRemoveDevicesApi.m in Sources */,
				AD12D60426438AB800188BC3 /* UPFamilyReplyApi.m in Sources */,
				AD12D60526438AB800188BC3 /* UPReplyJoinFamilyApi.m in Sources */,
				AD12D60626438AB800188BC3 /* UPFamilyRoomListApi.m in Sources */,
				AD12D60726438AB800188BC3 /* UPFamilyUnbindDevicesApi.m in Sources */,
				AD12D60826438AB800188BC3 /* UPQuitFamilyAsAdminApi.m in Sources */,
				AD12D60926438AB800188BC3 /* UPQuitFamilyAsMemberApi.m in Sources */,
				84B5D4AE2ABAE35F00C07E3F /* UPDeviceUpdateAndCheckNameApi.m in Sources */,
				AD12D60A26438AB800188BC3 /* UPRemoveRoomFromFamilyApi.m in Sources */,
				F7909315282A30FB00EE61D2 /* SEUpdateFamilyRoomNameApi.m in Sources */,
				************************ /* UPSetDefaultFamilyApi.m in Sources */,
				************************ /* UPUpdateFamilyRoomNameApi.m in Sources */,
				************************ /* UPAddFloorApi.m in Sources */,
				22F6FDC627953D810048F32F /* UPAddVirtualMemberApi.m in Sources */,
				************************ /* UPEditFloorApi.m in Sources */,
				************************ /* UPDeleteFloorApi.m in Sources */,
				************************ /* UPUserAccountVerifyApi.m in Sources */,
				F7909309282A302500EE61D2 /* SEFamilyRoomListApi.m in Sources */,
				221087DA27705929002D3A6B /* SECloudDevicePermission.m in Sources */,
				F79092F8282A122900EE61D2 /* SERemoveRoomFromFamilyApi.m in Sources */,
				************************ /* UPUserAddressApi.m in Sources */,
				************************ /* UPUserApplitionTokenApi.m in Sources */,
				************************ /* UPUserCreateNewAddressApi.m in Sources */,
				************************ /* UPUserDeleteAddressApi.m in Sources */,
				95BEBB172D92951E001A07D7 /* UpEditDeviceCardStatusApi.m in Sources */,
				F79092F4282A108900EE61D2 /* SEFamilyListApi.m in Sources */,
				F7909300282A12A400EE61D2 /* SECreateFamilyApi.m in Sources */,
				22EBEA3D2914A91A007DA887 /* UPGetGroupDevicesApi.m in Sources */,
				************************ /* UPUserEditAddressApi.m in Sources */,
				221AA347277EA6F4008143F4 /* ShopRefreshTokenApi.m in Sources */,
				22BF11B527C5CFC000EB43F5 /* ShopLogoutApi.m in Sources */,
				************************ /* UPUserInfoApi.m in Sources */,
				************************ /* UPUserLoginLogsApi.m in Sources */,
				F7909319282A312200EE61D2 /* SESetDefaultFamilyApi.m in Sources */,
				AD12D61926438AB800188BC3 /* UPUserLoginTerminalApi.m in Sources */,
				AD12D61A26438AB800188BC3 /* UPUserQRCancleLoginApi.m in Sources */,
				AD12D61B26438AB800188BC3 /* UPUserQRConfirmLoginApi.m in Sources */,
				AD12D61C26438AB800188BC3 /* UPUserQRloginPollApi.m in Sources */,
				F79092FC282A127900EE61D2 /* SEAddRoomToFamilyApi.m in Sources */,
				AD12D61D26438AB800188BC3 /* UPUserQRScanApi.m in Sources */,
				AD12D61E26438AB800188BC3 /* UPUserRefreshTokenApi.m in Sources */,
				AD12D61F26438AB800188BC3 /* UPUserServiceOrderApi.m in Sources */,
				AD12D62026438AB800188BC3 /* UPUserUpdatePwdApi.m in Sources */,
				221087DE27705971002D3A6B /* SECloudDeviceAuth.m in Sources */,
				AD12D62126438AB800188BC3 /* UPUserUpdateUserInfoApi.m in Sources */,
				334AFE1D267343C0003F443B /* SYNServerAPIBase.m in Sources */,
				950119F22DA9189900F92F77 /* UPModifyMemberTypeApi.m in Sources */,
				F790930D282A309300EE61D2 /* SEUpdateFamilyInfoApi.m in Sources */,
				AD12D62226438AB800188BC3 /* UPUserUploadAvatarApi.m in Sources */,
				AD12D62326438AB800188BC3 /* UPZJRefreshTokenApi.m in Sources */,
				AD12D62426438AB800188BC3 /* UPZJUserInfoApi.m in Sources */,
				95EC2CAB2DB61D3D00E22D0E /* UPSaveRoomsOrderNewApi.m in Sources */,
				2252139928A1F4F3001D2832 /* UPModifyMemberRoleApi.m in Sources */,
				223223EE28B5F36B00A7A0E7 /* UPModifyVirtualMemberRoleApi.m in Sources */,
				AD12D62526438AB800188BC3 /* UPZJUserLogOutApi.m in Sources */,
				AD12D62626438AB800188BC3 /* APPServerAPIBase.m in Sources */,
				AD12D62726438AB800188BC3 /* ZJServerAPIBase.m in Sources */,
				AD12D62826438AB800188BC3 /* UserCenterAPIBase.m in Sources */,
				AD12D62926438AB800188BC3 /* UPDeviceListApi.m in Sources */,
				F7909311282A30D100EE61D2 /* SEDestroyFamilyApi.m in Sources */,
				AD12D62A26438AB800188BC3 /* UPDeviceUpdateNameApi.m in Sources */,
				F8AEC6912D7EFD76008C92C5 /* UPConfirmDeviceSharingRelationApi.m in Sources */,
				221AA34C277ED064008143F4 /* ShopServerAPIBase.m in Sources */,
				AD12D62B26438AB800188BC3 /* UPUpdateDeviceInfoApi.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		AD8DD1F52643F12900086CBF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = ADB50C7E2643848F00062881 /* UserDomainAPIs */;
			targetProxy = AD8DD1F42643F12900086CBF /* PBXContainerItemProxy */;
		};
		AD8DD1F72643F23300086CBF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AD12D7482643933E00188BC3 /* UserDomainDataSource */;
			targetProxy = AD8DD1F62643F23300086CBF /* PBXContainerItemProxy */;
		};
		AD8DD1F92643F23D00086CBF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AD12D63726438F9F00188BC3 /* upuserdomain */;
			targetProxy = AD8DD1F82643F23D00086CBF /* PBXContainerItemProxy */;
		};
		AD8DD1FB2643F24200086CBF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AD12D63726438F9F00188BC3 /* upuserdomain */;
			targetProxy = AD8DD1FA2643F24200086CBF /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		71ECF94B241A2C0D009BBAD0 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				71ECF94C241A2C0D009BBAD0 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		71ECF950241A2C0D009BBAD0 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				71ECF951241A2C0D009BBAD0 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		71ECF956241A2C0D009BBAD0 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 54F389628B7448F69016EEC7 /* Pods-Debugger.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "Apple Development: Lin Luan (L8DG76PVBL)";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = PP27UD8NYZ;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = Debugger/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = Uplus99Dev;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		71ECF957241A2C0D009BBAD0 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 28B017AD855424FCE3E0C745 /* Pods-Debugger.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "Apple Development: Lin Luan (L8DG76PVBL)";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = PP27UD8NYZ;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = Debugger/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = Uplus99Dev;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		72321FA72255DE1F005F6A86 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		72321FA82255DE1F005F6A86 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		72321FAD2255DE1F005F6A86 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 03975CE6D71736702F84FEE9 /* Pods-upuserdomainTests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = upuserdomainTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = mh_bundle;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC,-lz",
					"-ObjC",
					"-l\"Cucumberish\"",
					"-l\"OCMock\"",
					"-framework",
					"\"XCTest\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.uhome.haier.upuserdomainTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]" = "";
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		72321FAE2255DE1F005F6A86 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2F763910D9DB3EF678D1714A /* Pods-upuserdomainTests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = upuserdomainTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = mh_bundle;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC,-lz",
					"-ObjC",
					"-l\"Cucumberish\"",
					"-l\"OCMock\"",
					"-framework",
					"\"XCTest\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.uhome.haier.upuserdomainTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]" = "";
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		AD12D63F26438F9F00188BC3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BD365F3E3A34F003F0F44B09 /* Pods-upuserdomain.debug.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		AD12D64026438F9F00188BC3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BF0FDC8008868F802BADFABE /* Pods-upuserdomain.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		AD12D7502643933E00188BC3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 17FE49FAA2DB41B7CEB10E77 /* Pods-UserDomainDataSource.debug.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		AD12D7512643933E00188BC3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2FEB87918B976CE9D9222179 /* Pods-UserDomainDataSource.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		ADB50C862643848F00062881 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A65CF39308F3D3BA350A8097 /* Pods-UserDomainAPIs.debug.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		ADB50C872643848F00062881 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 85439034EEEFBA9A132FD802 /* Pods-UserDomainAPIs.release.xcconfig */;
			buildSettings = {
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		71ECF958241A2C0D009BBAD0 /* Build configuration list for PBXNativeTarget "Debugger" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				71ECF956241A2C0D009BBAD0 /* Debug */,
				71ECF957241A2C0D009BBAD0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		72321F782255DE1E005F6A86 /* Build configuration list for PBXProject "upuserdomain" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				72321FA72255DE1F005F6A86 /* Debug */,
				72321FA82255DE1F005F6A86 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		72321FAC2255DE1F005F6A86 /* Build configuration list for PBXNativeTarget "upuserdomainTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				72321FAD2255DE1F005F6A86 /* Debug */,
				72321FAE2255DE1F005F6A86 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AD12D63E26438F9F00188BC3 /* Build configuration list for PBXNativeTarget "upuserdomain" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AD12D63F26438F9F00188BC3 /* Debug */,
				AD12D64026438F9F00188BC3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AD12D74F2643933E00188BC3 /* Build configuration list for PBXNativeTarget "UserDomainDataSource" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AD12D7502643933E00188BC3 /* Debug */,
				AD12D7512643933E00188BC3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		ADB50C852643848F00062881 /* Build configuration list for PBXNativeTarget "UserDomainAPIs" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				ADB50C862643848F00062881 /* Debug */,
				ADB50C872643848F00062881 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 72321F752255DE1E005F6A86 /* Project object */;
}
